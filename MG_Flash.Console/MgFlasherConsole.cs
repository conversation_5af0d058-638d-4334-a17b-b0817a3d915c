﻿
using DiagProtocolInterface;
using MgFlasher.Ecu.Client.Commands;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NLog.Extensions.Logging;
using UNI_Flash;

namespace MG_Flash.Console
{
    class MgFlasherConsole
    {
        private static ISet<int> _progressValues = new HashSet<int>();
        private static long _flashTotalBytes;
        private static int _totalBytesCompleted;
        private static int _lastBytesCompleted;
        private static IServiceProvider _serviceProvider;
        private static ILogger<MgFlasherConsole> _logger;
        private static ILoggerFactory _loggerFactory;

        static async Task<int> Main(string[] args)
        {
            _serviceProvider = BuildServiceProvider();
            _logger = _serviceProvider.GetRequiredService<ILogger<MgFlasherConsole>>();
            _loggerFactory = _serviceProvider.GetRequiredService<ILoggerFactory>();

            AttachEvents();
            InitUcl();
            await AttachCable(args);
            return MgFlasherCore.Main(args);
        }

        private static void AttachEvents()
        {
            MgFlasherCore.FlashTotalBytesDetermined += Program_FlashTotalBytesDetermined;
            MgFlasherCore.ProgressSegmentBytesCompleted += Program_ProgressSegmentBytesCompleted;
            MgFlasherCore.ProgressUpdated += Program_ProgressUpdated;
            MgFlasherCore.OnMessage += Program_OnMessage;
        }

        private static async Task AttachCable(string[] args)
        {
            var mode = args.ToList().IndexOf("-p") is int index && index != -1 ?
                args.ElementAtOrDefault(index + 1)?.Replace("ENET", "", StringComparison.InvariantCultureIgnoreCase) : null;

            string host;
            int port;

            if (string.IsNullOrEmpty(mode) || mode.Contains("AUTO", StringComparison.InvariantCultureIgnoreCase))
            {
                var command = new GetDetectedEcuQuery();
                var handler = new GetDetectedEcuQueryHandler(_serviceProvider.GetRequiredService<ILogger<GetDetectedEcuQueryHandler>>());
                var result = await handler.Handle(command, CancellationToken.None);
                if(!result.Success)
                {
                    throw new InvalidOperationException("Could not detect any ECU");
                }
                host = result.IpEndpoint.Address.ToString();
                port = result.IpEndpoint.Port;
            }
            else
            {
                var spliited = mode.Split(":");
                host = spliited[0];
                port = int.Parse(spliited[1]);
            }

            var diag = MgFlasherCore._UDSDiagIf = new UDS_BMW(_serviceProvider.GetRequiredService<ILogger<UDS_BMW>>());
            var cable = diag.AttachCable(host, port, _loggerFactory);

            MgFlasherCore.attachedCable = cable;

            if (cable is null)
            {
                throw new ArgumentException(nameof(port));
            }

            if (!diag.AttachCableToTransportWrapper(cable, _loggerFactory))
            {
                throw new ArgumentException(nameof(port));
            }
        }

        private static void InitUcl()
        {
            var uclNativeService = new UclNativeService();
            MgFlasherCore.Ucl_Nrv = new Ucl_NRV(uclNativeService, _serviceProvider.GetRequiredService<ILogger<Ucl_NRV>>());
        }

        private static void Program_FlashTotalBytesDetermined(object sender, long e)
        {
            _flashTotalBytes = e;
            _totalBytesCompleted = 0;
            _lastBytesCompleted = -1;
        }

        private static void Program_ProgressUpdated(object sender, double e)
        {
            if (Math.Abs(e) < 0.001)
            {
                _progressValues.Clear();
            }

            if (Math.Abs(e) >= 200)
            {
                var segVal = (int)(e * 1000);
                if (_progressValues.Add(segVal))
                {
                    _logger.LogInformation("                                    Total progress updated -> {segVal}%", (segVal - 200000) / 10d);
                }
            }

            var val = (int)(e * 1000);
            if (_progressValues.Add(val))
            {
                _logger.LogInformation("Segment progress updated -> {val}%", val / 10d);
            }
        }

        private static void Program_ProgressSegmentBytesCompleted(object sender, int e)
        {
            if (e < _lastBytesCompleted)
            {
                _totalBytesCompleted = _lastBytesCompleted;
            }
            var flashProgress = (100 * (double)(_totalBytesCompleted + e) / _flashTotalBytes) / 100.0;
            Program_ProgressUpdated(e, flashProgress + 200);
            _lastBytesCompleted = e;
        }

        private static void Program_OnMessage(object sender, string e)
        {
            _logger.LogInformation(e);
        }
        
        private static IServiceProvider BuildServiceProvider()
        {
            var services = new ServiceCollection();

            services.AddLogging(builder =>
            {
                builder.SetMinimumLevel(LogLevel.Information);
                builder.AddNLog("nlog.config");
            });
            
            return services.BuildServiceProvider();
        }
    }
}
