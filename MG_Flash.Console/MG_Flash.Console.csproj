﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net9.0</TargetFramework>
		<ControlFlowObfuscation>if=on;goto=on;switch=on;case=on;true</ControlFlowObfuscation>
		<ControlFlowIterations>3</ControlFlowIterations>
		<StringEncryption>true</StringEncryption>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Condition="'$(Configuration)' == 'Release' " Include="Babel.Obfuscator" Version="10.9.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
		<PackageReference Include="NLog.Web.AspNetCore" Version="5.3.11" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="ucl.x86.dll" />
		<None Remove="ucl.x64.dll" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="ucl.x64.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</EmbeddedResource>
		<EmbeddedResource Include="ucl.x86.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\MgFlasher.Ecu.Client\MgFlasher.Ecu.Client.csproj" />
		<ProjectReference Include="..\MG_Flash.Core\MG_Flash.Core.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="NLog.config">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>