
mg1_flash First install quickstart:

1) make a ful read from stock DME with "mg1_flash -m DME -r 5" (to get a full readout)
2) use your favourite editor to put in the changed DST into your_read_stock.bin and save to your_stock_plus_changed_dst.bin
3) correct/prepare a patched bin with (progcounter freeze) and crc correction: "dme8x1_sum -p your_stock_plus_changed_dst.bin your_stock_plus_changed_dst_patched_crcok.bin"
4) upload orginal: "mg1_flash -m DME -bi -w 6 -i your_read_stock.bin"
5) upload patched btld with: "mg1_flash -m DME -f 992296 -bp -w 4 -i your_stock_plus_changed_dst_patched_crcok.bin"
6) upload dst with: "mg1_flash -m DME -f 992296 -bp -w 1 -i your_stock_plus_changed_dst_patched_crcok.bin"
-w 1 - when dst only
-w 3 - when PST +DST applying read protection
7) after this last upload the ecu should be running, coding restored all codes cleared

mg1_flash Any following (DST) upload quickstart:

1) use your favourite editor to make (some more) changes to your_stock_plus_changed_dst.bin
1) crc correct file with: "dme8x1_sum -p your_stock_plus_changed_dst.bin your_stock_plus_changed_dst_patched_crcok.bin"
2) upload to DME: "mg1_flash -m DME -w 1 -i your_patched_plus_changed_dst_crcok.bin"
3)when WiFi : mg1_flash -p WIFI:192.168.4.1:23 -m DME -w 1 -i 

Full recovery backup with orginal btld.

1) mg_flash -p ENET:192.168.1.80 -m DME -w 4 -i
2) mg_flash -p ENET -m DME -f 992296 -w 3 -i

when ECU in stuck in Boot 

mg_flash -p ENET:192.168.1.80 -m DME -f 992296 -bp -w 3 -i

when DST fail and ecu in boot

mg_flash -p ENET:192.168.1.80 -m DME -f 992296 -bp -w 2 -i


WRITING NORMALLY:

DST:
mg_flash -p ENET -m DME -w 1 -i

PST:
mg_flash -p ENET -m DME -w 2 -i

DST+PST:
mg_flash -p ENET -m DME -w 3 -i

ENET remotly 

orginal version

Read DTC
mg_flash -p ENET:192.168.1.80 -m DME -d

Damian build

MG_Flash.Console.exe -p ENET:99.246.136.39:6801 -m DME -d

