﻿using DiagProtocolUtils;
using System;
using System.Reflection;
using System.Runtime.InteropServices;


namespace MG_Flash.Console
{
    public class UclNativeService : IUclNativeService
    {
        public const string UclLib = "ucl";

        static UclNativeService()
        {
            NativeLibrary.SetDllImportResolver(Assembly.GetExecutingAssembly(), DllImportResolver);
        }

        private static IntPtr DllImportResolver(string libraryName, Assembly assembly, DllImportSearchPath? searchPath)
        {
            if (libraryName != "ucl")
            {
                return IntPtr.Zero;
            }

            var libName = IntPtr.Size switch
            {
                4 when RuntimeInformation.IsOSPlatform(OSPlatform.Windows) => "ucl.x86.dll",
                8 when RuntimeInformation.IsOSPlatform(OSPlatform.Windows) => "ucl.x64.dll",
                _ when RuntimeInformation.IsOSPlatform(OSPlatform.Linux) => "ucl",
                _ => throw new PlatformNotSupportedException("Cannot resolve lib ucl for runtime platform")
            };

            return NativeLibrary.Load(libName, assembly, searchPath);
        }

        public int Ucl_init()
        {
            return ucl_init();
        }

        public int Ucl_nrv2db_99_compress(IntPtr src, uint src_len, IntPtr dst, ref uint dst_len, IntPtr cb, int level, IntPtr conf, IntPtr result)
        {
            return ucl_nrv2db_99_compress(src, src_len, dst, ref dst_len, cb, level, conf, result);
        }

        public int Ucl_nrv2db_decompress_8(IntPtr src, uint src_len, IntPtr dst, ref uint dst_len, IntPtr wrkmem)
        {
            return ucl_nrv2db_decompress_8(src, src_len, dst, ref dst_len, wrkmem);
        }

        public IntPtr Ucl_version_string()
        {
            return ucl_version_string();
        }

        [DllImport(UclLib)]
        private static extern int ucl_init();

        [DllImport(UclLib)]
        private static extern IntPtr ucl_version_string();

        [DllImport(UclLib)]
        private static extern int ucl_nrv2db_decompress_8(IntPtr src, uint src_len,
                                        IntPtr dst, ref uint dst_len,
                                        IntPtr wrkmem);

        [DllImport(UclLib)]
        private static extern int ucl_nrv2db_99_compress(IntPtr src, uint src_len,
                                   IntPtr dst, ref uint dst_len,
                                   IntPtr cb,
                                   int level,
                                   IntPtr conf,
                                   IntPtr result);
    }
}