﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MgFlasher.CustomCode.Configuration;
using MgFlasher.CustomCode.Extensions;
using MgFlasher.CustomCode.Models;
using MgFlasher.CustomCode.Options.Configuration.User;
using MgFlasher.CustomCode.Options.DTC;
using MgFlasher.CustomCode.Options.Extensions;
using MgFlasher.CustomCode.Services;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Options;

public partial class CustomCodeOptionsApplier : ICustomCodeOptionsApplier
{
    private readonly IReferenceFileVersionExtractor _mapDescriptorExtractor;
    private readonly ICustomCodeOptionAddressResolver _addressResolver;
    private readonly ICustomCodeOptionValueResolver _valueResolver;
    private readonly IDtcCodeRemover _dtcRemover;
    private readonly ILogger<CustomCodeOptionsApplier> _logger;

    public CustomCodeOptionsApplier(
        IReferenceFileVersionExtractor mapDescriptorExtractor,
        ICustomCodeOptionAddressResolver addressResolver,
        ICustomCodeOptionValueResolver valueResolver,
        IDtcCodeRemover dtcRemover,
        ILogger<CustomCodeOptionsApplier> logger)
    {
        _mapDescriptorExtractor = mapDescriptorExtractor;
        _addressResolver = addressResolver;
        _valueResolver = valueResolver;
        _dtcRemover = dtcRemover;
        _logger = logger;
    }

    public async Task ApplyAsync(CustomOptionsUserConfiguration options, CustomCodeContext carInfo, byte[] data)
    {
        try
        {
            var fileInfo = _mapDescriptorExtractor.Extract(data, carInfo.ProcessorArchitectureType);

            if (options == null || carInfo.Stage == StageType.Stock)
            {
                _logger.LogInformation("custom options are null, probably flashing stock map...");
                return;
            }

            if (options.ForceFlashPST
                && options.ForceFlashPST_WithoutCustomCode
                && carInfo.Stage == StageType.Custom
                && fileInfo != null
                && fileInfo.CustomCodeId != null)
            {
                _logger.LogInformation($"Setting target Custom Code version to [{fileInfo.CustomCodeId}]...");
                carInfo.SetTargetCustomCodeVersion(fileInfo.CustomCodeId.ToVersion().ToString());
            }

            _logger.LogInformation("applying custom options...");

            #region OPF Delete

            // OPF Delete
            if (options.OpfDelete)
            {
                _logger.LogInformation("applying OPF Delete...");
                var _customOptionName = "OPF Delete";

                var _data___PFlt_stActv_C = new byte[] { 0x00 };
                var _data___PFlt_stPfilTyp_C = new byte[] { 0x00 };
                var _data___BMWgpfdiag_rf_LimPclFilMin_T = new byte[] { 0x7F, 0xFF, 0x7F, 0xFF, 0x7F, 0xFF, 0x7F, 0xFF, };

                await data.ApplyMapDataAsync(fileInfo, carInfo, "PFlt_stActv_C", _data___PFlt_stActv_C, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "PFlt_stPfilTyp_C", _data___PFlt_stPfilTyp_C, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWgpfdiag_rf_LimPclFilMin_T", _data___BMWgpfdiag_rf_LimPclFilMin_T, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                var dtcRemovalList = new List<string>
                {
                    "DFC_HsLnIceAcmPPFltDiff_C",
                    "DFC_PFltAshLoadMax_C",
                    "DFC_NplPresSensPPFltDiff_C",
                    "DFC_NplHsChngPPFltDiff_C",
                    "DFC_SRCMaxPPFltDiff_C",
                    "DFC_SRCMinPPFltDiff_C",
                    "DFC_SotPPFltDiff_C",
                    "DFC_PFltPfilDmgTotPBasdB1_C",
                    "DFC_BMW_GPF_PMAX_C",
                    "DFC_BMW_GPF_PMAX_QCKBS_C",
                    "DFC_BMW_GPF_PWARN_C",
                    "DFC_BMW_GPF_PWARNNORGN_C",
                };

                foreach (var code in dtcRemovalList)
                {
                    await _dtcRemover.RemoveAsync(code, fileInfo.A2L, data, optional: true);
                }
            }
            else
            {
                _logger.LogInformation("OPF delete is not enabled");
            }

            #endregion OPF Delete

            #region LSD Installed Option

            // LsdInstalled
            if (options.LsdInstalled)
            {
                _logger.LogInformation("applying LSD Installed...");
                var _customOptionName = "LSD Installed";

                var finalDriveRatio_Eeprom = (int)Math.Round(options.LsdInstalledFinalDrive * 1024, 0);
                if (options.LsdInstalledFinalDrive > 5.0)
                {
                    finalDriveRatio_Eeprom = 5 * 1024;
                }
                else if (options.LsdInstalledFinalDrive < 2.0)
                {
                    finalDriveRatio_Eeprom = 2 * 1024;
                }

                var _data___K_I_HA = new byte[] { (byte)((finalDriveRatio_Eeprom) >> 8), (byte)(finalDriveRatio_Eeprom) };
                var _data___K_I_HAAT = new byte[] { (byte)((finalDriveRatio_Eeprom) >> 8), (byte)(finalDriveRatio_Eeprom) };
                var _data___K_I_HADKG = new byte[] { (byte)((finalDriveRatio_Eeprom) >> 8), (byte)(finalDriveRatio_Eeprom) };

                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_I_HA", _data___K_I_HA, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_I_HAAT", _data___K_I_HAAT, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_I_HADKG", _data___K_I_HADKG, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
            }
            else
            {
                _logger.LogInformation("LSD Installed is not enabled");
            }

            #endregion LSD Installed Option

            #region Cold start delete Option

            // Cold Start Delete
            if (options.ColdStartDelete)
            {
                _logger.LogInformation("applying cold start delete...");
                var _customOptionName = "Cold Start Delete";

                var _data___BMWchst_t_CatMax_C = new byte[] { 0x00, 0x00 };

                var _data___BMWchas_v_VehMax_C = new byte[] { 0x00, 0x00 };
                var _data___BMWchas_dst_TstrDi_C = new byte[] { 0x00, 0x00 };
                var _data___BMWchas_t_CatMax_C = new byte[] { 0x00, 0x00 };

                var _data___8x8_BMWchold_st_OpmBasc_M = new byte[] {
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                };

                if (options.ColdStartDeleteIfWarm)
                {
                    _logger.LogInformation("ColdStartDeleteIfWarm: applying 80 Degrees for BMWchxx_t_CatMax_C maps...");
                    _data___BMWchst_t_CatMax_C = new byte[] { 0x03, 0x20 };
                    _data___BMWchas_t_CatMax_C = new byte[] { 0x03, 0x20 };
                }
                else
                {
                    _logger.LogInformation("ColdStartDeleteAlways: applying 0 Degrees for BMWchxx_t_CatMax_C maps...");
                }

                // Aurix only: BMWchst_t_CatMax_C
                await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWchst_t_CatMax_C", _data___BMWchst_t_CatMax_C, valueBitSize: 16, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                // PPC only: BMWchas_v_VehMax_C, BMWchas_dst_TstrDi_C, BMWchas_t_CatMax_C
                await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWchas_v_VehMax_C", _data___BMWchas_v_VehMax_C, valueBitSize: 16, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWchas_dst_TstrDi_C", _data___BMWchas_dst_TstrDi_C, valueBitSize: 16, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWchas_t_CatMax_C", _data___BMWchas_t_CatMax_C, valueBitSize: 16, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                if (fileInfo.Pst.Id == 0x3E7D)
                {
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWchold_st_OpmBasc_M", _data___8x8_BMWchold_st_OpmBasc_M, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
            }
            else
            {
                _logger.LogInformation("Cold start delete is not enabled");
            }

            #endregion Cold start delete Option

            #region Dcat Option

            // Dcat
            if (options.Dcat)
            {
                _logger.LogInformation("applying dcat...");
                var _customOptionName = "Dcat";

                var _data___CDKAT = new byte[] { 0x00 };
                var _data___CDLSH = new byte[] { 0x00 };

                await data.ApplyMapDataAsync(fileInfo, carInfo, "CDKAT", _data___CDKAT, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CDLSH", _data___CDLSH, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                var dtcRemovalList = new List<string>
                {
                    "DFC_EGSDUS2B1LtrPT1_C",
                    "DFC_HEGOS2B1ElecSig_C",
                    "DFC_TWCDPriCatB1_C",
                    "DFC_HEGOS2B1HtrPsMax_C",
                    "DFC_HEGOS2B1HtrPsMin_C",
                    "DFC_HEGOS2B1HtrPsSig_C",
                    "DFC_EGSDUS2B1LtrDly_C",
                    "DFC_HEGOS2B1ElecMin_C",
                    "DFC_HEGOS2B1HtgNpl_C",
                    "DFC_EGSDUS2B1RtlDly_C",
                    "DFC_TWCDPriCatB2_C",
                    "DFC_EGSDUS2B1TarRich_C",
                    "DFC_TWCDScndCatB1_C",
                };

                foreach (var code in dtcRemovalList)
                {
                    await _dtcRemover.RemoveAsync(code, fileInfo.A2L, data, optional: true);
                }
            }
            else
            {
                _logger.LogInformation("D-Cat is not enabled");
            }

            #endregion Dcat Option

            #region Intake Installed Option

            // Intake Installed
            if (options.IntakeInstalled)
            {
                _logger.LogInformation("applying Intake Installed...");

                var dtcRemovalList = new List<string>
                {
                    "DFC_BMW_HFMPLCD_MX_C",
                };

                foreach (var code in dtcRemovalList)
                {
                    await _dtcRemover.RemoveAsync(code, fileInfo.A2L, data, optional: true);
                }
            }
            else
            {
                _logger.LogInformation("Intake Installed is not enabled");
            }

            #endregion Intake Installed Option

            #region Vmax Option

            // Vmax
            if (options.Vmax)
            {
                _logger.LogInformation("applying Vmax...");
                var _customOptionName = "Vmax";

                var _data___K_VLIM_APPLI = new byte[] { 0x44, 0x5C };
                var _data___K_VMAX_SPA = new byte[] { 0x0D, 0xAC };
                var _data___CWVMAXCOD = new byte[] { 0x00 };
                var _data___VMAXOVC = new byte[] { 0xAF, 0x00 };
                var _data___K_BZA_FAHRER_RANGV = new byte[] { 0x00, 0x00 };
                var _data___K_SCH_VSICHKK = new byte[] { 0x01, 0x5E };
                var _data___VROHMAX = new byte[] { 0xC0, 0x80 };
                var _data___BMWlhm_v_VehLimApplVal_C = new byte[] { 0x13, 0x88 };

                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_VLIM_APPLI", _data___K_VLIM_APPLI, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_VMAX_SPA", _data___K_VMAX_SPA, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWVMAXCOD", _data___CWVMAXCOD, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMAXOVC", _data___VMAXOVC, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_BZA_FAHRER_RANGV", _data___K_BZA_FAHRER_RANGV, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_SCH_VSICHKK", _data___K_SCH_VSICHKK, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VROHMAX", _data___VROHMAX, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWlhm_v_VehLimApplVal_C", _data___BMWlhm_v_VehLimApplVal_C, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
            }
            else
            {
                _logger.LogInformation("Vmax is not enabled");
            }

            #endregion Vmax Option

            #region Exhaust Flap Option

            // Exhaust Flap Always Open
            if (options.ExhaustFlapAlwaysOpen)
            {
                _logger.LogInformation("applying exhaust flap always open...");
                var _customOptionName = "Exhaust Flap Always Open";

                // New method:
                //set all minimum pedal threshold percentages to 0.0% (0x00 0x00)
                var _data___12x10_ThresholdPercentage = new byte[] {
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                };
                var _data___8x8_ThresholdPercentage = new byte[] {
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                };
                var _data___8x6_ThresholdPercentage = new byte[] {
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                };

                ////set all minimum pedal threshold percentages to 7.03% (0x12 0x00)
                //var _data___12x10_ThresholdPercentage = new byte[] {
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //};
                //var _data___8x8_ThresholdPercentage = new byte[] {
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //};
                //var _data___8x6_ThresholdPercentage = new byte[] {
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //    0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00, 0x12, 0x00,
                //};

                var _data___1x1_CodeWord = new byte[] { 0x4D };
                // CodeWord breakdown:
                // bit 0 = Flag AKR_flgReqExhFlapClsng => try 0
                // bit 1 = Flag AKR_flgWpedBtnStrt => try 0 ?
                // bit 2 = Regarding speed ? => try 1
                // bit 3 = 'else pathway' => try 1
                // bit 4 = difference of pedal/ torque request => try 1
                // bit 5 = Invert AKR_stFlapEngShtOff_C => set 1 to use noninverted value of AKR_stFlapEngShtOff_C(which should be true)
                // bit 6 = Flag AKR_stSollPosExtEgFlap => try 0 to force selection of bit 7 as value
                // bit 7 = depends on bit 6 => try 1

                var _data___VMINAKR = new byte[] { 0x00 };
                var _data___VMINAKR_VAR1 = new byte[] { 0x00 };
                var _data___VMINAKR_VAR2 = new byte[] { 0x00 };
                var _data___VMINAKR_VAR3 = new byte[] { 0x00 };

                //S58 Specific data
                var _data___BMWefc_swi_ManSp_C = new byte[] { 0x01 };
                var _data___BMWefc_posn_ManSp1_C = new byte[] { 0x64 };
                var _data___BMWefc_posn_ManSp2_C = new byte[] { 0x64 };

                if (fileInfo.Pst.Id == 0x5C64)
                {
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWefc_swi_ManSp_C", _data___BMWefc_swi_ManSp_C, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWefc_posn_ManSp1_C", _data___BMWefc_posn_ManSp1_C, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWefc_posn_ManSp2_C", _data___BMWefc_posn_ManSp2_C, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }

                // 12x10
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC[0]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC[1]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT[0]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT[1]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM[0]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM[1]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM[0]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM[1]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCO", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCO[0]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCO[1]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOAT", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOAT[0]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOAT[1]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOATM", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOATM[0]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOATM[1]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOM", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOM[0]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOM[1]", _data___12x10_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                // 8x8
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM_VAR1", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM_VAR1[0]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM_VAR1[1]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM_VAR2", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM_VAR2[0]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM_VAR2[1]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM_VAR3", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM_VAR3[0]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCATM_VAR3[1]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT_VAR1", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT_VAR1[0]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT_VAR1[1]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT_VAR2", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT_VAR2[0]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT_VAR2[1]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT_VAR3", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT_VAR3[0]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCAT_VAR3[1]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOATM_VAR1", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOATM_VAR1[0]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOATM_VAR1[1]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOAT_VAR1", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOAT_VAR1[0]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOAT_VAR1[1]", _data___8x8_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                // 8x6
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM_VAR1", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM_VAR1[0]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM_VAR1[1]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM_VAR2", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM_VAR2[0]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM_VAR2[1]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM_VAR3", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM_VAR3[0]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCCM_VAR3[1]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC_VAR1", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC_VAR1[0]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC_VAR1[1]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC_VAR2", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC_VAR2[0]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC_VAR2[1]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC_VAR3", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC_VAR3[0]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCC_VAR3[1]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOM_VAR1", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOM_VAR1[0]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCOM_VAR1[1]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCO_VAR1", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCO_VAR1[0]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AKRGRCO_VAR1[1]", _data___8x6_ThresholdPercentage, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                // 1x1
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR[0]", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR[1]", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR_VAR1", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR_VAR1[0]", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR_VAR1[1]", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR_VAR2", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR_VAR2[0]", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR_VAR2[1]", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR_VAR3", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR_VAR3[0]", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CWAKR_VAR3[1]", _data___1x1_CodeWord, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR", _data___VMINAKR, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR[0]", _data___VMINAKR, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR[1]", _data___VMINAKR, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR_VAR1", _data___VMINAKR_VAR1, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR_VAR1[0]", _data___VMINAKR_VAR1, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR_VAR1[1]", _data___VMINAKR_VAR1, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR_VAR2", _data___VMINAKR_VAR2, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR_VAR2[0]", _data___VMINAKR_VAR2, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR_VAR2[1]", _data___VMINAKR_VAR2, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR_VAR3", _data___VMINAKR_VAR3, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR_VAR3[0]", _data___VMINAKR_VAR3, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "VMINAKR_VAR3[1]", _data___VMINAKR_VAR3, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
            }
            else
            {
                _logger.LogInformation("Exhaust Flap Always Open is not enabled");
            }

            #endregion Exhaust Flap Option

            #region Power Gauge Option

            {
                // Power Gauge option
                if (options.PowerGauge)
                {
                    _logger.LogInformation("applying power gauges...");
                    var _customOptionName = "Power Gauge";

                    //800 kW => (100 units (internal multiplier of 8)) round to nearest multiple of 10
                    //(int)(Math.Ceiling((physicalValue / K_EDA_ANZ_SPORT_BEREICHE) / 10.0) * 10)

                    // K_EDA_P_ANZ_SPORT_BS_SCAL_CODEXXX value is multiplied by K_EDA_ANZ_SPORT_BEREICHE (8) to get max value on scale
                    // K_EDA_P_ANZ_SPORT_BS_SCAL_CODE0 => Default units (kW)
                    // K_EDA_P_ANZ_SPORT_BS_SCAL_CODE1 => kW
                    // K_EDA_P_ANZ_SPORT_BS_SCAL_CODE2 => hp
                    // K_EDA_P_ANZ_SPORT_BS_SCAL_CODE3 => invalid (ungültig)
                    if (options.PowerGaugeMax_kW <= 100 * 8 && options.PowerGaugeMax_kW >= 10 * 8) // 800 kW > x > 80 kW
                    {
                        var _data___K_EDA_P_ANZ_SPORT_BS_SCAL_CODE_kW = new byte[] { (byte)(int)(Math.Ceiling((options.PowerGaugeMax_kW / 8) / 10.0) * 10) };
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "K_EDA_P_ANZ_SPORT_BS_SCAL_CODE0", _data___K_EDA_P_ANZ_SPORT_BS_SCAL_CODE_kW, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "K_EDA_P_ANZ_SPORT_BS_SCAL_CODE1", _data___K_EDA_P_ANZ_SPORT_BS_SCAL_CODE_kW, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "K_EDA_P_ANZ_SPORT_BS_SCAL_CODE3", _data___K_EDA_P_ANZ_SPORT_BS_SCAL_CODE_kW, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    }

                    if (options.PowerGaugeMax_hp <= 120 * 8 && options.PowerGaugeMax_hp >= 10 * 8) // 960 hp > x > 80 hp
                    {
                        var _data___K_EDA_P_ANZ_SPORT_BS_SCAL_CODE_hp = new byte[] { (byte)(int)(Math.Ceiling((options.PowerGaugeMax_hp / 8) / 10.0) * 10) };
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "K_EDA_P_ANZ_SPORT_BS_SCAL_CODE2", _data___K_EDA_P_ANZ_SPORT_BS_SCAL_CODE_hp, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    }
                }
                else
                {
                    _logger.LogInformation("Power gauge option is not enabled");
                }
            }

            #endregion Power Gauge Option

            #region Torque Gauge Option

            {
                // Torque Gauge option
                if (options.PowerGauge)
                {
                    _logger.LogInformation("applying torque gauges...");
                    var _customOptionName = "Torque Gauge";

                    //1040 kW => (130 units (internal multiplier of 8)) round to nearest multiple of 10
                    //(int)(Math.Ceiling((physicalValue / K_EDA_ANZ_SPORT_BEREICHE) / 10.0) * 10)

                    // K_EDA_MD_ANZ_BS_SCAL_CODEXXX value is multiplied by K_EDA_ANZ_SPORT_BEREICHE (8) to get max value on scale
                    // K_EDA_MD_ANZ_BS_SCAL_CODE0 => Default units (Nm)
                    // K_EDA_MD_ANZ_BS_SCAL_CODE1 => Nm
                    // K_EDA_MD_ANZ_BS_SCAL_CODE2 => lb-ft
                    // K_EDA_MD_ANZ_BS_SCAL_CODE3 => kgm
                    // K_EDA_MD_ANZ_BS_SCAL_CODE4 => invalid (ungültig)
                    if (options.TorqueGaugeMax_Nm <= 120 * 8 && options.TorqueGaugeMax_Nm >= 10 * 8) // 960 Nm > x > 80 Nm
                    {
                        var _data___K_EDA_MD_ANZ_BS_SCAL_CODE_Nm = new byte[] { (byte)(int)(Math.Ceiling((options.TorqueGaugeMax_Nm / 8) / 10.0) * 10) };
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "K_EDA_MD_ANZ_BS_SCAL_CODE0", _data___K_EDA_MD_ANZ_BS_SCAL_CODE_Nm, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "K_EDA_MD_ANZ_BS_SCAL_CODE1", _data___K_EDA_MD_ANZ_BS_SCAL_CODE_Nm, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "K_EDA_MD_ANZ_BS_SCAL_CODE4", _data___K_EDA_MD_ANZ_BS_SCAL_CODE_Nm, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    }

                    if (options.TorqueGaugeMax_lbft <= 100 * 8 && options.TorqueGaugeMax_lbft >= 10 * 8) // 800 lbft > x > 80 lbft
                    {
                        var _data___K_EDA_MD_ANZ_BS_SCAL_CODE_lbft = new byte[] { (byte)(int)(Math.Ceiling((options.TorqueGaugeMax_lbft / 8) / 10.0) * 10) };
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "K_EDA_MD_ANZ_BS_SCAL_CODE2", _data___K_EDA_MD_ANZ_BS_SCAL_CODE_lbft, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    }

                    if (options.TorqueGaugeMax_kgm <= 20 * 8 && options.TorqueGaugeMax_kgm >= 5 * 8) // 160 kgm > x > 40 kgm
                    {
                        var _data___K_EDA_MD_ANZ_BS_SCAL_CODE_kgm = new byte[] { (byte)(int)(Math.Ceiling((options.TorqueGaugeMax_kgm / 8) / 5.0) * 5) };
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "K_EDA_MD_ANZ_BS_SCAL_CODE3", _data___K_EDA_MD_ANZ_BS_SCAL_CODE_kgm, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    }
                }
                else
                {
                    _logger.LogInformation("Torque gauge option is not enabled");
                }
            }

            #endregion Torque Gauge Option

            #region Radiator Flap Option

            // Radiator Flap
            if (options.RadiatorFlap)
            {
                _logger.LogInformation("applying Radiator Flap...");
                var _customOptionName = "Radiator Flap";

                var _data___KF_ZST_KKS = new byte[] {
                    0x09, 0x09, 0x09, 0x09, 0x09, 0x09,
                    0x09, 0x09, 0x09, 0x09, 0x09, 0x09,
                    0x09, 0x09, 0x09, 0x09, 0x09, 0x09,
                    0x09, 0x09, 0x09, 0x09, 0x09, 0x09,
                    0x09, 0x09, 0x09, 0x09, 0x09, 0x09,
                    0x09, 0x09, 0x09, 0x09, 0x09, 0x09,
                };
                await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_ZST_KKS", _data___KF_ZST_KKS, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                var dtcRemovalList = new List<string>
                {
                    "DFC_RadShtPostOpnBotm_C",
                };

                foreach (var code in dtcRemovalList)
                {
                    await _dtcRemover.RemoveAsync(code, fileInfo.A2L, data, optional: true);
                }
            }
            else
            {
                _logger.LogInformation("Radiator Flap is not enabled");
            }

            #endregion Radiator Flap Option

            #region Cooling Option

            // Cooling
            if (options.Cooling)
            {
                _logger.LogInformation("applying cooling settings... Regular Target Engine Temp");
                var _customOptionName = "Cooling";

                // [90-110] multiply by 100, then read hex split as high & low bytes
                var _rTETBytes = new byte[2]; //_regularTargetEngineTemperatureBytes
                _rTETBytes[0] = (byte)((options.MaxCoolingRegularTargetEngineTemperature * 100) >> 8);
                _rTETBytes[1] = (byte)(options.MaxCoolingRegularTargetEngineTemperature * 100);

                var _regularTargetEngineTemperatureShort = new byte[] { _rTETBytes[0], _rTETBytes[1] };
                var _regularTargetEngineTemperatureLong = new byte[] { _rTETBytes[0], _rTETBytes[1], _rTETBytes[0], _rTETBytes[1], _rTETBytes[0], _rTETBytes[1], _rTETBytes[0], _rTETBytes[1], };

                await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_TKW_NO", _regularTargetEngineTemperatureLong, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_TKW_HI", _regularTargetEngineTemperatureLong, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_TKW_VWM_MX", _regularTargetEngineTemperatureShort, valueBitSize: 16, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_TKW_EC", _regularTargetEngineTemperatureShort, valueBitSize: 16, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_TKW_EC", _regularTargetEngineTemperatureLong, valueBitSize: 16, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_TKW_WA", _regularTargetEngineTemperatureShort, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_TKW_HZ", _regularTargetEngineTemperatureShort, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_WA_VSKR_TMOT", _regularTargetEngineTemperatureShort, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "K_WA_TMOT_MX", _regularTargetEngineTemperatureShort, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                // Max Cooling
                if (options.MaxCooling)
                {
                    _logger.LogInformation("applying Max Cooling settings... first two bytes are Max Cooling Target Engine Temp, followed by two bytes of Target Intercooler Coolant Volume Flow");

                    // [90-110] multiply by 100, then read hex split as high & low bytes
                    var _targetEngineTemperature = new byte[2];
                    _targetEngineTemperature[0] = (byte)((options.MaxCoolingTargetEngineTemperature * 100) >> 8);
                    _targetEngineTemperature[1] = (byte)(options.MaxCoolingTargetEngineTemperature * 100);

                    // ([1500-1800] multiply by 2
                    // read hex split as high & low bytes
                    var _targetIntercoolerCoolantVolumeFlow = new byte[2];
                    _targetIntercoolerCoolantVolumeFlow[0] = (byte)((options.MaxCoolingTargetIntercoolerCoolantVolumeFlow * 2) >> 8);
                    _targetIntercoolerCoolantVolumeFlow[1] = (byte)(options.MaxCoolingTargetIntercoolerCoolantVolumeFlow * 2);

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_TargetTemperature", _targetEngineTemperature, valueBitSize: 16, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_TargetPumpSpeed", _targetIntercoolerCoolantVolumeFlow, valueBitSize: 16, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_max_cooling", new byte[] { 0x01 }, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                else
                {
                    _logger.LogInformation("Max Cooling is not enabled");
                    if (options.TunerOverrideDst)
                    {
                        _logger.LogInformation("Not disabling max cooling codeword, tuner override is marked!");
                    }
                    else
                    {
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_max_cooling", new byte[] { 0x00 }, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    }
                }
            }
            else
            {
                _logger.LogInformation("Cooling is not enabled");
            }

            #endregion Cooling Option

            #region HPFP Option

            // High Pressure Fuel Pump
            if (options.HpfpInstalled)
            {
                _logger.LogInformation("applying HPFP maps...");
                var _customOptionName = "HPFP";

                if (options.SpoolPerformance_Fx150_Installed
                    || options.SpoolPerformance_Fx180_Installed)
                {
                    _logger.LogInformation("applying Spool Performance FX150/FX180 HPFP changes...");
                    var _data___MfVlv_iHold_C = new byte[] { 0x5A };
                    var _data___MfVDTypC_iPullin_MAP = new byte[] {
                        0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B,
                        0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B,
                        0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B,
                        0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B,
                        0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B };
                    var _data___MfVDTypC_TMSVRECURO = new byte[] { 0x0A, 0xAB };
                    var _data___MfVDTypC_TMSVRECURU = new byte[] { 0x0A, 0xAB };
                    var _data___MfVDTypC_KLVZMSVUB = new byte[] { 0x0E, 0x10, 0x09, 0x60, 0x09, 0x2E, 0x08, 0xFC, 0x08, 0x98 };

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVlv_iHold_C", _data___MfVlv_iHold_C, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_iPullin_MAP", _data___MfVDTypC_iPullin_MAP, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_TMSVRECURO", _data___MfVDTypC_TMSVRECURO, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_TMSVRECURU", _data___MfVDTypC_TMSVRECURU, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_KLVZMSVUB", _data___MfVDTypC_KLVZMSVUB, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                else if (options.SpoolPerformance_Fx170_Installed)
                {
                    _logger.LogInformation("applying Spool Performance FX170 HPFP changes...");

                    if (options.InjectorsInstalled &&
                        (options.XtremeDI_40_Installed
                        || options.XtremeDI_75_Installed))
                    {
                        _logger.LogInformation("Maps KF_PRAIL_H will be overwritten by Xtreme DI injector calibrations.");
                    }

                    var _data___MfVDTypC_iPullin_MAP = new byte[] {
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, };
                    var _data___MfVlv_iHold_C = new byte[] { 0x53 };
                    var _data___MfVDTypC_KLVZMSVUB = new byte[] {
                        0x92, 0x09, 0xCA, 0x08, 0x02, 0x08, 0xD0, 0x07, 0x3A, 0x07 };
                    var _data___MfVDTypC_TMSVRECURO = new byte[] { 0xCB, 0x08 };
                    var _data___KLVSTMSVG = new byte[] {
                        0x14, 0xCC, 0x12, 0xDA, 0x10, 0xEB, 0x10, 0x39, 0x10, 0x5E, 0x0F, 0x64, 0x0F, 0x00, 0x10, 0x17 };
                    var _data___KF_PRAIL_H = new byte[] {
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x56, 0x66, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x5D, 0x4F, 0x65, 0x4F, 0x68, 0x00, 0x68, 0xF5, 0x68, 0xF5,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x6A, 0x3D, 0x6F, 0x4B, 0x76, 0xF9, 0x7A, 0x3D, 0x7A, 0x3D,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x6E, 0xB8, 0x72, 0x8F, 0x7B, 0x85, 0x80, 0x00, 0x80, 0x00,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x86, 0x66,
                        0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x8C, 0xCC,
                        0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x93, 0x33,
                        0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x99, 0x99, };

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_iPullin_MAP", _data___MfVDTypC_iPullin_MAP, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVlv_iHold_C", _data___MfVlv_iHold_C, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_KLVZMSVUB", _data___MfVDTypC_KLVZMSVUB, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_TMSVRECURO", _data___MfVDTypC_TMSVRECURO, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KLVSTMSVG", _data___KLVSTMSVG, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_PRAIL_H", _data___KF_PRAIL_H, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                else if (options.SpoolPerformance_Fx200_Installed)
                {
                    _logger.LogInformation("applying Spool Performance FX200 HPFP changes...");

                    if (options.InjectorsInstalled &&
                        (options.XtremeDI_40_Installed
                        || options.XtremeDI_75_Installed))
                    {
                        _logger.LogInformation("Maps KF_PRAIL_H will be overwritten by Xtreme DI injector calibrations.");
                    }

                    var _data___MfVDTypC_iPullin_MAP = new byte[] {
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, };
                    var _data___MfVlv_iHold_C = new byte[] { 0x53 };
                    var _data___MfVDTypC_KLVZMSVUB = new byte[] {
                        0x92, 0x09, 0xCA, 0x08, 0x02, 0x08, 0xD0, 0x07, 0x3A, 0x07 };
                    var _data___MfVDTypC_TMSVRECURO = new byte[] { 0xCB, 0x08 };
                    var _data___KLVSTMSVG = new byte[] {
                        0x1B, 0x33, 0x18, 0xA8, 0x16, 0x20, 0x15, 0x37, 0x15, 0x67, 0x14, 0x20, 0x13, 0x9D, 0x15, 0x0B };
                    var _data___KF_PRAIL_H = new byte[] {
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x56, 0x66, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x5D, 0x4F, 0x65, 0x4F, 0x68, 0x00, 0x68, 0xF5, 0x68, 0xF5,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x6A, 0x3D, 0x6F, 0x4B, 0x76, 0xF9, 0x7A, 0x3D, 0x7A, 0x3D,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x6E, 0xB8, 0x72, 0x8F, 0x7B, 0x85, 0x80, 0x00, 0x80, 0x00,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x86, 0x66,
                        0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x8C, 0xCC,
                        0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x93, 0x33,
                        0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x99, 0x99, };

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_iPullin_MAP", _data___MfVDTypC_iPullin_MAP, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVlv_iHold_C", _data___MfVlv_iHold_C, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_KLVZMSVUB", _data___MfVDTypC_KLVZMSVUB, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_TMSVRECURO", _data___MfVDTypC_TMSVRECURO, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KLVSTMSVG", _data___KLVSTMSVG, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_PRAIL_H", _data___KF_PRAIL_H, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                else if (options.SpoolPerformance_Fx350_Installed)
                {
                    _logger.LogInformation("applying Spool Performance FX350 HPFP changes...");
                    var _data___MfVDTypC_iPullin_MAP = new byte[] {
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
                        0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, };
                    var _data___MfVlv_iHold_C = new byte[] { 0x53 };
                    var _data___MfVDTypC_KLVZMSVUB = new byte[] {
                        0x92, 0x09, 0xCA, 0x08, 0x02, 0x08, 0xD0, 0x07, 0x3A, 0x07 };
                    var _data___MfVDTypC_TMSVRECURO = new byte[] { 0xCB, 0x08 };
                    var _data___KLVSTMSVG = new byte[] {
                        0x82, 0x11, 0x20, 0x10, 0xAB, 0x0F, 0x70, 0x0F, 0x48, 0x0E, 0x0D, 0x0E, 0xD2, 0x0D, 0x97, 0x0D };
                    var endiannessIsAlreadyCorrect = true;

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_iPullin_MAP", _data___MfVDTypC_iPullin_MAP, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVlv_iHold_C", _data___MfVlv_iHold_C, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_KLVZMSVUB", _data___MfVDTypC_KLVZMSVUB, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_TMSVRECURO", _data___MfVDTypC_TMSVRECURO, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KLVSTMSVG", _data___KLVSTMSVG, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                }
                //else if (options.SpoolPerformance_Fx400_Installed)
                //{
                //    _logger.LogInformation("applying Spool Performance FX400 HPFP changes...");
                //}
                //else if (options.SpoolPerformance_Fx400X_Installed)
                //{
                //    _logger.LogInformation("applying Spool Performance FX400X HPFP changes...");
                //    var endiannessIsAlreadyCorrect = true;
                //}
                else if (options.DorchEngineering_Stage1_DS1_Installed
                    || options.DorchEngineering_Stage15_DS15_Installed
                    )
                {
                    _logger.LogInformation("applying Dorch Engineering Stage 1 & 1.5 (DS1/DS15) HPFP changes...");

                    if (options.InjectorsInstalled &&
                        (options.XtremeDI_40_Installed
                        || options.XtremeDI_75_Installed))
                    {
                        _logger.LogInformation("Maps KF_PRAIL_H_K, KF_PRAIL_HGD_K, KF_PRAIL_HGD and KF_PRAIL_H will be overwritten by Xtreme DI injector calibrations.");
                    }

                    var _data___KF_PRAIL_H_K = new byte[] {
                        0x39, 0x9A, 0x39, 0x9A, 0x53, 0x33, 0x56, 0x66, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
                        0x39, 0x9A, 0x39, 0x9A, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x68, 0xF6,
                        0x39, 0x9A, 0x39, 0x9A, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x7A, 0x3D,
                        0x39, 0x9A, 0x39, 0x9A, 0x40, 0x00, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x60, 0x00, 0x80, 0x00,
                        0x40, 0x00, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCC, 0x4C, 0xCC, 0x59, 0x9A, 0x66, 0x66, 0x80, 0x00,
                        0x46, 0x66, 0x46, 0x66, 0x46, 0x66, 0x4C, 0xCC, 0x4C, 0xCC, 0x59, 0x99, 0x6C, 0xCD, 0x80, 0x00,
                        0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x53, 0x33, 0x5C, 0xCC, 0x73, 0x33, 0x80, 0x00,
                        0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x53, 0x33, 0x56, 0x66, 0x60, 0x00, 0x80, 0x00, 0x80, 0x00, };
                    var _data___KF_PRAIL_HGD_K = new byte[] {
                        0x39, 0x9A, 0x39, 0x9A, 0x53, 0x33, 0x56, 0x66, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
                        0x39, 0x9A, 0x39, 0x9A, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x68, 0xF6,
                        0x39, 0x9A, 0x39, 0x9A, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x7A, 0x3D,
                        0x39, 0x9A, 0x39, 0x9A, 0x40, 0x00, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x60, 0x00, 0x80, 0x00,
                        0x40, 0x00, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCC, 0x4C, 0xCC, 0x59, 0x9A, 0x66, 0x66, 0x80, 0x00,
                        0x46, 0x66, 0x46, 0x66, 0x46, 0x66, 0x4C, 0xCC, 0x4C, 0xCC, 0x59, 0x99, 0x6C, 0xCD, 0x80, 0x00,
                        0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x53, 0x33, 0x5C, 0xCC, 0x73, 0x33, 0x80, 0x00,
                        0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x53, 0x33, 0x56, 0x66, 0x60, 0x00, 0x80, 0x00, 0x80, 0x00,};
                    var _data___KF_PRAIL_HGD = new byte[] {
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x60, 0x00,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x68, 0xF6,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x7A, 0x3D,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x60, 0x00, 0x80, 0x00,
                        0x40, 0x00, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCC, 0x4C, 0xCC, 0x59, 0x9A, 0x66, 0x66, 0x80, 0x00,
                        0x46, 0x66, 0x46, 0x66, 0x46, 0x66, 0x4C, 0xCC, 0x4C, 0xCC, 0x59, 0x99, 0x6C, 0xCD, 0x80, 0x00,
                        0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x53, 0x33, 0x5C, 0xCC, 0x73, 0x33, 0x80, 0x00,
                        0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x53, 0x33, 0x56, 0x66, 0x60, 0x00, 0x80, 0x00, 0x80, 0x00, };
                    var _data___KF_PRAIL_H = new byte[] {
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x60, 0x00,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x68, 0xF6,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x7A, 0x3D,
                        0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x60, 0x00, 0x80, 0x00,
                        0x40, 0x00, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCC, 0x4C, 0xCC, 0x59, 0x9A, 0x66, 0x66, 0x80, 0x00,
                        0x46, 0x66, 0x46, 0x66, 0x46, 0x66, 0x4C, 0xCC, 0x4C, 0xCC, 0x59, 0x99, 0x6C, 0xCD, 0x80, 0x00,
                        0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x53, 0x33, 0x5C, 0xCC, 0x73, 0x33, 0x80, 0x00,
                        0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x53, 0x33, 0x56, 0x66, 0x60, 0x00, 0x80, 0x00, 0x80, 0x00, };
                    var _data___BMWchas_p_RailInjSng_M = new byte[] {
                        0x39, 0x9A, 0x39, 0x9A, 0x53, 0x33, 0x56, 0x66, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
                        0x39, 0x9A, 0x39, 0x9A, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x68, 0xF6,
                        0x39, 0x9A, 0x39, 0x9A, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x7A, 0x3D,
                        0x39, 0x9A, 0x39, 0x9A, 0x40, 0x00, 0x4C, 0xCD, 0x53, 0x33, 0x59, 0x9A, 0x60, 0x00, 0x80, 0x00,
                        0x40, 0x00, 0x40, 0x00, 0x46, 0x66, 0x4C, 0xCC, 0x4C, 0xCC, 0x59, 0x9A, 0x66, 0x66, 0x80, 0x00,
                        0x46, 0x66, 0x46, 0x66, 0x46, 0x66, 0x4C, 0xCC, 0x4C, 0xCC, 0x59, 0x99, 0x6C, 0xCD, 0x80, 0x00,
                        0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x53, 0x33, 0x5C, 0xCC, 0x73, 0x33, 0x80, 0x00,
                        0x4C, 0xCC, 0x4C, 0xCC, 0x4C, 0xCC, 0x53, 0x33, 0x56, 0x66, 0x60, 0x00, 0x80, 0x00, 0x80, 0x00, };
                    var _data___BMWchas_p_RailInjMpl_M = new byte[] {
                        0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x79, 0x9A, 0x79, 0x9A,
                        0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x79, 0x9A, 0x79, 0x9A,
                        0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x79, 0x9A, 0x79, 0x9A,
                        0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x79, 0x9A, 0x79, 0x9A,
                        0x6C, 0xCD, 0x6C, 0xCD, 0x6C, 0xCD, 0x6C, 0xCD, 0x79, 0x9A, 0x79, 0x9A,
                        0x79, 0x9A, 0x79, 0x9A, 0x79, 0x9A, 0x79, 0x9A, 0x79, 0x9A, 0x79, 0x9A,};
                    var _data___K_PRAIL_ST_MAX = new byte[] { 0x60, 0x00 };
                    var _data___S_PRAIL_GL_LA1 = new byte[] { 0x01 };
                    var _data___InjSys_nLowrThdCvo_C = new byte[] { 0x17, 0x70 };
                    var _data___MfVDTypC_iPullin_MAP = new byte[] {
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D };
                    var _data___MfVDTypC_KLVZMSVUB = new byte[] { 0x0B, 0x22, 0x08, 0x98, 0x07, 0xD0, 0x07, 0x6C, 0x06, 0xA4 };
                    var _data___MfVDTypC_TMSVRECURO = new byte[] { 0x06, 0xC3 };
                    var _data___MfVlv_iHold_C = new byte[] { 0x4B };

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_PRAIL_H_K", _data___KF_PRAIL_H_K, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_PRAIL_HGD_K", _data___KF_PRAIL_HGD_K, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_PRAIL_HGD", _data___KF_PRAIL_HGD, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_PRAIL_H", _data___KF_PRAIL_H, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWchas_p_RailInjSng_M", _data___BMWchas_p_RailInjSng_M, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWchas_p_RailInjMpl_M", _data___BMWchas_p_RailInjMpl_M, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "K_PRAIL_ST_MAX", _data___K_PRAIL_ST_MAX, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "S_PRAIL_GL_LA1", _data___S_PRAIL_GL_LA1, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "InjSys_nLowrThdCvo_C", _data___InjSys_nLowrThdCvo_C, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_iPullin_MAP", _data___MfVDTypC_iPullin_MAP, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_KLVZMSVUB", _data___MfVDTypC_KLVZMSVUB, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_TMSVRECURO", _data___MfVDTypC_TMSVRECURO, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVlv_iHold_C", _data___MfVlv_iHold_C, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                else if (options.DorchEngineering_Stage2_DS2_Installed
                    || options.DorchEngineering_Stage3_DS3_Installed
                    || options.DorchEngineering_Stage25_DS25_250Bar_Installed
                    )
                {
                    _logger.LogInformation("applying Dorch Engineering Stage 2 (DS2) and Stage 3 (DS3) HPFP changes...");
                    var _data___FWPHDR = new byte[] { 0x40, 0x40, 0x40, 0x40, 0x33, 0x29, 0x24, 0x1A };
                    var _data___KLIHDR = new byte[] { 0x14, 0x29, 0x21, 0x99, 0x28, 0x52, 0x28, 0x52, 0x21, 0x99, 0x14, 0x29, };
                    var _data___KLPHDR = new byte[] { 0x20, 0x00, 0x0C, 0xCD, 0x08, 0x00, 0x08, 0x00, 0x0C, 0xCD, 0x20, 0x00, };
                    var _data___SPD06HDSW = new byte[] { 0xFC, 0x18, 0xFF, 0x06, 0xFF, 0xCE, 0x00, 0x32, 0x00, 0xFA, 0x03, 0xE8, };
                    var _data___MfVDTypC_iPullin_MAP = new byte[] {
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D };
                    var _data___MfVDTypC_KLVZMSVUB = new byte[] { 0x0B, 0x22, 0x08, 0x98, 0x07, 0xD0, 0x07, 0x6C, 0x06, 0xA4, };
                    var _data___MfVDTypC_TMSVRECURO = new byte[] { 0x06, 0xC3 };
                    var _data___MfVlv_iHold_C = new byte[] { 0x4B };
                    var _data___KFVSTMSVO = new byte[] {
                        0x00, 0x7C, 0x00, 0xAD, 0x00, 0xAD, 0x00, 0xC6, 0x00, 0xE7, 0x01, 0x2E, 0x00, 0x71, 0x00, 0x97,
                        0x00, 0xA0, 0x00, 0xB2, 0x00, 0xCE, 0x00, 0xFD, 0x00, 0x74, 0x00, 0x97, 0x00, 0xA0, 0x00, 0xB0,
                        0x00, 0xCD, 0x00, 0xF6, 0x00, 0x79, 0x00, 0x7D, 0x00, 0x90, 0x00, 0xA0, 0x00, 0xB7, 0x00, 0xDF,
                        0x00, 0x7D, 0x00, 0x9A, 0x00, 0x90, 0x00, 0x9E, 0x00, 0xB0, 0x00, 0xD0, 0x00, 0x88, 0x00, 0x9C,
                        0x00, 0xA0, 0x00, 0xAB, 0x00, 0xBA, 0x00, 0xD5, 0x00, 0x8F, 0x00, 0x9E, 0x00, 0xA0, 0x00, 0xA8,
                        0x00, 0xB2, 0x00, 0xC6, 0x00, 0x9C, 0x00, 0xA0, 0x00, 0xA1, 0x00, 0xA3, 0x00, 0xA6, 0x00, 0xAB,};
                    var _data___KLVSTMSVG = new byte[] { 0x13, 0x33, 0x11, 0x48, 0x0F, 0x5C, 0x0E, 0xB8, 0x0F, 0x5C, 0x0E, 0x14, 0x0E, 0x14, 0x0E, 0xB8, };

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "FWPHDR", _data___FWPHDR, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KLIHDR", _data___KLIHDR, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KLPHDR", _data___KLPHDR, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "SPD06HDSW", _data___SPD06HDSW, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_iPullin_MAP", _data___MfVDTypC_iPullin_MAP, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_KLVZMSVUB", _data___MfVDTypC_KLVZMSVUB, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_TMSVRECURO", _data___MfVDTypC_TMSVRECURO, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVlv_iHold_C", _data___MfVlv_iHold_C, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KFVSTMSVO", _data___KFVSTMSVO, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KLVSTMSVG", _data___KLVSTMSVG, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                else if (options.DorchEngineering_Stage25_DS25_350Bar_Installed)
                {
                    var _data___DWMSVMN = new byte[] { 0x00, 0x00 };
                    var _data___KLIHDR = new byte[] { 0x28, 0x14, 0x99, 0x21, 0x51, 0x28, 0x51, 0x28, 0x99, 0x21, 0x28, 0x14, };
                    var _data___KLPHDR = new byte[] { 0x33, 0x13, 0x85, 0x0B, 0x33, 0x07, 0x33, 0x07, 0x85, 0x0B, 0x33, 0x13, };
                    var _data___MfVDTypC_iPullin_MAP = new byte[] {
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
                        0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D };
                    var _data___MfVDTypC_KLVZMSVUB = new byte[] { 0x22, 0x0B, 0x34, 0x08, 0xD0, 0x07, 0x6C, 0x07, 0x40, 0x06, };
                    var _data___MfVDTypC_TMSVRECURO = new byte[] { 0xC3, 0x06 };
                    var _data___MfVlv_iHold_C = new byte[] { 0x4B };
                    var endiannessIsAlreadyCorrect = true;

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "DWMSVMN", _data___DWMSVMN, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KLIHDR", _data___KLIHDR, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KLPHDR", _data___KLPHDR, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_iPullin_MAP", _data___MfVDTypC_iPullin_MAP, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_KLVZMSVUB", _data___MfVDTypC_KLVZMSVUB, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_TMSVRECURO", _data___MfVDTypC_TMSVRECURO, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVlv_iHold_C", _data___MfVlv_iHold_C, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                }
                else if (options.PrecisionRaceworks_HPFP_Installed)
                {
                    var _data___MfVDTypC_iPullin_MAP = new byte[] { 
                        0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x98, 
                        0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x98, 
                        0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x98, 
                        0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x98, 
                        0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x98 };
                    var _data___MfVDTypC_KLVZMSVUB = new byte[] { 0x11, 0x94, 0x0F, 0x28, 0x0F, 0x1E, 0x0E, 0xA6, 0x0E, 0x10 };
                    var _data___KFVSTMSVO = new byte[] { 
                        0x00, 0x67, 0x00, 0x7C, 0x00, 0x90, 0x00, 0xA5, 0x00, 0xC1, 0x00, 0xFB, 0x00, 0x5E, 0x00, 0x70,
                        0x00, 0x83, 0x00, 0x95, 0x00, 0xAC, 0x00, 0xD3, 0x00, 0x61, 0x00, 0x6F, 0x00, 0x7D, 0x00, 0x8B,
                        0x00, 0xA0, 0x00, 0xC2, 0x00, 0x64, 0x00, 0x6D, 0x00, 0x75, 0x00, 0x7D, 0x00, 0x91, 0x00, 0xAF,
                        0x00, 0x6A, 0x00, 0x70, 0x00, 0x75, 0x00, 0x7B, 0x00, 0x8B, 0x00, 0xA4, 0x00, 0x71, 0x00, 0x78,
                        0x00, 0x7F, 0x00, 0x87, 0x00, 0x92, 0x00, 0xA7, 0x00, 0x75, 0x00, 0x7A, 0x00, 0x7E, 0x00, 0x83,
                        0x00, 0x8C, 0x00, 0xA2, 0x00, 0x69, 0x00, 0x69, 0x00, 0x68, 0x00, 0x68, 0x00, 0x6A, 0x00, 0x6E };
                    var _data___KLVSTMSVG = new byte[] { 0x19, 0x99, 0x17, 0x2B, 0x14, 0xDD, 0x12, 0x2D, 0x12, 0x1C, 0x10, 0xF5, 0x10, 0x00, 0x0F, 0x8D };
                    var _data___FWIHDR = new byte[] { 0xB2, 0xA6, 0xA6, 0x72, 0x59, 0x4E, 0x48, 0x2E };
                    var _data___MfVlv_iHold_C = new byte[] { 0x5A };
                    var endiannessIsAlreadyCorrect = true;

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_iPullin_MAP", _data___MfVDTypC_iPullin_MAP, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVDTypC_KLVZMSVUB", _data___MfVDTypC_KLVZMSVUB, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KFVSTMSVO", _data___KFVSTMSVO, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KLVSTMSVG", _data___KLVSTMSVG, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "FWIHDR", _data___FWIHDR, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MfVlv_iHold_C", _data___MfVlv_iHold_C, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);

                }
                else // B58 TU doesnt require changes
                {
                    _logger.LogInformation("B58 TU pump selected (uses OEM mapping, so no actual changes)");
                }

                if (options.HpfpLambdaEnrichment)
                {
                    var _data___KF_BSLGF = new byte[] {
                        0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02,
                        0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02, 0x0D, 0x02,
                    };
                    var _data___KF_LAMIN_H = new byte[] {
                        0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD,
                        0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD,
                        0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD,
                        0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD,
                        0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD,
                        0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD, 0x0C, 0xFD,
                    };
                    var _data___K_BS_MN = new byte[] { 0x0C, 0xCC };
                    var _data___KF_LABAS_1 = new byte[] {
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x0F, 0xDA,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x0F, 0x84,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x0F, 0xC3, 0x0F, 0x5C, 0x0E, 0xBB,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
                        0x10, 0x00, 0x10, 0x00, 0x0F, 0x85, 0x0E, 0xF6, 0x0E, 0xA0, 0x0E, 0x2C,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x0F, 0xED, 0x0F, 0xED,
                        0x0F, 0xEA, 0x0F, 0x45, 0x0F, 0x45, 0x0F, 0x27, 0x0F, 0x0D, 0x0E, 0xF2,
                        0x0E, 0xC8, 0x0E, 0x70, 0x0E, 0x1C, 0x0D, 0xB4, 0x0D, 0x61, 0x0D, 0x32,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x0F, 0xDB, 0x0F, 0xDA,
                        0x0F, 0xD4, 0x0F, 0x2F, 0x0F, 0x2F, 0x0F, 0x11, 0x0E, 0xDE, 0x0E, 0xA8,
                        0x0E, 0x54, 0x0D, 0xFC, 0x0D, 0xB7, 0x0D, 0x66, 0x0D, 0x3D, 0x0D, 0x23,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x0F, 0xC9, 0x0F, 0xC8,
                        0x0F, 0xBF, 0x0F, 0x1A, 0x0F, 0x1A, 0x0E, 0xFC, 0x0E, 0xAF, 0x0E, 0x5E,
                        0x0D, 0xE0, 0x0D, 0x89, 0x0D, 0x53, 0x0D, 0x0E, 0x0D, 0x0E, 0x0D, 0x0E,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x0F, 0xD9, 0x0F, 0xD8,
                        0x0F, 0xCF, 0x0F, 0x2A, 0x0F, 0x2A, 0x0E, 0xA8, 0x0E, 0x44, 0x0D, 0xEE,
                        0x0D, 0x99, 0x0D, 0x70, 0x0D, 0x3A, 0x0D, 0x0C, 0x0D, 0x0C, 0x0D, 0x0C,
                        0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x0F, 0xD9, 0x0F, 0xD8,
                        0x0F, 0xCF, 0x0F, 0x2A, 0x0F, 0x2A, 0x0E, 0xA8, 0x0E, 0x44, 0x0D, 0xEE,
                        0x0D, 0x99, 0x0D, 0x70, 0x0D, 0x3A, 0x0D, 0x07, 0x0D, 0x08, 0x0D, 0x08,
                    };

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_BSLGF", _data___KF_BSLGF, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_LAMIN_H", _data___KF_LAMIN_H, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "K_BS_MN", _data___K_BS_MN, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_LABAS_1", _data___KF_LABAS_1, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                else
                {
                    _logger.LogInformation("HPFP Lambda enrichment not selected");
                }
            }
            else
            {
                _logger.LogInformation("HPFP is not enabled");
            }

            #endregion HPFP Option

            #region Injectors Option

            // Injectors
            if (options.InjectorsInstalled)
            {
                _logger.LogInformation("applying Injector maps...");
                var _customOptionName = "Injectors";

                if (options.SpoolPerformance_Ifx150_Installed)
                {
                    _logger.LogInformation("applying Spool Performance IFX150 Injectors changes...");
                    var _data___MK2TE = new byte[] { 0x16, 0x80 };
                    var _data___TIMINP = new byte[] { 0x00, 0x00 };

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MK2TE", _data___MK2TE, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "TIMINP", _data___TIMINP, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                else if (options.SpoolPerformance_Ifx350_Installed)
                {
                    _logger.LogInformation("applying Spool Performance IFX350 Injectors changes...");
                    var _data___InjSys_facInjrFlowCorr_T = new byte[] {
                        0x2E, 0x73, 0x85, 0x5E, 0xDC, 0x53, 0x57, 0x46, 0x9F, 0x3A, 0x9F, 0x3A, 0x0B, 0x2A, 0x59, 0x26,
                        0x3F, 0x1F, 0xE6, 0x1C, 0xEF, 0x19, 0x57, 0x17, 0xDE, 0x14, 0xA9, 0x12, 0xA8, 0x10, 0x6F, 0x0E,
                        0x63, 0x0C, 0xBF, 0x0A, 0x4B, 0x09, 0x0C, 0x08, 0x05, 0x07, 0x24, 0x06, 0xAD, 0x07, };
                    var _data___TIMINP = new byte[] { 0x00, 0x00 };
                    var endiannessIsAlreadyCorrect = true;

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "InjSys_facInjrFlowCorr_T", _data___InjSys_facInjrFlowCorr_T, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "TIMINP", _data___TIMINP, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, endiannessIsAlreadyCorrect: endiannessIsAlreadyCorrect);
                }
                //else if (options.SpoolPerformance_Ifx350X_Installed)
                //{
                //    _logger.LogInformation("applying Spool Performance IFX350X Injectors changes...");
                //    var endiannessIsAlreadyCorrect = true;
                //    // missing map info...
                //}
                //else if (options.XtremeDI_40_Installed)
                //{
                //    _logger.LogInformation("applying Xtreme DI +40% Injectors changes...");
                //    // missing map info...
                //}
                else if (options.XtremeDI_75_Installed)
                {
                    _logger.LogInformation("applying Xtreme DI +75% Injectors changes...");

                    var _data___KF_PRAIL_H_K = new byte[] {
                        0x13, 0x33, 0x13, 0x33, 0x26, 0x66, 0x39, 0x99, 0x4C, 0xCC, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x28, 0x66, 0x3D, 0x99, 0x52, 0xCC, 0x68, 0x00, 0x68, 0xF6, 0x68, 0xF6,
                        0x13, 0x33, 0x13, 0x33, 0x2C, 0x24, 0x45, 0x16, 0x5E, 0x08, 0x76, 0xFA, 0x7A, 0x3D, 0x7A, 0x3D,
                        0x13, 0x33, 0x13, 0x33, 0x2D, 0x47, 0x47, 0x5C, 0x61, 0x70, 0x7B, 0x85, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, };
                    var _data___KF_PRAIL_HGD_K = new byte[] {
                        0x13, 0x33, 0x13, 0x33, 0x26, 0x66, 0x39, 0x99, 0x4C, 0xCC, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x28, 0x66, 0x3D, 0x99, 0x52, 0xCC, 0x68, 0x00, 0x68, 0xF6, 0x68, 0xF6,
                        0x13, 0x33, 0x13, 0x33, 0x2C, 0x24, 0x45, 0x16, 0x5E, 0x08, 0x76, 0xFA, 0x7A, 0x3D, 0x7A, 0x3D,
                        0x13, 0x33, 0x13, 0x33, 0x2D, 0x47, 0x47, 0x5C, 0x61, 0x70, 0x7B, 0x85, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, };
                    var _data___KF_PRAIL_HGD = new byte[] {
                        0x13, 0x33, 0x13, 0x33, 0x26, 0x66, 0x39, 0x99, 0x4C, 0xCC, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x28, 0x66, 0x3D, 0x99, 0x52, 0xCC, 0x68, 0x00, 0x68, 0xF6, 0x68, 0xF6,
                        0x13, 0x33, 0x13, 0x33, 0x2C, 0x24, 0x45, 0x16, 0x5E, 0x08, 0x76, 0xFA, 0x7A, 0x3D, 0x7A, 0x3D,
                        0x13, 0x33, 0x13, 0x33, 0x2D, 0x47, 0x47, 0x5C, 0x61, 0x70, 0x7B, 0x85, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, };
                    var _data___KF_PRAIL_H = new byte[] {
                        0x13, 0x33, 0x13, 0x33, 0x26, 0x66, 0x39, 0x99, 0x4C, 0xCC, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x28, 0x66, 0x3D, 0x99, 0x52, 0xCC, 0x68, 0x00, 0x68, 0xF6, 0x68, 0xF6,
                        0x13, 0x33, 0x13, 0x33, 0x2C, 0x24, 0x45, 0x16, 0x5E, 0x08, 0x76, 0xFA, 0x7A, 0x3D, 0x7A, 0x3D,
                        0x13, 0x33, 0x13, 0x33, 0x2D, 0x47, 0x47, 0x5C, 0x61, 0x70, 0x7B, 0x85, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
                        0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, };
                    var _data___MK2TE = new byte[] { 0x21, 0xB2 };

                    if (options.HpfpInstalled)
                    {
                        if (options.DorchEngineering_Stage1_DS1_Installed || options.DorchEngineering_Stage15_DS15_Installed)
                        {
                            throw new Exception("This HPFP and Injector combo is not supported!");
                        }

                        if (options.SpoolPerformance_Fx170_Installed
                            || options.SpoolPerformance_Fx200_Installed)
                        {
                            _logger.LogInformation("Maps KF_PRAIL_H are overwriting Spool HPFP calibrations.");

                            _data___KF_PRAIL_H = new byte[] {
                                0x13, 0x33, 0x13, 0x33, 0x26, 0x66, 0x39, 0x99, 0x4C, 0xCC, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
                                0x13, 0x33, 0x13, 0x33, 0x28, 0x66, 0x3D, 0x99, 0x52, 0xCC, 0x68, 0x00, 0x68, 0xF6, 0x68, 0xF6,
                                0x13, 0x33, 0x13, 0x33, 0x2C, 0x24, 0x45, 0x16, 0x5E, 0x08, 0x76, 0xFA, 0x7A, 0x3D, 0x7A, 0x3D,
                                0x13, 0x33, 0x13, 0x33, 0x2D, 0x47, 0x47, 0x5C, 0x61, 0x70, 0x7B, 0x85, 0x80, 0x00, 0x80, 0x00,
                                0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x86, 0x66,
                                0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x8C, 0xCC,
                                0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x93, 0x33,
                                0x13, 0x33, 0x13, 0x33, 0x2E, 0x66, 0x49, 0x99, 0x64, 0xCC, 0x80, 0x00, 0x80, 0x00, 0x99, 0x99, };
                        }
                    }

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_PRAIL_H_K", _data___KF_PRAIL_H_K, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_PRAIL_HGD_K", _data___KF_PRAIL_HGD_K, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_PRAIL_HGD", _data___KF_PRAIL_HGD, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_PRAIL_H", _data___KF_PRAIL_H, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "MK2TE", _data___MK2TE, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                //else if (options.Nostrum_Stage1_Installed)
                //{
                //    _logger.LogInformation("applying Nostrum Stage 1 Injectors changes...");
                //    // missing map info...
                //}
                //else if (options.Nostrum_Stage2_Installed)
                //{
                //    _logger.LogInformation("applying Nostrum Stage 2 Injectors changes...");
                //    // missing map info...
                //}
            }
            else
            {
                _logger.LogInformation("Injector is not enabled");
            }

            #endregion Injectors Option

            #region StartupRoar Option

            // Startup Roar
            if (options.StartupRoar)
            {
                _logger.LogInformation("applying startup roar...");
                var _customOptionName = "Startup Roar";

                // Startup Roar Aggression
                // [1-102] subtract 1, then divide by 100 to get percentage, multiply this by the difference from a given torque value to the max torque to get a new target torque value. Round to nearest 0.1 Nm, finally, multiply by 10 for ecu conversion
                var _sramaxBytes = new byte[] { 0x00, 0x00 }; //_startupRoarAggressionMaxBytes
                var _srahighBytes = new byte[] { 0x00, 0x00 }; //_startupRoarAggressionHighBytes
                var _sramidBytes = new byte[] { 0x00, 0x00 }; //_startupRoarAggressionMidBytes
                var _sralowBytes = new byte[] { 0x00, 0x00 }; //_startupRoarAggressionLowBytes
                if (options.StartupRoarAggression > 0)
                {
                    var _maxTorqueValue = 200.0; // Nm
                    var sliderValue = ((int)options.StartupRoarAggression - 2) / 100.0; // %
                    if (sliderValue <= 0.0)
                    {
                        sliderValue = 0.0;
                    }
                    else if (sliderValue > 1.0)
                    {
                        sliderValue = 1.0;
                    }

                    _maxTorqueValue = _maxTorqueValue * sliderValue;
                    _maxTorqueValue = (Math.Ceiling(_maxTorqueValue * 10)) / 10; // round to nearest 0.1
                    var _highTorqueValue = (Math.Ceiling(_maxTorqueValue * 0.55 * 10)) / 10; // round to nearest 0.1
                    var _midTorqueValue = (Math.Ceiling(_maxTorqueValue * 0.33 * 10)) / 10; // round to nearest 0.1
                    var _lowTorqueValue = (Math.Ceiling(_maxTorqueValue * 0.11 * 10)) / 10; // round to nearest 0.1
                    var _maxTorqueValue_s16 = Convert.ToInt16(_maxTorqueValue * 10);
                    var _highTorqueValue_s16 = Convert.ToInt16(_highTorqueValue * 10);
                    var _midTorqueValue_s16 = Convert.ToInt16(_midTorqueValue * 10);
                    var _lowTorqueValue_s16 = Convert.ToInt16(_lowTorqueValue * 10);
                    _sramaxBytes[0] = (byte)((_maxTorqueValue_s16) >> 8);
                    _sramaxBytes[1] = (byte)(_maxTorqueValue_s16);
                    _srahighBytes[0] = (byte)((_highTorqueValue_s16) >> 8);
                    _srahighBytes[1] = (byte)(_highTorqueValue_s16);
                    _sramidBytes[0] = (byte)((_midTorqueValue_s16) >> 8);
                    _sramidBytes[1] = (byte)(_midTorqueValue_s16);
                    _sralowBytes[0] = (byte)((_lowTorqueValue_s16) >> 8);
                    _sralowBytes[1] = (byte)(_lowTorqueValue_s16);
                    _logger.LogInformation("startup roar aggression set to a max of {_torqueValue} Nm", _maxTorqueValue);
                }

                var _startupRoarAggressionTable = new byte[] { 0x00, 0x00, 0x00, 0x14, 0x00, 0x0A, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x14, 0x00, 0x0A, 0x00, 0x00,
                    _srahighBytes[0], _srahighBytes[1], _srahighBytes[0], _srahighBytes[1], _sralowBytes[0], _sralowBytes[1], 0x00, 0x00,
                    _sramaxBytes[0], _sramaxBytes[1], _sramaxBytes[0], _sramaxBytes[1], _sramidBytes[0], _sramidBytes[1], 0x00, 0x00, };
                // Startup Roar Duration
                // [1-10], divide by 10 to get seconds, finally, multiply by 100 for ecu conversion
                var _srd1Bytes = new byte[] { 0x00, 0x64 }; //_startupRoarDuration1Bytes
                var _srd2Bytes = new byte[] { 0x00, 0xC8 }; //_startupRoarDuration2Bytes
                if (options.StartupRoarDuration > 0)
                {
                    var duration = (int)options.StartupRoarDuration / 10.0; // s
                    if (duration <= 0.1)
                    {
                        duration = 0.1;
                    }
                    else if (duration >= 1.0)
                    {
                        duration = 1.0;
                    }

                    var duration2 = duration * 2;
                    _srd1Bytes[0] = (byte)((int)(duration * 100.0) >> 8);
                    _srd1Bytes[1] = (byte)(duration * 100.0);
                    _srd2Bytes[0] = (byte)((int)(duration2 * 100.0) >> 8);
                    _srd2Bytes[1] = (byte)(duration2 * 100.0);
                    _logger.LogInformation("startup roar duration set to {duration} s", duration);
                }

                var _startupRoarDurationTable = new byte[] { 0x00, 0x00, _srd1Bytes[0], _srd1Bytes[1], _srd2Bytes[0], _srd2Bytes[1], 0x01, 0xF4, };

                await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_MD_RES_NST_KEY", _startupRoarAggressionTable, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "AXX_KF_MD_RES_NST_KEY", _startupRoarDurationTable, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                await data.ApplyMapDataAsync(fileInfo, carInfo, "AXY_KF_MD_RES_NST_KEY", await _valueResolver.GetAsync(_customOptionName, "AXY_KF_MD_RES_NST_KEY", "StartupRoarTemperatureAxis"), valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
            }
            else
            {
                _logger.LogInformation("StartupRoar is not enabled");
            }

            #endregion StartupRoar Option

            #region TorqueByGear Option

            // Torque By Gear
            if (options.TorqueByGear)
            {
                _logger.LogInformation("applying torque by gear...");
                var _customOptionName = "Torque By Gear";

                // Enable codewords:
                _logger.LogInformation("applying torque by gear codewords...");
                var CW_Data1 = await SetBitsAsync(data, fileInfo, carInfo, "CW_TQGMXLMSRCHAXLPRONENAMXDYNTQC", mask: (UInt16)0b_0000_1000_0000_0000);
                var CW_Data2 = await SetBitsAsync(data, fileInfo, carInfo, "CW_TQGMXLMSRCHAXLPRONMXSTATTQC", mask: (UInt16)0b_0000_1000_0000_0000);
                var CW_Data3 = await SetBitsAsync(data, fileInfo, carInfo, "CW_TQGMXLMSRCHAXLPRONENAMXDYN", mask: (UInt16)0b_0000_1000_0000_0000);
                var CW_Data4 = await SetBitsAsync(data, fileInfo, carInfo, "CW_TQGMXLMSRCHAXLPRONENAMXSTAT", mask: (UInt16)0b_0000_1000_0000_0000);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CW_TQGMXLMSRCHAXLPRONENAMXDYNTQC", CW_Data1, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CW_TQGMXLMSRCHAXLPRONMXSTATTQC", CW_Data2, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CW_TQGMXLMSRCHAXLPRONENAMXDYN", CW_Data3, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CW_TQGMXLMSRCHAXLPRONENAMXSTAT", CW_Data4, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                // Torque by gear x
                _logger.LogInformation("applying torque by gear limiters...");
                // [200-1000] give torque in Nm, multiply by 10 for ecu conversion
                var _tbg0Bytes = new byte[] { 0x7F, 0xFF }; //_torqueByGear0Bytes
                var _tbg1Bytes = new byte[] { 0x7F, 0xFF }; //_torqueByGear1Bytes
                var _tbg2Bytes = new byte[] { 0x7F, 0xFF }; //_torqueByGear2Bytes
                var _tbg3Bytes = new byte[] { 0x7F, 0xFF }; //_torqueByGear3Bytes
                var _tbg4Bytes = new byte[] { 0x7F, 0xFF }; //_torqueByGear4Bytes
                var _tbg5Bytes = new byte[] { 0x7F, 0xFF }; //_torqueByGear5Bytes
                var _tbg6Bytes = new byte[] { 0x7F, 0xFF }; //_torqueByGear6Bytes
                var _tbg7Bytes = new byte[] { 0x7F, 0xFF }; //_torqueByGear7Bytes
                var _tbg8Bytes = new byte[] { 0x7F, 0xFF }; //_torqueByGear8Bytes
                var _tbg9Bytes = new byte[] { 0x7F, 0xFF }; //_torqueByGear9Bytes
                var minAllowableTorque = 200;
                var maxAllowableTorque = 1000;
                var unlimitedTorque = 3000;

                if (options.TorqueByGear_AllGears)
                {
                    // All gear are set to the same value
                    var _torqueByGearX = options.TorqueByGear_GearAll_MaxTorque; // Nm
                    if (_torqueByGearX <= minAllowableTorque)
                    {
                        _torqueByGearX = minAllowableTorque;
                    }
                    else if (_torqueByGearX >= maxAllowableTorque)
                    {
                        _torqueByGearX = unlimitedTorque;
                    }

                    var _torqueByGearX_s16 = Convert.ToInt16(_torqueByGearX * 10);

                    _tbg0Bytes = new byte[] { (byte)((_torqueByGearX_s16) >> 8), (byte)(_torqueByGearX_s16) };
                    _tbg1Bytes = new byte[] { (byte)((_torqueByGearX_s16) >> 8), (byte)(_torqueByGearX_s16) };
                    _tbg2Bytes = new byte[] { (byte)((_torqueByGearX_s16) >> 8), (byte)(_torqueByGearX_s16) };
                    _tbg3Bytes = new byte[] { (byte)((_torqueByGearX_s16) >> 8), (byte)(_torqueByGearX_s16) };
                    _tbg4Bytes = new byte[] { (byte)((_torqueByGearX_s16) >> 8), (byte)(_torqueByGearX_s16) };
                    _tbg5Bytes = new byte[] { (byte)((_torqueByGearX_s16) >> 8), (byte)(_torqueByGearX_s16) };
                    _tbg6Bytes = new byte[] { (byte)((_torqueByGearX_s16) >> 8), (byte)(_torqueByGearX_s16) };
                    _tbg7Bytes = new byte[] { (byte)((_torqueByGearX_s16) >> 8), (byte)(_torqueByGearX_s16) };
                    _tbg8Bytes = new byte[] { (byte)((_torqueByGearX_s16) >> 8), (byte)(_torqueByGearX_s16) };
                    _tbg9Bytes = new byte[] { (byte)((_torqueByGearX_s16) >> 8), (byte)(_torqueByGearX_s16) };

                    _logger.LogInformation("torque by gear set all gears to a max of {_torqueByGearX} Nm", _torqueByGearX);
                }
                else
                {
                    // if options.TorqueByGear_IndividualGears each gear is calculated individually
                    {
                        var _torqueByGearX = options.TorqueByGear_Gear0_MaxTorque; // Nm
                        if (_torqueByGearX <= minAllowableTorque)
                        {
                            _torqueByGearX = minAllowableTorque;
                        }
                        else if (_torqueByGearX >= maxAllowableTorque)
                        {
                            _torqueByGearX = unlimitedTorque;
                        }

                        var _torqueByGearX_s16 = Convert.ToInt16(_torqueByGearX * 10);
                        _tbg0Bytes[0] = (byte)((_torqueByGearX_s16) >> 8);
                        _tbg0Bytes[1] = (byte)(_torqueByGearX_s16);
                        _logger.LogInformation("torque by gear 0 set to a max of {_torqueByGearX} Nm", _torqueByGearX);
                    }

                    {
                        var _torqueByGearX = options.TorqueByGear_Gear1_MaxTorque; // Nm
                        if (_torqueByGearX <= minAllowableTorque)
                        {
                            _torqueByGearX = minAllowableTorque;
                        }
                        else if (_torqueByGearX >= maxAllowableTorque)
                        {
                            _torqueByGearX = unlimitedTorque;
                        }

                        var _torqueByGearX_s16 = Convert.ToInt16(_torqueByGearX * 10);
                        _tbg1Bytes[0] = (byte)((_torqueByGearX_s16) >> 8);
                        _tbg1Bytes[1] = (byte)(_torqueByGearX_s16);
                        _logger.LogInformation("torque by gear 1 set to a max of {_torqueByGearX} Nm", _torqueByGearX);
                    }

                    {
                        var _torqueByGearX = options.TorqueByGear_Gear2_MaxTorque; // Nm
                        if (_torqueByGearX <= minAllowableTorque)
                        {
                            _torqueByGearX = minAllowableTorque;
                        }
                        else if (_torqueByGearX >= maxAllowableTorque)
                        {
                            _torqueByGearX = unlimitedTorque;
                        }

                        var _torqueByGearX_s16 = Convert.ToInt16(_torqueByGearX * 10);
                        _tbg2Bytes[0] = (byte)((_torqueByGearX_s16) >> 8);
                        _tbg2Bytes[1] = (byte)(_torqueByGearX_s16);
                        _logger.LogInformation("torque by gear 2 set to a max of {_torqueByGearX} Nm", _torqueByGearX);
                    }

                    {
                        var _torqueByGearX = options.TorqueByGear_Gear3_MaxTorque; // Nm
                        if (_torqueByGearX <= minAllowableTorque)
                        {
                            _torqueByGearX = minAllowableTorque;
                        }
                        else if (_torqueByGearX >= maxAllowableTorque)
                        {
                            _torqueByGearX = unlimitedTorque;
                        }

                        var _torqueByGearX_s16 = Convert.ToInt16(_torqueByGearX * 10);
                        _tbg3Bytes[0] = (byte)((_torqueByGearX_s16) >> 8);
                        _tbg3Bytes[1] = (byte)(_torqueByGearX_s16);
                        _logger.LogInformation("torque by gear 3 set to a max of {_torqueByGearX} Nm", _torqueByGearX);
                    }

                    {
                        var _torqueByGearX = options.TorqueByGear_Gear4_MaxTorque; // Nm
                        if (_torqueByGearX <= minAllowableTorque)
                        {
                            _torqueByGearX = minAllowableTorque;
                        }
                        else if (_torqueByGearX >= maxAllowableTorque)
                        {
                            _torqueByGearX = unlimitedTorque;
                        }

                        var _torqueByGearX_s16 = Convert.ToInt16(_torqueByGearX * 10);
                        _tbg4Bytes[0] = (byte)((_torqueByGearX_s16) >> 8);
                        _tbg4Bytes[1] = (byte)(_torqueByGearX_s16);
                        _logger.LogInformation("torque by gear 4 set to a max of {_torqueByGearX} Nm", _torqueByGearX);
                    }

                    {
                        var _torqueByGearX = options.TorqueByGear_Gear5_MaxTorque; // Nm
                        if (_torqueByGearX <= minAllowableTorque)
                        {
                            _torqueByGearX = minAllowableTorque;
                        }
                        else if (_torqueByGearX >= maxAllowableTorque)
                        {
                            _torqueByGearX = unlimitedTorque;
                        }

                        var _torqueByGearX_s16 = Convert.ToInt16(_torqueByGearX * 10);
                        _tbg5Bytes[0] = (byte)((_torqueByGearX_s16) >> 8);
                        _tbg5Bytes[1] = (byte)(_torqueByGearX_s16);
                        _logger.LogInformation("torque by gear 5 set to a max of {_torqueByGearX} Nm", _torqueByGearX);
                    }

                    {
                        var _torqueByGearX = options.TorqueByGear_Gear6_MaxTorque; // Nm
                        if (_torqueByGearX <= minAllowableTorque)
                        {
                            _torqueByGearX = minAllowableTorque;
                        }
                        else if (_torqueByGearX >= maxAllowableTorque)
                        {
                            _torqueByGearX = unlimitedTorque;
                        }

                        var _torqueByGearX_s16 = Convert.ToInt16(_torqueByGearX * 10);
                        _tbg6Bytes[0] = (byte)((_torqueByGearX_s16) >> 8);
                        _tbg6Bytes[1] = (byte)(_torqueByGearX_s16);
                        _logger.LogInformation("torque by gear 6 set to a max of {_torqueByGearX} Nm", _torqueByGearX);
                    }

                    {
                        var _torqueByGearX = options.TorqueByGear_Gear7_MaxTorque; // Nm
                        if (_torqueByGearX <= minAllowableTorque)
                        {
                            _torqueByGearX = minAllowableTorque;
                        }
                        else if (_torqueByGearX >= maxAllowableTorque)
                        {
                            _torqueByGearX = unlimitedTorque;
                        }

                        var _torqueByGearX_s16 = Convert.ToInt16(_torqueByGearX * 10);
                        _tbg7Bytes[0] = (byte)((_torqueByGearX_s16) >> 8);
                        _tbg7Bytes[1] = (byte)(_torqueByGearX_s16);
                        _logger.LogInformation("torque by gear 7 set to a max of {_torqueByGearX} Nm", _torqueByGearX);
                    }

                    {
                        var _torqueByGearX = options.TorqueByGear_Gear8_MaxTorque; // Nm
                        if (_torqueByGearX <= minAllowableTorque)
                        {
                            _torqueByGearX = minAllowableTorque;
                        }
                        else if (_torqueByGearX >= maxAllowableTorque)
                        {
                            _torqueByGearX = unlimitedTorque;
                        }

                        var _torqueByGearX_s16 = Convert.ToInt16(_torqueByGearX * 10);
                        _tbg8Bytes[0] = (byte)((_torqueByGearX_s16) >> 8);
                        _tbg8Bytes[1] = (byte)(_torqueByGearX_s16);
                        _logger.LogInformation("torque by gear 8 set to a max of {_torqueByGearX} Nm", _torqueByGearX);
                    }

                    {
                        var _torqueByGearX = options.TorqueByGear_Gear9_MaxTorque; // Nm
                        if (_torqueByGearX <= minAllowableTorque)
                        {
                            _torqueByGearX = minAllowableTorque;
                        }
                        else if (_torqueByGearX >= maxAllowableTorque)
                        {
                            _torqueByGearX = unlimitedTorque;
                        }

                        var _torqueByGearX_s16 = Convert.ToInt16(_torqueByGearX * 10);
                        _tbg9Bytes[0] = (byte)((_torqueByGearX_s16) >> 8);
                        _tbg9Bytes[1] = (byte)(_torqueByGearX_s16);
                        _logger.LogInformation("torque by gear 9 set to a max of {_torqueByGearX} Nm", _torqueByGearX);
                    }
                }

                var _torqueByGearTable = new byte[] { _tbg0Bytes[0], _tbg0Bytes[1],
                    _tbg1Bytes[0], _tbg1Bytes[1],
                    _tbg2Bytes[0], _tbg2Bytes[1],
                    _tbg3Bytes[0], _tbg3Bytes[1],
                    _tbg4Bytes[0], _tbg4Bytes[1],
                    _tbg5Bytes[0], _tbg5Bytes[1],
                    _tbg6Bytes[0], _tbg6Bytes[1],
                    _tbg7Bytes[0], _tbg7Bytes[1],
                    _tbg8Bytes[0], _tbg8Bytes[1],
                    _tbg9Bytes[0], _tbg9Bytes[1], };

                await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_MDG_MAX_GANG_AT", _torqueByGearTable, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_MDG_MAX_GANG_HS", _torqueByGearTable, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
            }
            else
            {
                _logger.LogInformation("TorqueByGear is not enabled");
            }

            #endregion TorqueByGear Option

            #region DTC code masking Option

            // DTC_Codes
            if (options.DtcCodeMasking)
            {
                _logger.LogInformation("DTC code masking enabled, removing general DTCs");

                foreach (var code in options.GeneralDtcCodesToRemove ?? new())
                {
                    await _dtcRemover.RemoveAsync(code, fileInfo.A2L, data, optional: false);
                }
            }
            else
            {
                _logger.LogInformation("General DTC code masking not enabled");
            }

            if (options.UserDtcCodesDeletion)
            {
                _logger.LogInformation("User DTC code masking enabled, removing User DTCs");

                foreach (var code in options.UserDtcCodesToRemove ?? new())
                {
                    await _dtcRemover.RemoveAsync(code, fileInfo.A2L, data, optional: false);
                }
            }
            else
            {
                _logger.LogInformation("User DTC code masking not enabled");
            }

            if (options.DeleteBenchEcuDtcs)
            {
                _logger.LogInformation("Delete Bench ECU DTC code masking enabled");

                var benchCodes = new List<string>();
                if (carInfo.VIN.ToUpper().Equals("WBA8B7G51HNU37556".ToUpper())) // PPC
                {
                    benchCodes = new List<string>
                    {
                        "135808",
                        "CDA028",
                        "CD8488",
                        "1F4011",
                        "20F217",
                        "20F21A",
                        "1F0515",
                        "103108",
                        "103102",
                        "103002",
                        "103008",
                        "109001",
                        "CDA684",
                        "113027",
                        "123701",
                        "121001",
                        "119001",
                        "194604",
                        "108B0A",
                        "10890C",
                        "CD8B12",
                        "1B2305",
                        "CDA031",
                        "10021D",
                        "101711",
                        "100225",
                        "1F400A",
                        "108004",
                        "136805",
                        "1F4A10",
                        "191004",
                        "231F04",
                        "195001",
                        "10C151",
                        "CDB204",
                        "1B9804",
                        "CD8B01",
                        "10B105",
                        "193108",
                        "CD8B29",
                        "CD8B20",
                        "20A504",
                        "CDA056",
                        "CD9412",
                        "CD940F",
                        "CD8B0E",
                        "CD940C",
                        "CD8B08",
                        "CD8B0B",
                        "CD8B23",
                        "CD8B26",
                        "CDA025",
                        "138104",
                        "1B6008",
                        "CDA032",
                        "152007",
                        "135608",
                        "160504",
                        "11B030",
                        "CDA728",
                        "CDA50C",
                        "CD8B0F",
                        "1C0204",
                        "1C3002",
                        "CDA53D",
                        "1B0A21",
                        "1A2604",
                        "CDA50D",
                        "CDB804",
                        "CDA71E",
                        "CD8B17",
                        "CDA037",
                        "20A105",
                        "CD8B1D",
                        "CD8B1A",
                        "CDA50E",
                        "1A2004",
                        "CD8E10",
                        "CD9409",
                        "10351C",
                        "102A58",
                        "1A2009",
                        "CD8E12",
                        "CD8F01",
                        "193008",
                        "193A20",
                        "cdb309",
                        "cdb504",
                        "20a503",
                        "20a203",
                        "cdae04",
                        "1a2005",
                    };
                }

                if (carInfo.VIN.ToUpper().Equals("WBA5U9C07LA383408".ToUpper())) // Aurix
                {
                    benchCodes = new List<string>
                    {
                        "CDBB91",
                        "CDC126",
                        "CDA73A",
                        "CDA737",
                        "CDA711",
                        "CD8407",
                        "CDA811",
                        "CDC12F",
                        "CD8403",
                        "CDA812",
                        "CDA80E",
                        "CDA035",
                        "135B11",
                        "20F227",
                        "119113",
                        "CD9204",
                        "CD9203",
                        "10C151",
                        "CDC124",
                        "CD9711",
                        "20F22A",
                        "103002",
                        "103102",
                        "113027",
                        "121001",
                        "123701",
                        "1F0515",
                        "135808",
                        "109001",
                        "194604",
                        "100225",
                        "108B0A",
                        "10890C",
                        "101711",
                        "10021D",
                        "108004",
                        "1F400A",
                        "1F4A10",
                        "CD8E10",
                        "191004",
                        "32B800",
                        "CD9014",
                        "CDA032",
                        "CDA025",
                        "1B2305",
                        "CDA031",
                        "CD8B0B",
                        "152007",
                        "195001",
                        "32B400",
                        "13822D",
                        "CDB204",
                        "20F20F",
                        "10B105",
                        "1B9804",
                        "20F206",
                        "193108",
                        "193008",
                        "CDB504",
                        "160504",
                        "CDC31A",
                        "CDA056",
                        "CDA050",
                        "CD8B08",
                        "CDA058",
                        "CDA057",
                        "CDA046",
                        "CDC132",
                        "CDC136",
                        "CDC317",
                        "CDC318",
                        "CDA72E",
                        "11B030",
                        "CDA71A",
                        "CDC129",
                        "102A63",
                        "CDA734",
                        "13822E",
                        "CDA728",
                        "CDC135",
                        "1C0204",
                        "CDA038",
                        "CDA725",
                        "CDA514",
                        "1C3002",
                        "CD8C1E",
                        "CD8D2D",
                        "CD8E12",
                        "CD9012",
                        "CD9001",
                        "CDC31C",
                        "CD990F",
                        "10A003",
                        "1A2604",
                        "CDBB92",
                        "CDC134",
                        "CDA71E",
                        "CD8C1A",
                        "CD8C1B",
                        "CD8C17",
                        "CD8C16",
                        "CD8C18",
                        "CD8C1D",
                        "CD8C1C",
                        "CD8C1F",
                        "CD8C19",
                        "CDBA3B",
                        "CDAD0B",
                        "CDB804",
                        "CD8604",
                        "CDA037",
                        "CD8D2C",
                        "CDA50E",
                        "CDAD09",
                        "CDAE04",
                        "CDB309",
                        "1A2206",
                        "1A220B",
                        "1A4011",
                        "1A2022",
                        "030E57",
                        "20F22B",
                        "10351C",
                        "20F205",
                        "030E53",
                        "030DA8",
                        "20A23F",
                        "20A234",
                        "030E1C",
                        "138222",
                        "138221",
                    };
                }

                foreach (var code in benchCodes)
                {
                    await _dtcRemover.RemoveAsync(code, fileInfo.A2L, data, optional: true);
                }
            }
            else
            {
                _logger.LogInformation("Delete Bench ECU DTC code masking not enabled");
            }

            #endregion DTC code masking Option

            #region Induction Noise Option

            if (options.InductionNoise)
            {
                _logger.LogInformation("applying Induction Noise...");
                var _customOptionName = "Induction Noise";

                var _data___BMWtqe_swi_NoiseWiDiDfco_C = new byte[] { 0x01 };

                await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_swi_NoiseWiDiDfco_C", _data___BMWtqe_swi_NoiseWiDiDfco_C, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

            }
            else
            {
                _logger.LogInformation("Induction Noise is not enabled");
            }

            #endregion Induction Noise Option

            #region Burbles Option

            // Burble
            if (options.Burble)
            {
                _logger.LogInformation("applying burble...");
                var _customOptionName = "Burble";

                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_BOTF", new byte[] { 0x01 }, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                var _burbleAggressionTable = await _valueResolver.GetAsync(_customOptionName, "KF_ZWMIN_KSOT", "BurbleOffAggressiveness");
                var _burbleDeltaSportTable = await _valueResolver.GetAsync(_customOptionName, "KF_DZWMIN_SPORT_SOT", "BurbleDeltaSportMap");
                var _burbleDeltaNormalTable = await _valueResolver.GetAsync(_customOptionName, "KF_DZWMIN_NORMAL_SOT", "BurbleDeltaNormalMap");
                if (options.BurbleAggressionStyleIndex_Selected == 0)
                {
                    // Disable all burbles per user request
                    options.Burble_CW_SOUND = true;
                    options.Burble_CW_SOUND_Value = 0x00; // disabled
                    options.BurbleDurationSport = 1; // 0 seconds
                    options.BurbleDurationSportPlus = 1; // 0 seconds
                }
                else if (options.BurbleAggressionStyleIndex_Selected == 3) // dynamic
                {
                    //_burbleAggressionTable = (byte[])CustomOptionsConstants.BurbleStyle3Aggressiveness[0]; // OEM map
                    _burbleAggressionTable = await _valueResolver.GetAsync(_customOptionName, "KF_ZWMIN_KSOT", "BurbleStyle3Aggressiveness", 1); // Lowered aggression OEM map by a few degrees

                    if (options.BurbleStyle3SportAggressiveness > 0
                        || options.BurbleStyle3NormalAggressiveness > 0)
                    {
                        _logger.LogInformation("applying burble delta maps...");

                        var maxRetardAngleSport = -10.0;
                        var maxRetardAngleNormal = -10.0;
                        double maxSportTableValue = maxRetardAngleSport;
                        double maxNormalTableValue = maxRetardAngleSport;

                        for (int idx = 0; idx < _burbleDeltaSportTable.Length; idx++)
                        {
                            var testSportVal = (((sbyte)_burbleDeltaSportTable[idx]) * 0.5);
                            if (testSportVal > maxSportTableValue)
                            {
                                maxSportTableValue = testSportVal;
                            }

                            var testNormalVal = (((sbyte)_burbleDeltaNormalTable[idx]) * 0.5);
                            if (testNormalVal > maxNormalTableValue)
                            {
                                maxNormalTableValue = testNormalVal;
                            }
                        }

                        for (int idx = 0; idx < _burbleDeltaSportTable.Length; idx++)
                        {
                            // Sport
                            var sliderInputSport = options.BurbleStyle3SportAggressiveness / 100.0;
                            if (sliderInputSport > 1.0)
                            {
                                sliderInputSport = 1.0;
                            }
                            else if (sliderInputSport < 0.0)
                            {
                                sliderInputSport = 0.0;
                            }

                            var timingValueSport = (((sbyte)_burbleDeltaSportTable[idx]) * 0.5);
                            var newTimingValueSport = ((maxRetardAngleSport - (timingValueSport - maxSportTableValue)) * sliderInputSport) + (timingValueSport - maxSportTableValue);

                            newTimingValueSport = (Math.Ceiling(newTimingValueSport * 2)) / 2; // round to nearest 0.5
                            if (newTimingValueSport < maxRetardAngleSport)
                            {
                                newTimingValueSport = maxRetardAngleSport;
                            }
                            else if (newTimingValueSport > 0.0)
                            {
                                newTimingValueSport = 0.0;
                            }

                            _burbleDeltaSportTable[idx] = (byte)Convert.ToSByte(newTimingValueSport * 2);

                            // Normal
                            var sliderInputNormal = options.BurbleStyle3NormalAggressiveness / 100.0;
                            if (sliderInputNormal > 1.0)
                            {
                                sliderInputNormal = 1.0;
                            }
                            else if (sliderInputNormal < 0.0)
                            {
                                sliderInputNormal = 0.0;
                            }

                            var timingValueNormal = (((sbyte)_burbleDeltaNormalTable[idx]) * 0.5);
                            var newTimingValueNormal = ((maxRetardAngleNormal - (timingValueNormal - maxNormalTableValue)) * sliderInputNormal) + (timingValueNormal - maxNormalTableValue);

                            newTimingValueNormal = (Math.Ceiling(newTimingValueNormal * 2)) / 2; // round to nearest 0.5
                            if (newTimingValueNormal < maxRetardAngleNormal)
                            {
                                newTimingValueNormal = maxRetardAngleNormal;
                            }
                            else if (newTimingValueNormal > 0.0)
                            {
                                newTimingValueNormal = 0.0;
                            }

                            _burbleDeltaNormalTable[idx] = (byte)Convert.ToSByte(newTimingValueNormal * 2);
                        }
                    }
                }
                else if (options.BurbleAggressionStyleIndex_Selected == 2)
                {
                    _burbleAggressionTable = await _valueResolver.GetAsync(_customOptionName, "KF_ZWMIN_KSOT", "BurbleStyle2Aggressiveness", options.BurbleStyle2Aggressiveness);
                }
                else if (options.BurbleAggressionStyleIndex_Selected == 1)
                {
                    _burbleAggressionTable = await _valueResolver.GetAsync(_customOptionName, "KF_ZWMIN_KSOT", "BurbleStyle1Aggressiveness", options.BurbleStyle1Aggressiveness);
                }

                // Lambda Target Values & Extra Aggression
                // [1-101] subtract 1 then divide by 100 to get percentage, multiply this by the difference from a given timing value to the max retard angle to get a new target retard angle. Round to nearest 0.5, finally, multiply by 2 for ecu conversion
                // [1-32] multiply by 0.1 and add 0.69 to get lambda, then multiply by 4096 for ecu conversion, finally read hex split as high & low bytes
                var _bFFBytes = new byte[] { 0x10, 0x00 }; //_burbleFlameFuelBytes
                if (options.BurbleAggressionStyleIndex_Selected != 0
                    && options.Dcat
                    && options.BurbleFlameTiming > 0
                    && options.BurbleFlame)
                {
                    _logger.LogInformation("applying burble extra aggression (flame map)...");
                    var columns = _burbleAggressionTable.Length / 8; // 8 rows
                    var rowIndex = 0;
                    for (int idx = 0; idx < _burbleAggressionTable.Length; idx++)
                    {
                        if (idx >= columns && (idx % columns == 0))
                        {
                            rowIndex++;
                        }

                        var columnIndex = idx - (rowIndex * columns);
                        if (rowIndex <= 2 && columnIndex >= 3 && columnIndex <= 7)
                        {
                            // if less than 33.7% load & between 1600 - 6528 rpm
                            var timingValue = (((sbyte)_burbleAggressionTable[idx]) * 0.5);
                            var sliderinput = (options.BurbleFlameTiming - 1) / 100.0;
                            if (sliderinput > 1.0)
                            {
                                sliderinput = 1.0;
                            }
                            else if (sliderinput < 0.0)
                            {
                                sliderinput = 0.0;
                            }

                            var maxRetardAngle = -35.0;

                            if (options.BurbleAggressionStyleIndex_Selected == 3)
                            {
                                maxRetardAngle = -27.0;
                            }

                            var newTimingValue = ((maxRetardAngle - timingValue) * sliderinput) + timingValue;
                            newTimingValue = (Math.Ceiling(newTimingValue * 2)) / 2; // round to nearest 0.5
                            if (newTimingValue < maxRetardAngle)
                            {
                                newTimingValue = maxRetardAngle;
                            }
                            else if (newTimingValue > 0.0)
                            {
                                newTimingValue = 0.0;
                            }

                            _burbleAggressionTable[idx] = (byte)Convert.ToSByte(newTimingValue * 2);
                        }
                    }

                    _logger.LogInformation("applying burble extra fuel (flame map)...");
                    if (options.BurbleFlameFuel > 0)
                    {
                        var _lambda = 0.69 + (options.BurbleFlameFuel * 0.01);
                        if (options.BurbleFlameFuel >= 32 || options.BurbleFlameFuel < 1)
                        {
                            _lambda = 1.0;
                        }

                        var _lambda_ecu = (int)Math.Floor(_lambda * 4096);
                        _bFFBytes[0] = (byte)((_lambda_ecu) >> 8);
                        _bFFBytes[1] = (byte)(_lambda_ecu);
                        _logger.LogInformation("burble lambda target set to {_lambda} lambda", _lambda);
                    }
                }
                var _burbleTargetLambdaTable = new byte[] { 0x10, 0x00, _bFFBytes[0], _bFFBytes[1], _bFFBytes[0], _bFFBytes[1], _bFFBytes[0], _bFFBytes[1], _bFFBytes[0], _bFFBytes[1], 0x10, 0x00 }; // [1.0,x,x,x,x,1.0]

                //  Hybrid Patch                
                if (options.BurbleHybridPatch)
                {
                    _logger.LogInformation("burble 'hybrid' patch is marked to be applied:");

                    var _data___K_TD_UESA_MAX = new byte[] { 0x03, 0xE8 };
                    var _data___CW_STAT_REIB_SA_VMLIM = new byte[] { 0xFF };
                    var _data___BMWtqe_fac_TqMinDly_C = new byte[] { 0x80 };
                    var _data___BMWtqe_fac_TqMinCmbDly_C = new byte[] { 0x80 };
                    var _data___S_ANF_ZWRAMPE_WE = new byte[] { 0x01 };
                    var _data___S_ANF_ZWRAMPE_SA = new byte[] { 0x01 };
                    var _data___S_ANF_PATTERN_WE = new byte[] { 0x01 };
                    var _data___S_SOT_CALC = new byte[] { 0x01 };

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "K_TD_UESA_MAX", _data___K_TD_UESA_MAX, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CW_STAT_REIB_SA_VMLIM", _data___CW_STAT_REIB_SA_VMLIM, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_fac_TqMinDly_C", _data___BMWtqe_fac_TqMinDly_C, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_fac_TqMinCmbDly_C", _data___BMWtqe_fac_TqMinCmbDly_C, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "S_ANF_ZWRAMPE_WE", _data___S_ANF_ZWRAMPE_WE, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "S_ANF_ZWRAMPE_SA", _data___S_ANF_ZWRAMPE_SA, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "S_ANF_PATTERN_WE", _data___S_ANF_PATTERN_WE, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "S_SOT_CALC", _data___S_SOT_CALC, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                else
                {
                    _logger.LogInformation("Burble 'Hybrid' patch is not marked for install...");
                }

                //  Temperature limit increase
                if (options.BurbleTemperatureLimitIncrease)
                {
                    _logger.LogInformation("burble temperature limit increase is marked to be applied:");


                    if (options.Dcat && options.OpfDelete)
                    {
                        var _data___1x4_KL_T_IKAT_SOUND_MX = new byte[] {
                            0x2A, 0xF8, 0x2A, 0xF8, 0x2A, 0xF8, 0x2A, 0xF8, // 1100'C
                            //0x27, 0x10, 0x27, 0x10, 0x27, 0x10, 0x27, 0x10, // 1000'C
                        };

                        await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_T_IKAT_SOUND_MX", _data___1x4_KL_T_IKAT_SOUND_MX, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    }
                    else
                    {
                        _logger.LogInformation("Dcat && OpfDelete aren't enabled, burble temperature patch is not applied!");
                    }
                }
                else
                {
                    _logger.LogInformation("Burble temperature limit increase is not marked for install...");
                }

                // CW_SOUND
                var _burble_CW_SOUND = await _valueResolver.GetAsync(_customOptionName, "CW_SOUND", "BurbleEnable");
                if (options.Burble_CW_SOUND)
                {
                    if (options.Burble_CW_SOUND_Value >= 0x00 && options.Burble_CW_SOUND_Value <= 0xFF)
                    {
                        _burble_CW_SOUND[0] = (byte)options.Burble_CW_SOUND_Value;
                    }
                    else
                    {
                        _burble_CW_SOUND[0] = 0x00;
                    }
                }
                _logger.LogInformation("burble CW_SOUND set to {CW_SOUND} [decimal]", _burble_CW_SOUND[0]);

                // Duration Sport
                // [0-41] divide by 10 to get seconds, then multiply by 100 for ecu conversion, finally read hex split as high & low bytes
                var _bDSBytes = new byte[] { 0x00, 0x00 }; //_burbleDurationBytes
                if (options.BurbleDurationSport > 0)
                {
                    int _duration = (int)options.BurbleDurationSport - 1;
                    if (options.BurbleDurationSport == 1)
                    {
                        _duration = 40;
                    }

                    _bDSBytes[0] = (byte)((_duration * 10) >> 8);
                    _bDSBytes[1] = (byte)(_duration * 10);
                    _logger.LogInformation("burble duration sport set to {duration}s", _duration / 10);
                }
                var _burbleDurationOtherTable = new byte[] { _bDSBytes[0], _bDSBytes[1], _bDSBytes[0], _bDSBytes[1], _bDSBytes[0], _bDSBytes[1], _bDSBytes[0], _bDSBytes[1], };

                // Duration Sport Plus
                // [0-41] divide by 10 to get seconds, then multiply by 100 for ecu conversion, finally read hex split as high & low bytes
                var _bDSPBytes = new byte[] { 0x00, 0x00 }; //_burbleDurationBytes
                if (options.BurbleDurationSportPlus > 0)
                {
                    int _duration = (int)options.BurbleDurationSportPlus - 1;
                    if (options.BurbleDurationSportPlus == 1)
                    {
                        _duration = 40;
                    }

                    _bDSPBytes[0] = (byte)((_duration * 10) >> 8);
                    _bDSPBytes[1] = (byte)(_duration * 10);
                    _logger.LogInformation("burble duration sport plus set to {duration}s", _duration / 10);
                }
                var _burbleDurationSportPlusTable = new byte[] { _bDSPBytes[0], _bDSPBytes[1], _bDSPBytes[0], _bDSPBytes[1], _bDSPBytes[0], _bDSPBytes[1], _bDSPBytes[0], _bDSPBytes[1], };

                // RPM Max
                // [0-62] Multiply by 100 then add 800 to get RPM value, read hex split as high & low bytes
                var _bRMxBytes = new byte[] { 0x05, 0xDC }; //_burbleRpmMaxBytes
                if (options.BurbleRpmMax > 0)
                {
                    int _rpmMax = ((int)options.BurbleRpmMax * 100) + 800;
                    if (options.BurbleRpmMax >= 62)
                    {
                        _rpmMax = 7000;
                    }

                    if (options.BurbleRpmMax <= 0)
                    {
                        _rpmMax = 900;
                    }

                    _bRMxBytes[0] = (byte)(_rpmMax >> 8);
                    _bRMxBytes[1] = (byte)(_rpmMax);
                    _logger.LogInformation("burble RPM max set to {_rpmMax} RPM", _rpmMax);
                }
                // 1x8 table of 16 bit values (signed)
                var _burbleRpmMaxTable = new byte[] { _bRMxBytes[0], _bRMxBytes[1], _bRMxBytes[0], _bRMxBytes[1], _bRMxBytes[0], _bRMxBytes[1], _bRMxBytes[0], _bRMxBytes[1],
                    _bRMxBytes[0], _bRMxBytes[1], _bRMxBytes[0], _bRMxBytes[1], _bRMxBytes[0], _bRMxBytes[1], _bRMxBytes[0], _bRMxBytes[1], };

                // RPM Min
                // [0-62] Multiply by 100 then add 800 to get RPM value, read hex split as high & low bytes
                var _bRMnBytes = new byte[] { 0x17, 0x70 }; //_burbleRpmMinBytes
                if (options.BurbleRpmMin > 0)
                {
                    int _rpmMin = ((int)options.BurbleRpmMin * 100) + 800;
                    if (options.BurbleRpmMin >= 62)
                    {
                        _rpmMin = 7000;
                    }

                    if (options.BurbleRpmMin <= 0)
                    {
                        _rpmMin = 900;
                    }

                    _bRMnBytes[0] = (byte)(_rpmMin >> 8);
                    _bRMnBytes[1] = (byte)_rpmMin;
                    _logger.LogInformation("burble RPM min set to {_rpmMin} RPM", _rpmMin);
                }
                var _burbleRpmMinTable = new byte[] { _bRMnBytes[0], _bRMnBytes[1], _bRMnBytes[0], _bRMnBytes[1], _bRMnBytes[0], _bRMnBytes[1], _bRMnBytes[0], _bRMnBytes[1],
                    _bRMnBytes[0], _bRMnBytes[1], _bRMnBytes[0], _bRMnBytes[1], _bRMnBytes[0], _bRMnBytes[1], _bRMnBytes[0], _bRMnBytes[1], };

                // Speed Max
                // [0-62] Multiply by 5 and subtract 5, then multiply by 50 for ecu conversion, finally read hex split as high & low bytes
                var _bSMxBytes = new byte[] { 0x7F, 0xFF }; //_burbleSpeedMaxBytes
                if (options.BurbleSpeedMax > 0)
                {
                    int _speedMax = ((int)options.BurbleSpeedMax * 5) - 5;
                    if (options.BurbleSpeedMax >= 62)
                    {
                        _speedMax = 350;
                    }

                    if (options.BurbleSpeedMax <= 0)
                    {
                        _speedMax = 0;
                    }

                    _bSMxBytes[0] = (byte)((_speedMax * 50) >> 8);
                    _bSMxBytes[1] = (byte)(_speedMax * 50);
                    _logger.LogInformation("burble Speed max set to {_speedMax} km/h", _speedMax);
                }
                var _burbleSpeedMaxTable = new byte[] { _bSMxBytes[0], _bSMxBytes[1] };

                // Speed Min
                // [0-62] Multiply by 5 and subtract 5, then multiply by 50 for ecu conversion, finally read hex split as high & low bytes
                var _bSMnBytes = new byte[] { 0x00, 0x00 }; //_burbleSpeedMinBytes
                if (options.BurbleSpeedMin > 0)
                {
                    int _speedMin = ((int)options.BurbleSpeedMin * 5) - 5;
                    if (options.BurbleSpeedMin >= 61)
                    {
                        _speedMin = 300;
                    }

                    if (options.BurbleSpeedMin <= 0)
                    {
                        _speedMin = 0;
                    }

                    _bSMnBytes[0] = (byte)((_speedMin * 50) >> 8);
                    _bSMnBytes[1] = (byte)(_speedMin * 50);
                    _logger.LogInformation("burble Speed min set to {_speedMin} km/h", _speedMin);
                }
                var _burbleSpeedMinTable = new byte[] { _bSMnBytes[0], _bSMnBytes[1] };

                _logger.LogInformation("looking up driving modes...");
                var _address___BMW_FES35_SC = await _addressResolver.TryResolveAsync(fileInfo.A2L, carInfo.TargetCustomCodeVersion, "BMW_FES35_SC") ?? 0;
                var _address___K_ASM_UC_MAPPING_ANTR = await _addressResolver.TryResolveAsync(fileInfo.A2L, carInfo.TargetCustomCodeVersion, "K_ASM_UC_MAPPING_ANTR") ?? 0;
                var _address___K_ASM_UC_FIND_ANTR = await _addressResolver.TryResolveAsync(fileInfo.A2L, carInfo.TargetCustomCodeVersion, "K_ASM_UC_FIND_ANTR") ?? 0;
                var _address___KL_ASM_ST_FES_PIA_MD = await _addressResolver.TryResolveAsync(fileInfo.A2L, carInfo.TargetCustomCodeVersion, "KL_ASM_ST_FES_PIA_MD") ?? 0;
                var _address___AXX_KL_ASM_ST_FES_PIA_MD = await _addressResolver.TryResolveAsync(fileInfo.A2L, carInfo.TargetCustomCodeVersion, "AXX_KL_ASM_ST_FES_PIA_MD") ?? 0;
                var _address___KL_ASM_ST_FES = await _addressResolver.TryResolveAsync(fileInfo.A2L, carInfo.TargetCustomCodeVersion, "KL_ASM_ST_FES") ?? 0;
                var _address___AXX_KL_ASM_ST_FES = await _addressResolver.TryResolveAsync(fileInfo.A2L, carInfo.TargetCustomCodeVersion, "AXX_KL_ASM_ST_FES") ?? 0;
                var _address___KL_ASM_ST_FESxxxx = 0x0u;
                var _address___AXX_KL_ASM_ST_FESxxx = 0x0u;
                if (_address___BMW_FES35_SC == 1)
                {
                    _address___KL_ASM_ST_FESxxxx = _address___KL_ASM_ST_FES_PIA_MD;
                    _address___AXX_KL_ASM_ST_FESxxx = _address___AXX_KL_ASM_ST_FES_PIA_MD;
                }
                else if (_address___BMW_FES35_SC == 0)
                {
                    _address___KL_ASM_ST_FESxxxx = _address___KL_ASM_ST_FES;
                    _address___AXX_KL_ASM_ST_FESxxx = _address___AXX_KL_ASM_ST_FES;
                }
                else
                {
                    _logger.LogInformation("Unable to determine BMW_FES35_SC!");
                }

                var drivingModes = GetDrivingModesFromBinary(data, carInfo,
                    Address___K_ASM_UC_MAPPING_ANTR: (uint)_address___K_ASM_UC_MAPPING_ANTR,
                    Address___K_ASM_UC_FIND_ANTR: (uint)_address___K_ASM_UC_FIND_ANTR,
                    Address___KL_ASM_ST_FESxxxx: (uint)_address___KL_ASM_ST_FESxxxx,
                    Address___AXX_KL_ASM_ST_FESxxx: (uint)_address___AXX_KL_ASM_ST_FESxxx);

                _ = drivingModes.TryGetValue(DrivingMode.Eco, out var drivingModesEco);
                _ = drivingModes.TryGetValue(DrivingMode.EcoPlus, out var drivingModesEcoPlus);
                _ = drivingModes.TryGetValue(DrivingMode.Comfort, out var drivingModesComfort);
                _ = drivingModes.TryGetValue(DrivingMode.ComfortPlus, out var drivingModesComfortPlus);
                _ = drivingModes.TryGetValue(DrivingMode.Sport, out var drivingModesSport);
                _ = drivingModes.TryGetValue(DrivingMode.SportPlus, out var drivingModesSportPlus);

                var burbleInEco = false;
                var burbleInEcoPlus = false;
                var burbleInComfort = false;
                var burbleInComfortPlus = false;
                var burbleInSport = true;
                var burbleInSportPlus = true;

                if (options.BurbleSpecifyDrivingModes)
                {
                    burbleInEco = options.BurbleEnableEcoDrivingMode;
                    burbleInEcoPlus = options.BurbleEnableEcoPlusDrivingMode;
                    burbleInComfort = options.BurbleEnableComfortDrivingMode;
                    burbleInComfortPlus = options.BurbleEnableComfortPlusDrivingMode;
                    burbleInSport = options.BurbleEnableSportDrivingMode;
                    burbleInSportPlus = options.BurbleEnableSportPlusDrivingMode;
                }

                _logger.LogInformation("enabled burble modes: Eco = {Eco}, EcoPlus = {EcoPlus}, Comfort = {Comfort}, ComfortPlus = {ComfortPlus}, Sport = {Sport}, SportPlus = {SportPlus}...",
                    burbleInEco, burbleInEcoPlus, burbleInComfort, burbleInComfortPlus, burbleInSport, burbleInSportPlus);

                _logger.LogInformation("applying burble duration selection map...");
                var _burbleModesDuration = await _valueResolver.GetAsync(_customOptionName, "BMWtqe_swi_NoiseDur_T", "BurbleModesDuration");
                for (int idx = 0; idx < _burbleModesDuration.Length; idx++)
                {
                    if (drivingModesEcoPlus.Contains(idx) && burbleInEcoPlus)
                    {
                        _burbleModesDuration[idx] = 0x02; // sport value
                    }
                    else if (drivingModesEco.Contains(idx) && burbleInEco)
                    {
                        _burbleModesDuration[idx] = 0x02; // sport value
                    }
                    else if (drivingModesComfortPlus.Contains(idx) && burbleInComfortPlus)
                    {
                        _burbleModesDuration[idx] = 0x02; // sport value
                    }
                    else if (drivingModesComfort.Contains(idx) && burbleInComfort)
                    {
                        _burbleModesDuration[idx] = 0x02; // sport value
                    }
                    else if (drivingModesSportPlus.Contains(idx) && burbleInSportPlus)
                    {
                        _burbleModesDuration[idx] = 0x01; // sport+ value
                    }
                    else if (drivingModesSport.Contains(idx) && burbleInSport)
                    {
                        _burbleModesDuration[idx] = 0x02; // sport value
                    }

                    if (fileInfo.Pst.Id == 0x3076 && fileInfo.Pst.Major == 50 && fileInfo.Pst.Minor == 6 && fileInfo.Pst.Patch == 2
                        && idx == 3)
                    {
                        // applied additional burble maps just for John's hybrid:
                        _burbleModesDuration[idx] = 0x01; // sport+ value
                    }
                }

                if (fileInfo.Pst.Id == 0x3076 && fileInfo.Pst.Major == 50 && fileInfo.Pst.Minor == 6 && fileInfo.Pst.Patch == 2)
                {
                    _logger.LogInformation("applied additional burble maps just for John's hybrid:");
                    _logger.LogInformation("   for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", fileInfo.Pst.Id.ToString("X4"), fileInfo.Pst.Major, fileInfo.Pst.Minor, fileInfo.Pst.Patch);
                }

                await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_swi_NoiseDur_T", _burbleModesDuration, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                var _burbleModesDurationAurixSpare = await _valueResolver.GetAsync(_customOptionName, "BMWtqe_swi_NoiseDurBrkgSou_T", "BurbleModesDurationAurixSpare");
                for (int idx = 0; idx < _burbleModesDurationAurixSpare.Length; idx++)
                {
                    if (drivingModesEcoPlus.Contains(idx) && burbleInEcoPlus)
                    {
                        _burbleModesDurationAurixSpare[idx] = 0x02; // sport value
                    }
                    else if (drivingModesEco.Contains(idx) && burbleInEco)
                    {
                        _burbleModesDurationAurixSpare[idx] = 0x02; // sport value
                    }
                    else if (drivingModesComfortPlus.Contains(idx) && burbleInComfortPlus)
                    {
                        _burbleModesDurationAurixSpare[idx] = 0x02; // sport value
                    }
                    else if (drivingModesComfort.Contains(idx) && burbleInComfort)
                    {
                        _burbleModesDurationAurixSpare[idx] = 0x02; // sport value
                    }
                    else if (drivingModesSportPlus.Contains(idx) && burbleInSportPlus)
                    {
                        _burbleModesDurationAurixSpare[idx] = 0x01; // sport+ value
                    }
                    else if (drivingModesSport.Contains(idx) && burbleInSport)
                    {
                        _burbleModesDurationAurixSpare[idx] = 0x02; // sport value
                    }
                }

                await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_swi_NoiseDurBrkgSou_T", _burbleModesDurationAurixSpare, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                if (carInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                {
                    _logger.LogInformation("applying burbles in specific driving modes...");
                    var _burbleModesEnabledPPC = await _valueResolver.GetAsync(_customOptionName, "BMWtqe_b_SptDet4NoiseAcvn_M", "BurbleModesEnabledPPC");
                    var columns = _burbleModesEnabledPPC.Length / 4; // 4 rows
                    var rowIndex = 0;
                    for (int idx = 0; idx < _burbleModesEnabledPPC.Length; idx++)
                    {
                        if (idx >= columns && (idx % columns == 0))
                        {
                            rowIndex++;
                        }
                        var columnIndex = idx - (rowIndex * columns);

                        if (drivingModesEcoPlus.Contains(columnIndex) && burbleInEcoPlus)
                        {
                            _burbleModesEnabledPPC[idx] = 0x01;
                        }
                        else if (drivingModesEco.Contains(columnIndex) && burbleInEco)
                        {
                            _burbleModesEnabledPPC[idx] = 0x01;
                        }
                        else if (drivingModesComfortPlus.Contains(columnIndex) && burbleInComfortPlus)
                        {
                            _burbleModesEnabledPPC[idx] = 0x01;
                        }
                        else if (drivingModesComfort.Contains(columnIndex) && burbleInComfort)
                        {
                            _burbleModesEnabledPPC[idx] = 0x01;
                        }
                        else if (drivingModesSportPlus.Contains(columnIndex) && burbleInSportPlus)
                        {
                            _burbleModesEnabledPPC[idx] = 0x01;
                        }
                        else if (drivingModesSport.Contains(columnIndex) && burbleInSport)
                        {
                            _burbleModesEnabledPPC[idx] = 0x01;
                        }

                        if ((fileInfo.Pst.Id == 0x3076 && fileInfo.Pst.Major == 50 && fileInfo.Pst.Minor == 6 && fileInfo.Pst.Patch == 2
                                || carInfo.IsHybrid)
                            && columnIndex == 3)
                        {
                            // applied additional burble maps just for John's and Siky's hybrids:
                            _burbleModesEnabledPPC[idx] = 0x01;
                        }
                    }

                    if (fileInfo.Pst.Id == 0x3076 && fileInfo.Pst.Major == 50 && fileInfo.Pst.Minor == 6 && fileInfo.Pst.Patch == 2)
                    {
                        _logger.LogInformation("applied additional burble maps just for John's hybrid:");
                        _logger.LogInformation("   for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", fileInfo.Pst.Id.ToString("X4"), fileInfo.Pst.Major, fileInfo.Pst.Minor, fileInfo.Pst.Patch);
                    }
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_b_SptDet4NoiseAcvn_M", _burbleModesEnabledPPC, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }

                if (carInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
                {
                    _logger.LogInformation("applying burbles in specific driving modes [enable threshold]...");
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWign_st_SptDet4NoiseAcvnThd_C", await _valueResolver.GetAsync(_customOptionName, "BMWign_st_SptDet4NoiseAcvnThd_C", "BurbleModesEnabledThresholdAurix"), valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                    _logger.LogInformation("applying burbles in specific driving modes [enable Aurix old]...");
                    var _burbleModesEnabledAurixOld = await _valueResolver.GetAsync(_customOptionName, "BMWtqe_st_SptDet4NoiseAcvn_M", "BurbleModesEnabledAurixOld");
                    var columns = (_burbleModesEnabledAurixOld.Length / 4); // 4 rows
                    var rowIndex = 0;
                    for (int idx = 0; idx < _burbleModesEnabledAurixOld.Length; idx++)
                    {
                        if (idx >= columns && (idx % columns == 0))
                        {
                            rowIndex++;
                        }
                        var columnIndex = idx - (rowIndex * columns);

                        if (drivingModesEcoPlus.Contains(columnIndex) && burbleInEcoPlus)
                        {
                            _burbleModesEnabledAurixOld[idx] = 0x01; // sport value
                        }
                        else if (drivingModesEco.Contains(columnIndex) && burbleInEco)
                        {
                            _burbleModesEnabledAurixOld[idx] = 0x01; // sport value
                        }
                        else if (drivingModesComfortPlus.Contains(columnIndex) && burbleInComfortPlus)
                        {
                            _burbleModesEnabledAurixOld[idx] = 0x01; // sport value
                        }
                        else if (drivingModesComfort.Contains(columnIndex) && burbleInComfort)
                        {
                            _burbleModesEnabledAurixOld[idx] = 0x01; // sport value
                        }
                        else if (drivingModesSportPlus.Contains(columnIndex) && burbleInSportPlus)
                        {
                            _burbleModesEnabledAurixOld[idx] = 0x02; // sport+ value
                        }
                        else if (drivingModesSport.Contains(columnIndex) && burbleInSport)
                        {
                            _burbleModesEnabledAurixOld[idx] = 0x01; // sport value
                        }
                    }

                    var specificDrivingModesFlashed = false;
                    if (await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_st_SptDet4NoiseAcvn_M", _burbleModesEnabledAurixOld, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger))
                    {
                        specificDrivingModesFlashed = true;
                    }

                    _logger.LogInformation("applying burbles in specific driving modes [enable Aurix new]...");
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_st_BrkgSou4NoiseAcvn_T", await _valueResolver.GetAsync(_customOptionName, "BMWtqe_st_BrkgSou4NoiseAcvn_T", "BurbleModesEnabledAurixNew"), valueBitSize: 8, signedData: false, optionalExclusion: specificDrivingModesFlashed, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }

                if (options.BurbleUpShift && options.BurbleUpShiftAggression != BurbleUpShiftAggressionConsts.OEM)
                {
                    _logger.LogInformation($"applying burble on upshift, aggression level: [{options.BurbleUpShiftAggression}]");

                    var upshiftAggression = (byte)0x00;
                    if (options.BurbleUpShiftAggression == BurbleUpShiftAggressionConsts.Soft)
                    {
                        upshiftAggression = (byte)0x02;
                    }
                    else if (options.BurbleUpShiftAggression == BurbleUpShiftAggressionConsts.Hard)
                    {
                        upshiftAggression = (byte)0x03;
                    }

                    var _data___BMWtqe_swi_Mod4GbxIntvNew_M = new byte[] { // ubyte, 8 x 5
                        upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression,
                        upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression,
                        upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression,
                        upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression,
                        upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression,
                    };
                    var _data___BMWtqe_swi_Mod4GbxIntv_M = new byte[] { // ubyte, 11 x 5
                        upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression,
                        upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression,
                        upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression,
                        upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression,
                        upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression, upshiftAggression,
                    };

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_swi_Mod4GbxIntv_M", _data___BMWtqe_swi_Mod4GbxIntv_M, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_swi_Mod4GbxIntvNew_M", _data___BMWtqe_swi_Mod4GbxIntvNew_M, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                else
                {
                    _logger.LogInformation($"Burble on upshift disabled or OEM");
                }

                if (options.BurbleFlame
                    && options.Dcat
                    && options.BurbleAggressionStyleIndex_Selected != 0)
                {
                    _logger.LogInformation($"applying burble 'Flame' mode");
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CW_LAMCO", await _valueResolver.GetAsync(_customOptionName, "CW_LAMCO", "BurbleTargetLambdaCodeword"), valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "AXX_KL_LA_SBL", await _valueResolver.GetAsync(_customOptionName, "AXX_KL_LA_SBL", "BurbleAxisTargetLambda"), valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_LA_SBL", _burbleTargetLambdaTable, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
                else
                {
                    _logger.LogInformation($"Burble 'Flame' disabled, 'Dcat' not marked or BurbleAggressionStyle is 'Off'");
                }

                _logger.LogInformation("applying burble enable codeword...");
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CW_SOUND", _burble_CW_SOUND, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                if (options.BurbleAggressionStyleIndex_Selected == 0
                    || options.BurbleStyle1Aggressiveness > 0
                    || options.BurbleStyle2Aggressiveness > 0
                    || options.BurbleStyle3NormalAggressiveness > 0
                    || options.BurbleStyle3SportAggressiveness > 0)
                {
                    //if (false)
                    {
                        _logger.LogInformation("applying faster rate of ignition timing decrease during burbles (required for F-series B48 hybrids)...");
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_ag_gra_IgDfco_T", await _valueResolver.GetAsync(_customOptionName, "BMWtqe_ag_gra_IgDfco_T", "BurbleIgnitionTimingDecreaseCurve"), valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "BMWtqe_ag_gra_IgDfcoNoise_C", await _valueResolver.GetAsync(_customOptionName, "BMWtqe_ag_gra_IgDfcoNoise_C", "BurbleIgnitionTimingDecreaseFactor"), valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        //await data.ApplyMapDataAsync(moduleInfo, "BMWtqe_ag_gra_IgDfco_Ax", (byte[])CustomOptionsConstants.BurbleIgnitionTimingDecreaseCurveAxis[0], valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _customCodeOptionAddressResolver); // affects another map, we won't flash this for now
                    }

                    _logger.LogInformation("applying burble aggressiveness...");
                    if (!CustomCodeVersions.IsAtLeastCustomCodev71(carInfo.TargetCustomCodeVersion))
                    {
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_ZWMIN_WSOT", _burbleAggressionTable, valueBitSize: 8, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_ZWMIN_KSOT", _burbleAggressionTable, valueBitSize: 8, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    }

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "AXX_KF_ZWMIN_WSOT", await _valueResolver.GetAsync(_customOptionName, "AXX_KF_ZWMIN_WSOT", "BurbleXAxisAggressiveness"), valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "AXX_KF_ZWMIN_KSOT", await _valueResolver.GetAsync(_customOptionName, "AXX_KF_ZWMIN_KSOT", "BurbleXAxisAggressiveness"), valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "AXY_KF_ZWMIN_WSOT", await _valueResolver.GetAsync(_customOptionName, "AXY_KF_ZWMIN_WSOT", "BurbleYAxisAggressiveness"), valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "AXY_KF_ZWMIN_KSOT", await _valueResolver.GetAsync(_customOptionName, "AXY_KF_ZWMIN_KSOT", "BurbleYAxisAggressiveness"), valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                    // Default burble slot data
                    // 1000 RPM, slider/base:
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_KF_ZWMIN_KSOT_PF_Slot_1", _burbleAggressionTable, valueBitSize: 8, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_KF_ZWMIN_WSOT_PF_Slot_1", _burbleAggressionTable, valueBitSize: 8, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    // 2000 RPM, 20%:
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_KF_ZWMIN_KSOT_PF_Slot_2", await _valueResolver.GetAsync(_customOptionName, "CustomCode_KF_ZWMIN_KSOT_PF_Slot_2", "CustomCodeBurbleAggressionMapSlot2"), valueBitSize: 8, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_KF_ZWMIN_WSOT_PF_Slot_2", await _valueResolver.GetAsync(_customOptionName, "CustomCode_KF_ZWMIN_WSOT_PF_Slot_2", "CustomCodeBurbleAggressionMapSlot2"), valueBitSize: 8, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    // 3000 RPM, 40%:
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_KF_ZWMIN_KSOT_PF_Slot_3", await _valueResolver.GetAsync(_customOptionName, "CustomCode_KF_ZWMIN_KSOT_PF_Slot_3", "CustomCodeBurbleAggressionMapSlot3"), valueBitSize: 8, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_KF_ZWMIN_WSOT_PF_Slot_3", await _valueResolver.GetAsync(_customOptionName, "CustomCode_KF_ZWMIN_WSOT_PF_Slot_3", "CustomCodeBurbleAggressionMapSlot3"), valueBitSize: 8, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    // 4000 RPM, 70%:
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_KF_ZWMIN_KSOT_PF_Slot_4", await _valueResolver.GetAsync(_customOptionName, "CustomCode_KF_ZWMIN_KSOT_PF_Slot_4", "CustomCodeBurbleAggressionMapSlot4"), valueBitSize: 8, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_KF_ZWMIN_WSOT_PF_Slot_4", await _valueResolver.GetAsync(_customOptionName, "CustomCode_KF_ZWMIN_WSOT_PF_Slot_4", "CustomCodeBurbleAggressionMapSlot4"), valueBitSize: 8, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    // 5000 RPM, off:
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_KF_ZWMIN_KSOT_PF_Slot_5", await _valueResolver.GetAsync(_customOptionName, "CustomCode_KF_ZWMIN_KSOT_PF_Slot_5", "CustomCodeBurbleAggressionMapSlot5"), valueBitSize: 8, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_KF_ZWMIN_WSOT_PF_Slot_5", await _valueResolver.GetAsync(_customOptionName, "CustomCode_KF_ZWMIN_WSOT_PF_Slot_5", "CustomCodeBurbleAggressionMapSlot5"), valueBitSize: 8, signedData: true, optionalExclusion: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                    if (options.BurbleAggressionStyleIndex_Selected == 3)
                    {
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_DZWMIN_SPORT_SOT", _burbleDeltaSportTable, valueBitSize: 8, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "AXX_KF_DZWMIN_SPORT_SOT", await _valueResolver.GetAsync(_customOptionName, "AXX_KF_DZWMIN_SPORT_SOT", "BurbleDeltaXAxis"), valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "AXY_KF_DZWMIN_SPORT_SOT", await _valueResolver.GetAsync(_customOptionName, "AXY_KF_DZWMIN_SPORT_SOT", "BurbleDeltaYAxis"), valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_DZWMIN_NORMAL_SOT", _burbleDeltaNormalTable, valueBitSize: 8, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "AXX_KF_DZWMIN_NORMAL_SOT", await _valueResolver.GetAsync(_customOptionName, "AXX_KF_DZWMIN_NORMAL_SOT", "BurbleDeltaXAxis"), valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "AXY_KF_DZWMIN_NORMAL_SOT", await _valueResolver.GetAsync(_customOptionName, "AXY_KF_DZWMIN_NORMAL_SOT", "BurbleDeltaYAxis"), valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    }
                }

                if (options.BurbleForceMaxAggressionAndDurationFactor || options.BurbleAggressionStyleIndex_Selected == 3 || (fileInfo.Pst.Id == 0x3E7D && options.BurbleAggressionStyleIndex_Selected > 0))
                {
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "K_V_SOT_MN", await _valueResolver.GetAsync(_customOptionName, "K_V_SOT_MN", "BurbleDelta_RequirementA"), valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "K_F_SOT_RUECKBLICK_STAND", await _valueResolver.GetAsync(_customOptionName, "K_F_SOT_RUECKBLICK_STAND", "BurbleDelta_RequirementB"), valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }

                if (options.BurbleDurationSport > 0)
                {
                    _logger.LogInformation("applying burble duration...");
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_TD_SOUND_MX_SPORT", _burbleDurationSportPlusTable, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }

                if (options.BurbleDurationSportPlus > 0)
                {
                    _logger.LogInformation("applying burble duration...");
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_TD_SOUND_MX_FREI", _burbleDurationOtherTable, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_TD_SOUND_MX", _burbleDurationOtherTable, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }

                if (options.BurbleRpmMin > 0)
                {
                    _logger.LogInformation("applying burble rpm min...");
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_NKW_SOUND_MN", _burbleRpmMinTable, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }

                if (options.BurbleRpmMax > 0)
                {
                    _logger.LogInformation("applying burble rpm max...");
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "KL_NKW_SOUND_MX", _burbleRpmMaxTable, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }

                if (options.BurbleSpeedMin > 0)
                {
                    _logger.LogInformation("applying burble speed min...");
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "K_V_SOUND_MN", _burbleSpeedMinTable, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }

                if (options.BurbleSpeedMax > 0)
                {
                    _logger.LogInformation("applying burble speed max...");
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "K_V_SOUND_MX", _burbleSpeedMaxTable, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
            }
            else
            {
                _logger.LogInformation("Burbles are not marked");

                if (options.TunerOverrideDst)
                {
                    _logger.LogInformation("Default burble values are NOT used for BOTF Slot 0 because tuner override is marked!");
                }
                else
                {
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_BOTF", new byte[] { 0x00 }, valueBitSize: 8, signedData: false, optionalExclusion: true, customOptionName: "Burble", addressResolver: _addressResolver, logger: _logger);
                }
            }

            #endregion Burbles Option

            #region Switchable maps (and ethanol) Option

            // Switchable maps (and ethanol)
            {
                if (options.TunerOverrideDst && carInfo.Stage == StageType.Custom)
                {
                    _logger.LogInformation("TunerOverrideDst uses code words from custom DST for switchable maps (and ethanol), app is not overriding values");
                }
                else
                {
                    _logger.LogInformation("applying code words to DST for switchable maps (and ethanol)...");
                    // Set Octane Switchable (and ethanol) Maps State
                    byte _octaneSwitchableMapsState;
                    byte _ethanolMapState;

                    if (carInfo.Stage == StageType.Stock)
                    {
                        _logger.LogInformation("Flashing stock, disabling switchable maps (and ethanol)");
                        _octaneSwitchableMapsState = 0x00;
                        _ethanolMapState = 0x00;
                    }
                    else
                    {
                        if (options.SwitchableMaps)
                        {
                            _logger.LogInformation("Switchable maps (and ethanol) are enabled");
                            // Set Octane Switchable (and ethanol) Maps State
                            _octaneSwitchableMapsState = 0x01;
                            _ethanolMapState = 0x01;
                            _logger.LogInformation("overwritting default state with value in custom options (sliders/toggles)");
                        }
                        else
                        {
                            _logger.LogInformation("Switchable maps (and ethanol) are NOT enabled");
                            // Set Octane Switchable (and ethanol) Maps State
                            _octaneSwitchableMapsState = 0x00;
                            _ethanolMapState = 0x00;
                            _logger.LogInformation("overwritting default state with value in custom options (sliders/toggles)");
                        }
                    }

                    var _customOptionName = "Switchable Map Codeword";

                    if (CustomCodeVersions.IsAtLeastCustomCodev6x(carInfo.TargetCustomCodeVersion))
                    {
                        _logger.LogInformation("applying at Custom Code v6.0+ uniform addresses...");
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_OctaneSwitch", new byte[] { _octaneSwitchableMapsState }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_Ethanol", new byte[] { _ethanolMapState }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                    }

                    // Fxx
                    else if (fileInfo.Pst.Id == 0x3076 && fileInfo.Pst.Major == 80 && fileInfo.Pst.Minor == 17 && fileInfo.Pst.Patch == 3)
                    {
                        _logger.LogInformation("applying for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", fileInfo.Pst.Id.ToString("X4"), fileInfo.Pst.Major, fileInfo.Pst.Minor, fileInfo.Pst.Patch);
                        data.ApplyValues(new object[] {
                            new object[] { 0x6F842Eu, 0x6F842Eu, new byte[] { _octaneSwitchableMapsState } },
                            new object[] { 0x6F842Fu, 0x6F842Fu, new byte[] { _ethanolMapState } },
                        });
                    }
                    else if (fileInfo.Pst.Id == 0x3076 && fileInfo.Pst.Major == 80 && fileInfo.Pst.Minor == 29 && fileInfo.Pst.Patch == 2)
                    {
                        _logger.LogInformation("applying for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", fileInfo.Pst.Id.ToString("X4"), fileInfo.Pst.Major, fileInfo.Pst.Minor, fileInfo.Pst.Patch);
                        data.ApplyValues(new object[] {
                            new object[] { 0x6F9AFEu, 0x6F9AFEu, new byte[] { _octaneSwitchableMapsState } },
                            new object[] { 0x6F9AFFu, 0x6F9AFFu, new byte[] { _ethanolMapState } },
                        });
                    }
                    else if (fileInfo.Pst.Id == 0x3076 && fileInfo.Pst.Major == 90 && fileInfo.Pst.Minor == 20 && fileInfo.Pst.Patch == 3)
                    {
                        _logger.LogInformation("applying for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", fileInfo.Pst.Id.ToString("X4"), fileInfo.Pst.Major, fileInfo.Pst.Minor, fileInfo.Pst.Patch);
                        data.ApplyValues(new object[] {
                            new object[] { 0x6FA67Eu, 0x6FA67Eu, new byte[] { _octaneSwitchableMapsState } },
                            new object[] { 0x6FA67Fu, 0x6FA67Fu, new byte[] { _ethanolMapState } },
                        });
                    }

                    // Gxx
                    //else if (info.Pst.Id == 0x3081 && info.Pst.Major == 80 && info.Pst.Minor == 21 && info.Pst.Patch == 000)
                    //{
                    //    _logger.LogInformation("applying for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", info.Pst.Id.ToString("X4"), info.Pst.Major, info.Pst.Minor, info.Pst.Patch);
                    //    data.ApplyValues(new object[] {
                    //        new object[] { 0x6FDF0Eu, 0x6FDF0Eu, new byte[] { _octaneSwitchableMapsState } },
                    //    });
                    //}
                    //else if (info.Pst.Id == 0x3081 && info.Pst.Major == 80 && info.Pst.Minor == 29 && info.Pst.Patch == 000)
                    //{
                    //    _logger.LogInformation("applying for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", info.Pst.Id.ToString("X4"), info.Pst.Major, info.Pst.Minor, info.Pst.Patch);
                    //    data.ApplyValues(new object[] {
                    //        new object[] { 0x6FF00Eu, 0x6FF00Eu, new byte[] { _octaneSwitchableMapsState } },
                    //    });
                    //}
                    //else if (info.Pst.Id == 0x3081 && info.Pst.Major == 90 && info.Pst.Minor == 10 && info.Pst.Patch == 000)
                    //{
                    //    _logger.LogInformation("applying for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", info.Pst.Id.ToString("X4"), info.Pst.Major, info.Pst.Minor, info.Pst.Patch);
                    //    data.ApplyValues(new object[] {
                    //        new object[] { 0x6FFB5Eu, 0x6FFB5Eu, new byte[] { _octaneSwitchableMapsState } },
                    //    });
                    //}
                    //else if (info.Pst.Id == 0x3081 && info.Pst.Major == 80 && info.Pst.Minor == 17 && info.Pst.Patch == 000)
                    //{
                    //    _logger.LogInformation("applying for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", info.Pst.Id.ToString("X4"), info.Pst.Major, info.Pst.Minor, info.Pst.Patch);
                    //    data.ApplyValues(new object[] {
                    //        new object[] { 0x6FD7D6u, 0x6FD7D6u, new byte[] { _octaneSwitchableMapsState } },
                    //    });
                    //}
                    else
                    {
                        _logger.LogInformation("not applying (swfl not matching). Current swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}.", fileInfo.Pst.Id.ToString("X4"), fileInfo.Pst.Major, fileInfo.Pst.Minor, fileInfo.Pst.Patch);
                    }
                }
            }

            #endregion Switchable maps (and ethanol) Option

            #region Antilag Option

            // Antilag
            {
                if (options.TunerOverrideDst && carInfo.Stage == StageType.Custom)
                {
                    _logger.LogInformation("TunerOverrideDst uses code words from custom DST for antilag, app is not overriding values");
                }
                else
                {
                    _logger.LogInformation("applying code words to DST for Antilag...");
                    var _customOptionName = "Antilag";

                    // Set Antilag State
                    byte _antilagState = 0x00;
                    if (carInfo.Stage == StageType.Stock)
                    {
                        _logger.LogInformation("Flashing stock, disabling Antilag");
                        _antilagState = 0x00;
                    }
                    else
                    {
                        _logger.LogInformation("overwritting default state with value in custom options (sliders/toggles)");
                        if (options.Antilag && options.Dcat)
                        {
                            _logger.LogInformation("Antilag is enabled");
                            // Set Antilag State
                            _antilagState = 0x01;
                        }
                        else
                        {
                            _logger.LogInformation("Antilag is NOT enabled");
                            // Set Antilag State
                            _antilagState = 0x00;
                        }
                    }

                    if (CustomCodeVersions.IsAtLeastCustomCodev6x(carInfo.TargetCustomCodeVersion))
                    {
                        _logger.LogInformation("applying at Custom Code v6.0+ uniform addresses...");
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_Antilag", new byte[] { _antilagState }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                        if (_antilagState == 0x01)
                        {
                            var antilagRpmSetpoint = (int)(options.AntilagRpmSetpoint * 100 * 2);
                            var _data___AntilagRpmSetpoint = new byte[] { (byte)((antilagRpmSetpoint) >> 8), (byte)(antilagRpmSetpoint) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_RpmSetpoint", _data___AntilagRpmSetpoint, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagRollingSpeedThreshold = (int)(options.AntilagRollingSpeedThreshold * 64);
                            var _data___AntilagRollingSpeedThreshold = new byte[] { (byte)((antilagRollingSpeedThreshold) >> 8), (byte)(antilagRollingSpeedThreshold) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_RollingSpeedThreshold", _data___AntilagRollingSpeedThreshold, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagRollRpmMin = (int)(options.AntilagRollRpmMin * 100 * 2);
                            var _data___AntilagRollRpmMin = new byte[] { (byte)((antilagRollRpmMin) >> 8), (byte)(antilagRollRpmMin) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_RollRpmMin", _data___AntilagRollRpmMin, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagRollRpmMax = (int)(options.AntilagRollRpmMax * 100 * 2);
                            var _data___AntilagRollRpmMax = new byte[] { (byte)((antilagRollRpmMax) >> 8), (byte)(antilagRollRpmMax) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_RollRpmMax", _data___AntilagRollRpmMax, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagTargetBoostRoll = (int)(options.AntilagTargetBoostRoll * 50 * 8);
                            var _data___AntilagTargetBoostRoll = new byte[] { (byte)((antilagTargetBoostRoll) >> 8), (byte)(antilagTargetBoostRoll) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_TargetBoostRoll", _data___AntilagTargetBoostRoll, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagTargetBoostStand = (int)(options.AntilagTargetBoostStand * 50 * 8);
                            var _data___AntilagTargetBoostStand = new byte[] { (byte)((antilagTargetBoostStand) >> 8), (byte)(antilagTargetBoostStand) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_TargetBoostStand", _data___AntilagTargetBoostStand, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagRpmDiffRoll = (int)(options.AntilagRpmDiffRoll * 2);
                            var _data___AntilagRpmDiffRoll = new byte[] { (byte)((antilagRpmDiffRoll) >> 8), (byte)(antilagRpmDiffRoll) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_RpmDelayUntilCutRoll", _data___AntilagRpmDiffRoll, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagRpmDiffStand = (int)(options.AntilagRpmDiffStand * 2);
                            var _data___AntilagRpmDiffStand = new byte[] { (byte)((antilagRpmDiffStand) >> 8), (byte)(antilagRpmDiffStand) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_RpmDelayUntilCutStand", _data___AntilagRpmDiffStand, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagOverBoostThreshold = (int)(options.AntilagOverBoostThreshold * 8);
                            var _data___AntilagOverBoostThreshold = new byte[] { (byte)((antilagOverBoostThreshold) >> 8), (byte)(antilagOverBoostThreshold) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_OverBoostThreshold", _data___AntilagOverBoostThreshold, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagPedal = (int)((options.AntilagPedal / 100.0) * 255);
                            var _data___AntilagPedal = new byte[] { (byte)antilagPedal };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_Pedal", _data___AntilagPedal, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagCoolantTempMin = (int)(options.AntilagCoolantTempMin * 100);
                            var _data___AntilagCoolantTempMin = new byte[] { (byte)((antilagCoolantTempMin) >> 8), (byte)(antilagCoolantTempMin) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_CoolantTempMin", _data___AntilagCoolantTempMin, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagCoolantTempMax = (int)(options.AntilagCoolantTempMax * 100);
                            var _data___AntilagCoolantTempMax = new byte[] { (byte)((antilagCoolantTempMax) >> 8), (byte)(antilagCoolantTempMax) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_CoolantTempMax", _data___AntilagCoolantTempMax, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagEgtTempMax = (int)(options.AntilagEgtTempMax * 50 * 10);
                            var _data___AntilagEgtTempMax = new byte[] { (byte)((antilagEgtTempMax) >> 8), (byte)(antilagEgtTempMax) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_EgtTempMax", _data___AntilagEgtTempMax, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagIatTempMax = (int)((options.AntilagIatTempMax + 48) / 0.75);
                            var _data___AntilagIatTempMax = new byte[] { (byte)antilagIatTempMax };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_IatTempMax", _data___AntilagIatTempMax, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            if (carInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
                            {
                                var nsberRpmLimitDownGradientByteArray = new byte[] { (byte)0x17, (byte)0x70 }; // 6000 rpm/10ms
                                var _data___Antilag_BMWtqe_gra_NMaxEngLimdDwn_T =
                                    nsberRpmLimitDownGradientByteArray
                                    .Concat(nsberRpmLimitDownGradientByteArray)
                                    .Concat(nsberRpmLimitDownGradientByteArray)
                                    .Concat(nsberRpmLimitDownGradientByteArray)
                                    .Concat(nsberRpmLimitDownGradientByteArray)
                                    .Concat(nsberRpmLimitDownGradientByteArray)
                                    .Concat(nsberRpmLimitDownGradientByteArray)
                                    .Concat(nsberRpmLimitDownGradientByteArray)
                                    .Concat(nsberRpmLimitDownGradientByteArray)
                                    .Concat(nsberRpmLimitDownGradientByteArray)
                                    .ToArray();
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_BMWtqe_gra_NMaxEngLimdDwn_T", _data___Antilag_BMWtqe_gra_NMaxEngLimdDwn_T, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            }

                            // _data___AntilagUseIgnitionCut values:
                            // 0x00 => fuel cut (bit of stuttering but safe)
                            // 0x01 => ignition cut (flame mode, very aggressive)
                            // 0x03 => factory rpm limiter (default, smoothest and safest)
                            var _data___AntilagUseIgnitionCut = new byte[] { (byte)0x03 };
                            if (options.AntilagUseIgnitionCut)
                            {
                                _data___AntilagUseIgnitionCut = new byte[] { (byte)0x01 };
                            }

                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_CutMethod", _data___AntilagUseIgnitionCut, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            if (CustomCodeVersions.IsAtLeastCustomCodev705(carInfo.TargetCustomCodeVersion))
                            {
                                var antilagTargetNumberOfCylindersToSuppress = (int)(options.AntilagTargetNumberOfCylindersToSuppress);
                                var _data___AntilagTargetNumberOfCylindersToSuppress = new byte[] { (byte)antilagTargetNumberOfCylindersToSuppress };
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_TargetNumberOfCylindersToSuppress", _data___AntilagTargetNumberOfCylindersToSuppress, valueBitSize: 8, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            }

                            var antilagActivationDelay = (int)(options.AntilagActivationDelay * 10);
                            var _data___AntilagActivationDelay = new byte[] { (byte)((antilagActivationDelay) >> 8), (byte)(antilagActivationDelay) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_ActivationDelay", _data___AntilagActivationDelay, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            var antilagMaxDuration = (int)(options.AntilagMaxDuration * 10);
                            var _data___AntilagMaxDuration = new byte[] { (byte)((antilagMaxDuration) >> 8), (byte)(antilagMaxDuration) };
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_MaxDuration", _data___AntilagMaxDuration, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_BMWtchctr_pwr_Pctl_M", await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_BMWtchctr_pwr_Pctl_M", "Antilag_BMWtchctr_pwr_Pctl_M", options.AntilagEngineSlot), valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_BMWtchctr_pwr_Pctl_Ax", await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_BMWtchctr_pwr_Pctl_Ax", "Antilag_BMWtchctr_pwr_Pctl_M_Xaxis_BMWtchsp_rat_p_CmprLim_uw", options.AntilagEngineSlot), valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_BMWtchctr_pwr_Pctl_Ay", await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_BMWtchctr_pwr_Pctl_Ay", "Antilag_BMWtchctr_pwr_Pctl_M_Yaxis_BMWtchsp_mf_CmprNorm_uw", options.AntilagEngineSlot), valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_BMWtchctr_p_DifCrtnPp_M", await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_BMWtchctr_p_DifCrtnPp_M", "Antilag_BMWtchctr_p_DifCrtnPp_M", options.AntilagEngineSlot), valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_BMWtchctr_p_DifCrtnPp_Ay", await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_BMWtchctr_p_DifCrtnPp_Ay", "Antilag_BMWtchctr_p_DifCrtnPp_M_Yaxis_BMWtchsp_mf_Ex_uw", options.AntilagEngineSlot), valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_BMWtchctr_p_DifCrtnPpDyn_M", await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_BMWtchctr_p_DifCrtnPpDyn_M", "Antilag_BMWtchctr_p_DifCrtnPpDyn_M", options.AntilagEngineSlot), valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_BMWtchctr_p_DifCrtnPpDyn_Ay", await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_BMWtchctr_p_DifCrtnPpDyn_Ay", "Antilag_BMWtchctr_p_DifCrtnPpDyn_M_Yaxis_BMWtchsp_mf_Ex_uw", options.AntilagEngineSlot), valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_BMWtchctr_pwr_Dp_M", await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_BMWtchctr_pwr_Dp_M", "Antilag_BMWtchctr_pwr_Dp_M", options.AntilagEngineSlot), valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            if (options.AntilagDevOptions && options.AntilagExpertOptions)
                            {
                                _logger.LogInformation("Antilag Dev mode enabled, setting flat maps");

                                var aSTC = options.AntilagFlatSafetyTorqueCap * 10;
                                var aSTC_H = (byte)(aSTC >> 8);
                                var aSTC_L = (byte)aSTC;
                                var _data___AntilagSafetyTorqueCap = new byte[36] { aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L,
                                    aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L, aSTC_H, aSTC_L};
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_SafetyTorqueCap", _data___AntilagSafetyTorqueCap, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                                var aFIS = (int)(Math.Round(options.AntilagFlatIgnStand, 1) * 10);
                                var aFIS_H = (byte)(aFIS >> 8);
                                var aFIS_L = (byte)aFIS;
                                var _data___AntilagFlatIgnStand = new byte[200] {
                                    aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L,
                                    aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L,
                                    aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L,
                                    aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L,
                                    aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L,
                                    aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L,
                                    aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L,
                                    aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L,
                                    aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L,
                                    aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L, aFIS_H, aFIS_L,
                                };
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_IgnStand", _data___AntilagFlatIgnStand, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                                var aFIR = (int)(Math.Round(options.AntilagFlatIgnRoll, 1) * 10);
                                var aFIR_H = (byte)(aFIR >> 8);
                                var aFIR_L = (byte)aFIR;
                                var _data___AntilagFlatIgnRoll = new byte[200] {
                                    aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L,
                                    aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L,
                                    aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L,
                                    aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L,
                                    aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L,
                                    aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L,
                                    aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L,
                                    aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L,
                                    aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L,
                                    aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L, aFIR_H, aFIR_L,
                                };
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_IgnRoll", _data___AntilagFlatIgnRoll, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                                var aFLS = (int)(Math.Round(options.AntilagFlatLambdaStand, 1) * 4096);
                                var aFLS_H = (byte)(aFLS >> 8);
                                var aFLS_L = (byte)aFLS;
                                var _data___AntilagFlatLambdaStand = new byte[200] {
                                    aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L,
                                    aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L,
                                    aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L,
                                    aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L,
                                    aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L,
                                    aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L,
                                    aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L,
                                    aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L,
                                    aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L,
                                    aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L, aFLS_H, aFLS_L,
                                };
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_LambdaStand", _data___AntilagFlatLambdaStand, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                                var aFLR = (int)(Math.Round(options.AntilagFlatLambdaRoll, 1) * 4096);
                                var aFLR_H = (byte)(aFLR >> 8);
                                var aFLR_L = (byte)aFLR;
                                var _data___AntilagFlatLambdaRoll = new byte[200] {
                                    aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L,
                                    aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L,
                                    aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L,
                                    aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L,
                                    aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L,
                                    aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L,
                                    aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L,
                                    aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L,
                                    aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L,
                                    aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L, aFLR_H, aFLR_L,
                                };
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_LambdaRoll", _data___AntilagFlatLambdaRoll, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            }
                            else
                            {
                                _logger.LogInformation("Antilag Dev mode disbled, setting curve maps");

                                #region AntilagCurveSafetyTorqueCap

                                _logger.LogInformation("Setting AntilagSafetyTorqueCap");
                                var antilagSafetyTorqueCap = GetCurveFromPercentageInterpolation_16bit(
                                    curvePercent0: await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_SafetyTorqueCap", "AntilagCurveSafetyTorqueCap"),
                                    curvePercent100: await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_SafetyTorqueCap", "AntilagCurveSafetyTorqueCap", 1),
                                    sliderInput: options.AntilagCurveSafetyTorqueCap,
                                    conversionForwardFactor: 0.1,
                                    conversionBackwardFactor: 10,
                                    decimalRounding: 1,
                                    numberOfRows: 1,
                                    bytesPerValue: 2);
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_SafetyTorqueCap", antilagSafetyTorqueCap, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                                #endregion AntilagCurveSafetyTorqueCap

                                var useMoreAggressiveIgnitionMaps = CustomCodeVersions.IsAtLeastCustomCodev705(carInfo.TargetCustomCodeVersion);

                                #region AntilagCurveIgnStand

                                _logger.LogInformation("Setting AntilagCurveIgnStand");

                                var antilagIgnStand_curvePercent0 = await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_IgnStand", "AntilagCurveIgn", useMoreAggressiveIgnitionMaps ? 2 : 0);
                                var antilagIgnStand_curvePercent100 = await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_IgnStand", "AntilagCurveIgn", useMoreAggressiveIgnitionMaps ? 3 : 1);

                                var antilagIgnStand = GetCurveFromPercentageInterpolation_16bit(
                                    curvePercent0: antilagIgnStand_curvePercent0,
                                    curvePercent100: antilagIgnStand_curvePercent100,
                                    sliderInput: options.AntilagCurveIgnStand,
                                    conversionForwardFactor: 0.1,
                                    conversionBackwardFactor: 10,
                                    decimalRounding: 1,
                                    numberOfRows: 10,
                                    bytesPerValue: 2);
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_IgnStand", antilagIgnStand, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                                #endregion AntilagCurveIgnStand

                                #region AntilagCurveIgnRoll

                                _logger.LogInformation("Setting AntilagCurveIgnRoll");

                                var antilagIgnRoll_curvePercent0 = await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_IgnRoll", "AntilagCurveIgn", useMoreAggressiveIgnitionMaps ? 2 : 0);
                                var antilagIgnRoll_curvePercent100 = await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_IgnRoll", "AntilagCurveIgn", useMoreAggressiveIgnitionMaps ? 3 : 1);

                                var antilagIgnRoll = GetCurveFromPercentageInterpolation_16bit(
                                    curvePercent0: antilagIgnRoll_curvePercent0,
                                    curvePercent100: antilagIgnRoll_curvePercent100,
                                    sliderInput: options.AntilagCurveIgnRoll,
                                    conversionForwardFactor: 0.1,
                                    conversionBackwardFactor: 10,
                                    decimalRounding: 1,
                                    numberOfRows: 10,
                                    bytesPerValue: 2);
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_IgnRoll", antilagIgnRoll, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                                #endregion AntilagCurveIgnRoll

                                #region AntilagCurveLambdaStand

                                _logger.LogInformation("Setting AntilagCurveLambdaStand");
                                var antilagLambdaStand = GetCurveFromPercentageInterpolation_16bit(
                                    curvePercent0: await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_LambdaStand", "AntilagCurveLambda"),
                                    curvePercent100: await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_LambdaStand", "AntilagCurveLambda", 1),
                                    sliderInput: options.AntilagCurveLambdaStand,
                                    conversionForwardFactor: 0.000244140625,
                                    conversionBackwardFactor: 4096,
                                    decimalRounding: 3,
                                    numberOfRows: 10,
                                    bytesPerValue: 2);
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_LambdaStand", antilagLambdaStand, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                                #endregion AntilagCurveLambdaStand

                                #region AntilagCurveLambdaRoll

                                _logger.LogInformation("Setting AntilagCurveLambdaRoll");
                                var antilagLambdaRoll = GetCurveFromPercentageInterpolation_16bit(
                                    curvePercent0: await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_LambdaRoll", "AntilagCurveLambda"),
                                    curvePercent100: await _valueResolver.GetAsync(_customOptionName, "CustomCode_Antilag_LambdaRoll", "AntilagCurveLambda", 1),
                                    sliderInput: options.AntilagCurveLambdaRoll,
                                    conversionForwardFactor: 0.000244140625,
                                    conversionBackwardFactor: 4096,
                                    decimalRounding: 3,
                                    numberOfRows: 10,
                                    bytesPerValue: 2);
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_Antilag_LambdaRoll", antilagLambdaRoll, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                                #endregion AntilagCurveLambdaRoll
                            }

                            var _antilagIgn_ActualBoostAxisRoll = await _valueResolver.GetAsync(_customOptionName, "CustomCode_AXY_Antilag_IgnRoll", "AntilagIgn_ActualBoostAxis");
                            var _antilagIgn_ActualBoostAxisRoll_UseAutoScale = true;
                            if (_antilagIgn_ActualBoostAxisRoll_UseAutoScale)
                            {
                                _antilagIgn_ActualBoostAxisRoll = ApplyAxisScaling_16bit_10row(
                                    originalAxis: await _valueResolver.GetAsync(_customOptionName, "CustomCode_AXY_Antilag_IgnRoll", "AntilagIgn_ActualBoostAxis"),
                                    conversionBackwardFactor: 8,
                                    decimalRounding: 0,
                                    targetValue: options.AntilagTargetBoostRoll * 50, // hPa
                                    hyst: 200); // hPa
                            }

                            var _antilagIgn_ActualBoostAxisStand = await _valueResolver.GetAsync(_customOptionName, "CustomCode_AXY_Antilag_IgnStand", "AntilagIgn_ActualBoostAxis");
                            var _antilagIgn_ActualBoostAxisStand_UseAutoScale = true;
                            if (_antilagIgn_ActualBoostAxisStand_UseAutoScale)
                            {
                                _antilagIgn_ActualBoostAxisStand = ApplyAxisScaling_16bit_10row(
                                    originalAxis: await _valueResolver.GetAsync(_customOptionName, "CustomCode_AXY_Antilag_IgnStand", "AntilagIgn_ActualBoostAxis"),
                                    conversionBackwardFactor: 8,
                                    decimalRounding: 0,
                                    targetValue: options.AntilagTargetBoostStand * 50, // hPa
                                    hyst: 200); // hPa
                            }

                            var _antilagLambda_EgtAxis = await _valueResolver.GetAsync(_customOptionName, "CustomCode_AXY_Antilag_LambdaRoll", "AntilagLambda_EgtAxis");
                            var _antilagLambda_EgtAxis_UseAutoScale = true;
                            if (_antilagLambda_EgtAxis_UseAutoScale)
                            {
                                _antilagLambda_EgtAxis = ApplyAxisScaling_16bit_10row(
                                    originalAxis: await _valueResolver.GetAsync(_customOptionName, "CustomCode_AXY_Antilag_LambdaRoll", "AntilagLambda_EgtAxis"),
                                    conversionBackwardFactor: 10,
                                    decimalRounding: 1,
                                    targetValue: options.AntilagEgtTempMax * 50, // 'C
                                    hyst: 200); // 'C
                            }

                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_AXX_Antilag_IgnStand", (byte[])await _valueResolver.GetAsync(_customOptionName, "CustomCode_AXX_Antilag_IgnStand", "AntilagIgn_EngineSpeedAxis"), valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_AXY_Antilag_IgnStand", _antilagIgn_ActualBoostAxisStand, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_AXX_Antilag_IgnRoll", (byte[])await _valueResolver.GetAsync(_customOptionName, "CustomCode_AXX_Antilag_IgnRoll", "AntilagIgn_EngineSpeedAxis"), valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_AXY_Antilag_IgnRoll", _antilagIgn_ActualBoostAxisRoll, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_AXX_Antilag_LambdaStand", (byte[])await _valueResolver.GetAsync(_customOptionName, "CustomCode_AXX_Antilag_LambdaStand", "AntilagLambda_EngineSpeedAxis"), valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_AXY_Antilag_LambdaStand", _antilagLambda_EgtAxis, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_AXX_Antilag_LambdaRoll", (byte[])await _valueResolver.GetAsync(_customOptionName, "CustomCode_AXX_Antilag_LambdaRoll", "AntilagLambda_EngineSpeedAxis"), valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_AXY_Antilag_LambdaRoll", _antilagLambda_EgtAxis, valueBitSize: 16, signedData: true, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                        }
                    }

                    // Fxx
                    else if (fileInfo.Pst.Id == 0x3076 && fileInfo.Pst.Major == 80 && fileInfo.Pst.Minor == 17 && fileInfo.Pst.Patch == 3)
                    {
                        _logger.LogInformation("applying for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", fileInfo.Pst.Id.ToString("X4"), fileInfo.Pst.Major, fileInfo.Pst.Minor, fileInfo.Pst.Patch);
                        data.ApplyValues(new object[] {
                            new object[] { 0x6FD160u, 0x6FD160u, new byte[] { _antilagState } },
                        });
                    }
                    else if (fileInfo.Pst.Id == 0x3076 && fileInfo.Pst.Major == 80 && fileInfo.Pst.Minor == 29 && fileInfo.Pst.Patch == 2)
                    {
                        _logger.LogInformation("applying for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", fileInfo.Pst.Id.ToString("X4"), fileInfo.Pst.Major, fileInfo.Pst.Minor, fileInfo.Pst.Patch);
                        data.ApplyValues(new object[] {
                            new object[] { 0x6FE830u, 0x6FE830u, new byte[] { _antilagState } },
                        });
                    }
                    else if (fileInfo.Pst.Id == 0x3076 && fileInfo.Pst.Major == 90 && fileInfo.Pst.Minor == 20 && fileInfo.Pst.Patch == 3)
                    {
                        _logger.LogInformation("applying for swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", fileInfo.Pst.Id.ToString("X4"), fileInfo.Pst.Major, fileInfo.Pst.Minor, fileInfo.Pst.Patch);
                        data.ApplyValues(new object[] {
                            new object[] { 0x6FF3B0u, 0x6FF3B0u, new byte[] { _antilagState } },
                        });
                    }
                    else
                    {
                        _logger.LogInformation("not applying (swfl not matching). Current swfl 0x{Pst.Id} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}.", fileInfo.Pst.Id.ToString("X4"), fileInfo.Pst.Major, fileInfo.Pst.Minor, fileInfo.Pst.Patch);
                    }
                }
            }

            #endregion Antilag Option

            #region TCU torque limiter option

            // TCU torque limiter
            {
                _logger.LogInformation("applying torque limit bypass (Com_Rx_LimTorqCrshGrbProte) to custom location in DST...");
                // which is assigned to Com_limTrqCrshGbxProte_msg, and later to mdkmaxgs_w_msg

                if (options.TcuTorqueLimiterPatchMethodIsDynamic)
                {
                    _logger.LogInformation("applying data for new TCU limiter patch which uses dynamic values (to handle clutch slipage)...");

                    var _tcuLimitedTorqueCodeWord = new byte[1];

                    if (options.TcuLimiterDynamic && CustomCodeVersions.IsAtLeastCustomCodev7x(carInfo.TargetCustomCodeVersion))
                    {
                        _logger.LogInformation("TcuLimiter removal is enabled for cc >= v7, enabling codeword...");
                        _tcuLimitedTorqueCodeWord = new byte[] { 0x01 };
                    }
                    else if (options.TcuLimiterDynamic)
                    {
                        _logger.LogInformation("TcuLimiter removal is enabled, enabling codeword...");
                        _tcuLimitedTorqueCodeWord = new byte[] { 0x03 };
                    }
                    else
                    {
                        _logger.LogInformation("TcuLimiter removal is NOT enabled, disabling codeword...");
                        _tcuLimitedTorqueCodeWord = new byte[] { 0x00 };
                    }

                    if (options.TunerOverrideDst && carInfo.Stage == StageType.Custom)
                    {
                        _logger.LogInformation("TunerOverrideDst uses code words and threshold values from custom DST for tcu torque bypass (dynamic), app is not overriding values");
                    }
                    else
                    {
                        var _customOptionName = "TCU Limiter bypass Codeword";

                        // PPC
                        if (carInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                        {
                            _logger.LogInformation("applying patch for PPC");
                            if (CustomCodeVersions.IsAtLeastCustomCodev7x(carInfo.TargetCustomCodeVersion))
                            {
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_TcuTorqueLimiter", _tcuLimitedTorqueCodeWord, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            }
                            else
                            {
                                data.ApplyValues(new object[] { new object[] { 0x77F7C2u, 0x77F7C2u, _tcuLimitedTorqueCodeWord } });
                            }
                        }
                        // Aurix
                        else if (carInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
                        {
                            _logger.LogInformation("applying patch for aurix");
                            if (CustomCodeVersions.IsAtLeastCustomCodev7x(carInfo.TargetCustomCodeVersion))
                            {
                                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_TcuTorqueLimiter", _tcuLimitedTorqueCodeWord, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                            }
                            else
                            {
                                data.ApplyValues(new object[] { new object[] { 0x7FF7D4u, 0x7FF7D4u, _tcuLimitedTorqueCodeWord.ChangeMapEndianness(8, false) } });
                            }
                        }
                        else
                        {
                            _logger.LogInformation("ECU not supported");
                        }
                    }
                }
                else
                {
                    _logger.LogInformation("applying data for OLD TCU LIMITER PATCH which overrides using a static value...");
                    // [250 Nm - 1000 Nm] add 1023.5 and multiply by 2, then read hex split as high & low bytes
                    var _tcuLimitedStaticTorque = new byte[2];

                    if (!carInfo.TargetCustomCodeVersion.Equals("v0.0")
                        && options.TcuLimiterStatic)
                    {
                        _logger.LogInformation("TcuLimiter removal is enabled");
                        // Calculate torque based on slider position...
                        _tcuLimitedStaticTorque[0] = (byte)((options.TcuLimiterStaticTorque * 2 + 2047) >> 8);
                        _tcuLimitedStaticTorque[1] = (byte)(options.TcuLimiterStaticTorque * 2 + 2047);
                    }
                    else
                    {
                        _logger.LogInformation("TcuLimiter removal is NOT enabled, remove limits (set to 1000Nm)");
                        _tcuLimitedStaticTorque[0] = 0x0F;
                        _tcuLimitedStaticTorque[1] = 0xCF;
                    }

                    if (carInfo.Stage == StageType.Stock)
                    {
                        _logger.LogInformation("Flashing stock, using OEM limits");
                        // set to 440 or 550...
                        _tcuLimitedStaticTorque[0] = (byte)((options.TcuLimiterTorqueStock * 2 + 2047) >> 8);
                        _tcuLimitedStaticTorque[1] = (byte)(options.TcuLimiterTorqueStock * 2 + 2047);
                    }

                    // PPC
                    if (carInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                    {
                        _logger.LogInformation("applying for PPC at 0x977F7D0");
                        data.ApplyValues(new object[] { new object[] { 0x77F7D0u, 0x77F7D0u, _tcuLimitedStaticTorque } });
                    }
                    // Aurix
                    else if (carInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
                    {
                        _logger.LogInformation("applying for aurix at 0x807FF7D0");
                        data.ApplyValues(new object[] { new object[] { 0x7FF7D0u, 0x7FF7D0u, _tcuLimitedStaticTorque.ChangeMapEndianness(16, false) } });
                    }
                    else
                    {
                        _logger.LogInformation("ECU not supported");
                    }
                }
            }

            #endregion TCU torque limiter option

            #region Idle RPM option

            // Idle RPM
            if (options.IdleRpm)
            {
                _logger.LogInformation("applying Idle RPM...");
                var _customOptionName = "Idle RPM";

                var idleRpmTargetBase = (short)options.IdleRpmTargetBase * 10; // 1 factor, int16, 8x8, Base idle rpm = 600 - 1500 rpm [10s of RPM]
                if (idleRpmTargetBase > 1500)
                {
                    idleRpmTargetBase = 1500;
                }
                else if (idleRpmTargetBase < 600)
                {
                    idleRpmTargetBase = 600;
                }

                _logger.LogInformation($"Idle base selected by user: [{idleRpmTargetBase} RPM]");

                var baseRpmMapList = new List<string> {
                    "KF_LLR_N_SOLL_GK_GD_RED",
                    "KF_LLR_N_SOLL_GK_GD_TT",
                    "KF_LLR_N_SOLL_OK_GD_HS",
                    "KF_LLR_N_SOLL_OK_GD_AT",
                    "KF_LLR_N_SOLL_GK_VVT_RED",
                    "KF_LLR_N_SOLL_GK_VVT_TT",
                    "KF_LLR_N_SOLL_OK_VVT_HS",
                    "KF_LLR_N_SOLL_OK_VVT_AT",
                };

                foreach (var baseRpmMap in baseRpmMapList)
                {
                    var _data___KF_LLR_N_SOLL_raw = new List<byte> { };
                    var mapSize_16x8 = 16 * 8;
                    var _address___KF_LLR_N_SOLL_xxx = await _addressResolver.TryResolveAsync(fileInfo.A2L, carInfo.TargetCustomCodeVersion, baseRpmMap);
                    if (!_address___KF_LLR_N_SOLL_xxx.HasValue)
                    {
                        _logger.LogInformation($"address for [{baseRpmMap}] not found, skipping...");
                        continue;
                    }

                    _logger.LogInformation($"Found [{baseRpmMap}] at [0x{_address___KF_LLR_N_SOLL_xxx.Value.ToString("X6")}], copying data...");

                    var sb = new StringBuilder("Table Values:\n");
                    var columns = 8;
                    for (var index = 0; index < mapSize_16x8; index += 2)
                    {
                        var highByte = data[_address___KF_LLR_N_SOLL_xxx.Value + index];
                        var lowByte = data[_address___KF_LLR_N_SOLL_xxx.Value + index + 1];
                        if (carInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
                        {
                            highByte = data[_address___KF_LLR_N_SOLL_xxx.Value + index + 1];
                            lowByte = data[_address___KF_LLR_N_SOLL_xxx.Value + index];
                        }

                        var physicalData = BitConverter.ToInt16(new byte[] { (byte)lowByte, (byte)highByte }, 0);
                        if (physicalData < idleRpmTargetBase || (physicalData < 800 && physicalData > idleRpmTargetBase))
                        {
                            highByte = (byte)(idleRpmTargetBase >> 8);
                            lowByte = (byte)idleRpmTargetBase;
                        }

                        _data___KF_LLR_N_SOLL_raw.Add(highByte);
                        _data___KF_LLR_N_SOLL_raw.Add(lowByte);

                        // for display:
                        physicalData = BitConverter.ToInt16(new byte[] { (byte)lowByte, (byte)highByte }, 0);
                        sb.Append($"{physicalData,10},");
                        var row = index / 2 + 1;
                        if (row >= columns && (row % columns == 0))
                        {
                            sb.Append("\n");
                        }
                    }

                    var _data___KF_LLR_N_SOLL_8x8 = _data___KF_LLR_N_SOLL_raw.ToArray();
                    if (_data___KF_LLR_N_SOLL_8x8.Length != mapSize_16x8)
                    {
                        _logger.LogInformation($"Unable to create map sized [{mapSize_16x8}] for [{baseRpmMap}]");
                        continue;
                    }

                    _logger.LogInformation("\n" + sb.ToString());

                    await data.ApplyMapDataAsync(fileInfo, carInfo, baseRpmMap, _data___KF_LLR_N_SOLL_8x8, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: true);
                }

                if (options.IdleRpm6mt) // 6MT Clutch settings
                {
                    _logger.LogInformation("applying Idle RPM clutch settings...");

                    var idleRpmTargetClutchDelay = (double)(options.IdleRpmTargetClutchDelay / 2.0); // 0.01 factor, uint16, Clutch-out rpm return delay = 0.5 - 15.0 s [0.5 of s]
                    if (idleRpmTargetClutchDelay > 15.0)
                    {
                        idleRpmTargetClutchDelay = 15.0;
                    }
                    else if (idleRpmTargetClutchDelay < 0.5)
                    {
                        idleRpmTargetClutchDelay = 0.5;
                    }

                    _logger.LogInformation($"Idle Clutch-out delay selected by user: [{idleRpmTargetClutchDelay} s]");

                    var _data___K_LLR_N_SOLL_KUP_TD = BitConverter.GetBytes((ushort)(idleRpmTargetClutchDelay * 100));
                    Array.Reverse(_data___K_LLR_N_SOLL_KUP_TD);

                    await data.ApplyMapDataAsync(fileInfo, carInfo, "K_LLR_N_SOLL_KUP_TD", _data___K_LLR_N_SOLL_KUP_TD, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: true);

                    var idleRpmTargetClutch = (short)options.IdleRpmTargetClutch * 10; // 1 factor, int16, 8x4, Clutch-in rpm = 600 - 1500 rpm [10s of RPM]
                    if (idleRpmTargetClutch > 1500)
                    {
                        idleRpmTargetClutch = 1500;
                    }
                    else if (idleRpmTargetClutch < 600)
                    {
                        idleRpmTargetClutch = 600;
                    }

                    _logger.LogInformation($"Idle Clutch-in selected by user: [{idleRpmTargetClutch} RPM]");

                    var _data___KF_LLR_N_SOLL_KUP_List = new List<byte> { };
                    var mapSize_16x4 = 16 * 4;
                    for (var index = 0; index < mapSize_16x4; index += 2)
                    {
                        _data___KF_LLR_N_SOLL_KUP_List.Add((byte)(idleRpmTargetClutch >> 8));
                        _data___KF_LLR_N_SOLL_KUP_List.Add((byte)idleRpmTargetClutch);
                    }

                    var _data___KF_LLR_N_SOLL_KUP = _data___KF_LLR_N_SOLL_KUP_List.ToArray();
                    if (_data___KF_LLR_N_SOLL_KUP.Length == mapSize_16x4)
                    {
                        await data.ApplyMapDataAsync(fileInfo, carInfo, "KF_LLR_N_SOLL_KUP", _data___KF_LLR_N_SOLL_KUP, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: true);
                    }
                    else
                    {
                        _logger.LogInformation($"Unable to create map sized [{mapSize_16x4}] for KF_LLR_N_SOLL_KUP");
                    }

                    if (options.IdleRpmTargetClutchFast)
                    {
                        _logger.LogInformation("applying fast idle RPM clutch change...");

                        var codewordMapList = new List<string> {
                            "CW_LLR_N_SOLL_RAMP_NEW",
                            "CW_LLR_N_SOLL_RAMP_FAST_UP",
                            "CW_LLR_N_SOLL_RAMP_FAST_DOWN",
                        };

                        foreach (var codewordMap in codewordMapList)
                        {
                            var _address___CW_LLR_N_SOLL_RAMP_xxx = await _addressResolver.TryResolveAsync(fileInfo.A2L, carInfo.TargetCustomCodeVersion, codewordMap);
                            if (!_address___CW_LLR_N_SOLL_RAMP_xxx.HasValue)
                            {
                                _logger.LogInformation($"address for [{codewordMap}] not found, skipping...");
                                continue;
                            }

                            _logger.LogInformation($"Found [{codewordMap}] at [0x{_address___CW_LLR_N_SOLL_RAMP_xxx.Value.ToString("X6")}], copying data...");

                            var _data___CW_LLR_N_SOLL_RAMP_xxx = new byte[4];
                            Array.Copy(data, _address___CW_LLR_N_SOLL_RAMP_xxx.Value, _data___CW_LLR_N_SOLL_RAMP_xxx, 0, 4);
                            if (carInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                            {
                                Array.Reverse(_data___CW_LLR_N_SOLL_RAMP_xxx);
                            }

                            var value = BitConverter.ToUInt32(_data___CW_LLR_N_SOLL_RAMP_xxx);
                            _logger.LogInformation($"Original codeword value for [{codewordMap}] is [{value}].");

                            var clutchBitValue = 1 << 21;
                            var newValue = (uint)(value + clutchBitValue);
                            _data___CW_LLR_N_SOLL_RAMP_xxx = BitConverter.GetBytes(newValue);
                            Array.Reverse(_data___CW_LLR_N_SOLL_RAMP_xxx);
                            _logger.LogInformation($"New codeword value for [{codewordMap}] is [{newValue}].");

                            await data.ApplyMapDataAsync(fileInfo, carInfo, codewordMap, _data___CW_LLR_N_SOLL_RAMP_xxx, valueBitSize: 32, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: true);
                        }
                    }
                    else
                    {
                        _logger.LogInformation("Idle RPM clutch fast change settings are not enabled");
                    }
                }
                else
                {
                    _logger.LogInformation("Idle RPM clutch settings are not enabled");
                }
            }
            else
            {
                _logger.LogInformation("Idle RPM is not enabled");
            }

            #endregion Idle RPM option

            #region Flex fuel option

            // Flex fuel
            if (options.EthanolSupport
                && CustomCodeVersions.IsAtLeastCustomCodev71(carInfo.TargetCustomCodeVersion))
            {
                _logger.LogInformation("applying Flex fuel...");
                var _customOptionName = "Flex fuel";

                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_FlexFuel", new byte[] { 0x01 }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                if (options.FlexFuelBlending)
                {
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_FlexFuelBlending", new byte[] { 0x01 }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);
                }

                var _data___sensorInstalled = options.FlexFuelSensorInstalled ? new byte[] { 0x01 } : new byte[] { 0x00 };
                _logger.LogInformation($"sensorInstalled selected by user: [{options.FlexFuelSensorInstalled}]");
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_SensorEthanol", _data___sensorInstalled, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                var delayToShowEthanolContentOnDash = options.FlexFuelDelayToShowOnDash / 10.0;
                if (delayToShowEthanolContentOnDash > 5.0)
                {
                    delayToShowEthanolContentOnDash = 5.0;
                }
                else if (delayToShowEthanolContentOnDash < 0.1)
                {
                    delayToShowEthanolContentOnDash = 0.1;
                }

                _logger.LogInformation($"DelayToShowEthanolContentOnDash selected by user: [{delayToShowEthanolContentOnDash} s]");
                var delayToShowEthanolContentOnDashConvertedForEcu = (UInt16)(delayToShowEthanolContentOnDash * 100.0);
                var _data___DelayToShowEthanolContentOnDash = BitConverter.GetBytes(delayToShowEthanolContentOnDashConvertedForEcu);
                Array.Reverse(_data___DelayToShowEthanolContentOnDash);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_FlexFuelEthanolDisplayActivationDelay", _data___DelayToShowEthanolContentOnDash, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                if (CustomCodeVersions.IsAtLeastCustomCodev73(carInfo.TargetCustomCodeVersion))
                {
                    var flexFuelEthanolContentSlot0 = 0x0000u;
                    var flexFuelEthanolContentSlot1 = 0x0000u;
                    var flexFuelEthanolContentSlot2 = 0x0000u;
                    var flexFuelEthanolContentSlot3 = 0x0000u;
                    var flexFuelEthanolContentSlot4 = 0x0000u;

                    if (options.FlexFuelEthanolContent
                        && !options.FlexFuelSensorInstalled)
                    {
                        flexFuelEthanolContentSlot0 = (uint)options.FlexFuelEthanolContentSlot0;
                        flexFuelEthanolContentSlot1 = (uint)options.FlexFuelEthanolContentSlot1;
                        flexFuelEthanolContentSlot2 = (uint)options.FlexFuelEthanolContentSlot2;
                        flexFuelEthanolContentSlot3 = (uint)options.FlexFuelEthanolContentSlot3;
                        flexFuelEthanolContentSlot4 = (uint)options.FlexFuelEthanolContentSlot4;
                    }

                    if (flexFuelEthanolContentSlot0 > 100)
                    {
                        flexFuelEthanolContentSlot0 = 100;
                    }
                    else if (flexFuelEthanolContentSlot0 < 0)
                    {
                        flexFuelEthanolContentSlot0 = 0;
                    }

                    _logger.LogInformation($"flexFuelEthanolContentSlot0 selected by user: [{flexFuelEthanolContentSlot0} %]");
                    var flexFuelEthanolContentSlot0ConvertedForEcu = (UInt16)(flexFuelEthanolContentSlot0 * 100.0);
                    var _data___FlexFuelEthanolContentSlot0 = BitConverter.GetBytes(flexFuelEthanolContentSlot0ConvertedForEcu);
                    Array.Reverse(_data___FlexFuelEthanolContentSlot0);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_FlexFuelEthanolValue_FF1_Slot_0", _data___FlexFuelEthanolContentSlot0, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                    if (flexFuelEthanolContentSlot1 > 100)
                    {
                        flexFuelEthanolContentSlot1 = 100;
                    }
                    else if (flexFuelEthanolContentSlot1 < 0)
                    {
                        flexFuelEthanolContentSlot1 = 0;
                    }

                    _logger.LogInformation($"flexFuelEthanolContentSlot1 selected by user: [{flexFuelEthanolContentSlot1} %]");
                    var flexFuelEthanolContentSlot1ConvertedForEcu = (UInt16)(flexFuelEthanolContentSlot1 * 100.0);
                    var _data___FlexFuelEthanolContentSlot1 = BitConverter.GetBytes(flexFuelEthanolContentSlot1ConvertedForEcu);
                    Array.Reverse(_data___FlexFuelEthanolContentSlot1);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_FlexFuelEthanolValue_FF1_Slot_1", _data___FlexFuelEthanolContentSlot1, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                    if (flexFuelEthanolContentSlot2 > 100)
                    {
                        flexFuelEthanolContentSlot2 = 100;
                    }
                    else if (flexFuelEthanolContentSlot2 < 0)
                    {
                        flexFuelEthanolContentSlot2 = 0;
                    }

                    _logger.LogInformation($"flexFuelEthanolContentSlot2 selected by user: [{flexFuelEthanolContentSlot2} %]");
                    var flexFuelEthanolContentSlot2ConvertedForEcu = (UInt16)(flexFuelEthanolContentSlot2 * 100.0);
                    var _data___FlexFuelEthanolContentSlot2 = BitConverter.GetBytes(flexFuelEthanolContentSlot2ConvertedForEcu);
                    Array.Reverse(_data___FlexFuelEthanolContentSlot2);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_FlexFuelEthanolValue_FF1_Slot_2", _data___FlexFuelEthanolContentSlot2, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                    if (flexFuelEthanolContentSlot3 > 100)
                    {
                        flexFuelEthanolContentSlot3 = 100;
                    }
                    else if (flexFuelEthanolContentSlot3 < 0)
                    {
                        flexFuelEthanolContentSlot3 = 0;
                    }

                    _logger.LogInformation($"flexFuelEthanolContentSlot3 selected by user: [{flexFuelEthanolContentSlot3} %]");
                    var flexFuelEthanolContentSlot3ConvertedForEcu = (UInt16)(flexFuelEthanolContentSlot3 * 100.0);
                    var _data___FlexFuelEthanolContentSlot3 = BitConverter.GetBytes(flexFuelEthanolContentSlot3ConvertedForEcu);
                    Array.Reverse(_data___FlexFuelEthanolContentSlot3);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_FlexFuelEthanolValue_FF1_Slot_3", _data___FlexFuelEthanolContentSlot3, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                    if (flexFuelEthanolContentSlot4 > 100)
                    {
                        flexFuelEthanolContentSlot4 = 100;
                    }
                    else if (flexFuelEthanolContentSlot4 < 0)
                    {
                        flexFuelEthanolContentSlot4 = 0;
                    }

                    _logger.LogInformation($"flexFuelEthanolContentSlot0 selected by user: [{flexFuelEthanolContentSlot4} %]");
                    var flexFuelEthanolContentSlot4ConvertedForEcu = (UInt16)(flexFuelEthanolContentSlot4 * 100.0);
                    var _data___FlexFuelEthanolContentSlot4 = BitConverter.GetBytes(flexFuelEthanolContentSlot4ConvertedForEcu);
                    Array.Reverse(_data___FlexFuelEthanolContentSlot4);
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_FlexFuelEthanolValue_FF1_Slot_4", _data___FlexFuelEthanolContentSlot4, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);
                }
            }
            else
            {
                _logger.LogInformation("Flex fuel is not enabled or didn't meet min CC version");
            }

            #endregion Flex fuel option

            #region Custom Code Menu option

            // Custom Code Menu
            if (options.CustomCodeMenu
                && CustomCodeVersions.IsAtLeastCustomCodev71(carInfo.TargetCustomCodeVersion))
            {
                _logger.LogInformation("applying Custom Code Menu...");
                var _customOptionName = "Custom Code Menu";

                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_CCMenu", new byte[] { 0x01 }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                var customCodeMenuDelayToEnter = options.CustomCodeMenuDelayToEnter / 10.0;
                if (customCodeMenuDelayToEnter > 5.0)
                {
                    customCodeMenuDelayToEnter = 5.0;
                }
                else if (customCodeMenuDelayToEnter < 0.1)
                {
                    customCodeMenuDelayToEnter = 0.1;
                }

                _logger.LogInformation($"CustomCodeMenuDelayToEnter selected by user: [{customCodeMenuDelayToEnter} s]");
                var customCodeMenuDelayToEnterConvertedForEcu = (UInt16)(customCodeMenuDelayToEnter * 100.0);
                var _data___CustomCodeMenuDelayToEnter = BitConverter.GetBytes(customCodeMenuDelayToEnterConvertedForEcu);
                Array.Reverse(_data___CustomCodeMenuDelayToEnter);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CcmEnterMenuDelay", _data___CustomCodeMenuDelayToEnter, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                var customCodeMenuTimeout = options.CustomCodeMenuTimeout / 1.0;
                if (customCodeMenuTimeout > 60.0)
                {
                    customCodeMenuTimeout = 60.0;
                }
                else if (customCodeMenuTimeout < 1.0)
                {
                    customCodeMenuTimeout = 1.0;
                }

                _logger.LogInformation($"CustomCodeMenuTimeout selected by user: [{customCodeMenuTimeout} s]");
                var customCodeMenuTimeoutConvertedForEcu = (UInt16)(customCodeMenuTimeout * 100.0);
                var _data___CustomCodeMenuTimeout = BitConverter.GetBytes(customCodeMenuTimeoutConvertedForEcu);
                Array.Reverse(_data___CustomCodeMenuTimeout);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CcmTimeout", _data___CustomCodeMenuTimeout, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                var assumeLowBdcSoftware = carInfo.HasLowBdcSoftware || options.CustomCodeMenuForceAllowAllButtonConfigurations;

                var enableRockerCombo = options.UseRockerCombo && assumeLowBdcSoftware ? (byte)0x01 : (byte)0x00; // disabled by default (to prevent bugs with RockerCombo config)
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_CruiseControlConfig", new byte[] { enableRockerCombo }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                var _data___UseAllCruiseControlCanBusData = assumeLowBdcSoftware ? (byte)0x01 : (byte)0x00;
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_UseAllCruiseControlCanBusData", new byte[] { _data___UseAllCruiseControlCanBusData }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                var uniRockerEnterDetectionDelay = options.UniRockerEnterDetectionDelay / 20.0;
                if (uniRockerEnterDetectionDelay > 5.0)
                {
                    uniRockerEnterDetectionDelay = 5.0;
                }
                else if (uniRockerEnterDetectionDelay < 0.1)
                {
                    uniRockerEnterDetectionDelay = 0.1;
                }

                _logger.LogInformation($"UniRockerEnterDetectionDelay selected by user: [{uniRockerEnterDetectionDelay} s]");
                var uniRockerEnterDetectionDelayConvertedForEcu = (UInt16)(uniRockerEnterDetectionDelay * 100.0);
                var _data___UniRockerEnterDetectionDelay = BitConverter.GetBytes(customCodeMenuDelayToEnterConvertedForEcu);
                Array.Reverse(_data___UniRockerEnterDetectionDelay);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_UniRockerEnterDetectionDelay", _data___UniRockerEnterDetectionDelay, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);
            }
            else
            {
                _logger.LogInformation("Custom Code Menu is not enabled or didn't meet min CC version");
            }

            #endregion Custom Code Menu option

            #region Radiator Flap On-TheFly option

            // Radiator Flap On-TheFly
            if (options.RadiatorFlapOnTheFly
                && options.CustomCodeMenu
                && CustomCodeVersions.IsAtLeastCustomCodev71(carInfo.TargetCustomCodeVersion)
                && carInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
            {
                _logger.LogInformation("applying Radiator Flap On-TheFly...");
                var _customOptionName = "Radiator Flap On-TheFly";
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_RadiatorFlap", new byte[] { 0x01 }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);
            }
            else
            {
                _logger.LogInformation("Radiator Flap On-TheFly or Custom Code Menu is not enabled or didn't meet min CC version");
            }

            #endregion Radiator Flap On-TheFly option

            #region Exhaust Flap On-TheFly option

            // Exhaust Flap On-TheFly
            if (options.ExhaustFlapOnTheFly
                && options.CustomCodeMenu
                && CustomCodeVersions.IsAtLeastCustomCodev71(carInfo.TargetCustomCodeVersion))
            {
                _logger.LogInformation("applying Exhaust Flap On-TheFly...");
                var _customOptionName = "Exhaust Flap On-TheFly";

                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_ExhaustFlap", new byte[] { 0x01 }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                var exhaustFlapOnTheFlyMaxLoad = (options.ExhaustFlapOnTheFlyMaxLoad * 5.0) + 50.0;
                if (exhaustFlapOnTheFlyMaxLoad > 200.0)
                {
                    exhaustFlapOnTheFlyMaxLoad = 200.0;
                }
                else if (exhaustFlapOnTheFlyMaxLoad < 50.0)
                {
                    exhaustFlapOnTheFlyMaxLoad = 50.0;
                }

                _logger.LogInformation($"ExhaustFlapOnTheFlyMaxLoad selected by user: [{exhaustFlapOnTheFlyMaxLoad} %]");
                var exhaustFlapOnTheFlyMaxLoadConvertedForEcu = (UInt16)(exhaustFlapOnTheFlyMaxLoad * 100.0);
                var _data___ExhaustFlapOnTheFlyMaxLoad = BitConverter.GetBytes(exhaustFlapOnTheFlyMaxLoadConvertedForEcu);
                Array.Reverse(_data___ExhaustFlapOnTheFlyMaxLoad);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_ExhaustFlapClosedMaxLoad", _data___ExhaustFlapOnTheFlyMaxLoad, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);
            }
            else
            {
                _logger.LogInformation("Exhaust Flap On-TheFly or Custom Code Menu is not enabled or didn't meet min CC version");
            }

            #endregion Exhaust Flap On-TheFly option

            #region Valet Mode option

            // Valet Mode
            if (options.ValetMode
                && options.CustomCodeMenu
                && CustomCodeVersions.IsAtLeastCustomCodev71(carInfo.TargetCustomCodeVersion))
            {
                _logger.LogInformation("applying Valet Mode...");
                var _customOptionName = "Valet Mode";

                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_Valet", new byte[] { 0x01 }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                var valetMaxPedal = options.ValetMaxPedal * 5.0;
                if (valetMaxPedal > 100.0)
                {
                    valetMaxPedal = 100.0;
                }
                else if (valetMaxPedal < 0.0)
                {
                    valetMaxPedal = 0.0;
                }

                _logger.LogInformation($"ValetMaxPedal selected by user: [{valetMaxPedal} %]");
                var valetMaxPedalConvertedForEcu = (int)((valetMaxPedal / 100.0) * 255);
                var _data___ValetMaxPedal = new byte[] { (byte)valetMaxPedalConvertedForEcu };
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_ValetMaxPedal", _data___ValetMaxPedal, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                var valetMaxEngineSpeed = (options.ValetMaxEngineSpeed * 100.0) + 1000.0;
                if (valetMaxEngineSpeed > 7000.0)
                {
                    valetMaxEngineSpeed = 7000.0;
                }
                else if (valetMaxEngineSpeed < 1000.0)
                {
                    valetMaxEngineSpeed = 1000.0;
                }

                _logger.LogInformation($"ValetMaxEngineSpeed selected by user: [{valetMaxEngineSpeed} RPM]");
                var valetMaxEngineSpeedConvertedForEcu = (Int16)(valetMaxEngineSpeed * 4.0);
                var _data___ValetMaxEngineSpeed = BitConverter.GetBytes(valetMaxEngineSpeedConvertedForEcu);
                Array.Reverse(_data___ValetMaxEngineSpeed);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_ValetMaxRpm", _data___ValetMaxEngineSpeed, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                var valetMaxVehicleSpeed = (options.ValetMaxVehicleSpeed * 5.0) + 5.0;
                if (valetMaxVehicleSpeed > 150.0)
                {
                    valetMaxVehicleSpeed = 150.0;
                }
                else if (valetMaxVehicleSpeed < 5.0)
                {
                    valetMaxVehicleSpeed = 5.0;
                }

                _logger.LogInformation($"ValetMaxVehicleSpeed selected by user: [{valetMaxVehicleSpeed} km/h]");
                var valetMaxVehicleSpeedConvertedForEcu = (UInt16)(valetMaxVehicleSpeed * 64.0);
                var _data___ValetMaxVehicleSpeed = BitConverter.GetBytes(valetMaxVehicleSpeedConvertedForEcu);
                Array.Reverse(_data___ValetMaxVehicleSpeed);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_ValetMaxSpeed", _data___ValetMaxVehicleSpeed, valueBitSize: 16, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                var _address___TorqueMap = await _addressResolver.TryResolveAsync(fileInfo.A2L, carInfo.TargetCustomCodeVersion, "TorqueMap");
                if (!_address___TorqueMap.HasValue)
                {
                    throw new Exception($"Failed to find Torque Map address");
                }

                _logger.LogInformation($"Found TorqueMap at [0x{_address___TorqueMap.Value.ToString("X6")}], copying data...");

                var numberOfBytesInTorqueMap = 36;
                var _data___TorqueMapSlot0 = new byte[numberOfBytesInTorqueMap];
                Array.Copy(data, _address___TorqueMap.Value, _data___TorqueMapSlot0, 0, numberOfBytesInTorqueMap);
                if (carInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
                {
                    _data___TorqueMapSlot0 = _data___TorqueMapSlot0.ChangeMapEndianness(16, true);
                }

                _logger.LogInformation($"ValetRegularTorqueLevel selected by user: [{options.ValetRegularTorqueLevel * 5} %]");
                var _data___ValetRegularTorqueLevel = GetCurveFromPercentageInterpolation_16bit(
                    curvePercent0: await _valueResolver.GetAsync(_customOptionName, "CustomCode_ValetTorqueMap_PF_Slot_1", "ValetTorqueMap_PF_Slot_1"),
                    curvePercent100: _data___TorqueMapSlot0,
                    sliderInput: options.ValetRegularTorqueLevel * 5,
                    conversionForwardFactor: 0.1,
                    conversionBackwardFactor: 10,
                    decimalRounding: 1,
                    numberOfRows: 1,
                    bytesPerValue: 2);

                _logger.LogInformation($"ValetRegularTorqueLevel selected by user: [{options.ValetRestrictiveTorqueLevel * 5} %]");
                var _data___ValetRestrictiveTorqueLevel = GetCurveFromPercentageInterpolation_16bit(
                    curvePercent0: await _valueResolver.GetAsync(_customOptionName, "CustomCode_ValetTorqueMap_PF_Slot_2", "ValetTorqueMap_PF_Slot_2"),
                    curvePercent100: _data___TorqueMapSlot0,
                    sliderInput: options.ValetRestrictiveTorqueLevel * 5,
                    conversionForwardFactor: 0.1,
                    conversionBackwardFactor: 10,
                    decimalRounding: 1,
                    numberOfRows: 1,
                    bytesPerValue: 2);

                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_ValetTorqueMap_PF_Slot_1", _data___ValetRegularTorqueLevel, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_ValetTorqueMap_PF_Slot_2", _data___ValetRestrictiveTorqueLevel, valueBitSize: 16, signedData: true, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);
            }
            else
            {
                _logger.LogInformation("Valet Mode or Custom Code Menu is not enabled or didn't meet min CC version");
            }

            #endregion Valet Mode option

            #region Custom Code MGF limp mode option

            // Custom Code MGF limp mode enabled installed
            if (CustomCodeVersions.IsAtLeastCustomCodev721(carInfo.TargetCustomCodeVersion))
            {
                _logger.LogInformation("applying Custom Code MGF limp mode settings...");
                var _customOptionName = "Custom Code Limp Mode System Setting";
                var ___data = options.MgfLimpModeProtectionEnabled ? new byte[] { 0x01 } : new byte[] { 0x00 };

                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_MgfLimpModeProtectionEnabled", new byte[] { 0x01 }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);
            }
            else
            {
                _logger.LogInformation("Custom Code MGF limp mode settings didn't meet min CC version");
            }

            #endregion Custom Code MGF limp mode option

            #region Low pressure fuel pressure sensor option

            // Low pressure fuel pressure sensor installed
            if (CustomCodeVersions.IsAtLeastCustomCodev721(carInfo.TargetCustomCodeVersion))
            {
                _logger.LogInformation("applying Low Pressure Fuel Pressure Sensor settings...");
                var _customOptionName = "Low Pressure Fuel Pressure Sensor";
                var ___data = options.LowPressureFuelPressureSensorInstalled ? new byte[] { 0x01 } : new byte[] { 0x00 };

                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_LowPressureFuelPressureSensorInstalled", new byte[] { 0x01 }, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);
            }
            else
            {
                _logger.LogInformation("Low Pressure Fuel Pressure Sensor settings didn't meet min CC version");
            }

            #endregion Low pressure fuel pressure sensor option

            #region Motiv reflex option

            // Motiv Re|Flex installed
            if (CustomCodeVersions.IsAtLeastCustomCodev721(carInfo.TargetCustomCodeVersion))
            {
                _logger.LogInformation("applying Motiv Reflex codeword...");
                var _customOptionName = "Motiv Re|Flex";

                var _data___CustomCode_CW_MotivReflexInstalled = options.MotivReflexInstalledCustomizable ? new byte[] { 0x01 } : new byte[] { 0x00 };
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CW_MotivReflexInstalled", _data___CustomCode_CW_MotivReflexInstalled, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);

                if (CustomCodeVersions.IsAtLeastCustomCodev9999(carInfo.TargetCustomCodeVersion))
                {
                    var _data___CustomCode_MotivReflexDeepIntegration = options.MotivReflexDeepIntegration ? new byte[] { 0x01 } : new byte[] { 0x00 };
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_MotivReflexDeepIntegration", _data___CustomCode_CW_MotivReflexInstalled, valueBitSize: 8, signedData: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger, optionalExclusion: false);
                }
            }
            else
            {
                _logger.LogInformation("Motiv Reflex settings didn't meet min CC version");
            }

            #endregion Motiv reflex option

            #region MGF CAN bus option

            if (CustomCodeVersions.IsAtLeastCustomCodev721(carInfo.TargetCustomCodeVersion))
            {
                _logger.LogInformation("applying MGF CAN bus IDs...");
                var _customOptionName = "MGF CAN bus IDs";

                // Cruise Control CAN bus ID
                var cruiseControlCanBusId = CanBusIds.GetCanBusIdFromString(CanBusIds.Id_0x0189);
                _logger.LogInformation($"applying Cruise Control CAN bus ID: [0x{cruiseControlCanBusId:X04}]");
                var _data___cruiseControlCanBusId = new byte[] { (byte)((cruiseControlCanBusId) >> 8), (byte)(cruiseControlCanBusId) };
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CustomCanBusCruiseControlIdMap", _data___cruiseControlCanBusId, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                // Turn signal CAN bus ID
                var turnSignalCanBusId = CanBusIds.GetCanBusIdFromString(CanBusIds.Id_0x01EE);
                _logger.LogInformation($"applying Turn Signal CAN bus ID: [0x{turnSignalCanBusId:X04}]");
                var _data___turnSignalCanBusId = new byte[] { (byte)((turnSignalCanBusId) >> 8), (byte)(turnSignalCanBusId) };
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CustomCanBusTurnSignalIdMap", _data___turnSignalCanBusId, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                // ECA CAN bus ID
                var ecaCanBusId = CanBusIds.GetCanBusIdFromString(options.FlexFuelSensorCanBusId);
                _logger.LogInformation($"applying ECA CAN bus ID: [0x{ecaCanBusId:X04}] for user selection [{options.FlexFuelSensorModel}]");
                var _data___ecaCanBusId = new byte[] { (byte)((ecaCanBusId) >> 8), (byte)(ecaCanBusId) };
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CustomCanBusEcaIdMap", _data___ecaCanBusId, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                // Motiv Reflex CAN bus ID
                var motivCanBusIdOutput = CanBusIds.GetCanBusIdFromString(options.MotivReflexCanBusIdOutput);
                _logger.LogInformation($"applying Motiv Reflex CAN bus ID (output): [{options.MotivReflexCanBusIdOutput}] -> [0x{motivCanBusIdOutput:X04}]");
                var _data___motivCanBusIdOutput = new byte[] { (byte)((motivCanBusIdOutput) >> 8), (byte)(motivCanBusIdOutput) };
                await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CustomCanBusMotivReflexOutputIdMap", _data___motivCanBusIdOutput, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);

                if (CustomCodeVersions.IsAtLeastCustomCodev9999(carInfo.TargetCustomCodeVersion))
                {
                    var motivCanBusIdInput = CanBusIds.GetCanBusIdFromString(options.MotivReflexCanBusIdInput);
                    _logger.LogInformation($"applying Motiv Reflex CAN bus ID (input): [{options.MotivReflexCanBusIdInput}] -> [0x{motivCanBusIdInput:X04}]");
                    var _data___motivCanBusIdInput = new byte[] { (byte)((motivCanBusIdInput) >> 8), (byte)(motivCanBusIdInput) };
                    await data.ApplyMapDataAsync(fileInfo, carInfo, "CustomCode_CustomCanBusMotivReflexInputIdMap", _data___motivCanBusIdInput, valueBitSize: 16, signedData: false, optionalExclusion: false, customOptionName: _customOptionName, addressResolver: _addressResolver, logger: _logger);
                }
            }
            else
            {
                _logger.LogInformation("MGF CAN bus settings didn't meet min CC version");
            }

            #endregion MGF CAN bus option

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "CustomCodeOptionsApplier error");
            throw;
        }
    }
}
