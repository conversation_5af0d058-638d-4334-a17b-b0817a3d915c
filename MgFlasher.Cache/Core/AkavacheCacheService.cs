﻿using System.Collections.Generic;
using System.Linq;
using System.Reactive.Linq;
using System.Threading.Tasks;
using Akavache;

namespace MgFlasher.Cache.Core;

public class AkavacheCacheService : ICacheService
{
    private readonly IBlobCache _cache;

    public AkavacheCacheService(IBlobCache blobCache)
    {
        _cache = blobCache;
    }

    public async Task OnShutdownAsync()
    {
        await _cache.Flush();
    }

    public async Task<IEnumerable<string>> GetAllKeys()
    {
        return await _cache.GetAllKeys();
    }

    public async Task InvalidateObjects<T>(IEnumerable<string> keys)
    {
        if (keys.Any())
        {
            await _cache.InvalidateObjects<T>(keys);
        }
    }

    public async Task<T> GetObject<T>(string key)
    {
        try
        {
            return await _cache.GetObject<T>(key);
        }
        catch (KeyNotFoundException)
        {
            return default;
        }
    }

    public async Task InsertObject<T>(string key, T target)
    {
        await _cache.InsertObject(key, target);
    }
}