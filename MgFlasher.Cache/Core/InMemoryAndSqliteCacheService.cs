﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace MgFlasher.Cache.Core;

public class InMemoryAndSqliteCacheService : ICacheService
{
    private readonly ICacheService _inMemoryCache;
    private readonly ICacheService _sqliteCache;
    private readonly ILogger<InMemoryAndSqliteCacheService> _logger;

    public InMemoryAndSqliteCacheService(
        ICacheService inMemoryCache,
        ICacheService sqliteCache,
        ILogger<InMemoryAndSqliteCacheService> logger)
    {
        _inMemoryCache = inMemoryCache;
        _sqliteCache = sqliteCache;
        _logger = logger;
    }

    public async Task OnShutdownAsync()
    {
        ResourceLockProvider.OnShutdown();
        await _inMemoryCache.OnShutdownAsync();
        await _sqliteCache.OnShutdownAsync();
    }

    public async Task<IEnumerable<string>> GetAllKeys()
    {
        return await _sqliteCache.GetAllKeys();
    }

    public async Task InvalidateObjects<T>(IEnumerable<string> keys)
    {
        await _sqliteCache.InvalidateObjects<T>(keys);
        await _inMemoryCache.InvalidateObjects<T>(keys);
    }

    public async Task<T> GetObject<T>(string key)
    {
        try
        {
            await ResourceLockProvider.WaitAsync(key, _logger);
            var obj = await _inMemoryCache.GetObject<T>(key);

            if (obj is null)
            {
                var objFromSqliteCache = await _sqliteCache.GetObject<T>(key);
                if (objFromSqliteCache != null)
                {
                    await _inMemoryCache.InsertObject(key, objFromSqliteCache);
                }

                obj = objFromSqliteCache;
            }

            return obj;
        }
        finally
        {
            ResourceLockProvider.Release(key);
        }
    }

    public async Task InsertObject<T>(string key, T target)
    {
        try
        {
            await ResourceLockProvider.WaitAsync(key, _logger);

            await _sqliteCache.InsertObject(key, target);
            await _inMemoryCache.InsertObject(key, target);
        }
        finally
        {
            ResourceLockProvider.Release(key);
        }
    }
}