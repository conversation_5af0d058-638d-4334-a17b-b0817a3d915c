﻿using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace MgFlasher.Cache.Core;

public static class ResourceLockProvider
{
    private static readonly ConcurrentDictionary<string, SemaphoreSlim> LockDictionary = new();
    private static readonly TimeSpan LockTimeout = TimeSpan.FromMilliseconds(1000);

    public static void OnShutdown()
    {
        LockDictionary.Clear();
    }

    public static async Task WaitAsync(string id, ILogger logger)
    {
        var result = await LockDictionary.GetOrAdd(id, new SemaphoreSlim(1, 1)).WaitAsync(LockTimeout);
        if (!result)
        {
            logger.LogWarning("ResourceLockProvider timeout occured for [{Id}]", id);
        }
    }

    public static void Release(string id)
    {
        if (LockDictionary.TryGetValue(id, out var semaphore))
        {
            try
            {
                semaphore.Release();
            }
            catch (SemaphoreFullException) { }
        }
    }
}