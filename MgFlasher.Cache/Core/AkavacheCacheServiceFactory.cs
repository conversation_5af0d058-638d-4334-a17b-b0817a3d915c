﻿using System;
using Akavache;
using Akavache.Sqlite3;

namespace MgFlasher.Cache.Core;

public class AkavacheCacheServiceFactory
{
    private readonly IAkavacheDatabasePathProvider _akavacheDatabasePathProvider;
    private readonly IEncryptionProvider _encryptionProvider;

    public AkavacheCacheServiceFactory(IAkavacheDatabasePathProvider akavacheDatabasePathProvider, IEncryptionProvider encryptionProvider)
    {
        _akavacheDatabasePathProvider = akavacheDatabasePathProvider;
        _encryptionProvider = encryptionProvider;

        BlobCache.ForcedDateTimeKind = DateTimeKind.Utc;
        Registrations.Start("mg-flasher", () => SQLitePCL.Batteries_V2.Init());
    }

    public IBlobCache CreateSqliteCache()
    {
        return new SQLiteEncryptedBlobCache(_akavacheDatabasePathProvider.GetDatabasePath(), _encryptionProvider);
    }
}