﻿using System.Security.Cryptography;

namespace MgFlasher.Cache.Core.Encryption;

public class AesEncryptor
{
    private readonly int _keySize;
    private readonly int _iterations;
    private readonly byte[] _salt;

    public AesEncryptor(int keySize, int iterations, byte[] salt)
    {
        _keySize = keySize;
        _iterations = iterations;
        _salt = salt;
    }

    public byte[] Decrypt(byte[] data, string password)
    {
        using var pdb = CreateKey(password);
        using var aes = CreateAes(pdb);
        using var decryptor = aes.CreateDecryptor();
        return decryptor.TransformFinalBlock(data, 0, data.Length);
    }

    public byte[] Encrypt(byte[] data, string password)
    {
        using var pdb = CreateKey(password);
        using var aes = CreateAes(pdb);
        using var encryptor = aes.CreateEncryptor();
        return encryptor.TransformFinalBlock(data, 0, data.Length);
    }

    private Aes CreateAes(Rfc2898DeriveBytes pdb)
    {
        var aes = Aes.Create();
        aes.Mode = CipherMode.CBC;
        aes.Padding = PaddingMode.PKCS7;
        aes.KeySize = _keySize;
        aes.BlockSize = 16 * 8;
        aes.Key = pdb.GetBytes(_keySize / 8);
        aes.IV = pdb.GetBytes(16);
        return aes;
    }

    private Rfc2898DeriveBytes CreateKey(string password)
    {
        return new Rfc2898DeriveBytes(password, _salt, _iterations);
    }
}