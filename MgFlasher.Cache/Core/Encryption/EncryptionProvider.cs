﻿using System;
using System.Linq;
using System.Reactive.Linq;
using System.Text;
using Akavache;

namespace MgFlasher.Cache.Core.Encryption;

public class EncryptionProvider : IEncryptionProvider
{
    private static readonly string[] Salt = {
        "9FVQYTOTYrMTxYICpCKi1EAykrCj8/GSZXE",
        "IwokHBskKUFfBkMsFTAeXT1NKjY+DhIZABA",
        "MUgnHTEIBzERGlpPXgk/IgIGTCYmA1E=",
        "ikmMxEcNVgzMlImOw0VNkI/DlIkOSkMLSkW",
        "qGlpeCxBGECIlBloXARc2MBxfNS9eNitHKw"
    };

    private static readonly string Key = GetKey();

    private readonly AesEncryptor _encryptor = new AesEncryptor(256, 10000, new byte[] {
        0x14, 0x14, 0xb5, 0xcc, 0x13, 0xa1, 0x03, 0x41, 0xab, 0x1a
    });

    public IObservable<byte[]> DecryptBlock(byte[] block)
    {
        return Observable.Return(_encryptor.Decrypt(block, Key));
    }

    public IObservable<byte[]> EncryptBlock(byte[] block)
    {
        return Observable.Return(_encryptor.Encrypt(block, Key));
    }

    private static string GetKey()
    {
        var data = Convert.FromBase64String(Salt[1] + Salt[4] + Salt[0] + Salt[3] + Salt[2]);
        var key = Encoding.UTF8.GetBytes("xdelete");
        return Encoding.UTF8.GetString(data.Select((b, i) => (byte)(b ^ key[i % key.Length])).ToArray());
    }
}