﻿using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reactive.Linq;
using System.Threading.Tasks;

namespace MgFlasher.Cache.Core;

public class TrueInMemoryCacheService : ICacheService
{
    private readonly ConcurrentDictionary<string, object> _cache = new ConcurrentDictionary<string, object>();

    public Task OnShutdownAsync()
    {
        _cache.Clear();

        return Task.CompletedTask;
    }

    public Task<IEnumerable<string>> GetAllKeys()
    {
        return Task.FromResult(_cache.Select(x => x.Key));
    }

    public Task<T> GetObject<T>(string key)
    {
        if (_cache.TryGetValue(key, out object val))
        {
            return Task.FromResult(GetVal<T>(val));
        }

        return Task.FromResult((T)default);
    }

    public Task InsertObject<T>(string key, T target)
    {
        _cache[key] = target;

        return Task.CompletedTask;
    }

    public Task InvalidateObjects<T>(IEnumerable<string> keys)
    {
        foreach (var key in keys)
        {
            _cache.TryRemove(key, out var _);
        }

        return Task.CompletedTask;
    }

    private T GetVal<T>(object obj) => obj is T t ? t : default;
}