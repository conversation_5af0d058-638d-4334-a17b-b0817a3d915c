﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MgFlasher.Cache;

/// <summary>
/// A generic repository for storing data.
/// </summary>
/// <typeparam name="T"></typeparam>
public interface IRepository<T>
{
    event EventHandler Changed;

    Task<IEnumerable<T>> GetAllAsync(string parentId);

    Task<T> GetAsync(string parentId, string id);

    Task InvalidateAsync(string parentId, params string[] ids);

    Task SetAsync(string parentId, string id, T record);

    /// <summary>
    /// Overrides everything for given parent.
    /// </summary>
    Task SetMultipleAsync(string parentId, IDictionary<string, T> records);

    /// <summary>
    /// Overrides only records with matched key.
    /// </summary>
    Task SetManyAsync(string parentId, IDictionary<string, T> records);
}