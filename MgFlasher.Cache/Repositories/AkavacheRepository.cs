﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace MgFlasher.Cache.Repositories;

public class AkavacheRepository<T> : IRepository<T>
{
    private static readonly string CachePrefix = $"Logger.Storage.{typeof(T).Name}";
    private readonly ICacheService _cacheService;
    private readonly SemaphoreSlim _semaphoreSlim;

    public event EventHandler Changed;

    public class AkavacheUserRepositoryParentContainer
    {
        private static Dictionary<string, AkavacheUserRepositoryParentContainer> _empty =
            new Dictionary<string, AkavacheUserRepositoryParentContainer>();

        public string ParentId { get; set; }
        public Dictionary<string, T> Items { get; set; } = new Dictionary<string, T>();

        public static AkavacheUserRepositoryParentContainer GetEmpty(string parentId)
        {
            parentId ??= string.Empty;

            if (!_empty.TryGetValue(parentId, out var existing) || existing.Items.Count > 0)
            {
                return _empty[parentId] = new AkavacheUserRepositoryParentContainer() { ParentId = parentId };
            }

            return _empty[parentId];
        }
    }

    public AkavacheRepository(ICacheService cacheService)
    {
        _cacheService = cacheService;
        _semaphoreSlim = new SemaphoreSlim(1, 1);
    }

    public async Task<IEnumerable<T>> GetAllAsync(string parentId)
    {
        var container = await GetContainerAsync(parentId);

        return container.Items.Values;
    }

    public async Task<T> GetAsync(string parentId, string id)
    {
        var container = await GetContainerAsync(parentId);

        container.Items.TryGetValue(id, out T value);

        return value;
    }

    public async Task InvalidateAsync(string parentId, params string[] ids)
    {
        if (ids is null || !ids.Any())
        {
            return;
        }

        try
        {
            await _semaphoreSlim.WaitAsync();

            var container = await GetContainerAsync(parentId);

            foreach (var id in ids)
            {
                if (container.Items.ContainsKey(id))
                {
                    container.Items.Remove(id);
                }
            }

            await SetContainerAsync(container);
        }
        finally
        {
            _semaphoreSlim.Release();

            Changed?.Invoke(this, EventArgs.Empty);
        }
    }

    public async Task SetAsync(string parentId, string id, T record)
    {
        try
        {
            await _semaphoreSlim.WaitAsync();

            var container = await GetContainerAsync(parentId);

            container.Items[id] = record;

            await SetContainerAsync(container);
        }
        finally
        {
            _semaphoreSlim.Release();

            Changed?.Invoke(this, EventArgs.Empty);
        }
    }

    public async Task SetMultipleAsync(string parentId, IDictionary<string, T> records)
    {
        try
        {
            await _semaphoreSlim.WaitAsync();

            var container = await GetContainerAsync(parentId);

            container.Items = records is Dictionary<string, T> impl ? impl : records.ToDictionary(x => x.Key, x => x.Value);

            await SetContainerAsync(container);
        }
        finally
        {
            _semaphoreSlim.Release();

            Changed?.Invoke(this, EventArgs.Empty);
        }
    }

    public async Task SetManyAsync(string parentId, IDictionary<string, T> records)
    {
        try
        {
            await _semaphoreSlim.WaitAsync();

            var container = await GetContainerAsync(parentId);

            container.Items ??= new Dictionary<string, T>();
            records ??= new Dictionary<string, T>();

            foreach (var record in records)
            {
                container.Items[record.Key] = record.Value;
            }

            await SetContainerAsync(container);
        }
        finally
        {
            _semaphoreSlim.Release();

            Changed?.Invoke(this, EventArgs.Empty);
        }
    }

    private async Task SetContainerAsync(AkavacheUserRepositoryParentContainer container)
    {
        var key = GetParentKey(container.ParentId);
        await _cacheService.InsertObject(key, container);
    }

    private async Task<AkavacheUserRepositoryParentContainer> GetContainerAsync(string parentId)
    {
        var key = GetParentKey(parentId);
        var result = await _cacheService.GetObject<AkavacheUserRepositoryParentContainer>(key);
        if (result is null)
        {
            result = AkavacheUserRepositoryParentContainer.GetEmpty(parentId);
            await SetContainerAsync(result);
        }

        return result;
    }

    private string GetParentKey(string parentId) => $"{CachePrefix}.{parentId.ToUpper()}";
}