﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MgFlasher.Cache.Repositories;

public class InMemoryRepository<T> : IRepository<T>
{
    private Dictionary<string, T> _items = new Dictionary<string, T>();

    public event EventHandler Changed;

    public Task<IEnumerable<T>> GetAllAsync(string parentId)
    {
        return Task.FromResult<IEnumerable<T>>(_items.Values);
    }

    public Task<T> GetAsync(string parentId, string id)
    {
        return Task.FromResult(_items.ContainsKey(id) ? _items[id] : default);
    }

    public Task InvalidateAsync(string parentId, params string[] ids)
    {
        Changed?.Invoke(this, EventArgs.Empty);

        return Task.CompletedTask;
    }

    public Task SetAsync(string parentId, string id, T record)
    {
        record = record ?? throw new ArgumentNullException(nameof(record));

        Changed?.Invoke(this, EventArgs.Empty);

        _items[id] = record;

        return Task.CompletedTask;
    }

    public Task SetMultipleAsync(string parentId, IDictionary<string, T> records)
    {
        foreach (var record in records)
        {
            _items[record.Key] = record.Value;
        }

        Changed?.Invoke(this, EventArgs.Empty);

        return Task.CompletedTask;
    }

    public Task SetManyAsync(string parentId, IDictionary<string, T> records)
    {
        Changed?.Invoke(this, EventArgs.Empty);

        return SetMultipleAsync(parentId, records);
    }
}