﻿using System;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace MgFlasher.Cache.Repositories;

public class AkavacheHashedRepository<T> : IHashedRepository<T>
{
    private static readonly string HashKey = $"HashedStorage.Templates.{typeof(T).Name}.Hash";
    private static readonly string RecordsKey = $"HashedStorage.Templates.{typeof(T).Name}.Records";
    private readonly ICacheService _cacheService;

    public AkavacheHashedRepository(ICacheService cacheService)
    {
        _cacheService = cacheService;
    }

    public async Task<T> GetCachedRecords()
    {
        return await TryGetCachedRecords() ?? throw new InvalidOperationException("Records must exists");
    }

    public async Task<T> TryGetCachedRecords()
    {
        var json = await _cacheService.GetObject<string>(RecordsKey);
        if (string.IsNullOrEmpty(json))
        {
            return default;
        }

        var deserialized = Deserialize(json);

        return deserialized;
    }

    public async Task<string> GetStoredItemsHash()
    {
        var hash = await _cacheService.GetObject<string>(HashKey);

        return hash;
    }

    public async Task SetRecordsAsync(string hash, T records)
    {
        await _cacheService.InsertObject(HashKey, hash);

        var serialized = Serialize(records);
        await _cacheService.InsertObject(RecordsKey, serialized);
    }

    private string Serialize(T records)
    {
        var serialized = JsonConvert.SerializeObject(records, GetSettings());

        return serialized;
    }

    private T Deserialize(string json)
    {
        var deserialized = JsonConvert.DeserializeObject<T>(json, GetSettings());

        return deserialized;
    }

    private JsonSerializerSettings GetSettings()
    {
        var settings = new JsonSerializerSettings()
        {
            NullValueHandling = NullValueHandling.Ignore,
            DefaultValueHandling = DefaultValueHandling.Ignore
        };
        return settings;
    }
}