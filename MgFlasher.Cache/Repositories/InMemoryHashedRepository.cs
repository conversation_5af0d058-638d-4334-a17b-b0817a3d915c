﻿using System;
using System.Threading.Tasks;

namespace MgFlasher.Cache.Repositories;

public class InMemoryHashedRepository<T> : IHashedRepository<T>
{
    private string _hash;
    private T _records;

    public Task<string> GetStoredItemsHash() => Task.FromResult(_hash);

    public async Task<T> GetCachedRecords() =>
        await TryGetCachedRecords() ?? throw new InvalidOperationException("Records must exists");

    public Task<T> TryGetCachedRecords() => Task.FromResult(_records);

    public Task SetRecordsAsync(string hash, T records)
    {
        _hash = hash;
        _records = records;
        return Task.CompletedTask;
    }
}