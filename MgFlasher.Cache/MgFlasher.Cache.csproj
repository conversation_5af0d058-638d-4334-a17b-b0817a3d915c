﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<GeneratePackageOnBuild>true</GeneratePackageOnBuild>
		<IncludeSource>True</IncludeSource>
		<IncludeSymbols>True</IncludeSymbols>
		<AssemblyName>MgFlasher.Cache</AssemblyName>
		<RootNamespace>MgFlasher.Cache</RootNamespace>
		<Company>JR Auto Performance Inc.</Company>
		<Authors>MGFlasher Team</Authors>
		<VersionPrefix>1.0.1</VersionPrefix>
		<RepositoryUrl>https://github.com/mgflasher-team/mgflasher-app</RepositoryUrl>
		<StringEncryption>hash</StringEncryption>
		<ResourceEncryption>true</ResourceEncryption>
		<ControlFlowObfuscation>if=on;switch=on;case=on;call=on</ControlFlowObfuscation>
		<ControlFlowIterations>3</ControlFlowIterations>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.1" />		
		<PackageReference Include="akavache.sqlite3" Version="10.1.6" />
		<PackageReference Include="SQLitePCLRaw.bundle_green" Version="2.1.10" />
		<PackageReference Condition="'$(Configuration)' == 'Release' " Include="Babel.Obfuscator" Version="10.9.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

</Project>
