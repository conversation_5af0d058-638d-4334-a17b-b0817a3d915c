﻿using System.Threading.Tasks;

namespace MgFlasher.Cache;

/// <summary>
/// A repository where records are stored with a hash to identify them.
/// </summary>
/// <typeparam name="T"></typeparam>
public interface IHashedRepository<T>
{
    Task<string> GetStoredItemsHash();

    /// <summary>
    /// Try getting a list of records from cache of a given Type.
    /// </summary>
    /// <returns></returns>
    Task<T> TryGetCachedRecords();

    Task<T> GetCachedRecords();

    Task SetRecordsAsync(string hash, T records);
}