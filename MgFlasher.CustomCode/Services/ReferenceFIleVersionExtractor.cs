﻿using System;
using System.Text;
using System.Text.RegularExpressions;
using MgFlasher.CustomCode.Models;
using MgFlasher.CustomCode.Storage.Changesets;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Services;

public class ReferenceFileVersionExtractor : IReferenceFileVersionExtractor
{
    private readonly ILogger<ReferenceFileVersionExtractor> _logger;

    public ReferenceFileVersionExtractor(ILogger<ReferenceFileVersionExtractor> logger)
    {
        _logger = logger;
    }

    public ReferenceFileVersion Extract(byte[] content, ProcessorArchitectureType processorArchitectureType)
    {
        try
        {
            var sweAddresses = GetSweAddresses(processorArchitectureType);
            var btldItem = GetMapDescriptorItem(content, sweAddresses.btldXweAddress, 0x06);
            var pstItem = GetMapDescriptorItem(content, sweAddresses.pstXweAddress, 0x08);
            var dstItem = GetMapDescriptorItem(content, sweAddresses.dstXweAddress, 0x0D);
            var a2l = GetA2lNumber(content);
            var customCodeId = GetCustomCodeId(content);

            return new ReferenceFileVersion()
            {
                Btld = btldItem,
                Pst = pstItem,
                Dst = dstItem,
                A2L = a2l,
                CustomCodeId = customCodeId,
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Extract error");

            throw;
        }
    }

    private SweVersion GetMapDescriptorItem(byte[] content, int address, byte expectedFirstValue)
    {
        var raw = new byte[8];
        Array.Copy(content, address, raw, 0, 8);
        if (raw[0] != expectedFirstValue)
        {
            XweItemDescription.Mapping.TryGetValue(raw[0], out string xweString);
            throw new InvalidOperationException($"could not resolve descriptor item, input xwe string: {xweString}");
        }

        var id = (raw[3] << 8) + raw[4];
        var major = (int)raw[5];
        var minor = (int)raw[6];
        var patch = (int)raw[7];

        return new SweVersion()
        {
            Id = id,
            Major = major,
            Minor = minor,
            Patch = patch,
            Raw = raw,
        };
    }

    private (int btldXweAddress, int pstXweAddress, int dstXweAddress) GetSweAddresses(ProcessorArchitectureType processorArchitectureType) => processorArchitectureType switch
    {
        ProcessorArchitectureType.PPC => (0x3FD00, 0x67FD00, 0x77FD00),
        ProcessorArchitectureType.AURIX => (0x5FD00, 0x6FFD00, 0x7FFD00),
        _ => throw new InvalidOperationException($"Cannot resolve swe address for processor type - {processorArchitectureType}"),
    };

    private string GetA2lNumber(byte[] data)
    {
        var swflDifVariante = GetPstDifVarianteFromDataFile(data);
        var a2lSplit = new Regex("([a-zA-Z0-9]){8}[B]", RegexOptions.Compiled);
        var matches = a2lSplit.Matches(swflDifVariante);
        if (matches.Count == 0)
        {
            throw new CustomCodeException(CustomCodeException.ErrorCodes.InvalidReferenceFileSize_CouldNotParse_A2lNumber, swflDifVariante);
        }

        var result = matches[0].Value.Remove(3, 1).Insert(3, "9");

        return result;
    }

    private CustomCodeId GetCustomCodeId(byte[] data)
    {
        var swflDifVariante = GetPstDifVarianteFromDataFile(data);
        var result = CustomCodeId.FromDifVariante(swflDifVariante);
        if (result == null)
        {
            _logger.LogDebug("No custom code version was matched from the input bin's SWFL dif variante.");
        }

        return result;
    }

    private string GetPstDifVarianteFromDataFile(byte[] data)
    {
        try
        {
            uint pstSweInfofieldTextOffset = data.Length switch
            {
                0x780000 => 0x67FE0C,
                0x800000 => 0x6FFE0C,
                _ => throw new CustomCodeException(CustomCodeException.ErrorCodes.InvalidReferenceFileSize, data.Length.ToString())
            };

            var swflInfo = GetSWEInfofieldText(data, pstSweInfofieldTextOffset);

            return swflInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            throw new Exception(ex.Message, ex);
        }
    }

    private string GetSWEInfofieldText(byte[] data, uint startAddress)
    {
        var headerInfoSize = 2;
        if (startAddress + headerInfoSize > data.Length)
        {
            throw new CustomCodeException(CustomCodeException.ErrorCodes.ReferenceFile_InvalidSweInfoStartAddress,
                $"GetSWEInfofieldText: Data file size is 0x{data.Length:X8}, INVALID ADDRESS @ 0x{startAddress + headerInfoSize:X8}");
        }

        var headerInfo = new byte[headerInfoSize];
        Array.Copy(data, startAddress, headerInfo, 0, headerInfoSize);

        var infoHeaderSize = headerInfo[1] * 8 + headerInfoSize;
        var infoTextLength = headerInfo[0] - infoHeaderSize;

        var size = Math.Min(255, infoTextLength);
        var infoFieldStartAddress = startAddress + infoHeaderSize;
        if (infoFieldStartAddress + size > data.Length)
        {
            throw new CustomCodeException(CustomCodeException.ErrorCodes.ReferenceFile_InvalidSweInfoStartAddress_InvalidAddress,
                $"GetSWEInfofieldText: INVALID ADDRESS @ 0x{infoFieldStartAddress + size:X8}");
        }

        var difVarianteAsByteArray = new byte[size];
        Array.Copy(data, infoFieldStartAddress, difVarianteAsByteArray, 0, size);

        var difVarianteAsString = Encoding.Default.GetString(difVarianteAsByteArray);
        return difVarianteAsString;
    }
}