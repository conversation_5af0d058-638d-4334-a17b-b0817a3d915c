﻿using System.Collections.Generic;

namespace MgFlasher.CustomCode.Services;

public static class XweItemDescription
{
    public static readonly IReadOnlyDictionary<byte, string> Mapping = new Dictionary<byte, string>()
    {
        [0x00] = "-",    // "ungueltig"
        [0x01] = "HWEL", // "Hardware (Elektronik)"
        [0x02] = "HWAP", // "Hardwareauspraegung"
        [0x03] = "HWFR", // "Hardwarefarbe"
        [0x05] = "CAFD", // "Codierdaten"
        [0x06] = "BTLD", // "Bootloader"
        [0x08] = "SWFL", // "Software ECU Speicherimage"
        [0x09] = "SWFF", // "Flash File Software"
        [0x0A] = "SWPF", // "Pruefsoftware"
        [0x0B] = "ONPS", // "Onboard Programmiersystem"
        [0x0F] = "FAFP", // "FA2FP"
        [0x1A] = "TLRT", // "Temporaere Loeschroutine"
        [0x1B] = "TPRG", // "Temporaere Programmierroutine"
        [0x07] = "FLSL", // "Flashloader Slave"
        [0x0C] = "IBAD", // "Interaktive Betriebsanleitung Daten"
        [0x10] = "FCFA", // "Freischaltcode Fahrzeug-Auftrag"
        [0x1C] = "BLUP", // "Bootloader-Update Applikation"
        [0x1D] = "FLUP", // "Flashloader-Update Applikation"
        [0xC0] = "SWUP", // "Software-Update Package"
        [0xC1] = "SWIP", // "Index Software-Update Package"
        [0xA0] = "ENTD", // "Entertainment Daten"
        [0xA1] = "NAVD", // "Navigation Daten"
        [0xA2] = "FCFN", // "Freischaltcode Funktion"
        [0x04] = "GWTB", // "Gateway-Tabelle"
        [0x0D] = "SWFK", // "BEGU: Detaillierung auf SWE-Ebene"
        [0xFF] = "-"     // "ungueltig"
    };
}