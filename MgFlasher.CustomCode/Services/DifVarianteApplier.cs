﻿using System;
using System.Text;
using MgFlasher.CustomCode.Models;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Storage.Static;

public class DifVarianteApplier : IDifVarianteApplier
{
    private readonly ILogger<DifVarianteApplier> _logger;

    public DifVarianteApplier(ILogger<DifVarianteApplier> logger)
    {
        _logger = logger;
    }

    public void Apply(byte[] data, ProcessorArchitectureType processorArchitectureType, EcuSection section, string tag)
    {
        if (string.IsNullOrEmpty(tag))
        {
            throw new Exception("Dif Variante tag is empty or null!");
        }

        _logger.LogInformation($"Writing tag [{tag}] to section [{section}]...");

        var tagBytes = Encoding.ASCII.GetBytes(string.Concat('#', tag));
        var binStartOffset = GetMgfBinOffsetForEcuSection(processorArchitectureType, section);
        var difVarianteSizeAddress = ResolveDifVarianteAddress(processorArchitectureType, section, (int)binStartOffset);
        if (difVarianteSizeAddress > data.Length)
        {
            throw new Exception($"Target address for Dif Variante 'Size' [0x{difVarianteSizeAddress:X08}] exceeds the bounds of {nameof(data)}: [0x{data.Length:X08}]");
        }

        data[difVarianteSizeAddress] = 0xFF;

        var tagAddress = FindDifVarianteWriteAddress(data, difVarianteSizeAddress, tagBytes.Length);
        if (tagAddress < 0
            || tagAddress + tagBytes.Length >= data.Length)
        {
            throw new Exception($"Tag address [0x{tagAddress:X08}] is wrong or dif variante address [0x{difVarianteSizeAddress:X08}] is wrong...");
        }

        _logger.LogDebug($"Writing [{Encoding.UTF8.GetString(tagBytes)}] at [0x{tagAddress:X08}]...");
        Array.Copy(tagBytes, 0, data, tagAddress, tagBytes.Length);
    }

    private uint FindDifVarianteWriteAddress(byte[] data, uint difVarianteSizeAddress, int tagValueLength)
    {
        var numberOfDependenciesOffset = data[difVarianteSizeAddress + 1] * 8u + 1u;
        var spaceAvailable = 0u;
        var tagAddress = 0x00u;

        _logger.LogDebug($"NumberOfDependenciesOffset: [0x{numberOfDependenciesOffset:X08}]");

        for (var i = difVarianteSizeAddress + 0xFF; i > difVarianteSizeAddress + numberOfDependenciesOffset; i--)
        {
            if (data[i] < 0x20 || data[i] > 0x7F)
            {
                data[i] = 0x5F;
            }

            if (tagAddress == 0x00)
            {
                if (data[i] == 0x5F)
                {
                    spaceAvailable++;
                    if (spaceAvailable >= tagValueLength + 1)
                    {
                        tagAddress = i;
                        _logger.LogDebug($"Found [0x{spaceAvailable:X08}] bytes free at [0x{tagAddress:X08}]...");
                    }
                }
                else
                {
                    spaceAvailable = 0;
                }
            }
        }

        return tagAddress;
    }

    public uint ResolveDifVarianteAddress(ProcessorArchitectureType architecture, EcuSection section, int binStartOffset)
    {
        var result = (architecture, section) switch
        {
            (ProcessorArchitectureType.PPC, EcuSection.BTLD) => (uint)(0x3FE0Cu + binStartOffset),
            (ProcessorArchitectureType.PPC, EcuSection.PST) => (uint)(0x63FE0Cu + binStartOffset),
            (ProcessorArchitectureType.PPC, EcuSection.DST) => (uint)(0xFFE0Cu + binStartOffset),
            (ProcessorArchitectureType.AURIX, EcuSection.BTLD) => (uint)(0x37E0Cu + binStartOffset),
            (ProcessorArchitectureType.AURIX, EcuSection.PST) => (uint)(0x67FE0Cu + binStartOffset),
            (ProcessorArchitectureType.AURIX, EcuSection.DST) => (uint)(0xFFE0Cu + binStartOffset),
            _ => throw new InvalidOperationException($"Unsupported architecture/section: [{architecture}]/[{section}]")
        };

        _logger.LogDebug($"ResolveDifVarianteAddress: [0x{result:X08}]");
        return result;
    }

    public uint GetMgfBinOffsetForEcuSection(ProcessorArchitectureType architecture, EcuSection section)
    {
        var result = (architecture, section) switch
        {
            (ProcessorArchitectureType.PPC, EcuSection.BTLD) => 0x0u,
            (ProcessorArchitectureType.PPC, EcuSection.PST) => 0x40000u,
            (ProcessorArchitectureType.PPC, EcuSection.DST) => 0x580000u,
            (ProcessorArchitectureType.AURIX, EcuSection.BTLD) => 0x28000u,
            (ProcessorArchitectureType.AURIX, EcuSection.PST) => 0x80000u,
            (ProcessorArchitectureType.AURIX, EcuSection.DST) => 0x700000u,
            _ => throw new InvalidOperationException($"Unsupported architecture/section: [{architecture}]/[{section}]")
        };

        _logger.LogDebug($"GetMgfBinOffsetForEcuSection: [0x{result:X08}]");
        return result;
    }
}