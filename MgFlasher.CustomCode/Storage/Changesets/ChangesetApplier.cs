﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Storage.Changesets;

public class ChangesetApplier
{
    private readonly int _pointerSize;
    private readonly ILogger<ChangesetApplier> _logger;
    private readonly Dictionary<uint, byte> _executionPlan = new Dictionary<uint, byte>();

    private ChangesetApplier(byte[] modificationData, int pointerSize, ILogger<ChangesetApplier> logger)
    {
        _pointerSize = pointerSize;
        _logger = logger;

        CreateExecutionPlan(modificationData);
    }

    private void CreateExecutionPlan(byte[] data)
    {
        try
        {
            for (int i = 0; i < data.Length; i += _pointerSize)
            {
                var byteAddress = new byte[4] { 0, 0, 0, 0 };
                data[i..(i + 3)].CopyTo(byteAddress, 1);
                byteAddress = byteAddress.Reverse().ToArray();
                var address = BitConverter.ToUInt32(byteAddress);
                var replaceValue = data[i + 3];
                _executionPlan.Add(address, replaceValue);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception while create execution plan for changes to stock file");
            throw;
        }
    }

    public void ApplyTo(byte[] data)
    {
        foreach (var change in _executionPlan)
        {
            data[change.Key] = change.Value;
        }
    }

    public static ChangesetApplier ForOtsChangeset(byte[] data, ILoggerFactory loggerFactory)
    {
        return new ChangesetApplier(data, 4, loggerFactory.CreateLogger<ChangesetApplier>());
    }

    public static ChangesetApplier CreateForCustomCodeChangeset(byte[] data, ILoggerFactory loggerFactory)
    {
        return new ChangesetApplier(data, 5, loggerFactory.CreateLogger<ChangesetApplier>());
    }
}