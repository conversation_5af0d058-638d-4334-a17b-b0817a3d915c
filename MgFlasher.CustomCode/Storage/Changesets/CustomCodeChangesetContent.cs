﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MgFlasher.CustomCode.Storage.Changesets;

public class CustomCodeChangesetContent
{
    public CustomCodeId Key { get; }
    public Func<Task<byte[]>> ContentFunc { get; }
    public bool Deprecated { get; }
    public List<uint> VinAddresses { get; }
    public Dictionary<uint, uint> LabasOffset { get; }
    public string ReleaseNotes { get; }
    public string ReleasedAtUtc { get; }
    public int CustomCodeServerId { get; }

    public CustomCodeChangesetContent(CustomCodeId key, Func<Task<byte[]>> contentFunc, bool deprecated, List<uint> vinAddresses, string releaseNotes, Dictionary<uint, uint> labasOffset, string releasedAtUtc, int serverMapId)
    {
        Key = key;
        ContentFunc = contentFunc;
        Deprecated = deprecated;
        VinAddresses = vinAddresses;
        ReleaseNotes = releaseNotes;
        LabasOffset = labasOffset;
        ReleasedAtUtc = releasedAtUtc;
        CustomCodeServerId = serverMapId;
    }
}