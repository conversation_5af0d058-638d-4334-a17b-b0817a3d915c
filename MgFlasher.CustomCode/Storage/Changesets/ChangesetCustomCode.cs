﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.CustomCode.Configuration;
using MgFlasher.CustomCode.Extensions;
using MgFlasher.CustomCode.Models;
using MgFlasher.CustomCode.Storage.Static;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace MgFlasher.CustomCode.Storage.Changesets;

public class ChangesetCustomCode : ICustomCode
{
    private CustomCodeChangesetContent _content;
    private readonly IDifVarianteApplier _difVarianteApplier;
    private readonly ICustomCodeOptionValueResolver _valueResolver;
    private readonly ICustomCodeOptionAddressResolver _addressResolver;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ILogger<ChangesetCustomCode> _logger;

    public CustomCodeId Version => _content.Key;
    public ReferenceFileVersion MapDescriptor { get; }
    public string ReleaseNotes => _content.ReleaseNotes;
    public string ReleasedAtUtc => _content.ReleasedAtUtc;
    public bool CanSelect { get; set; }
    public CustomCodeUnavailableReasonEnum? UnavailableReason { get; set; }

    public ChangesetCustomCode(CustomCodeChangesetContent content, ReferenceFileVersion descriptor,
        IDifVarianteApplier difVarianteApplier, ICustomCodeOptionValueResolver valueResolver,
        ICustomCodeOptionAddressResolver addressResolver,
        ILoggerFactory loggerFactory)
    {
        _content = content;
        MapDescriptor = descriptor;
        _difVarianteApplier = difVarianteApplier;
        _valueResolver = valueResolver;
        _addressResolver = addressResolver;
        _loggerFactory = loggerFactory;
        _logger = _loggerFactory.CreateLogger<ChangesetCustomCode>();

        CanSelect = !_content.Deprecated;
        UnavailableReason = CanSelect ? null : CustomCodeUnavailableReasonEnum.Obsolete;
    }

    public async Task<CustomCodeMapBuilderResult> ApplyAsync(byte[] map, CustomCodeContext context, CustomCodeDevelopmentSwitches switches)
    {
        try
        {
            if (switches.WithPstChanges)
            {
                await ApplyCustomCodeChangesetContentAsync(map);
            }

            if (switches.WithCustomCodeInitialValues)
            {
                await ApplyInitialCustomCodeOptions(map, context);
            }

            if (switches.WithLabasOffsets)
            {
                ApplyLabasOffsets(map, context);
            }

            if (switches.WithVin)
            {
                ApplyVinAddresses(map, context);
            }

            ApplyDifVarianteTags(map, context, switches);

            return SuccessResult(map);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Could not apply ccc file");
            return ErrorResult(ex, map);
        }
    }

    private async Task ApplyInitialCustomCodeOptions(byte[] map, CustomCodeContext context)
    {
        var values = await _valueResolver.GetAllFirstValuesForInitialApplyAsync();
        _logger.LogInformation("Applying [{ValuesCount}] initial values...", values.Count);
        var errors = new List<string>();
        foreach (var value in values)
        {
            var address = await _addressResolver.TryResolveAsync(MapDescriptor.A2L, Version.Version, value.A2lCharacteristic);
            if (address is not null)
            {
                try
                {
                    _logger.LogInformation("Applying initial value:\\n{SerializeObject}", JsonConvert.SerializeObject(value, Formatting.Indented));
                    var valueToApply = GetValueToApply(context, value);
                    map.Apply(address.Value, valueToApply);
                }
                catch (Exception ex)
                {
                    errors.Add($"Could not apply [{value.A2lCharacteristic}] with value length [{value.Value.Length}] to Address [{address.Value}] because [{ex.Message}]");
                    _logger.LogError(ex, "Could not apply [{A2lName}] with [{Value}] to Address [{Address}] to stock map for configuration [{Context}]",
                        value.A2lCharacteristic, string.Join(", ", value.Value), address.Value, JsonConvert.SerializeObject(context));
                }
            }
            else
            {
                _logger.LogInformation("Address is invalid for initial value:\\n{ValueA2LCharacteristic}", value.A2lCharacteristic);
            }
        }

        if (errors.Any())
        {
            throw new CustomCodeException(CustomCodeException.ErrorCodes.CannotApplyInitialOptions, string.Join("\r\n", errors));
        }
    }

    private static byte[] GetValueToApply(CustomCodeContext context, CustomCodeOptionInitialDefinitionValue value)
    {
        return context.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX) ? value.Value.ChangeMapEndianness(value.BitSize, value.Signed) : value.Value;
    }

    private void ApplyDifVarianteTags(byte[] data, CustomCodeContext context, CustomCodeDevelopmentSwitches switches)
    {
        _logger.LogInformation("Checking for third party unlock...");

        if (context.FemtoUnlock)
        {
            _logger.LogInformation("ECU has Femto unlock, adding tag to PST Dif Variante");
            _difVarianteApplier.Apply(data, context.ProcessorArchitectureType, EcuSection.DST, "FEMTO");
            _difVarianteApplier.Apply(data, context.ProcessorArchitectureType, EcuSection.PST, "FEMTO");
        }
        else if (context.Wave3Unlock)
        {
            _logger.LogInformation("ECU has Wave 3 unlock, adding tag to PST Dif Variante");
            _difVarianteApplier.Apply(data, context.ProcessorArchitectureType, EcuSection.DST, "MGF_3PU");
            _difVarianteApplier.Apply(data, context.ProcessorArchitectureType, EcuSection.PST, "MGF_3PU");
        }
        else if (context.ThirdPartyUnlock)
        {
            _logger.LogInformation("ECU has third party unlock, adding tag to PST Dif Variante");
            _difVarianteApplier.Apply(data, context.ProcessorArchitectureType, EcuSection.PST, "MGF_3PU");
        }
        else
        {
            _logger.LogInformation("ECU does not have third party unlock, no tag needed...");
        }

        if (switches.WithPstChanges)
        {
            _logger.LogInformation("Applying custom code version {version}, adding tag to PST Dif Variante...", Version);
            _difVarianteApplier.Apply(data, context.ProcessorArchitectureType, EcuSection.PST, $"CC{Version.Version}");
        }
    }

    private CustomCodeMapBuilderResult ErrorResult(Exception ex, byte[] map) => new CustomCodeMapBuilderResult()
    {
        Success = false,
        Content = map,
        Issues = new List<string>() { ex.ToString() },
        CustomCodeServerId = _content.CustomCodeServerId,
        Version = Version
    };

    private CustomCodeMapBuilderResult SuccessResult(byte[] map) => new CustomCodeMapBuilderResult()
    {
        Success = true,
        Content = map,
        Issues = new List<string>(),
        CustomCodeServerId = _content.CustomCodeServerId,
        Version = Version
    };

    private void ApplyLabasOffsets(byte[] map, CustomCodeContext context)
    {
        if (context.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
        {
            _logger.LogInformation($"Labas offset not needed on Aurix, skipping...");
            return;
        }

        _logger.LogInformation($"Applying [{_content.LabasOffset.Count}] Labas offset address(es) to DST...");
        foreach (var item in _content.LabasOffset.OrderBy(x => x.Key))
        {
            _logger.LogInformation($"Applying Labas offset [0x{item.Value:X08}] to DST at [0x{item.Key:X08}]...");
            var bytes = BitConverter.GetBytes(item.Value);
            if (context.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
            {
                Array.Reverse(bytes);
            }
            Array.Copy(bytes, 0, map, item.Key, bytes.Length);
        }
    }

    private void ApplyVinAddresses(byte[] map, CustomCodeContext context)
    {
        _logger.LogInformation("Applying [{VinAddressesCount}] character(s) of VIN {ContextVin} to PST...", _content.VinAddresses.Count, context.VIN);
        var vinStartIdx = 17 - _content.VinAddresses.Count;
        foreach (var addr in _content.VinAddresses.OrderBy(x => x)?.Take(_content.VinAddresses.Count) ?? new List<uint>())
        {
            var vinByte = (byte)context.VIN[vinStartIdx];
            _logger.LogInformation("Applying [{VinStartIdx}th] VIN character [{Unknown} => (0x{VinByte})] to PST at [0x{Addr}]...", vinStartIdx, context.VIN[vinStartIdx], vinByte, addr);
            vinStartIdx++;

            if (context.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
            {
                map.ApplyValues(new object[] { new object[] { addr, addr, new byte[] { vinByte } } });
            }
            else if (context.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
            {
                var instructionByte = map[addr];
                var firstParameter = map[addr + 1];
                var secondParameter = map[addr + 2];
                if (instructionByte != 0x8B)
                {
                    throw new Exception($"Failed to find 0x8B instruction at [0x{addr:X08}], found: [0x{instructionByte:X02}]");
                }

                if (((firstParameter & 0xF0) >> 4) != 0x9)
                {
                    throw new Exception($"Failed to find 0x9? (of 0x69 marker) at [0x{addr + 1:X08}], found: [0x{firstParameter:X02}]");
                }

                if ((secondParameter & 0x0F) != 0x6)
                {
                    throw new Exception($"Failed to find 0x?6 (of 0x69 marker) at [0x{addr + 2:X08}], found: [0x{secondParameter:X02}]");
                }

                var highVinByteNibble = (byte)((firstParameter & 0x0F) + ((vinByte & 0x0F) << 4));
                var lowVinByteNibble = (byte)((secondParameter & 0xF0) + ((vinByte & 0xF0) >> 4));
                map[addr + 1] = highVinByteNibble;
                map[addr + 2] = lowVinByteNibble;
            }
            else
            {
                _logger.LogInformation("Failed to find correct ProcessorArchitectureType [{ProcessorArchitectureType}] for applying vin!", context.ProcessorArchitectureType);
            }
        }
    }

    private async Task ApplyCustomCodeChangesetContentAsync(byte[] map)
    {
        _logger.LogInformation("Applying custom code version {version} to PST...", Version);

        var changeset = await _content.ContentFunc();
        var applier = ChangesetApplier.CreateForCustomCodeChangeset(changeset, _loggerFactory);
        applier.ApplyTo(map);

        _logger.LogInformation("Successfully applied custom code version {version} to PST!", Version);
    }
}