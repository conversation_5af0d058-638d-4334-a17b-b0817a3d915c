﻿using System.Collections.Generic;
using System.Threading.Tasks;
using MgFlasher.CustomCode.Configuration;
using MgFlasher.CustomCode.Models;
using MgFlasher.CustomCode.Storage.Static;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Storage.Changesets;

public class ChangesetsCustomCodeRepository : ICustomCodeRepository
{
    private readonly ICustomCodeChangesetContentRepository _repository;
    private readonly IDifVarianteApplier _difVarianteApplier;
    private readonly ICustomCodeOptionValueResolver _valueResolver;
    private readonly ICustomCodeOptionAddressResolver _addressResolver;
    private readonly ILoggerFactory _loggerFactory;

    public ChangesetsCustomCodeRepository(
        ICustomCodeChangesetContentRepository repository,
        IDifVarianteApplier difVarianteApplier,
        ICustomCodeOptionValueResolver valueResolver,
        ICustomCodeOptionAddressResolver addressResolver,
        ILoggerFactory loggerFactory)
    {
        _repository = repository;
        _difVarianteApplier = difVarianteApplier;
        _valueResolver = valueResolver;
        _addressResolver = addressResolver;
        _loggerFactory = loggerFactory;
    }

    public async Task<IReadOnlyList<ICustomCode>> GetAsync(ReferenceFileVersion descriptor)
    {
        var versions = await _repository.GetAvailableAsync(descriptor);
        var result = new List<ICustomCode>();
        foreach (var version in versions)
        {
            var content = await _repository.GetAsync(version, descriptor);
            var changesetCustomCode = new ChangesetCustomCode(content, descriptor, _difVarianteApplier, _valueResolver, _addressResolver, _loggerFactory);
            result.Add(changesetCustomCode);
        }

        return result;
    }
}