﻿using MgFlasher.CustomCode.Models;

namespace MgFlasher.CustomCode.Storage.Static;

public interface IStaticCustomCodeApplier
{
    bool ApplyCodeWordsDst(byte[] content, ReferenceFileVersion mapDescriptor, string version);

    bool ApplyCustomCodeDst(byte[] content, ReferenceFileVersion mapDescriptor, CustomCodeContext moduleInfo, string version);

    bool ApplyCustomCodePst(byte[] data, ReferenceFileVersion mapDescriptor, CustomCodeContext moduleInfo, string version);
}