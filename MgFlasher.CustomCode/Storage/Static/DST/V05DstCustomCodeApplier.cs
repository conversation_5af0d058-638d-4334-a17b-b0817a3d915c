﻿using MgFlasher.CustomCode.Extensions;
using MgFlasher.CustomCode.Models;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Storage.Static.DST;

public class V05DstCustomCodeApplier : IDstCustomCodeApplier
{
    private readonly ILogger<V05DstCustomCodeApplier> _logger;

    public V05DstCustomCodeApplier(ILogger<V05DstCustomCodeApplier> logger)
    {
        _logger = logger;
    }

    public bool Apply(byte[] data, ReferenceFileVersion info, CustomCodeContext moduleInfo)
    {
        var version = CustomCodeVersions.V05;

        _logger.LogInformation("nothing to apply for custom code {Version}. Current swfl 0x{S} major: {PstMajor}, minor: {PstMinor}, patch: {PstPatch}.", version, info.Pst.Id.ToString("X4"), info.Pst.Major, info.Pst.Minor, info.Pst.Patch);
        return true;
    }
}