﻿using MgFlasher.CustomCode.Extensions;
using MgFlasher.CustomCode.Models;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Storage.Static.DST;

public class V000DstCodeWordsApplier : IDstCodeWordsApplier
{
    private readonly ILogger<V000DstCodeWordsApplier> _logger;

    public V000DstCodeWordsApplier(ILogger<V000DstCodeWordsApplier> logger)
    {
        _logger = logger;
    }

    public bool Apply(byte[] data, ReferenceFileVersion info)
    {
        _logger.LogInformation("ApplyCodeWordsDst(): applying code words to DST...");

        var version = CustomCodeVersions.V000;

        _logger.LogInformation("nothing to apply for custom code {Version}. Current swfl 0x{S} major: {PstMajor}, minor: {PstMinor}, patch: {PstPatch}.", version, info.Pst.Id.ToString("X4"), info.Pst.Major, info.Pst.Minor, info.Pst.Patch);
        return true;
    }
}