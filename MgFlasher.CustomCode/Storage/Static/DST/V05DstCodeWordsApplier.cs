﻿using MgFlasher.CustomCode.Extensions;
using MgFlasher.CustomCode.Models;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Storage.Static.DST;

public class V05DstCodeWordsApplier : IDstCodeWordsApplier
{
    private readonly ILogger<V05DstCodeWordsApplier> _logger;

    public V05DstCodeWordsApplier(ILogger<V05DstCodeWordsApplier> logger)
    {
        _logger = logger;
    }

    public bool Apply(byte[] data, ReferenceFileVersion info)
    {
        _logger.LogInformation("ApplyCodeWordsDst(): applying code words to DST (TCU Limiter)...");
        byte _tcuLimiterState = 0x00;
        _logger.LogInformation("TCU Limiter: {_tcuLimiterState}, will be overwritten if applying custom options (toggles)", _tcuLimiterState);

        var pstId = info.Pst.Id;
        var version = CustomCodeVersions.V05;

        // Any Aurix
        if (pstId == 0x3E7D || pstId == 0x5D55 || pstId == 0x4ACF || pstId == 0x5C64 || pstId == 0x78F7 || pstId == 0x4963)
        {
            _logger.LogInformation("applying {Version} for swfl 0x{S} major: {PstMajor}, minor: {PstMinor}, patch: {PstPatch}...", version, pstId.ToString("X4"), info.Pst.Major, info.Pst.Minor, info.Pst.Patch);

            data.ApplyValues(new object[] { new object[] { 0x7FF7D4u, 0x7FF7D4u, new byte[] { _tcuLimiterState } } });
            return true;
        }
        // Any PPC
        else if (pstId == 0x3076 || pstId == 0x3081 || pstId == 0x7972)
        {
            _logger.LogInformation("applying {Version} for swfl 0x{S} major: {PstMajor}, minor: {PstMinor}, patch: {PstPatch}...", version, pstId.ToString("X4"), info.Pst.Major, info.Pst.Minor, info.Pst.Patch);

            data.ApplyValues(new object[] { new object[] { 0x77F7C2u, 0x77F7C2u, new byte[] { _tcuLimiterState } } });
            return true;
        }
        else
        {
            _logger.LogInformation("not applying (swfl not matching). Current swfl 0x{S} major: {PstMajor}, minor: {PstMinor}, patch: {PstPatch}.", pstId.ToString("X4"), info.Pst.Major, info.Pst.Minor, info.Pst.Patch);
            return true;
        }
    }
}