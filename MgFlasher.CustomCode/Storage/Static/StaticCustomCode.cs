﻿using System.Collections.Generic;
using System.Threading.Tasks;
using MgFlasher.CustomCode.Models;
using MgFlasher.CustomCode.Storage.Changesets;

namespace MgFlasher.CustomCode.Storage.Static;

public class StaticCustomCode : ICustomCode
{
    private readonly IStaticCustomCodeApplier _customCodeApplier;

    public CustomCodeId Version { get; }
    public ReferenceFileVersion MapDescriptor { get; }
    public string ReleaseNotes => GetReleaseNotes();
    public string ReleasedAtUtc => null;
    public bool CanSelect { get; set; } = true;
    public CustomCodeUnavailableReasonEnum? UnavailableReason { get; set; }

    public StaticCustomCode(CustomCodeId version, ReferenceFileVersion mapDescriptor, IStaticCustomCodeApplier customCodeApplier)
    {
        Version = version;
        MapDescriptor = mapDescriptor;
        _customCodeApplier = customCodeApplier;
    }

    public Task<CustomCodeMapBuilderResult> ApplyAsync(byte[] content, CustomCodeContext context, CustomCodeDevelopmentSwitches switches)
    {
        var issues = new List<string>();

        if (switches.WithPstChanges && !_customCodeApplier.ApplyCustomCodePst(content, MapDescriptor, context, Version.Version))
        {
            issues.Add("Cannot apply pst custom code");
        }

        if (switches.WithCustomCodeInitialValues && !_customCodeApplier.ApplyCustomCodeDst(content, MapDescriptor, context, Version.Version))
        {
            issues.Add("Cannot apply dst custom code");
        }

        if (switches.WithCustomCodeInitialValues && !_customCodeApplier.ApplyCodeWordsDst(content, MapDescriptor, Version.Version))
        {
            issues.Add("Cannot apply dst code words");
        }

        return Task.FromResult(new CustomCodeMapBuilderResult()
        {
            Success = issues.Count == 0,
            Content = content,
            Issues = issues,
            Version = Version
        });
    }

    private string GetReleaseNotes() => Version.Version switch
    {
        _ when Version.Version.StartsWith("v0.0") => "No custom code, effectively, stock functionality",
        _ when Version.Version.StartsWith("v0.") => "TCU limiter bypass only",
        _ when Version.Version.StartsWith("v3.") || Version.Version.StartsWith("v5.") => "Use pedals, MSA and dome light",
        _ when Version.Version.StartsWith("v6.") => "Use cruise control & BC button",
        _ => string.Empty
    };
}