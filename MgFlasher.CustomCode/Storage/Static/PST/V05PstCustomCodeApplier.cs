﻿using MgFlasher.CustomCode.Extensions;
using MgFlasher.CustomCode.Models;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Storage.Static.PST;

public class V05PstCustomCodeApplier : PstCustomCodeApplierHelper, IPstCustomCodeApplier
{
    private readonly ILogger<V05PstCustomCodeApplier> _logger;

    public V05PstCustomCodeApplier(ILogger<V05PstCustomCodeApplier> logger) : base(logger)
    {
        _logger = logger;
    }

    public bool Apply(byte[] data, SweVersion psdDescriptor, CustomCodeContext moduleInfo)
    {
        var pstId = psdDescriptor.Id;
        var pstMajor = psdDescriptor.Major;
        var pstMinor = psdDescriptor.Minor;
        var pstPatch = psdDescriptor.Patch;

        var version = CustomCodeVersions.V05;

        // Any Aurix
        if (pstId == 0x3E7D || pstId == 0x5D55 || pstId == 0x4ACF || pstId == 0x5C64 || pstId == 0x78F7 || pstId == 0x4963)
        {
            _logger.LogInformation("applying {version} for swfl 0x{PstFileId} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", version, pstId.ToString("X4"), pstMajor, pstMinor, pstPatch);
            var tcuLimiterPatchApplied = ApplyTcuLimiterRemoval(data, moduleInfo);
            var remoteStarterPatchApplied = pstId != 0x3E7D ? ApplyRemoteStarterPatch(data, moduleInfo) : true;
            if (tcuLimiterPatchApplied && remoteStarterPatchApplied)
            {
                return true;
            }
        }
        // Any PPC
        else if (pstId == 0x3076 || pstId == 0x3081 || pstId == 0x7972)
        {
            _logger.LogInformation("applying {version} for swfl 0x{PstFileId} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}...", version, pstId.ToString("X4"), pstMajor, pstMinor, pstPatch);
            var tcuLimiterPatchApplied = ApplyTcuLimiterRemoval(data, moduleInfo);
            if (tcuLimiterPatchApplied)
            {
                return true;
            }
        }
        else
        {
            _logger.LogInformation("not applying (swfl not matching). Current swfl 0x{PstFileId} major: {PstFile5}, minor: {PstFile6}, patch: {PstFile7}.", pstId.ToString("X4"), pstMajor, pstMinor, pstPatch);
            return true;
        }

        return false;
    }
}