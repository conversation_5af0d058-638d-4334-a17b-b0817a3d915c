﻿using System;
using System.Collections.Generic;
using System.Linq;
using MgFlasher.CustomCode.Models;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Storage.Static.PST;

public abstract class PstCustomCodeApplierHelper
{
    private readonly ILogger _logger;

    public PstCustomCodeApplierHelper(ILogger logger)
    {
        _logger = logger;
    }

    protected bool ApplyCanBusHardwareHandleTableRelocationAndExtension(byte[] data, CustomCodeContext moduleInfo, List<CanBusHardwareHandle> additionalCanBusHardwareHandles)
    {
        try
        {
            /*
             * Provided we have a unique search string, find:
             *  - start address of hardware handles
             *  - number of items for rx and tx
             * Validate the table contains this many of each item
             * Extend hardware handle  table to include new rx and tx requested from within the app
             * Change the start address to the new address
             * Increment number of rx and tx items in our table
             * Copy hardware handle table to the new address, either PST location: 0x0967D000 (preferred) or DST location: 0x0977A000
             */

            _logger.LogInformation("Applying can bus hardware handle table relocation and extension...");

            uint ecuArchitectureOffset = 0x00000000;
            uint customCodev60_TableStartAddress = 0x0967D000; // PST location: 0x0967D000, DST location: 0x0977A000
            var txCountOffset = 92; // 92 is an offset to reach the rba_Can_PB_Configuration_cst_0 tx count
            var rxCountOffset = 94; // 94 is an offset to reach the rba_Can_PB_Configuration_cst_0 rx count
            uint[] PST_address = { 0x00, 0x00 };
            var canHardwareHandleTableSearchString = new byte[] { };
            var searchMask = new byte[] { };

            var templateHandleDescription = new byte[] { };
            if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
            {
                // PPC
                ecuArchitectureOffset = 0x09000000;
                PST_address[0] = 0x40100;
                PST_address[1] = 0x67FFFF;

                canHardwareHandleTableSearchString = new byte[] { 0x09, 0x0B, 0x4C, 0x80, // Original Table Start address in example file
                    0x00, 0x0F, 0x42, 0x40,
                    0x00, 0x01, 0x0F, 0x22,
                    0x00, 0x0F, 0x42, 0x40, };

                searchMask = new byte[] {  0xFF, 0x00, 0x00, 0x00,
                    0xFF, 0xFF, 0xFF, 0xFF,
                    0xFF, 0xFF, 0xFF, 0xFF,
                    0xFF, 0xFF, 0xFF, 0xFF, };

                templateHandleDescription = new byte[] { 0x00, 0xD9,              // Id_uo
                    0x00, 0x00,              // null
                    0x00, 0x00, 0x7F, 0xFF,  // AcceptanceMask_u32
                    0x00,                    // Interrupt_b
                    0x00,                    // Direction_u8
                    0x02,                    // Node_u8
                    0x00,                    // Idtype_u8
                    0x00, 0x0A,              // CanIfHrhId_uo
                    0x06,                    // Buffer_u8
                    0x00,                    // null
                    0x00, 0x00,              // Handle_uo
                    0x00, 0x00,              // null
                };
            }
            else if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
            {
                // AURIX
                ecuArchitectureOffset = 0x80000000;
                PST_address[0] = 0x80100;
                PST_address[1] = 0x6FFFFF;
                _logger.LogInformation("ApplyCanBusHardwareHandleTableRelocationAndExtension() NOT SUPPORTED ON AURIX YET!");
                return false;
            }

            // search pst region for 'canHardwareHandleTableSearchString' string using 'searchMask'
            if (PST_address[1] - PST_address[0] > canHardwareHandleTableSearchString.Length)
            {
                _logger.LogInformation("Searching for canHardwareHandleTableSearchString match in range 0x{0:X08} to 0x{1:X08}", PST_address[0], PST_address[1]);
                int matchLocation = -1;

                for (int t = 0; t < PST_address[1] - PST_address[0]; t++)
                {
                    bool fullmatch = true;
                    for (int z = 0; z < canHardwareHandleTableSearchString.Length; z++)
                    {
                        if (data[PST_address[0] + t + z] != canHardwareHandleTableSearchString[z] && searchMask[z] == 0xFF)
                        {
                            fullmatch = false;
                        }
                    }

                    if (fullmatch == true)
                    {
                        matchLocation = t;
                        break;
                    }
                }

                if (matchLocation != -1)
                {
                    _logger.LogInformation("Match found @ 0x{0:X08}!", PST_address[0] + matchLocation);
                }
                else
                {
                    _logger.LogInformation("No match found, aborting!");
                    return false;
                }

                // Start address of hardware handle table
                uint canHardwareHandleTableStartAddress_Original = BitConverter.ToUInt32(data, (int)PST_address[0] + matchLocation);
                if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                {
                    var addressAsByteArray = BitConverter.GetBytes(canHardwareHandleTableStartAddress_Original);
                    Array.Reverse(addressAsByteArray);
                    canHardwareHandleTableStartAddress_Original = BitConverter.ToUInt32(addressAsByteArray);
                }

                _logger.LogInformation("Original start address of hardware handle table [0x{0}]", canHardwareHandleTableStartAddress_Original.ToString("X8"));

                // Number of items for rx and tx
                var numberOfRHandles_Original = BitConverter.ToUInt16(data, (int)PST_address[0] + matchLocation + rxCountOffset);
                if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                {
                    var valueAsByteArray = BitConverter.GetBytes(numberOfRHandles_Original);
                    Array.Reverse(valueAsByteArray);
                    numberOfRHandles_Original = BitConverter.ToUInt16(valueAsByteArray);
                }

                var numberOfTxHandles_Original = BitConverter.ToUInt16(data, (int)PST_address[0] + matchLocation + txCountOffset);
                if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                {
                    var valueAsByteArray = BitConverter.GetBytes(numberOfTxHandles_Original);
                    Array.Reverse(valueAsByteArray);
                    numberOfTxHandles_Original = BitConverter.ToUInt16(valueAsByteArray);
                }

                _logger.LogInformation("Found [{0}] rx handles and [{1}] tx handles", numberOfRHandles_Original, numberOfTxHandles_Original);

                // Read entire table and form a List<CanBusHardwareHandle> of original items, extract info from there for validation
                var originalCanBusHardwareHandles = new List<CanBusHardwareHandle> { };
                for (int t = 0; t < numberOfRHandles_Original + numberOfTxHandles_Original + 5; t++)
                {
                    var handle = new CanBusHardwareHandle();
                    var baseAddressForNextHandle = (int)canHardwareHandleTableStartAddress_Original - ecuArchitectureOffset + originalCanBusHardwareHandles.Count * CanBusHardwareHandle.HandleDescriptionSize;

                    // Grab handle information
                    handle.Id_uo = BitConverter.ToInt16(data, (int)baseAddressForNextHandle + CanBusHardwareHandle.Id_uo_ArrayPosition);
                    if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                    {
                        var valueAsByteArray = BitConverter.GetBytes(handle.Id_uo);
                        Array.Reverse(valueAsByteArray);
                        handle.Id_uo = BitConverter.ToInt16(valueAsByteArray);
                    }

                    handle.AcceptanceMask_u32 = BitConverter.ToUInt32(data, (int)baseAddressForNextHandle + CanBusHardwareHandle.AcceptanceMask_u32_ArrayPosition);
                    if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                    {
                        var valueAsByteArray = BitConverter.GetBytes(handle.AcceptanceMask_u32);
                        Array.Reverse(valueAsByteArray);
                        handle.AcceptanceMask_u32 = BitConverter.ToUInt32(valueAsByteArray);
                    }

                    handle.Interrupt_b = data[baseAddressForNextHandle + CanBusHardwareHandle.Interrupt_b_ArrayPosition];
                    handle.Direction_u8 = data[baseAddressForNextHandle + CanBusHardwareHandle.Direction_u8_ArrayPosition];
                    handle.Node_u8 = data[baseAddressForNextHandle + CanBusHardwareHandle.Node_u8_ArrayPosition];
                    handle.Idtype_u8 = data[baseAddressForNextHandle + CanBusHardwareHandle.Idtype_u8_ArrayPosition];
                    handle.CanIfHrhId_uo = BitConverter.ToInt16(data, (int)baseAddressForNextHandle + CanBusHardwareHandle.CanIfHrhId_uo_ArrayPosition);
                    if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                    {
                        var valueAsByteArray = BitConverter.GetBytes(handle.CanIfHrhId_uo);
                        Array.Reverse(valueAsByteArray);
                        handle.CanIfHrhId_uo = BitConverter.ToInt16(valueAsByteArray);
                    }

                    handle.Buffer_u8 = data[baseAddressForNextHandle + CanBusHardwareHandle.Buffer_u8_ArrayPosition];
                    handle.Handle_uo = BitConverter.ToInt16(data, (int)baseAddressForNextHandle + CanBusHardwareHandle.Handle_uo_ArrayPosition);
                    if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                    {
                        var valueAsByteArray = BitConverter.GetBytes(handle.Handle_uo);
                        Array.Reverse(valueAsByteArray);
                        handle.Handle_uo = BitConverter.ToInt16(valueAsByteArray);
                    }

                    // Check if valid
                    if (handle.Handle_uo > numberOfRHandles_Original + numberOfTxHandles_Original)
                    {
                        throw new CustomCodeException("CustomCodeData_TooManyCanBusHandles");
                    }

                    if (handle.Handle_uo < originalCanBusHardwareHandles.Count)
                    {
                        break;
                    }

                    originalCanBusHardwareHandles.Add(handle);
                }

                _logger.LogInformation("Found [{0}] original handles", originalCanBusHardwareHandles.Count);

                if (originalCanBusHardwareHandles.Where(h => h.Direction_u8 == CanBusHardwareHandle.RxDirection_u8).Count() != numberOfRHandles_Original)
                {
                    throw new CustomCodeException("CustomCodeData_IncorrectNumberOfRxHandles");
                }

                if (originalCanBusHardwareHandles.Where(h => h.Direction_u8 == CanBusHardwareHandle.TxDirection_u8).Count() != numberOfTxHandles_Original)
                {
                    throw new CustomCodeException("CustomCodeData_IncorrectNumberOfTxHandles");
                }

                // Merge additionalCanBusHardwareHandles with originalCanBusHardwareHandles before copying over to new location
                var newCanBusHardwareHandles = originalCanBusHardwareHandles.Concat(additionalCanBusHardwareHandles.Where(ah => originalCanBusHardwareHandles.All(oh => oh.Id_uo != ah.Id_uo))).ToList();

                // Move hardware handle table to new location
                var rxHandlesAdded = 0;
                var txHandlesAdded = 0;
                foreach (var handle in newCanBusHardwareHandles)
                {
                    templateHandleDescription[0] = (byte)(handle.Id_uo >> 8);
                    templateHandleDescription[1] = (byte)handle.Id_uo;
                    templateHandleDescription[2] = 0x00;
                    templateHandleDescription[3] = 0x00;
                    templateHandleDescription[4] = (byte)(handle.AcceptanceMask_u32 >> 24);
                    templateHandleDescription[5] = (byte)(handle.AcceptanceMask_u32 >> 16);
                    templateHandleDescription[6] = (byte)(handle.AcceptanceMask_u32 >> 8);
                    templateHandleDescription[7] = (byte)handle.AcceptanceMask_u32;
                    templateHandleDescription[8] = handle.Interrupt_b;
                    templateHandleDescription[9] = handle.Direction_u8;
                    templateHandleDescription[10] = handle.Node_u8;
                    templateHandleDescription[11] = handle.Idtype_u8;
                    if (handle.Direction_u8 == CanBusHardwareHandle.TxDirection_u8)
                    {
                        templateHandleDescription[12] = (byte)(handle.CanIfHrhId_uo >> 8);
                        templateHandleDescription[13] = (byte)handle.CanIfHrhId_uo;
                    }

                    if (handle.Direction_u8 == CanBusHardwareHandle.RxDirection_u8)
                    {
                        templateHandleDescription[12] = (byte)(rxHandlesAdded >> 8);
                        templateHandleDescription[13] = (byte)rxHandlesAdded;
                    }

                    templateHandleDescription[14] = handle.Buffer_u8;
                    templateHandleDescription[15] = 0x00;
                    templateHandleDescription[16] = (byte)(rxHandlesAdded + txHandlesAdded >> 8); // incremented in this function
                    templateHandleDescription[17] = (byte)(rxHandlesAdded + txHandlesAdded); // incremented in this function
                    templateHandleDescription[18] = 0x00;
                    templateHandleDescription[19] = 0x00;

                    var templateHandleDescription_StartAddress = customCodev60_TableStartAddress - ecuArchitectureOffset + (rxHandlesAdded + txHandlesAdded) * templateHandleDescription.Length;
                    for (int t = 0; t < templateHandleDescription.Length; t++)
                    {
                        data[templateHandleDescription_StartAddress + t] = templateHandleDescription[t];
                    }

                    if (handle.Direction_u8 == CanBusHardwareHandle.RxDirection_u8)
                    {
                        rxHandlesAdded++;
                    }

                    if (handle.Direction_u8 == CanBusHardwareHandle.TxDirection_u8)
                    {
                        txHandlesAdded++;
                    }
                }
                _logger.LogInformation("[{0}] handles have been added at 0x{1}", rxHandlesAdded + txHandlesAdded, customCodev60_TableStartAddress.ToString("X8"));

                // Change the start address to the new address
                var customCodev60_TableStartAddress_ByteArray = BitConverter.GetBytes(customCodev60_TableStartAddress);
                Array.Reverse(customCodev60_TableStartAddress_ByteArray);
                for (int t = 0; t < customCodev60_TableStartAddress_ByteArray.Length; t++)
                {
                    data[PST_address[0] + matchLocation + t] = customCodev60_TableStartAddress_ByteArray[t];
                }

                _logger.LogInformation("Updated start address of can hardware handles table to 0x{0}", customCodev60_TableStartAddress.ToString("X8"));

                // Increment number of rx items in our table
                var extendedNumberOfRxHandles = (short)newCanBusHardwareHandles.Where(i => i.Direction_u8 == CanBusHardwareHandle.RxDirection_u8).Count();
                var extendedNumberOfRxHandles_ByteArray = BitConverter.GetBytes(extendedNumberOfRxHandles);
                Array.Reverse(extendedNumberOfRxHandles_ByteArray);
                for (int t = 0; t < extendedNumberOfRxHandles_ByteArray.Length; t++)
                {
                    data[PST_address[0] + matchLocation + rxCountOffset + t] = extendedNumberOfRxHandles_ByteArray[t];
                }

                _logger.LogInformation("Updated number of rx items to [{0}] at [0x{1}]", extendedNumberOfRxHandles, (PST_address[0] + matchLocation + rxCountOffset).ToString("X8"));

                // Increment number of tx items in our table
                var extendedNumberOfTxHandles = (short)newCanBusHardwareHandles.Where(i => i.Direction_u8 == CanBusHardwareHandle.TxDirection_u8).Count();
                var extendedNumberOfTxHandles_ByteArray = BitConverter.GetBytes(extendedNumberOfTxHandles);
                Array.Reverse(extendedNumberOfTxHandles_ByteArray);
                for (int t = 0; t < extendedNumberOfTxHandles_ByteArray.Length; t++)
                {
                    data[PST_address[0] + matchLocation + txCountOffset + t] = extendedNumberOfTxHandles_ByteArray[t];
                }

                _logger.LogInformation("Updated number of tx items to [{0}] at [0x{1}]", extendedNumberOfTxHandles, (PST_address[0] + matchLocation + txCountOffset).ToString("X8"));

                _logger.LogInformation("Done!");
                return true;
            }
            else
            {
                _logger.LogInformation("File range invalid!");
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogInformation($"Failed to apply CAN BUS ID table changes: [{ex.Message}]");
            return false;
        }
    }

    protected bool ApplyRemoteStarterPatch(byte[] data, CustomCodeContext moduleInfo)
    {
        try
        {
            _logger.LogInformation("Applying Remote Starter patch...");

            uint[] PST_address = { 0x00, 0x00 };
            var remoteStarterPatch = new byte[] { };
            var searchMask = new byte[] { };
            var patchMask = new byte[] { };
            if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
            {
                _logger.LogInformation("ApplyRemoteStarterPatch() NOT SUPPORTED (or required) ON PPC!");
                return false;
            }
            else if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
            {
                PST_address[0] = 0x80100;
                PST_address[1] = 0x6FFFFF;

                remoteStarterPatch = new byte[] { 0x82, 0x1F, 0xDF, 0x22, 0x00, 0x00, 0x82, 0x1F, 0x91, 0x00, 0x00, 0x00, 0xD9, };
                searchMask = new byte[] { 0xFF, 0x0F, 0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0xFF, };
                patchMask = new byte[] { 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };
            }
            else
            {
                _logger.LogInformation("Invalid processor!");
                return false;
            }

            // search pst region for 'remoteStarterPatch' string using 'searchMask' and apply using 'patchMask'
            if (PST_address[1] - PST_address[0] > remoteStarterPatch.Length)
            {
                _logger.LogInformation("searching for remoteStarter match in range 0x{0:X08} to 0x{1:X08}", PST_address[0], PST_address[1]);
                int matchLocation = -1;

                for (int t = 0; t < PST_address[1] - PST_address[0]; t++)
                {
                    var fullmatch = true;
                    for (int z = 0; z < remoteStarterPatch.Length; z++)
                    {
                        if ((data[PST_address[0] + t + z] & searchMask[z]) != (remoteStarterPatch[z] & searchMask[z]))
                        {
                            fullmatch = false;
                        }
                    }

                    if (fullmatch == true)
                    {
                        matchLocation = t; break;
                    }
                }

                if (matchLocation != -1)
                {
                    var dataFound = new byte[remoteStarterPatch.Length];
                    Array.Copy(data, PST_address[0] + matchLocation, dataFound, 0x00, dataFound.Length);
                    _logger.LogInformation("match found @ 0x{0:X08}: [0x{1}],\n patching...", PST_address[0] + matchLocation, BitConverter.ToString(dataFound).Replace("-", ", 0x"));
                }
                else
                {
                    _logger.LogInformation("no match found, aborting!");
                    return false;
                }

                for (int t = 0; t < remoteStarterPatch.Length; t++)
                {
                    if (patchMask[t] == 0xFF)
                    {
                        data[PST_address[0] + matchLocation + t] = remoteStarterPatch[t];
                    }
                }

                var dataPatched = new byte[remoteStarterPatch.Length];
                Array.Copy(data, PST_address[0] + matchLocation, dataPatched, 0x00, dataPatched.Length);
                _logger.LogInformation("patched {0} bytes @ 0x{1:X08}: [0x{2}],\n done!", remoteStarterPatch.Length, PST_address[0] + matchLocation, BitConverter.ToString(dataPatched).Replace("-", ", 0x"));
                return true;
            }
            else
            {
                _logger.LogInformation("file range invalid!");
                _logger.LogInformation("failed to apply PST patch for Remote Starter!");
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogInformation($"Failed to apply remote starter patch: [{ex.Message}]");
            return false;
        }
    }

    protected byte[] CalculateJump(uint startAddress, uint targetAddress, ProcessorArchitectureType processorArchitectureType)
    {
        try
        {
            var returnArray = new byte[4];

            if (processorArchitectureType.Equals(ProcessorArchitectureType.PPC))
            {
                var difference = targetAddress - startAddress;
                returnArray[0] = (byte)(startAddress > targetAddress ? 0x79 : 0x78);
                returnArray[1] = (byte)((difference & 0xFF0000) >> 16);
                returnArray[2] = (byte)((difference & 0x00FF00) >> 8);
                returnArray[3] = (byte)(difference & 0xFF);
            }
            else if (processorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
            {
                returnArray[0] = 0x1D;
                var mul_div1 = 0x200000;
                var mul_div2 = 0x20000;
                var mul_div3 = 0x2000;
                var mul_div4 = 0x200;
                var mul_div5 = 0x20;
                var mul_div6 = 0x02;

                var difference_begin = targetAddress - startAddress;

                var result_a = difference_begin / mul_div1;
                var difference = difference_begin - mul_div1 * result_a;

                var result_b = difference / mul_div2;
                difference = difference - mul_div2 * result_b;

                returnArray[1] = (byte)(result_a * 0x10 + result_b & 0x00FF); //important for overflow reasons (can probably be written differently in c#)

                var result_c = difference / mul_div3;
                difference = difference - mul_div3 * result_c;

                var result_d = difference / mul_div4;
                difference = difference - mul_div4 * result_d;

                returnArray[3] = (byte)(result_c * 0x10 + result_d);

                var result_e = difference / mul_div5;
                difference = difference - mul_div5 * result_e;

                var result_f = difference / mul_div6;

                returnArray[2] = (byte)(result_e * 0x10 + result_f);
            }
            else
            {
                throw new Exception("Invalid processor!");
            }

            return returnArray;
        }
        catch (Exception ex)
        {
            _logger.LogInformation($"Failed to calculate jump points: [{ex.Message}]");
            return new byte[] { };
        }
    }

    protected bool ApplyTcuLimiterRemoval(byte[] data, CustomCodeContext moduleInfo)
    {
        try
        {
            _logger.LogInformation("Applying TCU limit removal patch...");

            // Pseudo code on ECU:
            //if (codword == true && BrakePedalIsNOTDetected) // B_bl_msg != 1
            //{
            //    mdkmaxgs_w_msg = 1022 Nm; // aka no limit
            //}
            //else
            //{
            //    mdkmaxgs_w_msg = Com_Rx_LimTorqCrshGrbProte; // OEM functionality
            //}

            uint[] PST_address = { 0x00, 0x00 };

            var tcuLimitOriginal = new byte[] { };
            var searchMask = new byte[] { };
            var tcuLimitPatch = new byte[] { };
            var occurances_tcuLimitOriginal = 0;

            var torqueLimiterCodeStartAddress = (uint)0x00;

            if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC))
            {
                // PPC
                torqueLimiterCodeStartAddress = 0x67F000;

                PST_address[0] = 0x40100;
                PST_address[1] = 0x67FFFF;
                tcuLimitOriginal = new byte[] {
                    0x30, 0x10, 0x00, 0x00,
                    0x65, 0x2C, 0x22, 0xD0,
                    0x7A, 0x00, 0x00, 0x00,
                    0x34, 0x02, 0x00, 0x00,
                    0x01, 0xC4, 0x48, 0x0D,
                    0x70, 0xE1, 0x00, 0x00,
                    0x70, 0xA8, 0x00, 0x00,
                    0x58, 0x67, 0x00, 0x00,
                    0x1C, 0xA5, 0x00, 0x00,
                    0x79, 0x00, 0x00, 0x00
                };
                searchMask = new byte[] {
                    0xFF, 0xFF, 0x00, 0x00,
                    0xFF, 0xFF, 0xFF, 0xFF,
                    0xFF, 0x00, 0x00, 0x00,
                    0xFF, 0xFF, 0x00, 0x00,
                    0xFF, 0xFF, 0xFF, 0xFF,
                    0xFF, 0xFF, 0x00, 0x00,
                    0xFF, 0xFF, 0x00, 0x00,
                    0xFF, 0xFF, 0x00, 0x00,
                    0xFF, 0xFF, 0x00, 0x00,
                    0xFF, 0x00, 0x00, 0x00
                };
                tcuLimitPatch = new byte[]  {
                    0x70, 0xE1, 0xE1, 0x77,
                    0x70, 0xFE, 0xC7, 0xC2,
                    0x80, 0x77,
                    0x2A, 0x37,
                    0x7A, 0x02, 0x00, 0x14,
                    0x30, 0xF0, 0x69, 0x69, //these two 0x69 bytes need to be overwritten with specific bytes of the hit/address find of B_bl_msg search string
                    0x2A, 0x17,
                    0xE6, 0x05,
                    0x70, 0x01, 0x07, 0xFC,
                    0x78, 0x00, 0x00, 0x08,
                };
                occurances_tcuLimitOriginal = 1;

                #region B_bl_msg

                var searchPattern_B_bl_msg = new byte[] {
                    0x30, 0x00, 0x00, 0x00,
                    0x7D, 0x00, 0x00, 0x00,
                    0x6C, 0x00, 0x71, 0x00,
                    0xCF, 0xF3
                };
                var searchMask_B_bl_msg = new byte[] {
                    0xFF, 0x00, 0x00, 0x00,
                    0xFF, 0x00, 0x00, 0x00,
                    0xFF, 0x00, 0xFF, 0x00,
                    0xFF, 0xFF
                };
                var occurances_B_bl_msg = 1;

                var searchResults_B_bl_msg = SearchMaskedPatternInByteArray(data, PST_address[0], PST_address[1], pattern: searchPattern_B_bl_msg, searchMask: searchMask_B_bl_msg, requiredOccurances: occurances_B_bl_msg, itemName: "B_bl_msg");
                if (searchResults_B_bl_msg.Count == 0)
                {
                    _logger.LogInformation("Failed to find B_bl_msg code!");
                    return false;
                }
                var b_bl_msg_address = searchResults_B_bl_msg.FirstOrDefault();

                tcuLimitPatch[18] = data[b_bl_msg_address + 2];
                tcuLimitPatch[19] = data[b_bl_msg_address + 3];
                _logger.LogInformation($"Patched data for B_bl_msg: 3rd byte = [0x{tcuLimitPatch[18]:X02}] and 4th byte = [0x{tcuLimitPatch[19]:X02}]");

                #endregion B_bl_msg
            }
            else if (moduleInfo.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
            {
                // AURIX
                torqueLimiterCodeStartAddress = 0x1DFFA0;

                PST_address[0] = 0x80100;
                PST_address[1] = 0x6FFFFF;
                tcuLimitOriginal = new byte[] {
                    0x8B, 0xF2, 0x20, 0x22,
                    0xF6, 0x25, 0xB7, 0xF5,
                    0x01, 0x58, 0x3B, 0x00,
                    0x04, 0x80, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00,
                    0xDF, 0x09, 0x13, 0x00
                };
                searchMask = new byte[] {
                    0xFF, 0xFF, 0xFF, 0xFF,
                    0xFF, 0xFF, 0xFF, 0xFF,
                    0xFF, 0xFF, 0xFF, 0xFF,
                    0xFF, 0xFF, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00,
                    0xFF, 0xFF, 0xFF, 0xFF,
                };
                tcuLimitPatch = new byte[]  {
                    0x91, 0x00, 0x08, 0xF8,
                    0xD9, 0xFF, 0x54, 0xFF,
                    0x0C, 0xF0, 0x5E, 0x37,
                    0x05, 0x6F, 0x69, 0x69, //these two 0x69 bytes need to be overwritten with specific bytes of the hit/address find of B_bl_msg search string
                    0x1E, 0x14, 0x3B, 0xC0,
                    0xFF, 0x20, 0x3C, 0x03
                };
                occurances_tcuLimitOriginal = 1;

                #region B_bl_msg

                var searchPattern_B_bl_msg = new byte[] {
                    0x05, 0x60, 0x00, 0x00,
                    0x80, 0x02, 0x00, 0x00,
                    0x06, 0x00, 0xA6, 0x00,
                    0x39
                };
                var searchMask_B_bl_msg = new byte[] {
                    0xFF, 0xF0, 0x00, 0x00,
                    0xF0, 0x0F, 0x00, 0x00,
                    0xFF, 0x00, 0xFF, 0x00,
                    0xFF
                };
                var occurances_B_bl_msg = 1;

                var searchResults_B_bl_msg = SearchMaskedPatternInByteArray(data, PST_address[0], PST_address[1], pattern: searchPattern_B_bl_msg, searchMask: searchMask_B_bl_msg, requiredOccurances: occurances_B_bl_msg, itemName: "B_bl_msg");
                if (searchResults_B_bl_msg.Count == 0)
                {
                    _logger.LogInformation("Failed to find B_bl_msg code!");
                    return false;
                }

                var b_bl_msg_address = searchResults_B_bl_msg.FirstOrDefault();

                tcuLimitPatch[14] = data[b_bl_msg_address + 2];
                tcuLimitPatch[15] = data[b_bl_msg_address + 3];
                _logger.LogInformation($"Patched data for B_bl_msg: 3rd byte = [0x{tcuLimitPatch[14]:X02}] and 4th byte = [0x{tcuLimitPatch[15]:X02}]");

                #endregion B_bl_msg
            }
            else
            {
                _logger.LogInformation("Invalid processor!");
                return false;
            }

            var searchResults_Com_Rx_LimTorqCrshGrbProte = SearchMaskedPatternInByteArray(data, PST_address[0], PST_address[1], pattern: tcuLimitOriginal, searchMask: searchMask, requiredOccurances: occurances_tcuLimitOriginal, itemName: "tcuLimitOriginal");
            if (searchResults_Com_Rx_LimTorqCrshGrbProte.Count == 0)
            {
                _logger.LogInformation("Failed to find Com_Rx_LimTorqCrshGrbProte code!");
                return false;
            }

            var location_Com_Rx_LimTorqCrshGrbProte = searchResults_Com_Rx_LimTorqCrshGrbProte.FirstOrDefault();

            var surroundingBytes = new byte[4];
            Array.Copy(data, location_Com_Rx_LimTorqCrshGrbProte + tcuLimitOriginal.Length, surroundingBytes, 0, surroundingBytes.Length);

            var calculatedJumpBack = CalculateJump(
                startAddress: (uint)(torqueLimiterCodeStartAddress + surroundingBytes.Length + tcuLimitPatch.Length),
                targetAddress: (uint)(location_Com_Rx_LimTorqCrshGrbProte + tcuLimitOriginal.Length),
                processorArchitectureType: moduleInfo.ProcessorArchitectureType
            );

            if (calculatedJumpBack == null || calculatedJumpBack.Length != 4)
            {
                throw new Exception("Failed to calculate jump back!");
            }

            var calculatedJumpTo = CalculateJump(
                startAddress: (uint)(location_Com_Rx_LimTorqCrshGrbProte + tcuLimitOriginal.Length),
                targetAddress: torqueLimiterCodeStartAddress,
                processorArchitectureType: moduleInfo.ProcessorArchitectureType
            );

            if (calculatedJumpTo == null || calculatedJumpTo.Length != 4)
            {
                throw new Exception("Failed to calculate jump to!");
            }

            var offset = location_Com_Rx_LimTorqCrshGrbProte + (uint)tcuLimitOriginal.Length;
            Array.Copy(calculatedJumpTo, 0, data, offset, calculatedJumpTo.Length);

            var codeSection = surroundingBytes
                .Concat(tcuLimitPatch)
                .Concat(surroundingBytes)
                .Concat(calculatedJumpBack)
                .ToArray();

            Array.Copy(codeSection, 0, data, torqueLimiterCodeStartAddress, codeSection.Length);

            _logger.LogInformation("patched {0} bytes at [0x{1:X08}]: [0x{2}],\n done!", codeSection.Length, torqueLimiterCodeStartAddress, BitConverter.ToString(codeSection).Replace("-", ", 0x"));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogInformation($"Failed to apply TCU limiter bypass: [{ex.Message}]");
            return false;
        }
    }

    protected List<uint> SearchMaskedPatternInByteArray(byte[] binaryByteArray, uint searchStartAddress, uint searchEndAddress, byte[] pattern, byte[] searchMask, int requiredOccurances, string itemName = "no name")
    {
        try
        {
            var resultAddresses = new List<uint> { };

            if (searchEndAddress - searchStartAddress < pattern.Length
                && binaryByteArray.Length > searchEndAddress)
            {
                throw new Exception("File range or pattern size invalid!");
            }

            _logger.LogInformation($"Searching for [{itemName}] pattern match in range [0x{searchStartAddress:X08}] to [0x{searchEndAddress:X08}]");
            for (int t = 0; t < searchEndAddress - searchStartAddress; t++)
            {
                var fullmatch = true;
                for (var z = 0; z < pattern.Length; z++)
                {
                    if ((binaryByteArray[searchStartAddress + t + z] & searchMask[z]) != (pattern[z] & searchMask[z]))
                    {
                        fullmatch = false;
                    }
                }

                if (fullmatch == true)
                {
                    resultAddresses.Add((uint)(searchStartAddress + t));
                }
            }

            if (resultAddresses.Count != requiredOccurances)
            {
                throw new Exception($"Did not find required number of occurances for input pattern! Found [{resultAddresses.Count}] of [{requiredOccurances}]");
            }

            _logger.LogInformation($"Found [{resultAddresses.Count}] occurances for [{itemName}]: {string.Join(", ", resultAddresses.Select(x => "0x" + x.ToString("X08")))}");
            return resultAddresses;
        }
        catch (Exception ex)
        {
            _logger.LogInformation($"Failed to find [{itemName}] with target pattern: [{ex.Message}]");
            return new List<uint> { };
        }
    }
}