﻿using MgFlasher.CustomCode.Extensions;
using MgFlasher.CustomCode.Models;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Storage.Static.PST;

public class V000PstCustomCodeApplier : PstCustomCodeApplierHelper, IPstCustomCodeApplier
{
    private readonly ILogger<V000PstCustomCodeApplier> _logger;

    public V000PstCustomCodeApplier(ILogger<V000PstCustomCodeApplier> logger) : base(logger)
    {
        _logger = logger;
    }

    public bool Apply(byte[] data, SweVersion psdDescriptor, CustomCodeContext moduleInfo)
    {
        _logger.LogInformation("ApplyCustomCodePst(): applying custom code to PST...");

        var version = CustomCodeVersions.V000;

        _logger.LogInformation($"nothing to apply for custom code {version}. Current swfl 0x{psdDescriptor.Id.ToString("X4")} major: {psdDescriptor.Major}, minor: {psdDescriptor.Minor}, patch: {psdDescriptor.Patch}.");
        return true;
    }
}