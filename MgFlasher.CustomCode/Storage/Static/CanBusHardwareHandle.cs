﻿using System;

namespace MgFlasher.CustomCode.Models;

/// <summary>
/// Can bus hardware handle definition table (Can_HrdHndlTable_acst_0) format:
///
/// short 0x00D9              # Id_uo (2 bytes) [requires change for Custom Code]
/// byte 0x00, 0x00           # null (2 bytes)
/// long 0x000007FF           # AcceptanceMask_u32 (4 bytes)
/// byte 0x00                 # Interrupt_b (1 byte)
/// byte 0x00                 # Direction_u8 (1 byte) [requires change for Custom Code]
/// byte 0x02                 # Node_u8 (1 byte) [requires change for Custom Code]
/// byte 0x00                 # Idtype_u8 (1 byte)
/// short 0x000A              # CanIfHrhId_uo (2 bytes)
/// byte 0x06                 # Buffer_u8 (1 byte)
/// byte 0x00                 # null (1 byte)
/// short 0x0000              # Handle_uo (2 bytes) [requires change for Custom Code]
/// byte 0x00, 0x00           # null (2 bytes)
///
/// [Id_uo, 2 null bytes, AcceptanceMask_u32, <PERSON>rupt_b, Direction_u8, Node_u8, Idtype_u8, CanIfHrhId_uo, Buffer_u8, 1 null byte, Handle_uo, 2 null bytes]
/// [0x00, 0xD9, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x02, 0x00, 0x00, 0x0A, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00]
/// </summary>
public class CanBusHardwareHandle
{
    /// <summary>
    /// Can bus ID
    /// </summary>
    public Int16 Id_uo { get; set; }

    /// <summary>
    /// Sending or receiving signal
    /// </summary>
    public byte Direction_u8 { get; set; }

    /// <summary>
    /// Appears to increment on Rx side based on number of rx signals, but Tx appears random
    /// </summary>
    public Int16 CanIfHrhId_uo { get; set; }

    public UInt32 AcceptanceMask_u32 { get; set; }
    public byte Buffer_u8 { get; set; }
    public Int16 Handle_uo { get; set; }
    public byte Idtype_u8 { get; set; }
    public byte Interrupt_b { get; set; }
    public byte Node_u8 { get; set; }

    public const int Id_uo_ArrayPosition = 0;
    public const int AcceptanceMask_u32_ArrayPosition = 4;
    public const int Interrupt_b_ArrayPosition = 8;
    public const int Direction_u8_ArrayPosition = 9;
    public const int Node_u8_ArrayPosition = 10;
    public const int Idtype_u8_ArrayPosition = 11;
    public const int CanIfHrhId_uo_ArrayPosition = 12;
    public const int Buffer_u8_ArrayPosition = 14;
    public const int Handle_uo_ArrayPosition = 16;

    /// <summary>
    /// single handle byte array size
    /// </summary>
    public const int HandleDescriptionSize = 20;

    public const byte RxDirection_u8 = 0x01;
    public const byte TxDirection_u8 = 0x00;
}