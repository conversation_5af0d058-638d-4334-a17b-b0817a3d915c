﻿using MgFlasher.CustomCode.Models;
using MgFlasher.CustomCode.Storage.Static.DST;
using MgFlasher.CustomCode.Storage.Static.PST;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Storage.Static;

public class StaticCustomCodeApplier : IStaticCustomCodeApplier
{
    private readonly IDifVarianteApplier _difVarianteApplier;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ILogger<StaticCustomCodeApplier> _logger;

    public StaticCustomCodeApplier(IDifVarianteApplier difVarianteApplier, ILoggerFactory loggerFactory)
    {
        _difVarianteApplier = difVarianteApplier;
        _loggerFactory = loggerFactory;
        _logger = loggerFactory.CreateLogger<StaticCustomCodeApplier>();
    }

    public bool ApplyCodeWordsDst(byte[] content, ReferenceFileVersion mapDescriptor, string version)
    {
        var applier = ResolveDstCodeWordsApplier(version);
        return applier.Apply(content, mapDescriptor);
    }

    public bool ApplyCustomCodeDst(byte[] content, ReferenceFileVersion mapDescriptor, CustomCodeContext moduleInfo, string version)
    {
        var applier = ResolveDstApplier(version);
        return applier.Apply(content, mapDescriptor, moduleInfo);
    }

    public bool ApplyCustomCodePst(byte[] data, ReferenceFileVersion mapDescriptor, CustomCodeContext moduleInfo, string version)
    {
        _logger.LogInformation("Checking if ApplyAllPSTCustomCode() & ApplyThirdPartyTag will apply...");
        ApplyThirdPartyTag(data, moduleInfo);
        if (!ApplyAllPSTCustomCode(data, mapDescriptor, moduleInfo, version))
        {
            _logger.LogInformation($"ApplyCustomCodeInstall(): Check failed!");
            return false;
        }
        return true;
    }

    private void ApplyThirdPartyTag(byte[] data, CustomCodeContext moduleInfo)
    {
        _logger.LogInformation("Checking for third party unlock...");

        if (moduleInfo.FemtoUnlock)
        {
            _logger.LogInformation("ECU has Femto unlock, adding tag to PST Dif Variante");
            _difVarianteApplier.Apply(data, moduleInfo.ProcessorArchitectureType, EcuSection.DST, "FEMTO");
            _difVarianteApplier.Apply(data, moduleInfo.ProcessorArchitectureType, EcuSection.PST, "FEMTO");
            return;
        }
        if (moduleInfo.Wave3Unlock)
        {
            _logger.LogInformation("ECU has a Wave 3 unlock, adding tag to PST Dif Variante");
            _difVarianteApplier.Apply(data, moduleInfo.ProcessorArchitectureType, EcuSection.DST, "MGF_3PU");
            _difVarianteApplier.Apply(data, moduleInfo.ProcessorArchitectureType, EcuSection.PST, "MGF_3PU");
            return;
        }
        if (moduleInfo.ThirdPartyUnlock)
        {
            _logger.LogInformation("ECU has third party unlock, adding tag to PST Dif Variante");
            _difVarianteApplier.Apply(data, moduleInfo.ProcessorArchitectureType, EcuSection.PST, "MGF_3PU");
            return;
        }
        _logger.LogInformation("ECU does not have third party unlock, no tag needed...");
    }

    private bool ApplyAllPSTCustomCode(byte[] data, ReferenceFileVersion info, CustomCodeContext moduleInfo, string version)
    {
        _logger.LogInformation("Applying custom code version {version} to PST file...", version);

        _difVarianteApplier.Apply(data, moduleInfo.ProcessorArchitectureType, EcuSection.PST, $"CC{version}");
        var pstApplier = ResolvePstApplier(version);
        var appliedPstCustomCode = pstApplier.Apply(data, info.Pst, moduleInfo);
        return appliedPstCustomCode;
    }

    private IPstCustomCodeApplier ResolvePstApplier(string version)
    {
        if (CustomCodeVersions.IsEqualToCustomCodev05(version))
        {
            return new V05PstCustomCodeApplier(_loggerFactory.CreateLogger<V05PstCustomCodeApplier>());
        }
        return new V000PstCustomCodeApplier(_loggerFactory.CreateLogger<V000PstCustomCodeApplier>());
    }

    private IDstCustomCodeApplier ResolveDstApplier(string version)
    {
        if (CustomCodeVersions.IsEqualToCustomCodev05(version))
        {
            return new V05DstCustomCodeApplier(_loggerFactory.CreateLogger<V05DstCustomCodeApplier>());
        }
        return new V000DstCustomCodeApplier(_loggerFactory.CreateLogger<V000DstCustomCodeApplier>());
    }

    private IDstCodeWordsApplier ResolveDstCodeWordsApplier(string version)
    {
        if (CustomCodeVersions.IsEqualToCustomCodev05(version))
        {
            return new V05DstCodeWordsApplier(_loggerFactory.CreateLogger<V05DstCodeWordsApplier>());
        }
        return new V000DstCodeWordsApplier(_loggerFactory.CreateLogger<V000DstCodeWordsApplier>());
    }
}