﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.CustomCode.Configuration.Templates.Files;
using MgFlasher.CustomCode.Extensions;
using MgFlasher.CustomCode.Models;
using MgFlasher.CustomCode.Storage.Changesets;

namespace MgFlasher.CustomCode.Storage.Static;

public class StaticCustomCodeRepository : ICustomCodeRepository
{
    private readonly IStaticCustomCodeApplier _customCodeApplier;
    private readonly ITemplateFileRepository _templateFileRepository;

    public StaticCustomCodeRepository(IStaticCustomCodeApplier customCodeApplier, ITemplateFileRepository templateFileRepository)
    {
        _customCodeApplier = customCodeApplier;
        _templateFileRepository = templateFileRepository;
    }

    public async Task<IReadOnlyList<ICustomCode>> GetAsync(ReferenceFileVersion mapDescriptor)
    {
        var available = await ResolveAvailable(mapDescriptor.Pst, mapDescriptor.A2L);
        var deprecated = ResolveDeprecated();
        var result = new List<ICustomCode>();
        foreach (var item in available)
        {
            var staticCustomCode = new StaticCustomCode(new CustomCodeId(item, "Public"), mapDescriptor, _customCodeApplier);
            result.Add(staticCustomCode);
        }

        foreach (var item in deprecated)
        {
            var staticCustomCode = new StaticCustomCode(new CustomCodeId(item, "Public", deprecated: true), mapDescriptor, _customCodeApplier);
            staticCustomCode.CanSelect = false;
            staticCustomCode.UnavailableReason = CustomCodeUnavailableReasonEnum.Obsolete;
            result.Add(staticCustomCode);
        }

        return result;
    }

    private string[] ResolveDeprecated()
    {
        var result = new List<string>
        {
            "v0.1",
            "v0.2",
            "v0.3",
            "v0.4",
            "v3.7",
            "v5.6",
            "v6.9.4",
        };

        return result.OrderByDescending(x => x.ResolveVersionFromString()).ToArray();
    }

    /// <summary>
    /// This method produces a list of available old CC which were created before we started storing them in
    /// the backend. Only v0.5 remains hardcoded in the app.
    /// </summary>
    /// <remarks>Soon to be removed.</remarks>
    /// <param name="pst"></param>
    /// <param name="a2l"></param>
    /// <returns>A list of hardcoded CC versions in the app for given engine.</returns>
    private async Task<string[]> ResolveAvailable(SweVersion pst, string a2l)
    {
        var result = new List<string>();

        // Any PPC or Aurix
        if (pst.Id == 0x3076
                 || pst.Id == 0x3081
                 || pst.Id == 0x3E7D
                 || pst.Id == 0x5D55
                 || pst.Id == 0x4ACF
                 || pst.Id == 0x5C64
                 || pst.Id == 0x7972
                 || pst.Id == 0x78F7
                 || pst.Id == 0x4963
                )
        {
            result.Add("v0.5");
        }

        string noCustomCode = "v0.0";
        result.Add(noCustomCode);

        return result.OrderByDescending(x => x.ResolveVersionFromString()).ToArray();
    }
}