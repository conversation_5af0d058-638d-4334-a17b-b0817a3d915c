﻿using System;
using MgFlasher.CustomCode.Storage.Changesets;

namespace MgFlasher.CustomCode;

public static class CustomCodeVersions
{
    public static readonly Version V694 = new Version(6, 9, 4);
    public static readonly Version V56 = new Version(5, 6);
    public static readonly Version V37 = new Version(3, 7);
    public static readonly Version V05 = new Version(0, 5);
    public static readonly Version V000 = new Version(0, 0, 0);

    public static bool IsAtLeastCustomCodev9999(string version) => false;

    public static bool IsAtLeastCustomCodev73(string version) => GetCustomCodeVersionFromString(version) >= new Version(7, 3);

    public static bool IsAtLeastCustomCodev721(string version) => GetCustomCodeVersionFromString(version) >= new Version(7, 2, 1);

    public static bool IsAtLeastCustomCodev72(string version) => GetCustomCodeVersionFromString(version) >= new Version(7, 2);

    public static bool IsAtLeastCustomCodev71(string version) => GetCustomCodeVersionFromString(version) >= new Version(7, 1);

    public static bool IsAtLeastCustomCodev705(string version) => GetCustomCodeVersionFromString(version) >= new Version(7, 0, 5);

    public static bool IsAtLeastCustomCodev7x(string version) => GetCustomCodeVersionFromString(version) >= new Version(7, 0);

    public static bool IsAtLeastCustomCodev69x(string version) => GetCustomCodeVersionFromString(version) >= new Version(6, 9);

    public static bool IsAtLeastCustomCodev6x(string version) => GetCustomCodeVersionFromString(version) >= new Version(6, 0);

    public static bool IsEqualToCustomCodev694(string version) => GetCustomCodeVersionFromString(version) == V694;

    public static bool IsEqualToCustomCodev56(string version) => GetCustomCodeVersionFromString(version) == V56;

    public static bool IsEqualToCustomCodev37(string version) => GetCustomCodeVersionFromString(version) == V37;

    public static bool IsEqualToCustomCodev05(string version) => GetCustomCodeVersionFromString(version) == V05;

    public static bool IsEqualToCustomCodev000(string version) => GetCustomCodeVersionFromString(version) == V000;

    public static Version GetCustomCodeVersionFromString(string version)
    {
        if (string.IsNullOrWhiteSpace(version))
        {
            return new Version(0, 0, 0);
        }

        var ccId = CustomCodeId.FromString(version);
        if (Version.TryParse(ccId.Version.Replace("v", ""), out Version parsed))
        {
            return parsed;
        }

        return new Version(0, 0, 0);
    }
}