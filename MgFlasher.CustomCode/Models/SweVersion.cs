﻿namespace MgFlasher.CustomCode.Models;

/// <summary>
/// A class representing the version of a SWE - ECU's embedded software.
/// </summary>
public class SweVersion
{
    public int Id { get; set; }
    public int Major { get; set; }
    public int Minor { get; set; }
    public int Patch { get; set; }
    public byte[] Raw { get; set; }

    public override string ToString()
    {
        return $"{Id:X}-{Major:000}.{Minor:000}.{Patch:000}";
    }
}