﻿using System;

namespace MgFlasher.CustomCode;

public class CustomCodeException : Exception
{
    public static class ErrorCodes
    {
        public const string CannotParseTemplateFileRow = nameof(CannotParseTemplateFileRow);
        public const string FileTemplateMustHaveAnyLocators = nameof(FileTemplateMustHaveAnyLocators);
        public const string LocatorMustHaveValidEcuAddress = nameof(LocatorMustHaveValidEcuAddress);
        public const string LocatorMustHaveValidA2L = nameof(LocatorMustHaveValidA2L);
        public const string CannotApplyOptionsMissingMapAddress = nameof(CannotApplyOptionsMissingMapAddress);
        public const string CannotApplyOptionsMissingMapEntry = nameof(CannotApplyOptionsMissingMapEntry);
        public const string CannotApplyInitialOptions = nameof(CannotApplyInitialOptions);
        public const string InvalidReferenceFileSize = nameof(InvalidReferenceFileSize);
        public const string ReferenceFile_InvalidSweInfoStartAddress = nameof(ReferenceFile_InvalidSweInfoStartAddress);
        public const string ReferenceFile_InvalidSweInfoStartAddress_InvalidAddress = nameof(ReferenceFile_InvalidSweInfoStartAddress_InvalidAddress);
        public const string InvalidReferenceFileSize_CouldNotParse_A2lNumber = nameof(InvalidReferenceFileSize_CouldNotParse_A2lNumber);
        public const string CouldNotReadTemplateFileProvider = nameof(CouldNotReadTemplateFileProvider);
        public const string CustomCodeFileTemplateMustHaveAnyValues = nameof(CustomCodeFileTemplateMustHaveAnyValues);
        public const string CustomCodeFileTemplateMustHaveAnyLocators = nameof(CustomCodeFileTemplateMustHaveAnyLocators);
        public const string CustomCodeFileTemplateMustHaveRequiredVariableForEachA2l = nameof(CustomCodeFileTemplateMustHaveRequiredVariableForEachA2l);
    }

    public string ErrorCode { get; }
    public string AdditionalMessage { get; }

    public CustomCodeException(string errorCode, string additionalMessage = null)
        : base(errorCode + (!string.IsNullOrEmpty(additionalMessage) ? $": [{additionalMessage}]" : ""))
    {
        ErrorCode = errorCode;
        AdditionalMessage = additionalMessage;
    }
}