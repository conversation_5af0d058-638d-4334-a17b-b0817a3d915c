﻿using System.Collections.Generic;
using MgFlasher.CustomCode.Extensions;

namespace MgFlasher.CustomCode.Storage.Changesets;

public class CustomCodeIdComparer : IComparer<CustomCodeId>
{
    public static readonly CustomCodeIdComparer Instance = new CustomCodeIdComparer();

    public int Compare(CustomCodeId x, CustomCodeId y)
    {
        var ver = x.Version.ResolveVersionFromString().CompareTo(y.Version.ResolveVersionFromString());
        if (ver != 0)
        {
            return ver;
        }

        if (string.IsNullOrEmpty(x.UserAvailabilityLevel))
        {
            return -1;
        }

        return x.UserAvailabilityLevel.CompareTo(y.UserAvailabilityLevel ?? "");
    }
}