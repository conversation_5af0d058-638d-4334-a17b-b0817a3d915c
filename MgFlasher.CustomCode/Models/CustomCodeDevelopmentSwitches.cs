﻿using System.Collections.Generic;

namespace MgFlasher.CustomCode.Models;

public class CustomCodeDevelopmentSwitches
{
    /// <summary>
    /// Provided by CCC file.
    /// Should be applied where user forced using - ForceFlashPST_WithCustomCode or Stockmap.PST != Module.Pst
    /// or when cc version upgrade (e.g. v7.0 -> v7.1)
    /// or when cc version is not selected
    /// </summary>
    public bool WithPstChanges { get; }

    /// <summary>
    /// When we flash official custom code or not flashing pst and ECU already have flashed official custom code.
    /// For CC v7.0+ only
    /// </summary>
    public bool WithLabasOffsets { get; }

    /// <summary>
    /// When we flash official custom code or not flashing pst and ECU already have flashed official custom code.
    /// For CC v7.0+ only
    /// </summary>
    public bool WithVin { get; }

    /// <summary>
    /// Excel initial values
    /// Should be applied only when TunerOverrideDst==false
    /// And when we flash official custom code or not flashing pst and ECU already have flashed official custom code.
    /// </summary>
    public bool WithCustomCodeInitialValues { get; }

    /// <summary>
    /// Any of the following: WithPstChanges || WithLabasOffsets || WithVin || WithCustomCodeInitialValues
    /// </summary>
    public bool AnyOptionSelected => WithPstChanges || WithLabasOffsets || WithVin || WithCustomCodeInitialValues;

    public CustomCodeDevelopmentSwitches(bool withPstChanges, bool withLabasOffsets, bool withVin, bool withCustomCodeInitialValues)
    {
        WithPstChanges = withPstChanges;
        WithVin = withVin;
        WithLabasOffsets = withLabasOffsets;
        WithCustomCodeInitialValues = withCustomCodeInitialValues;
    }

    public override string ToString()
    {
        var sb = new List<string>();
        if (WithPstChanges)
        {
            sb.Add(nameof(WithPstChanges));
        }
        if (WithLabasOffsets)
        {
            sb.Add(nameof(WithLabasOffsets));
        }
        if (WithVin)
        {
            sb.Add(nameof(WithVin));
        }
        if (WithCustomCodeInitialValues)
        {
            sb.Add(nameof(WithCustomCodeInitialValues));
        }
        return string.Join(" | ", sb);
    }
}