﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.ConstrainedExecution;
using System.Text.RegularExpressions;

namespace MgFlasher.CustomCode.Storage.Changesets;

public class CustomCodeId
{
    private static readonly Regex CustomCodeVersionPstTag_Regex = new Regex(@"#CCv(?<Major>\d{1,})\.(?<Minor>\d{1,})\.?(?<Build>\d{1,})?\.?(?<Revision>\d{1,})?.{0,}_?", RegexOptions.Compiled);

    public string Version { get; }
    public string UserAvailabilityLevel { get; }
    public bool Deprecated { get; } = false;

    public CustomCodeId(string version, string userAvailabilityLevel = null, bool deprecated = false)
    {
        if (string.IsNullOrWhiteSpace(version))
        {
            throw new ArgumentNullException(nameof(version));
        }

        if (!string.IsNullOrWhiteSpace(version) && version.Equals("NotFound"))
        {
            version = "v0.0";
        }

        if ((version.Contains(" ") || version.Contains("-")) && string.IsNullOrWhiteSpace(userAvailabilityLevel))
        {
            var parsed = FromString(version);
            Version = parsed.Version ?? throw new ArgumentNullException(nameof(version));
            UserAvailabilityLevel = parsed.UserAvailabilityLevel;
        }
        else
        {
            Version = version ?? throw new ArgumentNullException(nameof(version));
            UserAvailabilityLevel = userAvailabilityLevel;
        }

        Deprecated = deprecated;
    }

    public override string ToString()
    {
        return $"{Version}" + (!string.IsNullOrEmpty(UserAvailabilityLevel) ? $"-{UserAvailabilityLevel}" : "");
    }

    public static CustomCodeId FromString(string input)
    {
        if (string.IsNullOrEmpty(input) || input.ToUpper() == "NotFound".ToUpper())
        {
            return new CustomCodeId("v0.0", "Public");
        }

        var spliited = input.Split(" ", StringSplitOptions.RemoveEmptyEntries);
        if (input.Contains("-"))
        {
            spliited = input.Split("-", StringSplitOptions.RemoveEmptyEntries);
        }

        return new CustomCodeId(spliited.ElementAtOrDefault(0), spliited.ElementAtOrDefault(1));
    }

    public static CustomCodeId FromDifVariante(string difVariante)
    {
        var match = CustomCodeVersionPstTag_Regex.Match(difVariante);
        if (!match.Success)
        {
            return null;
        }

        Version version;
        {
            var majorString = match.Groups["Major"].Value;
            var minorString = match.Groups["Minor"].Value;
            var buildString = match.Groups["Build"].Value;
            var revisionString = match.Groups["Revision"].Value;

            if (!int.TryParse(majorString, out var major)
                || !int.TryParse(minorString, out var minor))
            {
                return null;
            }

            var build = 0;
            var revision = 0;
            if ((match.Groups["Build"].Success && !int.TryParse(buildString, out build))
                || (match.Groups["Revision"].Success && !int.TryParse(revisionString, out revision)))
            {
                return null;
            }

            if (build == 0 && revision == 0)
            {
                version = new Version(major, minor);
            }
            else if (build != 0 && revision == 0)
            {
                version = new Version(major, minor, build);
            }
            else
            {
                version = new Version(major, minor, build, revision);
            }
        }

        return new CustomCodeId(version.ToString(), "FromDifVariante");
    }

    public Version ToVersion()
    {
        return new Version(Version.ToUpper().Replace("V", ""));
    }
}