﻿using MgFlasher.CustomCode.Storage.Changesets;

namespace MgFlasher.CustomCode.Models;

public class ReferenceFileVersion
{
    public SweVersion Btld { get; set; }
    public SweVersion Pst { get; set; }
    public SweVersion Dst { get; set; }
    public string A2L { get; set; }
    public CustomCodeId CustomCodeId { get; set; }

    public override string ToString()
    {
        return $"{Btld} | {Pst} | {Dst} | {A2L} | {(CustomCodeId == null ? "null custom code" : CustomCodeId)}";
    }
}