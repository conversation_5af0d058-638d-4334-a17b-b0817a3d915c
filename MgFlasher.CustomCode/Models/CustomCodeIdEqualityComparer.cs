﻿using System.Collections.Generic;
using MgFlasher.CustomCode.Extensions;

namespace MgFlasher.CustomCode.Storage.Changesets;

public class CustomCodeIdEqualityComparer : IEqualityComparer<CustomCodeId>
{
    public static readonly CustomCodeIdEqualityComparer Instance = new CustomCodeIdEqualityComparer();

    public int Compare(CustomCodeId x, CustomCodeId y)
    {
        var ver = x.Version.ResolveVersionFromString().CompareTo(y.Version.ResolveVersionFromString());
        if (ver != 0)
        {
            return ver;
        }

        if (string.IsNullOrEmpty(x.UserAvailabilityLevel))
        {
            return -1;
        }

        return x.UserAvailabilityLevel.CompareTo(y.UserAvailabilityLevel ?? "");
    }

    public bool Equals(CustomCodeId x, CustomCodeId y)
    {
        if (x is null && y != null)
        {
            return false;
        }
        else if (x != null && y is null)
        {
            return false;
        }
        else if (x is null && y is null)
        {
            return true;
        }

        var matchingVersion = x.Version.Equals(y.Version);
        var matchingUserAvailabilityLevel = (x.UserAvailabilityLevel ?? "").Equals(y.UserAvailabilityLevel ?? "");
        return matchingVersion && matchingUserAvailabilityLevel;
    }

    public int GetHashCode(CustomCodeId obj)
    {
        return 13 ^ obj.Version.GetHashCode() * (obj.UserAvailabilityLevel?.GetHashCode() ?? 0);
    }
}