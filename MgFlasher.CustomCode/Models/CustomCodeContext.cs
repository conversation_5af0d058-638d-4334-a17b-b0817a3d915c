﻿using System;

namespace MgFlasher.CustomCode.Models;

public class CustomCodeContext
{
    public string TargetCustomCodeVersion { get; private set; }
    public string InstalledCustomCodeVersion { get; }
    public ProcessorArchitectureType ProcessorArchitectureType { get; }
    public string VIN { get; }
    public bool Wave3Unlock { get; }
    public bool FemtoUnlock { get; }
    public bool AnyEcuReqiresWave3Unlock { get; }
    public bool ThirdPartyUnlock { get; }
    public StageType Stage { get; }
    public bool HasLowBdcSoftware { get; }
    public bool IsMini { get; }
    public bool IsHybrid { get; }

    public CustomCodeContext(ProcessorArchitectureType processorArchitectureType, string vin, StageType stage, string targetCustomCodeVersion, bool wave3Unlock, bool femtoUnlock, bool anyEcuReqiresWave3Unlock, bool thirdPartyUnlock, bool isMini, bool isHybrid, bool hasLowBdcSoftware = false, string installedCustomCodeVersion = "")
    {
        VIN = !string.IsNullOrEmpty(vin) ? vin :
            throw new ArgumentNullException(nameof(vin));
        ProcessorArchitectureType = !processorArchitectureType.Equals(ProcessorArchitectureType.UNKNOWN) ? processorArchitectureType :
            throw new ArgumentNullException(nameof(processorArchitectureType));
        TargetCustomCodeVersion = targetCustomCodeVersion;
        Stage = stage;
        HasLowBdcSoftware = hasLowBdcSoftware;
        Wave3Unlock = wave3Unlock;
        FemtoUnlock = femtoUnlock;
        AnyEcuReqiresWave3Unlock = anyEcuReqiresWave3Unlock;
        ThirdPartyUnlock = thirdPartyUnlock;
        InstalledCustomCodeVersion = installedCustomCodeVersion;
        IsMini = isMini;
        IsHybrid = isHybrid;
    }

    public void SetTargetCustomCodeVersion(string version)
    {
        TargetCustomCodeVersion = version;
    }
}