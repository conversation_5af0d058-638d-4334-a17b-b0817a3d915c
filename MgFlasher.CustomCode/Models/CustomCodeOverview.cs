﻿using System;
using MgFlasher.CustomCode.Models;
using MgFlasher.CustomCode.Storage.Changesets;

namespace MgFlasher.CustomCode;

public class CustomCodeOverview
{
    public CustomCodeId Version { get; }
    public string ReleaseNotes { get; }
    public string ReleasedAtUtc { get; }
    public bool CanSelect { get; }
    public CustomCodeUnavailableReasonEnum? UnavailableReason { get; }

    public CustomCodeOverview(CustomCodeId version, string releaseNotes, string releasedAtUtc, bool canSelect, CustomCodeUnavailableReasonEnum? unavailableReason)
    {
        Version = version;
        ReleaseNotes = releaseNotes;
        ReleasedAtUtc = releasedAtUtc;
        CanSelect = canSelect;
        UnavailableReason = unavailableReason;
    }
}