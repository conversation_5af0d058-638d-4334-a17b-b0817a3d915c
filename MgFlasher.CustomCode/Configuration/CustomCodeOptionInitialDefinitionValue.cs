﻿using System;

namespace MgFlasher.CustomCode.Configuration;

public class CustomCodeOptionInitialDefinitionValue
{
    public string A2lCharacteristic { get; }
    public byte[] Value { get; }
    public string DataType { get; }

    public uint BitSize => DataType?.ToLower() switch
    {
        "int32" or "uint32" => 32,
        "int16" or "uint16" => 16,
        "sbyte" or "ubyte" or "byte" => 8,
        _ => throw new InvalidOperationException($"Not supported data type {DataType}")
    };

    public bool Signed => DataType?.ToLower() switch
    {
        "ubyte" or "uint16" or "uint32" or "byte" => false,
        _ => true
    };

    public CustomCodeOptionInitialDefinitionValue(string a2lCharacteristic, byte[] value, string dataType)
    {
        A2lCharacteristic = a2lCharacteristic;
        Value = value;
        DataType = dataType;
    }
}