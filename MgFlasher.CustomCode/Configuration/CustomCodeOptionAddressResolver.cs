﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.CustomCode.Configuration.Templates.Files;
using MgFlasher.CustomCode.Configuration.Templates.Storage;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode;

public class CustomCodeOptionAddressResolver : ICustomCodeOptionAddressResolver
{
    private readonly ITemplateFileRepository _templateFileRepository;
    private readonly ILogger<CustomCodeOptionAddressResolver> _logger;
    private Dictionary<string, Dictionary<string, List<CustomCodeOptionDefinitionLocator>>> _cache;

    public CustomCodeOptionAddressResolver(
        ITemplateFileRepository templateFileRepository,
        ILogger<CustomCodeOptionAddressResolver> logger)
    {
        _templateFileRepository = templateFileRepository;
        _logger = logger;
        _templateFileRepository.Initialized += (o, e) => _cache = null;
    }

    public async Task<uint?> TryResolveAsync(string a2l, string customCodeVersion, string a2lCharacteristic)
    {
        var source = await GetOrInitializeSourceAsync();

        if (!source.TryGetValue(a2l, out var a2lItems))
        {
            _logger.LogWarning("CouldNotFindCustomCodeItemsForA2L[{A2L}]", a2l);
            return null;
        }

        if (!a2lItems.TryGetValue(a2lCharacteristic, out var characteristicItems))
        {
            _logger.LogWarning("CouldNotFindCustomCodeItemsForA2lCharacteristic[{A2l}, {Characteristic}]", a2l, a2lCharacteristic);
            return null;
        }

        var result = ResolveByCustomCodeVersion(customCodeVersion, characteristicItems);
        if (result is null)
        {
            _logger.LogWarning("CouldNotFindCustomCodeItemsForCustomCodeVersion[{A2l}, {Characteristic}, {CcVer}]", a2l, a2lCharacteristic, customCodeVersion);
            return null;
        }

        return result.EcuParamAddress;
    }

    private CustomCodeOptionDefinitionLocator ResolveByCustomCodeVersion(string ccVersion, List<CustomCodeOptionDefinitionLocator> characteristicItems)
    {
        string SkipV(string input) => input.Replace("v", "").Split("-", StringSplitOptions.RemoveEmptyEntries)[0];
        var ccVersionParsed = Version.Parse(SkipV(ccVersion));

        foreach (var item in characteristicItems)
        {
            var requiredCcVersion = !string.IsNullOrEmpty(item.CustomCodeVersion) ? Version.Parse(SkipV(item.CustomCodeVersion)) : ccVersionParsed;
            if (ccVersionParsed == requiredCcVersion)
            {
                return item;
            }
        }

        return null;
    }

    private async Task<Dictionary<string, Dictionary<string, List<CustomCodeOptionDefinitionLocator>>>> GetOrInitializeSourceAsync()
    {
        if (_cache is not null)
        {
            return _cache;
        }

        var locators = await _templateFileRepository.GetLocatorsAsync();
        if (locators.Count == 0)
        {
            throw new InvalidOperationException("" +
                "Could not resolve app files. " +
                "Please perform full sync by going to car page and selecting \"Sync car data\" from context menu.");
        }

        _cache = locators.GroupBy(x => x.A2L).ToDictionary(
            x => x.Key,
            x => x.GroupBy(x => x.A2lCharacteristic)
                .ToDictionary(
                    x => x.Key,
                    x => x.OrderByDescending(x => x.CustomCodeVersion).ToList()));

        return _cache;
    }
}