﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MgFlasher.Cache;
using MgFlasher.CustomCode.Configuration.Templates.Storage;
using Microsoft.Extensions.Logging;
using MiniExcelLibs;
using MiniExcelLibs.OpenXml;

namespace MgFlasher.CustomCode.Configuration.Templates.Files;

public class TemplateFileRepository : ITemplateFileRepository
{
    private readonly ITemplateFileProvider _fileProvider;
    private readonly IHashedRepository<CustomCodeRecordsCollection> _repository;
    private readonly ILogger<TemplateFileRepository> _logger;
    private readonly SemaphoreSlim _semaphoreSlim;
    private CustomCodeRecordsCollection _recordsCollection;

    public event EventHandler Initialized;

    public TemplateFileRepository(
        ITemplateFileProvider fileProvider,
        IHashedRepository<CustomCodeRecordsCollection> repository,
        ILogger<TemplateFileRepository> logger)
    {
        _fileProvider = fileProvider;
        _repository = repository;
        _logger = logger;
        _semaphoreSlim = new SemaphoreSlim(1, 1);
    }

    public async Task<IReadOnlyList<CustomCodeOptionDefinitionLocator>> GetLocatorsAsync()
    {
        return await GetFromRepositoryAsync(x => x.Locators);
    }

    public async Task<IReadOnlyList<CustomCodeOptionDefinitionValue>> GetValuesAsync()
    {
        return await GetFromRepositoryAsync(x => x.Values);
    }

    public async Task UpdateFromFileProvidersAsync()
    {
        try
        {
            await _semaphoreSlim.WaitAsync();

            await ParseFileAsync();
            Initialized?.Invoke(this, EventArgs.Empty);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "InitializeAsync error");
            throw;
        }
        finally
        {
            _semaphoreSlim.Release();
        }
    }

    private async Task ParseFileAsync()
    {
        var canRead = await _fileProvider.CanRead();

        if (!canRead)
        {
            _recordsCollection = new CustomCodeRecordsCollection()
            {
                Locators = new(),
                Values = new()
            };
            await _repository.SetRecordsAsync("any", _recordsCollection);
            return;
        }

        var file = await _fileProvider.GetAsync();

        var locators = ParseLocators(file);
        var values = ParseValues(file);
        var records = new CustomCodeRecordsCollection()
        {
            Locators = locators.ToList(),
            Values = values.ToList()
        };
        _recordsCollection = records;
        await _repository.SetRecordsAsync("any", _recordsCollection);

        Initialized?.Invoke(this, EventArgs.Empty);
    }

    private IEnumerable<CustomCodeOptionDefinitionValue> ParseValues(Stream package)
    {
        var rows = GetTable(package, "Values");

        var idx = 0;

        foreach (IDictionary<string, object> row in rows.OfType<ExpandoObject>())
        {
            if (row["A2L Characteristic"] == null)
            {
                break;
            }

            yield return new CustomCodeOptionDefinitionValue()
            {
                A2lCharacteristic = GetAndValidate(row, idx, "A2L Characteristic", true),
                InAppName = GetAndValidate(row, idx, "InAppName"),
                DataType = GetAndValidate(row, idx, "Data Type", true),
                Value = GetAndValidate(row, idx, "Value", true) is string value ?
                    value.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(x =>
                            x.Split(",", StringSplitOptions.RemoveEmptyEntries)
                                .Select(x => x.Trim()).Where(x => !string.IsNullOrEmpty(x))
                                .Select(x =>
                                {
                                    try
                                    {
                                        return Convert.ToByte(x, 16);
                                    }
                                    catch (FormatException ex)
                                    {
                                        _logger.LogError(ex, "Could not parse row of CustomCodeOptionDefinitionValue with index {Index} and {Value}", idx, x);
                                        throw;
                                    }
                                }).ToArray())
                        .ToArray() : throw new InvalidOperationException($"Could not parse value in row {idx}"),
                ApplyInitially = GetAndValidate(row, idx, "Apply Initially", false)?.Trim()?.ToUpper() == "Y"
            };
            idx++;
        }
    }

    private IEnumerable<CustomCodeOptionDefinitionLocator> ParseLocators(Stream package)
    {
        var rows = GetTable(package, "Locators");
        var idx = 0;

        foreach (IDictionary<string, object> row in rows.OfType<ExpandoObject>())
        {
            if (row["A2L Characteristic"] == null)
            {
                yield break;
            }

            yield return new CustomCodeOptionDefinitionLocator()
            {
                A2lCharacteristic = GetAndValidate(row, idx, "A2L Characteristic", true),
                A2L = GetAndValidate(row, idx, "A2L", true),
                CustomCodeVersion = GetAndValidate(row, idx, "Custom Code Version"),
                EcuParamAddress = GetAndValidate(row, idx, "ECU Param Address", true) is string addr ? Convert.ToUInt32(addr, 16) : 0
            };
            idx++;
        }
    }

    private string GetAndValidate(IDictionary<string, object> row, int index, string fieldName, bool validateNull = false)
    {
        row.TryGetValue(fieldName, out object val);

        if (validateNull && val is null)
        {
            throw new CustomCodeException(
                CustomCodeException.ErrorCodes.CannotParseTemplateFileRow,
                 $"Row[{index}] does not have value in [{fieldName}] column");
        }

        if (val is decimal dec)
        {
            return dec.ToString(CultureInfo.InvariantCulture);
        }

        if (val is double doub)
        {
            return doub.ToString(CultureInfo.InvariantCulture);
        }

        if (val is int @int)
        {
            return @int.ToString(CultureInfo.InvariantCulture);
        }

        if (val is float @float)
        {
            return @float.ToString(CultureInfo.InvariantCulture);
        }

        if (val is string @string)
        {
            return @string.Trim().Replace("\u00A0", " ");
        }

        return val?.ToString().Trim();
    }

    private IEnumerable<ExpandoObject> GetTable(Stream package, string name)
    {
        var config = new OpenXmlConfiguration { EnableSharedStringCache = false };
        var rows = MiniExcel.Query(package, true, name, configuration: config);
        return rows.OfType<ExpandoObject>();
    }

    private async Task<T> GetFromRepositoryAsync<T>(Func<CustomCodeRecordsCollection, T> selector) where T : class, new()
    {
        try
        {
            await _semaphoreSlim.WaitAsync();

            if (_recordsCollection is null)
            {
                _recordsCollection ??= await _repository.TryGetCachedRecords();
                if (_recordsCollection is not null)
                {
                    Initialized?.Invoke(this, EventArgs.Empty);
                }
            }

            return _recordsCollection is not null ? selector.Invoke(_recordsCollection) : new T();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "GetFromRepositoryAsync error");
            throw;
        }
        finally
        {
            _semaphoreSlim.Release();
        }
    }
}