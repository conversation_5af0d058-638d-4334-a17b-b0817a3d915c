﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MgFlasher.CustomCode.Configuration.Templates.Storage;

namespace MgFlasher.CustomCode.Configuration.Templates.Files;

public interface ITemplateFileRepository
{
    event EventHandler Initialized;

    Task UpdateFromFileProvidersAsync();

    Task<IReadOnlyList<CustomCodeOptionDefinitionLocator>> GetLocatorsAsync();

    Task<IReadOnlyList<CustomCodeOptionDefinitionValue>> GetValuesAsync();
}