﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.Cache.Repositories;
using MgFlasher.CustomCode.Configuration.Templates.Storage;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Configuration.Templates.Files;

public class TemplateFileValidator : ITemplateFileValidator
{
    private static readonly string[] RequiredVariablesInA2lGroup =
    [
        "MoFTrqPtd_pwrMax_C",
        "PFlt_stActv_C",
        "PFlt_stPfilTyp_C"
    ];

    private readonly ILoggerFactory _loggerFactory;

    public TemplateFileValidator(ILoggerFactory loggerFactory)
    {
        _loggerFactory = loggerFactory;
    }

    public async Task<CustomCodeRecordsCollection> EnsureValidAsync(ITemplateFileProvider templateFileProvider)
    {
        if (!await templateFileProvider.CanRead())
        {
            throw new CustomCodeException(CustomCodeException.ErrorCodes.CouldNotReadTemplateFileProvider);
        }

        var repository = new InMemoryHashedRepository<CustomCodeRecordsCollection>();
        var templateFileRepository = new TemplateFileRepository(templateFileProvider, repository, _loggerFactory.CreateLogger<TemplateFileRepository>());

        await templateFileRepository.UpdateFromFileProvidersAsync();
        var locators = await templateFileRepository.GetLocatorsAsync();
        var values = await templateFileRepository.GetValuesAsync();

        if (!locators.Any())
        {
            throw new CustomCodeException(CustomCodeException.ErrorCodes.CustomCodeFileTemplateMustHaveAnyLocators);
        }

        if (!values.Any())
        {
            throw new CustomCodeException(CustomCodeException.ErrorCodes.CustomCodeFileTemplateMustHaveAnyValues);
        }

        ValidateLocatorRequirementForEachA2l(locators);

        return await repository.GetCachedRecords();
    }

    private static void ValidateLocatorRequirementForEachA2l(IReadOnlyList<CustomCodeOptionDefinitionLocator> locators)
    {
        var errors = new List<string>();
        foreach (var a2lLocators in locators.Where(x => !string.IsNullOrEmpty(x.A2L)).GroupBy(x => x.A2L))
        {
            foreach (var requiredVariable in RequiredVariablesInA2lGroup)
            {
                if (a2lLocators.All(x => x.A2lCharacteristic != requiredVariable))
                {
                    errors.Add($"{a2lLocators.Key} => {requiredVariable}");
                }
            }
        }

        if (errors.Any())
        {
            throw new CustomCodeException(CustomCodeException.ErrorCodes.CustomCodeFileTemplateMustHaveRequiredVariableForEachA2l, string.Join(", ", errors));
        }
    }
}