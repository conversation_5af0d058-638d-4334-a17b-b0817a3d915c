﻿namespace MgFlasher.CustomCode.Configuration.Templates.Storage;

public class CustomCodeOptionDefinitionValue
{
    public string A2lCharacteristic { get; set; }
    public string DataType { get; set; }
    public byte[][] Value { get; set; }
    public bool ApplyInitially { get; set; }
    public string InAppName { get; set; }

    public override string ToString()
    {
        return $"Definition: {A2lCharacteristic}, {DataType}, {InAppName}, apply initially: {ApplyInitially}";
    }
}