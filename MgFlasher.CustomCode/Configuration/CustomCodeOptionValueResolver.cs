﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.CustomCode.Configuration.Templates.Files;
using Microsoft.Extensions.Logging;

namespace MgFlasher.CustomCode.Configuration;

public class CustomCodeOptionValueResolver : ICustomCodeOptionValueResolver
{
    private readonly ITemplateFileRepository _repository;
    private readonly ILogger<CustomCodeOptionValueResolver> _logger;

    public CustomCodeOptionValueResolver(
        ITemplateFileRepository repository,
        ILogger<CustomCodeOptionValueResolver> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task<List<CustomCodeOptionInitialDefinitionValue>> GetAllFirstValuesForInitialApplyAsync()
    {
        var items = await _repository.GetValuesAsync();
        var result = items
            .Where(x => x.ApplyInitially)
            .Select(x => new CustomCodeOptionInitialDefinitionValue(x.A2lCharacteristic, x.Value[0], x.DataType))
            .ToList();
        return result;
    }

    public async Task<byte[]> GetAsync(string optionName, string a2lCharacteristic, string inAppName, int index = 0)
    {
        var items = await _repository.GetValuesAsync();
        var val = items.FirstOrDefault(x => x.InAppName == inAppName && x.A2lCharacteristic == a2lCharacteristic)?.Value?.ElementAt(index);
        if (val is null)
        {
            LogWarning(optionName, a2lCharacteristic, inAppName);
        }

        return val;
    }

    private void LogWarning(string optionName, string a2lCharacteristic, string inAppName)
    {
        _logger.LogWarning("Failed to get [{optionName}], could not find {a2lCharacteristic} with matching {inAppName}",
            optionName, a2lCharacteristic, inAppName);
        var errorMsg = $"Failed to apply changes for [{optionName}]!";
        throw new CustomCodeException(CustomCodeException.ErrorCodes.CannotApplyOptionsMissingMapEntry, errorMsg);
    }
}