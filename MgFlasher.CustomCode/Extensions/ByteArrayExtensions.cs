﻿using System;

namespace MgFlasher.CustomCode.Extensions;

public static class ByteArrayExtensions
{
    public static byte[] ChangeMapEndianness(this byte[] data, uint valueBitSize, bool signed)
    {
        var arraySize = Buffer.ByteLength(data);
        var output = new byte[arraySize];

        if (signed)
        {
            if (valueBitSize == 8)
            {
                for (var i = 0; i < arraySize; i += 1)
                {
                    output[i] = data[i];
                }
            }
            else if (valueBitSize == 16)
            {
                for (var i = 0; i < arraySize; i += 2)
                {
                    var tmp = BitConverter.ToInt16(data, i);
                    var tmpAsBytes = BitConverter.GetBytes(tmp);
                    Array.Reverse(tmpAsBytes);
                    output[i] = tmpAsBytes[0];
                    output[i + 1] = tmpAsBytes[1];
                }
            }
            else if (valueBitSize == 32)
            {
                for (var i = 0; i < arraySize; i += 4)
                {
                    var tmp = BitConverter.ToInt32(data, i);
                    var tmpAsBytes = BitConverter.GetBytes(tmp);
                    Array.Reverse(tmpAsBytes);
                    output[i] = tmpAsBytes[0];
                    output[i + 1] = tmpAsBytes[1];
                    output[i + 2] = tmpAsBytes[2];
                    output[i + 3] = tmpAsBytes[3];
                }
            }
            else if (valueBitSize == 64)
            {
                for (var i = 0; i < arraySize; i += 8)
                {
                    var tmp = BitConverter.ToInt64(data, i);
                    var tmpAsBytes = BitConverter.GetBytes(tmp);
                    Array.Reverse(tmpAsBytes);
                    output[i] = tmpAsBytes[0];
                    output[i + 1] = tmpAsBytes[1];
                    output[i + 2] = tmpAsBytes[2];
                    output[i + 3] = tmpAsBytes[3];
                    output[i + 4] = tmpAsBytes[4];
                    output[i + 5] = tmpAsBytes[5];
                    output[i + 6] = tmpAsBytes[6];
                    output[i + 7] = tmpAsBytes[7];
                }
            }
        }
        else
        {
            if (valueBitSize == 8)
            {
                for (var i = 0; i < arraySize; i += 1)
                {
                    output[i] = data[i];
                }
            }
            else if (valueBitSize == 16)
            {
                for (var i = 0; i < arraySize; i += 2)
                {
                    var tmp = BitConverter.ToUInt16(data, i);
                    var tmpAsBytes = BitConverter.GetBytes(tmp);
                    Array.Reverse(tmpAsBytes);
                    output[i] = tmpAsBytes[0];
                    output[i + 1] = tmpAsBytes[1];
                }
            }
            else if (valueBitSize == 32)
            {
                for (var i = 0; i < arraySize; i += 4)
                {
                    var tmp = BitConverter.ToUInt32(data, i);
                    var tmpAsBytes = BitConverter.GetBytes(tmp);
                    Array.Reverse(tmpAsBytes);
                    output[i] = tmpAsBytes[0];
                    output[i + 1] = tmpAsBytes[1];
                    output[i + 2] = tmpAsBytes[2];
                    output[i + 3] = tmpAsBytes[3];
                }
            }
            else if (valueBitSize == 64)
            {
                for (var i = 0; i < arraySize; i += 8)
                {
                    var tmp = BitConverter.ToUInt64(data, i);
                    var tmpAsBytes = BitConverter.GetBytes(tmp);
                    Array.Reverse(tmpAsBytes);
                    output[i] = tmpAsBytes[0];
                    output[i + 1] = tmpAsBytes[1];
                    output[i + 2] = tmpAsBytes[2];
                    output[i + 3] = tmpAsBytes[3];
                    output[i + 4] = tmpAsBytes[4];
                    output[i + 5] = tmpAsBytes[5];
                    output[i + 6] = tmpAsBytes[6];
                    output[i + 7] = tmpAsBytes[7];
                }
            }
        }
        return output;
    }

    public static void Apply(this byte[] data, uint startAddress, byte[] values)
    {
        for (var i = startAddress; i < startAddress + values.Length; i++)
        {
            data[i] = values[i - startAddress];
        }
    }

    public static void ApplyValues(this byte[] data, object[] addrs)
    {
        foreach (var item in addrs)
        {
            var parameters = item as object[];
            var addrFrom = (uint)parameters[0];
            var addrTo = (uint)parameters[1];
            var values = (byte[])parameters[2];
            for (var addr = addrFrom; addr <= addrTo; addr += (uint)values.Length)
            {
                for (var i = 0u; i < values.Length; i++)
                {
                    SetValue(data, addr + i, values[i]);
                }
            }
        }
    }

    private static void SetValue(byte[] data, uint addr, byte value)
    {
        data[addr] = value;
    }
}