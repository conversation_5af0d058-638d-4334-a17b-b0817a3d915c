﻿using MgFlasher.CustomCode.Models;

namespace MgFlasher.CustomCode.Extensions;

/// <summary>
/// Extensiosn related to SWE. TODO - explain what SWE is (short for Softwareentwicklung?) SWE is the software
/// in the ECU.
/// </summary>
public static class SweExtensions
{
    /// <summary>
    /// Compares if two <see cref="SweVersion" /> s are the same.
    /// </summary>
    /// <param name="compareFrom">First <see cref="SweVersion" /> to compare.</param>
    /// <param name="compareTo">Second <see cref="SweVersion" /> to compare.</param>
    /// <returns>True if both <see cref="SweVersion" /> are the same version.</returns>
    public static bool SweEquals(this SweVersion compareFrom, SweVersion compareTo)
    {
        return compareFrom.Id == compareTo.Id
               && compareFrom.Major == compareTo.Major
               && compareFrom.Minor == compareTo.Minor
               && compareFrom.Patch == compareTo.Patch;
    }
}