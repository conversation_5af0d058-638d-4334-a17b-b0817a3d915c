﻿using System;

namespace MgFlasher.CustomCode.Extensions;

public static class VersionExtensions
{
    public static Version ResolveVersionFromString(this string versionString)
    {
        if (string.IsNullOrEmpty(versionString))
        {
            throw new InvalidOperationException("Version cannot be empty");
        }
        Version.TryParse(versionString.Replace("v", ""), out Version result);
        return result;
    }
}