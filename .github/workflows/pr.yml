name: Unit Tests

on:
  pull_request:
  workflow_dispatch:

permissions:
  checks: write
  contents: write
  pull-requests: write

jobs:
  build:
    runs-on: windows-self-hosted
    timeout-minutes: 90

    steps:
    - name: Checkout repository
      uses: actions/checkout@v3

    - name: Update nuget.config local feed path
      run: |
        sed -i "s|./distribution-files/nuget|${{ github.workspace }}\\distribution-files\\nuget|g" nuget.config  

    - uses: weslleymurdock/setup-maui-action@v1.1
      with:
        dotnet-version: 9.0.x

    - name: workload api
      run: dotnet workload restore

    - name:  restore api
      run: dotnet restore ./MgFlasher.sln 

    - name: run tests
      run: dotnet test ./MgFlasher.sln --no-restore --verbosity normal --logger "trx;LogFileName=test-results.trx" --filter "Category!=RealDevice"
    
    - name: generate test report
      if: success() || failure()
      uses: dorny/test-reporter@v1       
      with:
          name: DotNET Tests
          path: "**/test-results.trx"                            
          reporter: dotnet-trx
          fail-on-error: true   