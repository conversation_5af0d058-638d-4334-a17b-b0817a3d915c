name: Windows Build

on:
  push:
    branches: [ "master" ]
  workflow_dispatch:

jobs:
  build:
    permissions:
      checks: write
      contents: write
      pull-requests: write

    runs-on: windows-self-hosted
    timeout-minutes: 60

    steps:
    - name: Checkout repository
      uses: actions/checkout@v3

    - name: Update nuget.config local feed path
      run: |
        sed -i "s|./distribution-files/nuget|${{ github.workspace }}\\distribution-files\\nuget|g" nuget.config  

    - uses: weslleymurdock/setup-maui-action@v1.1
      with:
        dotnet-version: 9.0.x

    - name: workload api
      run: dotnet workload install maui-windows maui-ios maui-android --version 9.0.202

    - name: Update version in AppxManifest
      run: |
        $csprojPath = "MgFlasher\MgFlasher.csproj"
        $appxmanifestPath = "MgFlasher\Platforms\Windows\Package.appxmanifest"
        $installerPath = "mgflasher-installer.iss"

        # Load the .csproj file and extract the version
        [xml]$csprojXml = Get-Content $csprojPath
        $version = $csprojXml.Project.PropertyGroup.ApplicationDisplayVersion
        $version = [system.String]::Join(" ", $version)
        $version = $version.replace(' ','') + ".0.${{ github.run_number }}.0"

        # Update the AppxManifest file
        [xml]$manifestXml = Get-Content $appxmanifestPath
        $manifestXml.Package.Identity.Version = $version
        $manifestXml.Save($appxmanifestPath)
        Write-Host "Updated Package.appxmanifest Version to $version"

        # Update the installer script
        $installerContent = Get-Content $installerPath
        $installerContent = $installerContent -replace '(#define MyAppDisplayVersion\s+)"\d+(\.\d+)*"', "`$1`"$version`""
        Set-Content $installerPath -Value $installerContent
        Write-Host $installerContent

    - name: Publish .NET project
      run: dotnet publish MgFlasher/MgFlasher.csproj -c Release -f net9.0-windows10.0.19041.0 /p:ApplicationVersion=${{ github.run_number }} /p:SentryProject=windows --output "${{ runner.temp }}\mgflasher-app-publish"

    - name: Rename msix package
      run: |
        # Get the path to the msix package
        $msixPath = Get-ChildItem "${{ github.workspace }}\MgFlasher\bin" -Recurse -Filter "*.msix" | Select-Object -First 1
        
        # Check if the msix package was found
        if ($msixPath -eq $null) {
            Write-Host "No msix package found"
            exit 1
        }

        # Rename the msix package to MgFlasher.msix
        $msixPath = $msixPath.FullName
        Move-Item $msixPath "${{ runner.temp }}\MgFlasher.msix" -Force
        Write-Host "Moving msix package to ${{ runner.temp }}\MgFlasher.msix"

    - name: Sign binary
      uses: lando/code-sign-action@v3
      with:
        file: "${{ runner.temp }}\\MgFlasher.msix"
        certificate-data: ${{ secrets.WINDOWSMGFLASHERPFXB64 }}
        certificate-password: mgflasher

    - name: Create installer
      shell: pwsh
      run: |
        # Define paths
        $7zPath = "${{ github.workspace }}\distribution-files\other\innosetup.zip"
        $extractPath = "${{ github.workspace }}\distribution-files\other"
        $isccPath = "${{ github.workspace }}\distribution-files\other\innosetup\app\ISCC.exe"
        # Unzip innosetup.7z using System.IO.Compression.ZipFile
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::ExtractToDirectory($7zPath, $extractPath)

        # Copy the installer script to the temp directory
        Copy-Item "${{ github.workspace }}/mgflasher-installer.iss" -Destination "${{ runner.temp }}/mgflasher-installer.iss"
        Copy-Item "${{ github.workspace }}/mgflasher-installer-info-before.txt" -Destination "${{ runner.temp }}/mgflasher-installer-info-before.txt"

        # Run the Inno Setup Compiler
        Push-Location ${{ runner.temp }}
        & "$isccPath" "${{ runner.temp }}\mgflasher-installer.iss"

    - name: Compile changelog
      id: vars
      shell: powershell
      run: |
        dotnet run --project Tools/MgFlasher.Tools.Changelog/MgFlasher.Tools.Changelog.csproj --working-directory ${{ github.workspace }}
        $csprojPath = "${{ github.workspace }}\MgFlasher\MgFlasher.csproj"
        [xml]$csprojXml = Get-Content $csprojPath
        $version = $csprojXml.Project.PropertyGroup.ApplicationDisplayVersion
        $version = [system.String]::Join("", $version).Trim()
        $buildId = "${{ github.run_number }}"
        echo "::set-output name=version::$version"
        echo "::set-output name=buildId::$buildId"

    - name: List all files from ${{ github.workspace }}
      run: |
        echo "Listing all files and directories recursively in ${{ github.workspace }}:"
        Get-ChildItem -Path "${{ github.workspace }}" -Recurse -File | ForEach-Object { Write-Host "File: $($_.FullName)" }

    - name: List all files from ${{ runner.temp }}
      run: |
        echo "Listing all files and directories recursively in ${{ runner.temp }}:"
        Get-ChildItem -Path "${{ runner.temp }}" -Recurse -File | ForEach-Object { Write-Host "File: $($_.FullName)" }

    - name: Create GitHub Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: win-v${{ steps.vars.outputs.version }}b${{ steps.vars.outputs.buildId }}
        release_name: win-v${{ steps.vars.outputs.version }}b${{ steps.vars.outputs.buildId }}
        body_path: ${{ github.workspace }}\ChangeLog.compiled.md
        draft: false
        prerelease: false

    - name: Upload Msix to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ${{ runner.temp }}/MgFlasher.msix
        asset_name: MgFlasher-v${{ steps.vars.outputs.version }}b${{ steps.vars.outputs.buildId }}.msix
        asset_content_type: application/vnd.ms-msix

    - name: Upload Installer to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ${{ runner.temp }}/mgflasher-installer.exe
        asset_name: mgflasher-installer-v${{ steps.vars.outputs.version }}b${{ steps.vars.outputs.buildId }}.exe
        asset_content_type: application/octet-stream

    - name: Upload artifacts to MgFlasher backend
      shell: pwsh
      run: |
        # Define variables
        $UPLOAD_URL = "https://backend.net.mgflasher.com/api/v1/distribution/manage/Windows/upload"
        $API_KEY = "${{ secrets.MGFLASHER_BACKEND_NET_API_KEY }}"
        
        # Find the installer file
        $FILE = "${{ runner.temp }}/mgflasher-installer.exe"
        if (-not (Test-Path $FILE)) {
          Write-Error "No .exe file found at $FILE"
          exit 1
        }
        $NEW_FILE_NAME = "mgflasher-installer-v${{ steps.vars.outputs.version }}b${{ steps.vars.outputs.buildId }}.exe"
        Rename-Item -Path $FILE -NewName $NEW_FILE_NAME -Force
        $FILE = "${{ runner.temp }}/$NEW_FILE_NAME"
        
        Write-Host "Uploading file: $FILE"
        
        # Upload the file using curl
        $headers = @{
          "ApiKey" = $API_KEY
        }
        
        # Use curl to upload the file
        curl.exe -X POST `
          -H "ApiKey: $API_KEY" `
          -F "file=@$FILE" `
          $UPLOAD_URL