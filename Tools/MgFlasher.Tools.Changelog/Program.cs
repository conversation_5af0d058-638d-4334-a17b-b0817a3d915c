using System.Text.RegularExpressions;

const string Input = "ChangeLog.md";
const string Output = "ChangeLog.compiled.md";
var SkipLineRegex = new Regex(@"\[Internal](.*)$", RegexOptions.Compiled);
var VersionRegex = new Regex(@"^## \[\d{3}\]$", RegexOptions.Compiled);

var file = await File.ReadAllLinesAsync(Input);
var lastVersionLines = new List<string>();
var lastVersionMatched = false;
foreach (var line in file)
{
    if (VersionRegex.IsMatch(line) && !lastVersionMatched)
    {
        lastVersionMatched = true;
        continue;
    }
    if (VersionRegex.IsMatch(line))
    {
        break;
    }
    if(SkipLineRegex.IsMatch(line)) 
    {
        continue;
    }
    var text = line.Replace("## ", "");
    lastVersionLines.Add(text);
}

var filtered = lastVersionLines
    .Select(x => x.Trim())
    .Where(x => !string.IsNullOrEmpty(x))
    .Skip(4); //preambule formula skip

var joined = string.Join("\n", filtered);

Console.WriteLine(joined);
await File.WriteAllTextAsync(Output, joined);