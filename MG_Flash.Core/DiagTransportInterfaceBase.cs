﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Globalization;
using System.Diagnostics;
using CableInterface;
using Microsoft.Extensions.Logging;

namespace DiagTransportInterface;

public abstract class DiagTransportInterfaceBase
{
    private readonly ILogger _logger;
    private static readonly CultureInfo Culture = CultureInfo.CreateSpecificCulture("en");

    protected bool HasAdapterEcho = true; // INPA K-DCAN always echo's back, CanRaw ignores this parameter in code

    public const int maxExecutionTimeDefault = 10000; // in ms, 10 seconds
    public const int maxWaitingTimeMSDefault = 20000; // in ms, 20 seconds

    protected const int EchoTimeout = 150; // should even allow the Wifi to run with Nagle/Delayed Ack enabled

    protected long LastResponseTick;

    protected Dictionary<byte, int> Nr78Dict = new Dictionary<byte, int>();

    protected static readonly long TickResolMs = Stopwatch.Frequency / 1000;

    protected int ParTimeoutStd;
    protected int ParRequestTimeNr21;
    protected int ParRequestTimeNr23;
    protected int ParRetryNr21;
    protected int ParRetryNr23;
    protected int ParRetryNr78;
    protected int ParTimeoutNr78;
    protected int ParTimeoutTelEnd;

    protected int ParInterbyteTime;
    protected int ParRegenTime;

    public CableInterfaceBase CableInterface;

    public abstract UInt32 TransportVersion { get; }
    public abstract string TransportName { get; }
    public abstract int TransportMaxPayloadSize { get; }

    public DiagTransportInterfaceBase(ILogger logger)
    {
        _logger = logger;
    }

    public abstract bool Initialize(string mode, int baudRate);

    public void SetTimingParameters(ushort P2max, uint P2star)
    {
        ParTimeoutStd = P2max;
        ParTimeoutNr78 = (int)P2star;
    }

    public ushort GetP2max()
    {
        return (ushort)ParTimeoutStd;
    }

    public ushort GetP2Starmax()
    {
        return (ushort)ParTimeoutNr78;
    }

    public abstract TransportErrorCodes SendReceive(byte testerId, byte moduleId, byte[] snddata, ref uint responseTesterId, ref uint responseModuleId, out byte[] rcvdata, bool responseExpected = true, int maxExecutionTime = maxExecutionTimeDefault, int maxWaitingTimeMS = maxWaitingTimeMSDefault, bool enableLogging = true);

    public enum TransportErrorCodes : uint
    {
        TRANSPORT_ERR_NONE = 0,
        TRANSPORT_ERR_SND_IF = 1,
        TRANSPORT_ERR_ECHO_NA = 2,
        TRANSPORT_ERR_ECHO_IV = 3,
        TRANSPORT_ERR_RSP_NOHEAD = 4,
        TRANSPORT_ERR_RSP_HEAD_IV = 5,
        TRANSPORT_ERR_RSP_NOTAIL = 6,
        TRANSPORT_ERR_RSP_CHKS = 7,
    }

    public string GetErrorDescription(TransportErrorCodes errorCode)
    {
        switch (errorCode)
        {
            case TransportErrorCodes.TRANSPORT_ERR_NONE: return "postiveResponse";
            case TransportErrorCodes.TRANSPORT_ERR_SND_IF: return "could not send data to interface";
            case TransportErrorCodes.TRANSPORT_ERR_ECHO_NA: return "no echo recieved from interface";
            case TransportErrorCodes.TRANSPORT_ERR_ECHO_IV: return "invalid echo recieved from interface";
            case TransportErrorCodes.TRANSPORT_ERR_RSP_NOHEAD: return "no header recieved from module";
            case TransportErrorCodes.TRANSPORT_ERR_RSP_HEAD_IV: return "invalid header recieved from module";
            case TransportErrorCodes.TRANSPORT_ERR_RSP_NOTAIL: return "no tail recieved from module";
            case TransportErrorCodes.TRANSPORT_ERR_RSP_CHKS: return "invalid checksum on data received from interface";
            default: return string.Format("unknown Error Code 0x{0:X04}", errorCode);
        }
    }

    protected void Nr78DictAdd(byte deviceAddr, bool enableLogging, long executionStartTick, int maxExecutionTime)
    {
        if (Nr78Dict.TryGetValue(deviceAddr, out int retries))
        {
            Nr78Dict.Remove(deviceAddr);
            if (++retries <= ParRetryNr78 && ((Stopwatch.GetTimestamp() - executionStartTick) < maxExecutionTime * TickResolMs))
            {
                if (enableLogging) _logger.LogFormat("NR78 ({0:X02}) count = {1}", deviceAddr, retries);
                Nr78Dict.Add(deviceAddr, retries);
            }
            else // maxExecutionTime or max retries exceeded
            {
                if (retries <= ParRetryNr78)
                {
                    if (enableLogging) _logger.LogFormat("*** NR78 ({0:X02}) maxExecutionTime exceeded", deviceAddr);
                }
                else
                {
                    if (enableLogging) _logger.LogFormat("*** NR78 ({0:X02}) maxRetryNr78 exceeded", deviceAddr);
                }
            }
        }
        else
        {
            if (enableLogging) _logger.LogFormat("NR78 ({0:X02}) added", deviceAddr);
            Nr78Dict.Add(deviceAddr, 0);
        }
    }

    protected void Nr78DictRemove(byte deviceAddr, bool enableLogging)
    {
        if (Nr78Dict.ContainsKey(deviceAddr))
        {
            if (enableLogging) _logger.LogFormat("NR78 ({0:X02}) removed", deviceAddr);
            Nr78Dict.Remove(deviceAddr);
        }
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            CableInterface.Dispose();
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}