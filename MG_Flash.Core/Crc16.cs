﻿/// OPTIONS FOR UDS BRANCH:
#define UDS_WRITE_VIN
#define USE_UDS_PROTOCOL
// #define FULL_COMPAT_CHECK // if not defined -> Compat check skip last 3 digits (minor version/revision)
#define LMV_XCP_OVER_FLEX_UNLOCK

/// OPTIONS FOR KWP2000 BRANCH:
#define KWP2000_WRITE_VIN
// #define UPLOAD_METHOD_CMD35_CMD36_CMD37
// #define GRACEFULL_READ_MODULE_EXIT

/// GENERIC OPTIONS (Both Branches)
// #define DOWNLOAD_BACKSTEP_AUTOFAIL

using System;

namespace UNI_Flash;

//
// TODO SPLIT
//
public class Crc16
{
    private const ushort polynomial = 0xA001;
    private ushort[] table = new ushort[256];

    public ushort ComputeChecksum(byte[] bytes, int startidx, int len)
    {
        ushort crc = 0;
        for (int i = 0; i < len; ++i)
        {
            byte index = (byte)(crc ^ bytes[i + startidx]);
            crc = (ushort)((crc >> 8) ^ table[index]);
        }
        return crc;
    }

    public byte[] ComputeChecksumBytes(byte[] bytes, int startidx, int len)
    {
        ushort crc = ComputeChecksum(bytes, startidx, len);
        return BitConverter.GetBytes(crc);
    }

    public Crc16()
    {
        ushort value;
        ushort temp;
        for (ushort i = 0; i < table.Length; ++i)
        {
            value = 0;
            temp = i;
            for (byte j = 0; j < 8; ++j)
            {
                if (((value ^ temp) & 0x0001) != 0)
                {
                    value = (ushort)((value >> 1) ^ polynomial);
                }
                else
                {
                    value >>= 1;
                }
                temp >>= 1;
            }
            table[i] = value;
        }
    }
}