﻿/// OPTIONS FOR UDS BRANCH:
#define UDS_WRITE_VIN
#define USE_UDS_PROTOCOL
#define BOSCH_MG1_UNLOCK_BYPASS
#define LMV_XCP_OVER_FLEX_UNLOCK

using CableInterface;
using DiagProtocolInterface;
using DiagProtocolUtils;
using EdiabasTest;
using MgFlasher.Changesets.SupportedEcu.ChipNames;
using MgFlasher.Changesets.SupportedEcu.Models;
using MgFlasher.Changesets.SupportedEcu.Services;
using Microsoft.Extensions.Logging;

/// GENERIC OPTIONS (Both Branches)
// #define DOWNLOAD_BACKSTEP_AUTOFAIL

using NDesk.Options;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Serialization;

namespace UNI_Flash;

public class MgFlasherCore
{
    public static event EventHandler<double> ProgressUpdated;

    public static event EventHandler<long> FlashTotalBytesDetermined;

    public static event EventHandler<long[]> FlashBlocksDetermined;

    public static event EventHandler<int> ProgressSegmentBytesCompleted;

    public static event EventHandler<string> OnMessage;

    public static event EventHandler<string> OnCodingStored;

    public static bool ClearDtcsDisabled;

    public static IDictionary<string, byte[]> PreloadedInFiles = new Dictionary<string, byte[]>();

    private static readonly CultureInfo Culture = CultureInfo.CreateSpecificCulture("en");

#if !(USE_UDS_PROTOCOL) // KWP2000
#if (GRACEFULL_READ_MODULE_EXIT || UPLOAD_METHOD_CMD35_CMD36_CMD37)
        private static bool keepRunning = true;
#else
        private static readonly bool keepRunning = true;
#endif
#endif
    private static DiagProtocolBase.ModuleIds moduleId = (DiagProtocolBase.ModuleIds)0xff;
    public static string forcedVariantIndexBoot = string.Empty;

    public static BmwEnet attachedCable;
    public static Ucl_NRV Ucl_Nrv;
    public static ILogger Logger;
#if USE_UDS_PROTOCOL
    public static UDS_BMW _UDSDiagIf;
    private static int SGBDIndex; // 3 bytes long
    public static ushort activeDiagSession = 0;
    public static ushort authentificationTime = 0, bootLoaderInstallTime = 0, checkMemoryTime = 0, eraseTime = 0, resetTime = 0, FTP_format = 0, transferDataTime = 0;
    private static byte eraseTimeFormat = 0, checkMemoryTimeFormat = 0, authentificationTimeFormat = 0, resetTimeFormat = 0, bootLoaderInstallTimeFormat = 0;
    private static bool updateFingerPrint = false;
    private static bool NrvCompressSegments = true;
#if BOSCH_MG1_UNLOCK_BYPASS
    private static bool BoschMG1RsaBypassBootloaderFirstUpload = false;
    private static bool BoschMG1RsaPatchedBootloaderPresent = false;
    private static bool skipBTLDcrcRead = false;
#endif
    private static UnlockWaveType SecurityWave = UnlockWaveType.Undefined;

    private static bool BTLD_old_aurix_install = false;
#else // KWP2000
        public static Kwp2000_BMW _Kwp2000DiagIf;
        public static string VariantIndexS = string.Empty;
        private static byte FlashAuthTime, FlashEraseTime, FlashResetTime, FlashSignatureTime;
        private static byte FlashBlockLengthDaten = 0;
#endif

    public static int Main(string[] args)
    {
        int resultCode = -1;
#if USE_UDS_PROTOCOL
        // initialize variables, args
        updateFingerPrint = false;
        NrvCompressSegments = true;
#if BOSCH_MG1_UNLOCK_BYPASS
        BoschMG1RsaBypassBootloaderFirstUpload = false;
        BoschMG1RsaPatchedBootloaderPresent = false;
#endif
        BTLD_old_aurix_install = false;
#endif
        string comPort = "FTDI";
#if USE_UDS_PROTOCOL
        bool inputModeSwflFiles = false;
#else
            bool performRsaBypass = false;
#endif
        List<ModuleConnectInfo> moduleConnectInfo = new List<ModuleConnectInfo>();
        byte requiredActions = (byte)ActionOption.NONE;
        List<string> inFiles = new List<string>();
        bool backupCodingOnly = false;
        bool anyUnlockAllowed = false;
        bool forcebackupcoding = false;
        bool performCodingEnabled = false;
        bool performCodingRestoreFromBackup = true;
        bool performDtcClear = false;
        bool performDtcRead = false;
        bool flashingFullStock = false;
        string codingFile = string.Empty;
        string alternateVIN = string.Empty;
        uint responseTesterId = 0;
        uint responseModuleId = 0;

        int readcommandOption = (int)ReadCommandOption.DISABLED;
        int writecommandOption = (int)WriteCommandOption.DISABLED;

        bool showHelp = false;
        string externalBackupPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);

#if GRACEFULL_READ_MODULE_EXIT
            Console.CancelKeyPress += delegate(object sender, ConsoleCancelEventArgs e)
            {
                e.Cancel = true;
                keepRunning = false;
            };
#endif
        var p = new MultiOptionSet()
        {
            {
                "p|port=", "COM port (COMx, FTDI (=default), ENET(:IP default AUTO), WIFI:IP:Port or CANUSB)",
                v => comPort = v.ToUpper()
            },
            {
                "m|module=", "name of the module to connect to (DME_MASTER, EGS, ...)",
                v => {
                    if (!(Enum.IsDefined(typeof(DiagProtocolBase.ModuleIds), v.ToUpper())))
                    {
                        string value = "  valid module names are: ";
                        foreach (DiagProtocolBase.ModuleIds m in Enum.GetValues(typeof(DiagProtocolBase.ModuleIds)))
                        {
                            value += (m.ToString() + " ");
                        }
                        throw new OptionException("Invalid module name for option -m: " + value,"-m");
                    }
                    else
                    {
                        moduleId = (DiagProtocolBase.ModuleIds) Enum.Parse(typeof(DiagProtocolBase.ModuleIds), v.ToUpper());
                    }
                }
            },
            {
                "i|in=", "input file name(s) for write action(s)",
                v => inFiles.Add (v.Trim())
            },
#if USE_UDS_PROTOCOL
            {
                "s|swfl",  "input files are SWFL files (default RAW binary)",
                v => inputModeSwflFiles = v != null
            },
#else // KWP2000
                {
                    "b|bypass",  "perform RSA bypass not normal RSA signature check",
                    v => performRsaBypass = v != null
                },
#endif
            {
                "bc|backupcoding",  "backup coding to ncd srec format",
                v => backupCodingOnly = v != null
            },
            {
                "au|anyunlock",  "allow any found unlock to be used for flashing",
                v => anyUnlockAllowed = v != null
            },
            {
                "fbc|forcebackupcoding",  "force backup coding to ncd srec format (even if it already exists)",
                v => forcebackupcoding = v != null
            },
            {
                "n|ncd=",  "coding data (ncd srec format) to apply (default=Coding backup)",
                v => {
                    codingFile = v;
                    if (string.IsNullOrWhiteSpace(codingFile)||(!(codingFile.EndsWith(".srec")||codingFile.EndsWith(".ncd")))||!(File.Exists(codingFile)))
                    {
                        throw new OptionException ("Invalid coding data file name for option -n.", "-n");
                    }
                    else
                    {
                        performCodingRestoreFromBackup = false;
                        performCodingEnabled = true;
                    }
                }
            },
            {
                "f|force=", "module in BOOT-Mode, forced Variant/SGBD index",
                v => forcedVariantIndexBoot = v.ToUpper()
            },
            {
                "fs|fullStock=", "full stock is targetted, flashing SWFL with Femto unlock is allowed",
                v => flashingFullStock = v != null
            },
            {
                "c|dtc_clear",  "clear DTCs (ALL modules)",
                v => performDtcClear = v != null
            },
            {
                "d|dtc_read",  "read DTCs",
                v => performDtcRead = v != null
            },
#if USE_UDS_PROTOCOL
            {
                "r|read=", "read action (r=2 read EEPROM from module to bin file, r=3 read DST from module to bin file, r=4 read BTLD, PST & DST from module to bin file, r=5 read full ecu from module to bin file, r=6 virtual read, reconstruct bin from SVK data)",
                v => {
                    if(Int32.TryParse(v, out readcommandOption))
                    {
                        if (!(readcommandOption == (int)ReadCommandOption.READ_MODULE_FULL_BIN
                              || readcommandOption == (int)ReadCommandOption.READ_MODULE_BTLD_PST_DST_BIN
                              || readcommandOption == (int)ReadCommandOption.READ_MODULE_DST_BIN
                              || readcommandOption == (int)ReadCommandOption.READ_MODULE_EEPROM_BIN
                              || readcommandOption == (int)ReadCommandOption.READ_VIRTUAL_BIN))
                        {
                            throw new OptionException ("Invalid read action for option -r.","-r");
                        }
                    }
                    else
                    {
                        throw new OptionException ("Attempted conversion of read action for option -r failed.", "-r");
                    }
                }
            },
            {
                "w|write=", "write action combo (default in SWFL mode=AUTO eliminate, w=1 DST, w=2 PST, w=4 BTLD, w=8 HWL or w=16 SWFL_ALL)",
                v => {
                    if(Int32.TryParse(v, out writecommandOption))
                    {
                        if (writecommandOption > ((int)WriteCommandOption.BTL_HWL_PST_DST | (int)WriteCommandOption.SWFL_ALL))
                        {
                            throw new OptionException ("Invalid write action for option -w:", "-w");
                        }
                    }
                    else
                    {
                        throw new OptionException ("Attempted conversion of write action for option -w failed.", "-w");
                    }
                }
            },
#if UDS_WRITE_VIN
            {
                "v|vin_force=", "long VIN (17 digits) to apply to module, overriding current VIN",
                v => {
                    alternateVIN = v.ToUpper();
                    if (alternateVIN.Length != 17)
                    {
                        throw new OptionException ("Invalid long VIN length for option -v.","-v");
                    }
                }
            },
#endif
            {
                "u|fingerprint_update",  "update fingerprint, default is keep identical",
                v => updateFingerPrint = v != null
            },
            {
                "no_nrv|no_nrv_compress",  "do not perform NRV compression on segments in RAW binary mode (default is NRV compress)",
                v => NrvCompressSegments = v == null
            },
#if BOSCH_MG1_UNLOCK_BYPASS
            {
                "bi|bltd_rbp_install",  "Bosch MG1 btld RSA bypass first upload (needs -r 5/6 style stock bin as input file)",
                v => BoschMG1RsaBypassBootloaderFirstUpload = v != null
            },
            {
                "bp|bltd_rpa_present",  "Bosch MG1 RSA patched btld installed (normally auto detected except when stuck in boot)",
                v => BoschMG1RsaPatchedBootloaderPresent = v != null
            },
#endif
            {
                "ba|bltd_old_aurix_install",  "Bosch MG1 Old BTLD install (Aurix ONLY)",
                v => BTLD_old_aurix_install = v != null
            },
#endif
            {
                "ebp|external_backup_path=", "Provider path to save backup files",
                v => externalBackupPath = v
            },
            {
                "h|help",  "show this message and exit",
                v => showHelp = v != null
            },
            { "<>", // default
                v => {
                    throw new OptionException (string.Format("Unknown option: '{0}'", v), v);
                }
            },
        };

        string assemblyName = Assembly.GetExecutingAssembly().GetName().Name;
        string assemblyVersion = Assembly.GetExecutingAssembly().GetName().Version.ToString();
        string assemblyDescription = string.Empty;

        var attribute = Assembly.GetExecutingAssembly()
            .GetCustomAttributes(typeof(AssemblyDescriptionAttribute), false)
            .Cast<AssemblyDescriptionAttribute>().FirstOrDefault();
        if (attribute != null)
        {
            assemblyDescription = attribute.Description;
        }

        const string programProtocol = "UDS";

        Logger.LogInformation("{0} {1} {2} [{3}]", assemblyName, programProtocol, assemblyVersion, assemblyDescription);
        Logger.LogInformation("Written by the MG Flasher Team!");

        if (args.Length <= 1)
        {
            ShowHelp(p);
            return 0;
        }

        try
        {
            p.Parse(args);

            if ((byte)moduleId == 0xff) throw new OptionException("Missing required option -m:", "-m");
#if USE_UDS_PROTOCOL && BOSCH_MG1_UNLOCK_BYPASS
            if (BoschMG1RsaBypassBootloaderFirstUpload == true && BoschMG1RsaPatchedBootloaderPresent == true)
            {
                throw new OptionException("Bosch MG1 btld RSA bypass first upload and RSA patched btld installed are mutually exclusive, specify either one not both", "-bi -bp");
            }
#endif
            if (readcommandOption != 0xff && writecommandOption != 0xff) throw new OptionException("Read and write actions are mutually exclusive, specify either one not both", "-r -w");
#if USE_UDS_PROTOCOL
            if (inputModeSwflFiles == true && writecommandOption == (int)WriteCommandOption.DISABLED) writecommandOption = (int)WriteCommandOption.AUTO;

#if BOSCH_MG1_UNLOCK_BYPASS
            if (BoschMG1RsaBypassBootloaderFirstUpload == true) writecommandOption = (int)WriteCommandOption.BTL_PST;
            if (BTLD_old_aurix_install == true) Logger.LogInformation("Invoked '-ba' command for old BTLD install, use with Aurix only!");
#endif
#endif
        }
        catch (OptionException e)
        {
            string thisName = Path.GetFileNameWithoutExtension(AppDomain.CurrentDomain.FriendlyName);
            Logger.LogInformation(thisName + ": ");
            Logger.LogInformation(e.Message);
            Logger.LogInformation("Try `" + thisName + " --help' for more information.");
            return 1;
        }

        if (showHelp)
        {
            ShowHelp(p);
            return 0;
        }
        //
        // if we get here the list of command line options should be valid
        // process options and create list of tasks to perform
        //
        if (backupCodingOnly == true)
        {
            requiredActions = (byte)ActionOption.READ_CODING; // coding backup only
        }
        if (readcommandOption != (int)ReadCommandOption.DISABLED)
        {
            requiredActions |= (byte)ActionOption.READ_MODULE; // read module only
        }
        else if (writecommandOption != (int)WriteCommandOption.DISABLED && forcebackupcoding)
        {
            requiredActions = ((byte)ActionOption.READ_CODING | (byte)ActionOption.WRITE_MODULE | (byte)ActionOption.WRITE_CODING | (byte)ActionOption.CLEAR_DTCS); // write full
        }
        else if (writecommandOption != (int)WriteCommandOption.DISABLED && !string.IsNullOrWhiteSpace(codingFile))
        {
            requiredActions = ((byte)ActionOption.WRITE_MODULE | (byte)ActionOption.WRITE_CODING | (byte)ActionOption.CLEAR_DTCS); // write full
        }
        else if (writecommandOption != (int)WriteCommandOption.DISABLED)
        {
            requiredActions = ((byte)ActionOption.READ_CODING | (byte)ActionOption.WRITE_MODULE | (byte)ActionOption.WRITE_CODING | (byte)ActionOption.CLEAR_DTCS); // write full
        }
        else if (performCodingEnabled == true)
        {
            requiredActions = ((byte)ActionOption.WRITE_CODING | (byte)ActionOption.CLEAR_DTCS); // write coding only
        }

        if (performDtcClear == true)
        {
            requiredActions |= ((byte)ActionOption.CLEAR_DTCS);
        }
        if (performDtcRead == true)
        {
            requiredActions |= ((byte)ActionOption.READ_DTCS);
        }

        int previousModuleConnectInfoIdx = -1;

        try
        {
            moduleConnectInfo = Deserialize().ToList();
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Error");
        }

#if USE_UDS_PROTOCOL // UDS BRANCH

        var transportName = comPort == "CANUSB" ? "CANRAW_DIAG" : "BMW-FAST";
        Logger.LogInformation("MODULE INFO:");

        if (FetchModuleActiveDiagnosticSession(ref activeDiagSession) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            goto forced_exit;
        }

        string serialNo = string.Empty;

        //
        // read ECUSerialNumber from module
        //
        if (_UDSDiagIf.ReadDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.ECUSerialNumber, out byte[] recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogError("Could not read ECUSerialNumber from module, exiting");
            goto forced_exit;
        }
        else
        {
            if ((recordData[0] & 0x30) == 0x30) serialNo = StringFunctions.ConvertBytesToAsciiString(recordData, 0, recordData.Length); // Ascii serial
            else for (int i = 0; i < recordData.Length; i++) serialNo += string.Format("{0:X01}", recordData[i]); // dumb packed BCD serial
        }

        // find index containing previous info for this moduleId and serialNo, -1 if NA
        previousModuleConnectInfoIdx = moduleConnectInfo.FindIndex(x => (x.moduleId == moduleId && x.serialNo == serialNo));

        //
        // read SGBD from module
        //
        string bootForced = string.Empty;
        if (IsBootMode(activeDiagSession) == false)
        {
            if (_UDSDiagIf.ReadDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.SGBDIndex, out recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogError("Could not read SGBDIndex from module, exiting");
                //goto forced_exit;
                bootForced = " (BOOT forced)";
                if (string.IsNullOrEmpty(forcedVariantIndexBoot)) // no forced variant specified
                {
                    // try to utilize the moduleConnectInfo db
                    if (previousModuleConnectInfoIdx != -1)
                    {
                        bootForced = " (BOOT forced from moduleConnectInfo)";
                        SGBDIndex = moduleConnectInfo[previousModuleConnectInfoIdx].SGBDIndex;
                    }
                    else
                    {
                        Logger.LogInformation("  BMW SGBD Index{0}: Not specified, use -f SGBDIndex, aborting", bootForced);
                        goto forced_exit;
                    }
                }
                else
                {
                    SGBDIndex = Convert.ToInt32(forcedVariantIndexBoot);
                }
            }
            else
            {
                SGBDIndex = ((recordData[0] << 16) + (recordData[1] << 8) + recordData[2]);
            }
        }
        else // bootmode, not allowed to read SGBDIndex
        {
            bootForced = " (BOOT forced)";
            if (string.IsNullOrEmpty(forcedVariantIndexBoot)) // no forced variant specified
            {
                // try to utilize the moduleConnectInfo db
                if (previousModuleConnectInfoIdx != -1)
                {
                    bootForced = " (BOOT forced from moduleConnectInfo)";
                    SGBDIndex = moduleConnectInfo[previousModuleConnectInfoIdx].SGBDIndex;
                }
                else
                {
                    Logger.LogInformation("  BMW SGBD Index{0}: Not specified, use -f SGBDIndex, aborting", bootForced);
                    goto forced_exit;
                }
            }
            else
            {
                SGBDIndex = Convert.ToInt32(forcedVariantIndexBoot);
            }
        }

        ModuleInfo moduleInfo = DecodeModuleInfoFromSGBDIndex(SGBDIndex, skipFallbackSgbd: false);
        if (!moduleInfo.supplierTypeName.ToUpper().Contains("MG1"))
        {
            var message = $"Unsupported module (not MG1), exiting";
            Logger.LogError(message);
            throw new Exception(message);
        }
        moduleInfo.master = moduleId == DiagProtocolBase.ModuleIds.DME_MASTER;
        moduleInfo.moduleId = moduleId;
        moduleInfo.serialNo = serialNo;

        Logger.LogInformation("  BMW SGBD Index{0}: {1}{2}", bootForced, moduleInfo.SGBDIndex, !string.IsNullOrEmpty(moduleInfo.name) ? " -> " + moduleInfo.name + ", " + moduleInfo.supplierTypeName : "");
        Logger.LogInformation("  BMW ECUSerialNumber: " + moduleInfo.serialNo);

        // update module connect records timeline (and info)
        ModuleConnectInfo newModConInfo;
        newModConInfo.moduleId = moduleId;
        newModConInfo.serialNo = moduleInfo.serialNo;
        newModConInfo.SGBDIndex = moduleInfo.SGBDIndex;

        if (previousModuleConnectInfoIdx != -1) moduleConnectInfo.RemoveAt(previousModuleConnectInfoIdx);
        moduleConnectInfo.Add(newModConInfo);

        // update module connect stats (file moduleConnectInfo.xml)
        try
        {
            Serialize(moduleConnectInfo.ToArray());
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error");
        }

        //
        // read VIN from module
        //
        if (_UDSDiagIf.ReadDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.VIN, out recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogError("Could not read VIN from module, exiting");
            goto forced_exit;
        }
        else
        {
            moduleInfo.VIN = StringFunctions.ConvertBytesToAsciiString(recordData, 0, recordData.Length);
            Logger.LogInformation("  BMW VIN: " + moduleInfo.VIN);
        }

        //
        // read SVK_AKTUELL from module
        //
        UDS_BMW.UDS_RecordIdentifier SVK_recordId = UDS_BMW.UDS_RecordIdentifier.SVK_AKTUELL;

        Logger.LogInformation("{0} INFO:", SVK_recordId);

        if (_UDSDiagIf.ReadDataByIdentifier(SVK_recordId, out recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogError("  Could not read {0} from module, exiting", SVK_recordId);
            goto forced_exit;
        }
        else
        {
            // parse SVK_AKTUELL
            byte nrOfRecords = recordData[3];
            byte blockOffset = 17;

            Logger.LogInformation("  SVKVersion: {0}", recordData[0]);
            Logger.LogInformation("  ProgDeps: {0} {1}", recordData[1], _UDSDiagIf.GetCheckProgrammingDependenciesDescription(recordData[1]));
            Logger.LogInformation("  XweCount: {0}", nrOfRecords);
            Logger.LogInformation("  ProgDate: {0:X02}.{1:X02}.{2:X02}", recordData[6], recordData[5], recordData[4]);

            for (byte i = 0; i < nrOfRecords; i++)
            {
                // Store bootloader, coding Id for later
                if (moduleInfo.btldId.All(b => b == 0) && recordData[blockOffset] == 6) // store FIRST BTLD-ID match as active
                {
                    System.Array.Copy(recordData, blockOffset, moduleInfo.btldId, 0, 8);
                }
                else if (recordData[blockOffset] == 5) // CAFD
                {
                    System.Array.Copy(recordData, blockOffset, moduleInfo.cafdId, 0, 8);
                }

                SweArtType sweArt;
                switch (i)
                {
                    case 0:
                    case 1: sweArt = SweArtType.HW; break;
                    case 2: sweArt = SweArtType.BTLD; break;
                    case 3: sweArt = SweArtType.PST; break;
                    case 4: sweArt = SweArtType.DST; break;
                    case 5:
                    default: sweArt = SweArtType.UNKNOWN; break;
                }

                var sweVersion = new SweVersion(StringFunctions.ExtractSectionArray(recordData, blockOffset, 8)) { SweArt = sweArt };
                moduleInfo.sweVersions.Add(sweVersion);

                Logger.LogInformation("  Xwe_Id {0, 2}: {1}", i, sweVersion.GetXweString());
                blockOffset += 8;
            }

            moduleInfo.currentFingerprint = new byte[13];
            for (int t = 0; t < 13; t++) moduleInfo.currentFingerprint[t] = recordData[4 + t];

            Logger.LogTrace("Fingerprint Current: {0}", StringFunctions.ConvertBytesToHexString(moduleInfo.currentFingerprint));
        }

        moduleInfo.bootctrlVersion = ReadBootctrlVersion();
        moduleInfo.manufacturingDate = ReadEcuManufacturingDate();

#if BOSCH_MG1_UNLOCK_BYPASS
        if (!IsSupportedArchitecture(moduleInfo)) // BOSCH MG1 Extra info and commands handling (only allowed on PPC or AURIX DME)
        {
            BoschMG1RsaBypassBootloaderFirstUpload = false;
            BoschMG1RsaPatchedBootloaderPresent = false;

            var message = $"  Error, unsupported processor type. Only PPC and Aurix are supported. Type: {moduleInfo.ProcessorArchitectureType}";
            Logger.LogError(message);
            throw new Exception(message);
        }

        uint btldIdMayorUint = ProtocolUtils.btldMayorIdOf(moduleInfo.btldId);
        bool UnableToReadCRC = false;

        Logger.LogInformation("BOSCH MG1 EXTRA INFO:");

        if (BoschMG1RsaBypassBootloaderFirstUpload == true)
        {
            Logger.LogInformation("  *************************************************************");
            Logger.LogInformation("   MG1 SPECIAL MODE: BoschMG1RsaBypassBootloaderFirstUpload ON");
            Logger.LogInformation("  *************************************************************");
        }
        if (BoschMG1RsaPatchedBootloaderPresent == true)
        {
            Logger.LogInformation("  *****************************************************************");
            Logger.LogInformation("   FORCED MG1 SPECIAL MODE: BoschMG1RsaPatchedBootloaderPresent ON");
            Logger.LogInformation("  *****************************************************************");
        }
        if (BoschMG1RsaBypassBootloaderFirstUpload == false && BoschMG1RsaPatchedBootloaderPresent == false)
        {
            Logger.LogInformation("  *****************************************************************************************");
            Logger.LogInformation("   BoschMG1RsaBypassBootloaderFirstUpload OFF || BoschMG1RsaPatchedBootloaderPresent OFF");
            Logger.LogInformation("  *****************************************************************************************");
        }

        CheckEcuForNewSecurityBtld(ref moduleInfo);
        SecurityWave = CheckForSecurityWave(ref moduleInfo);
        var bootctrlRequiresWave3Unlock = SecurityWave == UnlockWaveType.Wave3;
        CheckForTasOr1769Unlock(ref moduleInfo);

        // TRY to find if patched bootloader is applied and report
        if (IsBootMode(activeDiagSession) && moduleInfo.readMemoryByAddressAvailableInBoot == false)
        {
            var ecuMgfUnlockVersion = MgFlasherCore.GetMgfUnlockVersion(moduleInfo, 0);
            if (ecuMgfUnlockVersion >= new Version(1, 0))
            {
                moduleInfo.unlockCompany = UnlockCompanyName.MG_Flasher;
                moduleInfo.unlocked = "MGF";
                BoschMG1RsaPatchedBootloaderPresent = true;
            }

            if (BoschMG1RsaPatchedBootloaderPresent == false)
            {
                Logger.LogInformation("  No CMD 0x23 ReadMemoryByAddress available in UDS Boot-Mode for module {0}, assuming OEM btld (override with -bp)", moduleInfo.name);
            }
        }
        else
        {
            byte[] stock_btld_compare = { 0x00, 0x00, 0x00, 0x00 };

            uint btld_forceCRC32_location = 0x00;
            uint btld_forceCRC32_location_BMWexplorer = 0x00;
            uint btld_forceCRC32_location_Universal = 0x00;
            uint btld_forceCRC32_location_MHD = 0x00;
            uint btld_forceCRC32_location_btldRsaChecksum = 0x00; // checksum location
            uint btld_forceCRC32_location_bflash = 0x00; // checksum location
            uint btld_forceCRC32_location_bflash2 = 0x00; // checksum location
            uint femto_Unlock_location = 0X00;

            if (IsPpcArchitecture(moduleInfo))
            {
                btld_forceCRC32_location = 0x09000290;
                btld_forceCRC32_location_BMWexplorer = 0x09000260;
                btld_forceCRC32_location_Universal = 0x00000000;
                btld_forceCRC32_location_MHD = 0x0977F6E8;
                btld_forceCRC32_location_btldRsaChecksum = 0x0903FD0C; // checksum location
                btld_forceCRC32_location_bflash = 0x097541BC;
                btld_forceCRC32_location_bflash2 = 0x090141BC;
                femto_Unlock_location = 0x0977F6E8;
            }
            else if (IsAurixArchitecture(moduleInfo))
            {
                btld_forceCRC32_location = 0x80028290;
                btld_forceCRC32_location_BMWexplorer = 0x80028260;
                btld_forceCRC32_location_Universal = 0x8005F7DC;
                btld_forceCRC32_location_MHD = 0x807FF6E8;
                btld_forceCRC32_location_btldRsaChecksum = 0x8005FD0C; // checksum location
                btld_forceCRC32_location_bflash = 0x80028290; // not a real address!
                btld_forceCRC32_location_bflash2 = 0x00; // not a real address!
                femto_Unlock_location = 0x807FF6E8;
            }

            if (IsNewBtldSecurity(SecurityWave) && (_UDSDiagIf.ReadMemoryByAddress((uint)btld_forceCRC32_location, (byte)stock_btld_compare.Length, out byte[] data) == UDS_BMW.UDS_ErrorCodes.UDS_NRC_0010))
            {
                Logger.LogInformation("  'General Reject' when reading from MG Flasher location:" + string.Format("0x{0:X08}, chunksize {1}. This may happen when OBD reading is prohibited via 3rd party unlock, continuing anyway.", btld_forceCRC32_location, 0x4));
                BoschMG1RsaBypassBootloaderFirstUpload = false;
                BoschMG1RsaPatchedBootloaderPresent = false;
                UnableToReadCRC = true;
            }
            else if (MgFlasherCore._UDSDiagIf.ReadMemoryByAddress((uint)btld_forceCRC32_location, (byte)stock_btld_compare.Length, out data) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogInformation("  Error reading from MG Flasher location: " + string.Format("0x{0:X08}, chunksize {1}. Unable to determine Bootloader patch, continuing anyway.", btld_forceCRC32_location, 0x4));
                BoschMG1RsaBypassBootloaderFirstUpload = false;
                BoschMG1RsaPatchedBootloaderPresent = false;
                UnableToReadCRC = true;
            }
            else // read ok, compare
            {
                Logger.LogInformation("Read from MG Flasher location, searching for any unlock methods...");

                bool stockBltdMatch = true;
                Logger.LogInformation($"Data at [0x{btld_forceCRC32_location:X08}] => [0x{string.Join(", 0x", data.Select(x => x.ToString("X02")))}]");
                if (data.Any(b => b != 0x00))
                {
                    Logger.LogInformation("Found modified btld using new conditional check, but its not active!");
                    stockBltdMatch = false;
                }

                if (stockBltdMatch == true) // read another time to check for BmwExplorer style patch in other location
                {
                    Logger.LogInformation("Checking BmwExplorer location for any unlock methods...");
                    if (_UDSDiagIf.ReadMemoryByAddress((uint)btld_forceCRC32_location_BMWexplorer, (byte)stock_btld_compare.Length, out data) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                    {
                        Logger.LogInformation("  Error reading from BmwExplorer location: " + string.Format("0x{0:X08}, chunksize {1}. Unable to determine Bootloader patch, continuing anyway.", btld_forceCRC32_location_BMWexplorer, 0x4));
                        BoschMG1RsaBypassBootloaderFirstUpload = false;
                        BoschMG1RsaPatchedBootloaderPresent = false;
                        UnableToReadCRC = true;
                        skipBTLDcrcRead = true;
                    }
                    if (!skipBTLDcrcRead)
                    {
                        Logger.LogInformation($"Data at [0x{btld_forceCRC32_location_BMWexplorer:X08}] => [0x{string.Join(", 0x", data.Select(x => x.ToString("X02")))}]");
                        if (data.Any(b => b != 0x00))
                        {
                            Logger.LogInformation("Found modified btld using new conditional check, but its not active!");
                            stockBltdMatch = false;
                        }
                    }
                }
                if (stockBltdMatch == true && IsNewBtldSecurity(SecurityWave) && IsAurixArchitecture(moduleInfo)) // read another time to check for universal style patch in other location
                {
                    Logger.LogInformation("Checking 'universal' location for any unlock methods...");
                    if (MgFlasherCore._UDSDiagIf.ReadMemoryByAddress((uint)btld_forceCRC32_location_Universal, (byte)stock_btld_compare.Length, out data) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                    {
                        Logger.LogInformation("  Error reading from universal location: " + string.Format("0x{0:X08}, chunksize {1}. Unable to determine Bootloader patch, continuing anyway.", btld_forceCRC32_location_Universal, 0x4));
                        BoschMG1RsaPatchedBootloaderPresent = false;
                        UnableToReadCRC = true;
                        skipBTLDcrcRead = true;
                    }
                    if (!skipBTLDcrcRead)
                    {
                        Logger.LogInformation($"Data at [0x{btld_forceCRC32_location_Universal:X08}] => [0x{string.Join(", 0x", data.Select(x => x.ToString("X02")))}]");
                        if (data.Any(b => b != 0x00))
                        {
                            Logger.LogInformation("Found modified btld using new conditional check, but its not active!");
                            stockBltdMatch = false;
                        }

                        if (!stockBltdMatch)
                        {
                            if (IsNewBtldSecurity(SecurityWave) || anyUnlockAllowed)
                            {
                                BoschMG1RsaPatchedBootloaderPresent = true;
                                skipBTLDcrcRead = !anyUnlockAllowed;
                                moduleInfo.unlockCompany = UnlockCompanyName.Unknown;
                                moduleInfo.unlocked = "UnknownPatchPresent";
                                Logger.LogInformation("Found unknown patch at " + string.Format("0x{0:X08}, chunksize {1}. We'll assume its a 3rd party unlock (PROCEED WITH CAUTION).", btld_forceCRC32_location_Universal, 0x4));
                            }
                            else
                            {
                                Logger.LogInformation("Found unknown patch at " + string.Format("0x{0:X08}, chunksize {1}. But btld security is low and we want to force our unlock method so skip this.", btld_forceCRC32_location_Universal, 0x4));
                            }
                        }
                    }
                }

                if (stockBltdMatch == false && !skipBTLDcrcRead) // determine what patch is applied from the bootCRC and Btld version lookup
                {
                    Logger.LogInformation("Checking what patch was applied via bootcrc and btld version...");
                    uint btldCrcPatch = BitConverter.ToUInt32(StringFunctions.SwitchEndian(data), 0);

                    var mgfUnlockVersion = MgFlasherCore.GetMgfUnlockVersion(moduleInfo, btldCrcPatch);
                    var hasMgfBootloaderUnlock = mgfUnlockVersion >= new Version(1, 0);

                    if (!hasMgfBootloaderUnlock)
                    {
                        if (IsNewBtldSecurity(SecurityWave) || anyUnlockAllowed)
                        {
                            BoschMG1RsaPatchedBootloaderPresent = true;
                            skipBTLDcrcRead = !anyUnlockAllowed;
                            moduleInfo.unlockCompany = UnlockCompanyName.Unknown;
                            moduleInfo.unlocked = "UnknownPatchPresent";
                            Logger.LogInformation("Found unknown patch! We'll assume its a 3rd party unlock (PROCEED WITH CAUTION).");
                        }
                        else
                        {
                            BoschMG1RsaPatchedBootloaderPresent = false;
                            skipBTLDcrcRead = true;
                            Logger.LogInformation($" Unable to determine MG Flasher Bootloader patch, continuing anyway.");
                            Logger.LogInformation(" Found unknown patch but btld security is low and we want to force our unlock method so skip this.");
                        }
                    }
                    if (!skipBTLDcrcRead)
                    {
                        Logger.LogInformation($"  BTLD MGF Unlock version: [v{mgfUnlockVersion}]");

                        if (BoschMG1RsaBypassBootloaderFirstUpload == false && BoschMG1RsaPatchedBootloaderPresent == false && hasMgfBootloaderUnlock)
                        {
                            moduleInfo.unlockCompany = UnlockCompanyName.MG_Flasher;
                            moduleInfo.unlocked = "MGF";
                            BoschMG1RsaPatchedBootloaderPresent = true;
                        }
                    }
                }
                else if (BoschMG1RsaPatchedBootloaderPresent == false && IsNewBtldSecurity(SecurityWave))
                {
                    if (btld_forceCRC32_location_bflash != 0x00)
                    {
                        if (MgFlasherCore._UDSDiagIf.ReadMemoryByAddress((uint)btld_forceCRC32_location_bflash, 0x4, out byte[] bFlashPatchData) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                        {
                            Logger.LogInformation("Error reading from bFlash location: " + string.Format("0x{0:X08}, chunksize {1}. Unable to determine Bootloader patch, continuing anyway.", btld_forceCRC32_location_bflash, 0x4));
                            BoschMG1RsaPatchedBootloaderPresent = false;
                            UnableToReadCRC = true;
                        }
                        else
                        {
                            Logger.LogInformation("Read " + string.Format("chunksize {0} from bFlash location: 0x{1:X08}", bFlashPatchData.Length, btld_forceCRC32_location_bflash));
                            Logger.LogInformation($"bFlash patch:\n0x{string.Join(", 0x", bFlashPatchData.Select(x => x.ToString("X02")))}");
                            if (bFlashPatchData[0] == 0x48 && bFlashPatchData[2] == 0x48 && bFlashPatchData[3] == 0x03)
                            {
                                BoschMG1RsaPatchedBootloaderPresent = true;
                                moduleInfo.unlockCompany = UnlockCompanyName.bFlash;
                                moduleInfo.unlocked = "bFlash";
                                Logger.LogInformation($"It appears to have the expected bFlash patch...");
                            }
                            else
                            {
                                Logger.LogInformation($"bFlash patch doesn't appear in this location?");
                            }
                        }
                    }

                    if (btld_forceCRC32_location_bflash2 != 0x00)
                    {
                        if (MgFlasherCore._UDSDiagIf.ReadMemoryByAddress((uint)btld_forceCRC32_location_bflash2, 0x4, out byte[] bFlash2PatchData) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                        {
                            Logger.LogInformation("Error reading from bFlash2 location: " + string.Format("0x{0:X08}, chunksize {1}. Unable to determine Bootloader patch, continuing anyway.", btld_forceCRC32_location_bflash2, 0x4));
                            BoschMG1RsaPatchedBootloaderPresent = false;
                            UnableToReadCRC = true;
                        }
                        else
                        {
                            Logger.LogInformation("Read " + string.Format("chunksize {0} from bFlash2 location: 0x{1:X08}", bFlash2PatchData.Length, btld_forceCRC32_location_bflash2));
                            Logger.LogInformation($"bFlash patch:\n0x{string.Join(", 0x", bFlash2PatchData.Select(x => x.ToString("X02")))}");
                            if (bFlash2PatchData[0] == 0x48 && bFlash2PatchData[2] == 0x44 && bFlash2PatchData[3] == 0x00)
                            {
                                BoschMG1RsaPatchedBootloaderPresent = true;
                                moduleInfo.unlockCompany = UnlockCompanyName.bFlash;
                                moduleInfo.unlocked = "bFlash";
                                Logger.LogInformation($"It appears to have the expected bFlash2 patch...");
                            }
                            else
                            {
                                Logger.LogInformation($"bFlash2 patch doesn't appear in this location?");
                            }
                        }
                    }

                    Logger.LogInformation("Checking btld checksum region for null data...");
                    if (MgFlasherCore._UDSDiagIf.ReadMemoryByAddress((uint)btld_forceCRC32_location_btldRsaChecksum, 0x100, out byte[] checksumData) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                    {
                        Logger.LogInformation("Error reading from btld checksum location: " + string.Format("0x{0:X08}, chunksize {1}. Unable to determine Bootloader patch, continuing anyway.", btld_forceCRC32_location_btldRsaChecksum, 0x100));
                        BoschMG1RsaPatchedBootloaderPresent = false;
                        UnableToReadCRC = true;
                    }
                    else // read ok, count number of null bytes
                    {
                        Logger.LogInformation("Read " + string.Format("chunksize {0} from btld checksum location: 0x{1:X08}", checksumData.Length, btld_forceCRC32_location_btldRsaChecksum));
                        Logger.LogInformation($"Checksum:\n0x{string.Join(", 0x", checksumData.Select(x => x.ToString("X02")))}");
                        Logger.LogInformation("Counting number of null bytes in the checksum...");
                        int numberOfNullBytes = 0;
                        for (int i = 0; i < checksumData.Length; i++)
                        {
                            if (checksumData[i] == 0xFF || checksumData[i] == 0x00 || checksumData[i] == 0xC3)
                            {
                                numberOfNullBytes++;
                            }
                        }
                        Logger.LogInformation($"Found [{numberOfNullBytes}] null bytes in the checksum!");

                        if (numberOfNullBytes > 0xE0) // found a lot!
                        {
                            if (IsNewBtldSecurity(SecurityWave) || anyUnlockAllowed)
                            {
                                BoschMG1RsaPatchedBootloaderPresent = true;
                                moduleInfo.unlockCompany = UnlockCompanyName.bFlash;
                                moduleInfo.unlocked = "bFlash";
                                Logger.LogInformation("Found altered btld checksum at " + string.Format("0x{0:X08}, chunksize {1}. By having a modified btld checksum we'll assume its unlocked.", btld_forceCRC32_location_btldRsaChecksum, 0x100));
                            }
                            else
                            {
                                Logger.LogInformation("Found altered btld checksum at " + string.Format("0x{0:X08}, chunksize {1}. But btld security is low and we want to force our unlock method so skip this.", btld_forceCRC32_location_btldRsaChecksum, 0x100));
                            }
                        }
                        else
                        {
                            Logger.LogInformation("This btld checksum region doesn't have enough null data to be considered 'unlocked'!");
                        }
                    }
                }
                else if (!skipBTLDcrcRead)
                {
                    Logger.LogInformation("  OEM BTLD detected, make sure to use -bi -w 6 (PST|BTLD) option on first upload with STOCK bin; then upload patched btld with -bp -w 4");
                }
            }

            if (BoschMG1RsaPatchedBootloaderPresent == false && IsNewBtldSecurity(SecurityWave))
            {
                Logger.LogInformation("Checking DST region for tags...");
                if (MgFlasherCore._UDSDiagIf.ReadMemoryByAddress((uint)btld_forceCRC32_location_MHD, 0x100, out data) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    Logger.LogInformation("Error reading from MHD DST location: " + string.Format("0x{0:X08}, chunksize {1}. Unable to determine Bootloader patch, continuing anyway.", btld_forceCRC32_location_MHD, 0x100));
                    BoschMG1RsaPatchedBootloaderPresent = false;
                    UnableToReadCRC = true;
                }
                else // read ok, compare
                {
                    Logger.LogInformation("Read " + string.Format("chunksize {0} from MHD DST location: 0x{1:X08}", data.Length, btld_forceCRC32_location_MHD));

                    bool stockDSTMatch = true;
                    int fafaRegion = 0;

                    byte[] stock_dst_compare = {
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
                        0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xFA, 0xFA, 0xFA, 0xFA,
                        0xFA, 0xFA, 0xFA, 0xFA
                    };
                    Logger.LogInformation("Comparing DST from stock to MHD...");
                    for (int i = 0; i < stock_dst_compare.Length - 8; i++)
                    {
                        if (data[i] != stock_dst_compare[i])
                        {
                            Logger.LogInformation("Found modified DST!");
                            stockDSTMatch = false;
                            break;
                        }
                    }

                    for (int i = stock_dst_compare.Length - 8; i < stock_dst_compare.Length; i++)
                    {
                        if (data[i] == stock_dst_compare[i])
                        {
                            fafaRegion++;
                        }
                    }

                    if (!stockDSTMatch && fafaRegion == 8)
                    {
                        string partialDstRead = StringFunctions.ConvertBytesToAsciiString(data, 0, data.Length); // Ascii serial
                        if (partialDstRead.ToUpper().Contains("MHD") || ByteArrayContains(data, "MHD"))
                        {
                            if (IsNewBtldSecurity(SecurityWave) || anyUnlockAllowed)
                            {
                                BoschMG1RsaPatchedBootloaderPresent = true;
                                moduleInfo.unlockCompany = UnlockCompanyName.MHD;
                                moduleInfo.unlocked = "MHD";
                                Logger.LogInformation("Found MHD patch at " + string.Format("0x{0:X08}, chunksize {1}. DST region shows MHD tag, we'll assume its unlocked (PROCEED WITH CAUTION).", btld_forceCRC32_location_MHD, 0x100));
                            }
                            else
                            {
                                Logger.LogInformation("Found MHD patch at " + string.Format("0x{0:X08}, chunksize {1}. But btld security is low and we want to force our unlock method so skip this.", btld_forceCRC32_location_MHD, 0x100));
                            }
                        }
                        else
                        {
                            Logger.LogInformation("Did not find MHD tag");
                        }
                    }
                    else
                    {
                        Logger.LogInformation("This DST region is stock");
                    }
                }

                var unableToRDBA = _UDSDiagIf.ReadMemoryByAddress((uint)femto_Unlock_location, (byte)0x01, out byte[] _) == UDS_BMW.UDS_ErrorCodes.UDS_NRC_0010;
                if (unableToRDBA && bootctrlRequiresWave3Unlock && !GetWave3UnlockStatus(moduleInfo.unlockCompany))
                {
                    // Checked for TAS and 1769 unlocks before, failed to find
                    BoschMG1RsaPatchedBootloaderPresent = true;
                    moduleInfo.unlockCompany = UnlockCompanyName.FEMTO;
                    moduleInfo.unlocked = "FEMTO";
                    Logger.LogInformation("Found FEMTO patch by confirming we're unable to read femto_Unlock_location (RBMA is disabled), new bootloader security, and new boot control version. We'll assume its unlocked (PROCEED WITH CAUTION).");
                }
                else
                {
                    Logger.LogInformation($"Did not find FEMTO patch! unableToRDBA: {unableToRDBA}, bootctrlRequiresWave3Unlock: {bootctrlRequiresWave3Unlock}, UnlockCompany: {moduleInfo.unlockCompany}");
                }
            }
        }

        if (MgFlasherCore.attachedCable is BmwEnet)
        {
            ReadDifVariante(ref moduleInfo);
        }
        else
        {
            Logger.LogInformation("Not using ENET connection, not reading Dif Variante!");
        }

        if (BoschMG1RsaPatchedBootloaderPresent
            && !GetWave3UnlockStatus(moduleInfo.unlockCompany)
            && bootctrlRequiresWave3Unlock
            && IsBootMode(activeDiagSession))
        {
            moduleInfo.unlockCompany = UnlockCompanyName.FEMTO;
            moduleInfo.unlocked = "FEMTO";
            Logger.LogInformation("Found FEMTO patch by confirming we're in bootmode, new bootloader security, new boot control version, and some bootloader patch is present (-bp marks bootloader patched). We'll assume its Femto unlocked (PROCEED WITH CAUTION).");
        }

        if (bootctrlRequiresWave3Unlock && !flashingFullStock && GetWave3UnlockStatus(moduleInfo.unlockCompany))
        {
            // For now, we're blocking
            writecommandOption &= ~((byte)UNI_Flash.SweArtType.PST);
            Logger.LogInformation($"For now, we're blocking all Wave3 unlocks from flashing PST until they confirm flashing via app");
            Logger.LogInformation($"FEMTO unlock prohibits flashing PST, flashingFullStock: [{flashingFullStock}], removed it from 'writecommandOption'");
        }
        else
        {
            Logger.LogInformation($"UnlockCompany: [{moduleInfo.unlockCompany}], flashingFullStock: [{flashingFullStock}], 'writecommandOption' is unchanged!");
        }

        // Check if CVN and CALID are different, unlocked if so
        var mismatchedCvnCalId = !string.IsNullOrWhiteSpace(moduleInfo.cvn)
                                    && !string.IsNullOrWhiteSpace(moduleInfo.calid)
                                    && !moduleInfo.cvn.ToUpper().Equals(moduleInfo.calid.ToUpper());
        if (mismatchedCvnCalId)
        {
            if (string.IsNullOrEmpty(moduleInfo.unlocked))
            {
                BoschMG1RsaPatchedBootloaderPresent = true;
                moduleInfo.unlockCompany = UnlockCompanyName.Unknown;
                moduleInfo.unlocked = "UnknownPatchPresent";
                Logger.LogInformation($"Found mismatched CVN/CalID, btld is unlocked!");
            }
            else
            {
                Logger.LogInformation($"Found mismatched CVN/CalID, but moduleInfo.unlocked is not null/empty: [{moduleInfo.unlocked}], unsure if btld is unlocked!");
            }
        }

        // Check for alternative unlock methods and flag for MG Flasher reunlock/repatch
        // Check for altered CRC
        // Check for CMD
        // Check for MHD (has tag in DST)
        // Check for FLEX (Uses our method)
        // Check for BM3 (uses ATAT)
        // Check for BMW Explorer (We use their method)
        // Check for AutoTuner (dif_variante --> "ATAT")
        // Check for Femto
        // Check for 1769
        // Check for TAS
        // Do these check by reading the CRC correction region of the BTLD: 0x5F7DC

        if (!string.IsNullOrEmpty(moduleInfo.unlocked))
        {
            if (IsNewBtldSecurity(SecurityWave) || anyUnlockAllowed)
            {
                BoschMG1RsaPatchedBootloaderPresent = true;
                Logger.LogInformation("Found unknown patch! We'll assume its a 3rd party unlock (PROCEED WITH CAUTION).");
            }
            else
            {
                Logger.LogInformation("Found unknown patch! But btld security is low and we want to force our unlock method so skip this.");
            }
        }
        else
        {
            Logger.LogInformation("'moduleInfo.unlocked' string is empty, did not find 3rd party unlock");
        }
        if ((!string.IsNullOrEmpty(moduleInfo.unlocked) || IsThirdPartyUnlocked(moduleInfo.unlockCompany)) && BoschMG1RsaPatchedBootloaderPresent)
        {
            Logger.LogInformation($"3rd party BTLD unlock detected: '{moduleInfo.unlocked}'");
        }

        if (!BoschMG1RsaPatchedBootloaderPresent && !IsBootMode(activeDiagSession) && UnableToReadCRC && (IsNewBtldSecurity(SecurityWave) || anyUnlockAllowed))
        {
            BoschMG1RsaPatchedBootloaderPresent = true;
            moduleInfo.unlockCompany = UnlockCompanyName.Unknown;
            moduleInfo.unlocked = "UnknownPatchPresent";
            Logger.LogInformation("Falling back to BoschMG1RsaPatchedBootloaderPresent = true!");
            Logger.LogInformation("Unable to read crc and non-stock ppc/aurix with new security, we'll assume its a 3rd party unlock (PROCEED WITH CAUTION).");
            Logger.LogInformation("User selected (MYCAR PAGE) to allow app to reach this step.");
        }
        else
        {
            Logger.LogInformation($"Not checking for a fall back. BoschMG1RsaPatchedBootloaderPresent:{BoschMG1RsaPatchedBootloaderPresent}, activeDiagSession: {activeDiagSession}, UnableToReadCRC: {UnableToReadCRC}, SecurityWave: {SecurityWave}, moduleInfo.ProcessorArchitectureType: {moduleInfo.ProcessorArchitectureType}");
        }

        if (IsBootMode(activeDiagSession)) // BOSCH MG1 commands only available in boot mode (give info on stay in boot reason)
        {
            //
            // read MG1_CB_id from module
            //
            if (_UDSDiagIf.ReadDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.MG1_CB_id, out recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogWarning("Could not read {0} from module", UDS_BMW.UDS_RecordIdentifier.MG1_CB_id);
            }
            else
            {
                string cb_id = StringFunctions.ConvertBytesToAsciiString(recordData, 0, recordData.Length);
                Logger.LogInformation("  CB id ({0}): {1}", cb_id.Length, cb_id);
            }

            //
            // read MG1_SB_id from module
            //
            if (_UDSDiagIf.ReadDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.MG1_SB_id, out recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogWarning("Could not read {0} from module", (ushort)UDS_BMW.UDS_RecordIdentifier.MG1_CB_id);
            }
            else
            {
                string sb_id = StringFunctions.ConvertBytesToAsciiString(recordData, 0, recordData.Length);
                Logger.LogInformation("  SB id ({0}): {1}", sb_id.Length, sb_id);
            }

            //
            // read MG1_SWE_OCB from module
            //
            if (_UDSDiagIf.ReadDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.MG1_SWE_OCB, out recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogWarning("Could not read {0} from module", UDS_BMW.UDS_RecordIdentifier.MG1_SWE_OCB);
            }
            else
            {
                string seedString = StringFunctions.ConvertBytesToHexString(recordData);
                Logger.LogInformation("  Swe Ocb array ({0}): {1}", recordData.Length, seedString);

                // parse it and print
                int nrOfRecords = (recordData.Length / 11);
                for (int i = 0; i < nrOfRecords; i++)
                {
                    Logger.LogInformation("  * BlockId: 0x{0:X02}, OCB: {1}", recordData[0 + i * 11], StringFunctions.ConvertBytesToHexString(StringFunctions.ExtractSectionArray(recordData, 1 + i * 11, 10)));
                }
                Logger.LogInformation("");
            }

            //
            // read MG1_CHECK_STRUCT from module
            //
            if (_UDSDiagIf.ReadDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.MG1_CHECK_STRUCT, out recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogWarning("Could not read {0} from module", UDS_BMW.UDS_RecordIdentifier.MG1_CHECK_STRUCT);
            }
            else
            {
                string seedString = StringFunctions.ConvertBytesToHexString(recordData);
                Logger.LogInformation("  Check state array ({0}): {1}", recordData.Length, seedString);

                // parse it and print
                int nrOfRecords = (recordData.Length / 0xD);
                for (int i = 0; i < nrOfRecords; i++)
                {
                    int recordOffset = i * 0xD;

                    byte BlockId = recordData[0 + recordOffset];
                    uint BlockStartAddress = StringFunctions.SwitchEndianUInt32(BitConverter.ToUInt32(recordData, 1 + recordOffset));
                    uint BlockStatus = StringFunctions.SwitchEndianUInt32(BitConverter.ToUInt32(recordData, 5 + recordOffset));
                    uint BlockError = StringFunctions.SwitchEndianUInt32(BitConverter.ToUInt32(recordData, 9 + recordOffset));

                    string BlockErrorS = "-> ";

                    if (BlockError == 0) BlockErrorS += "OK";
                    else BlockErrorS += "ERROR ";

                    if ((BlockError & 0x2000) > 0) BlockErrorS += "ERASED ";
                    if ((BlockError & 0x10) > 0) BlockErrorS += "BLK_MKR ";
                    if ((BlockError & 0x8) > 0) BlockErrorS += "BLK_CRC_CHKS ";
                    if ((BlockError & 0x4) > 0) BlockErrorS += "BLK_RSA ";
                    if ((BlockError & 0x2) > 0) BlockErrorS += "BLK_HDR_CRC ";

                    Logger.LogInformation("  * BlockId: 0x{0:X02}, StartAddress: 0x{1:X08}, BlockStatus: 0x{2:X08}, BlockError: 0x{3:X08} {4}", BlockId, BlockStartAddress, BlockStatus, BlockError, BlockErrorS);
                }
                Logger.LogInformation("");
            }
        }
#endif

        //
        // TASK BACKUP CODING
        //
        if ((requiredActions & (byte)ActionOption.READ_CODING) == (byte)ActionOption.READ_CODING)
        {
            Logger.LogInformation("READING CODING:");
            OnMessage?.Invoke(typeof(MgFlasherCore), "reading coding...");

            if (moduleInfo.moduleHasCoding == true)
            {
                string moduleName = (string.IsNullOrEmpty(moduleInfo.name) ? string.Format("{0}", moduleId) : moduleInfo.name);

                // GenericPPC_696902_0030763552_WBA1S92080VD06267_CAFD.srec
                // DME840_992294_0031647669_5UXTR9C5XKLD94799_CAFD.srec
                string codingFileName = Path.Combine(externalBackupPath, string.Format("{0}_{1}_{2}_{3}_CAFD.srec", moduleName, moduleInfo.SGBDIndex, moduleInfo.serialNo, moduleInfo.VIN));
                bool codingAbortOnRecreateBackupFail = performCodingRestoreFromBackup && !(File.Exists(codingFileName));
                string abortMsg = codingAbortOnRecreateBackupFail ? ", aborting" : ", but earlier backup exists continuing";

                if (!string.IsNullOrWhiteSpace(codingFile))
                {
                    codingAbortOnRecreateBackupFail = false;
                    abortMsg = ", but alternate coding applied later continuing";
                }

                if ((activeDiagSession & 0xfff0) == 0x0180) // ApplicationDefaultSessionXXX, coding backup allowed
                {
                    if (CafdCoded(moduleInfo.cafdId) || (SGBDIndex == 988288 && backupCodingOnly)) // Module is coded or special VGSG-F30 always backup (only when backupCodingOnly)
                    {
                        List<CodingRecord> CodingDataset;
                        bool codingFetchOk;

                        if (SGBDIndex == 988288 && backupCodingOnly && !CafdCoded(moduleInfo.cafdId)) // special VGSG-F30 always backup (maybe bad) file name alteration (only when backupCodingOnly)
                        {
                            codingFileName = codingFileName.Replace("_CAFD", "_CAFD_SPECIAL");
                        }

                        if (moduleInfo.codingRecordIds.Length > 0)
                        {
                            codingFetchOk = FetchCodingRecords(moduleInfo.codingRecordIds, out CodingDataset); // known record id, fast backup
                        }
                        else
                        {
                            codingFetchOk = FetchCodingRecordsBruteForce(out CodingDataset); // unknown record ids (but module has coding), brute force
                        }

                        if (codingFetchOk == false || (moduleInfo.codingRecordIds.Length > 0 && PrepareCheckSignatureCodingRecords(ref CodingDataset, moduleInfo.btldId, false) == false))
                        {
                            Logger.LogInformation("  Could not fetch Coding Records from module or signature invalid, no coding backup file created{0}", abortMsg);
                            if (codingAbortOnRecreateBackupFail == true)
                            {
                                goto forced_exit; // cannot restore (later) without good backup, abort
                            }
                        }
                        else
                        {
                            if (StoreCodingRecordsToSREC(codingFileName, moduleInfo.cafdId, CodingDataset) == 0)
                            {
                                Logger.LogInformation("  Could NOT create coding data backup file: '{0}'{1}", codingFileName, abortMsg);
                                if (codingAbortOnRecreateBackupFail == true)
                                {
                                    goto forced_exit; // cannot restore (later) without good backup, abort
                                }
                            }
                            else
                            {
                                Logger.LogInformation("  Coding data {0} backup file created: '{1}', saved {2} Coding Records from module",
                                    new SweVersion(moduleInfo.cafdId).GetXweString(), codingFileName, CodingDataset.Count);
                                OnMessage?.Invoke(typeof(MgFlasherCore), "coding stored!");
                            }
                        }
                    }
                    else
                    {
                        Logger.LogInformation("  Module uncoded, could not create coding backup file{0}", abortMsg);
                        if (codingAbortOnRecreateBackupFail == true)
                        {
                            resultCode = 11;
                            goto forced_exit; // cannot restore (later) without good backup, abort
                        }
                    }
                }
                else
                {
                    Logger.LogInformation("  Coding not valid in session {0}, could not create coding backup file{1}", _UDSDiagIf.GetDiagSessionDescription(activeDiagSession), abortMsg);
                    if (codingAbortOnRecreateBackupFail == true)
                    {
                        goto forced_exit; // cannot restore (later) without good backup, abort
                    }
                }
            }
            else
            {
                Logger.LogInformation("  Module does not have coding, no coding backup file created");
            }
        }

        //
        // TASK READ MODULE
        //
        if ((requiredActions & (byte)ActionOption.READ_MODULE) == (byte)ActionOption.READ_MODULE)
        {
            string moduleName = (string.IsNullOrEmpty(moduleInfo.name) ? string.Format("{0}", moduleId) : moduleInfo.name);
            string fileNameStr = string.Format("{0}_{1}_{2}_{3}_", moduleName, moduleInfo.SGBDIndex, moduleInfo.serialNo, moduleInfo.VIN);
            if (externalBackupPath != null)
            {
                fileNameStr = Path.Combine(externalBackupPath, fileNameStr);
            }

            if (readcommandOption == (int)ReadCommandOption.READ_MODULE_FULL_BIN
                || readcommandOption == (int)ReadCommandOption.READ_MODULE_BTLD_PST_DST_BIN
                || readcommandOption == (int)ReadCommandOption.READ_MODULE_DST_BIN
                || readcommandOption == (int)ReadCommandOption.READ_MODULE_EEPROM_BIN) // read from module to binfile (with cmd23)
            {
                //int readDataChunkSize = moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp;
                uint readDataChunkSize = 0x5FA; // Manually selected value for decent size with success

                UDS_BMW.UDS_DiagCmd readMemoryCmdToUse = ((moduleId == DiagProtocolBase.ModuleIds.ZGW) ? UDS_BMW.UDS_DiagCmd.ZGWDebugService : UDS_BMW.UDS_DiagCmd.ReadMemoryByAddress);

                Logger.LogInformation("READING FROM MODULE:");
                OnMessage?.Invoke(typeof(MgFlasherCore), $"reading ecu binary...");

                //
                // BOOTMODE readable segments, special handling
                //
                if (IsBootMode(activeDiagSession))
                {
                    if (moduleInfo.readMemoryByAddressAvailableInBoot == false)
                    {
                        Logger.LogInformation("  No CMD 0x{0:X02} ReadMemoryByAddress available in UDS Boot-Mode for module {1}, aborting", (byte)readMemoryCmdToUse, moduleInfo.name);
                        goto forced_exit;
                    }
                    else fileNameStr += "BOOTM_";
                }

                List<Range> flashRangeList = new List<Range>();

                // separate definitions only required for aurix
                List<Range> flashRangeListPadding1 = new List<Range>();
                List<Range> flashRangeListBTLD = new List<Range>();
                List<Range> flashRangeListPadding2 = new List<Range>();
                List<Range> flashRangeListPST = new List<Range>();
                List<Range> flashRangeListDST = new List<Range>();
                List<Range> flashRangeListEEPROM = new List<Range>();

                uint[] Padding1_address = { 0x00000000, 0x00000000 }; // not needed on PPC
                uint[] BTLD_address = { 0x00000000, 0x00000000 };
                uint[] Padding2_address = { 0x00000000, 0x00000000 }; // not needed on PPC
                uint[] PST_address = { 0x00000000, 0x00000000 };
                uint[] DST_address = { 0x00000000, 0x00000000 };
                uint[] EEPROM_address = { 0x00000000, 0x00000000 };

                if (!IsSupportedArchitecture(moduleInfo))
                {
                    Logger.LogInformation("  Unsupported module variant {0}, aborting", SGBDIndex);
                    goto forced_exit;
                }

                if (IsPpcArchitecture(moduleInfo))
                {
                    // testing purposes, reads only small section (10 bytes each)
                    //Padding1_address[0] = 0x00000000; // not used on PPC
                    //Padding1_address[1] = 0x00000000; // not used on PPC
                    //BTLD_address[0] = 0x09000000;
                    //BTLD_address[1] = 0x09000009;
                    //Padding2_address[0] = 0x00000000; // not used on PPC
                    //Padding2_address[1] = 0x00000000; // not used on PPC
                    //PST_address[0] = 0x09000100;
                    //PST_address[1] = 0x09000109;
                    //DST_address[0] = 0x09000200;
                    //DST_address[1] = 0x09000209;
                    //EEPROM_address[0] = 0x09000300;
                    //EEPROM_address[1] = 0x09000309;

                    // Proper addresses:
                    Padding1_address[0] = 0x00000000; // not used on PPC
                    Padding1_address[1] = 0x00000000; // not used on PPC
                    BTLD_address[0] = 0x09000000;
                    BTLD_address[1] = 0x0903FFFF;
                    Padding2_address[0] = 0x00000000; // not used on PPC
                    Padding2_address[1] = 0x00000000; // not used on PPC
                    PST_address[0] = 0x09040000;
                    PST_address[1] = 0x0967FFFF;
                    DST_address[0] = 0x09680000;
                    DST_address[1] = 0x0977FFFF;
                    EEPROM_address[0] = 0x09000000;
                    EEPROM_address[1] = 0x09000009;
                }
                else if (IsAurixArchitecture(moduleInfo))
                {
                    // testing purposes, reads only small section (10 bytes each)
                    //Padding1_address[0] = 0x80028000;
                    //Padding1_address[1] = 0x80028009;
                    //BTLD_address[0] = 0x80028100;
                    //BTLD_address[1] = 0x80028109;
                    //Padding2_address[0] = 0x80028200;
                    //Padding2_address[1] = 0x80028209;
                    //PST_address[0] = 0x80028300;
                    //PST_address[1] = 0x80028309;
                    //DST_address[0] = 0x80028400;
                    //DST_address[1] = 0x80028409;
                    //EEPROM_address[0] = 0x80028500;
                    //EEPROM_address[1] = 0x80028509;

                    // Proper addresses:
                    Padding1_address[0] = 0x80000000;
                    Padding1_address[1] = 0x80027FFF;
                    BTLD_address[0] = 0x80028000;
                    BTLD_address[1] = 0x8005FFFF;
                    Padding2_address[0] = 0x80060000;
                    Padding2_address[1] = 0x8007FFFF;
                    PST_address[0] = 0x80080000;
                    PST_address[1] = 0x806FFFFF;
                    DST_address[0] = 0x80700000;
                    DST_address[1] = 0x807FFFFF;
                    EEPROM_address[0] = 0x80028000;
                    EEPROM_address[1] = 0x80028009;
                }

                int numberOfSectionToRead = 0;
                uint numberOfBytesToRead = 0;
                string sectionsToRead = "BinarySections";
                if (readcommandOption == (int)ReadCommandOption.READ_MODULE_DST_BIN)
                {
                    numberOfSectionToRead = 1;
                    flashRangeListDST.Add(GenerateReadRange(DST_address[0], DST_address[1], 0));
                    Logger.LogInformation("Generating Read Range: 0x{0:X08} to 0x{1:X08}", DST_address[0], DST_address[1]);
                    sectionsToRead += "_DST";
                    numberOfBytesToRead += DST_address[1] - DST_address[0];
                }
                else
                {
                    numberOfSectionToRead = (IsPpcArchitecture(moduleInfo) ? 3 : 5); // PPC doesn't need Padding1 or Padding2
                    if (IsAurixArchitecture(moduleInfo))
                    {
                        flashRangeListPadding1.Add(GenerateReadRange(Padding1_address[0], Padding1_address[1], 0)); // PPC doesn't need Padding1
                        Logger.LogInformation("Generating Read Range: 0x{0:X08} to 0x{1:X08}", Padding1_address[0], Padding1_address[1]);
                        sectionsToRead += "_Padding";
                        numberOfBytesToRead += Padding1_address[1] - Padding1_address[0];
                    }
                    else
                    {
                        Logger.LogInformation("PPC doesn't need Padding1");
                    }

                    flashRangeListBTLD.Add(GenerateReadRange(BTLD_address[0], BTLD_address[1], 0));
                    Logger.LogInformation("Generating Read Range: 0x{0:X08} to 0x{1:X08}", BTLD_address[0], BTLD_address[1]);
                    sectionsToRead += "_BTLD";
                    numberOfBytesToRead += BTLD_address[1] - BTLD_address[0];

                    if (IsAurixArchitecture(moduleInfo))
                    {
                        flashRangeListPadding2.Add(GenerateReadRange(Padding2_address[0], Padding2_address[1], 0)); // PPC doesn't need Padding2
                        Logger.LogInformation("Generating Read Range: 0x{0:X08} to 0x{1:X08}", Padding2_address[0], Padding2_address[1]);
                        sectionsToRead += "_Padding";
                        numberOfBytesToRead += Padding2_address[1] - Padding2_address[0];
                    }
                    else
                    {
                        Logger.LogInformation("PPC doesn't need Padding2");
                    }

                    flashRangeListPST.Add(GenerateReadRange(PST_address[0], PST_address[1], 0));
                    Logger.LogInformation("Generating Read Range: 0x{0:X08} to 0x{1:X08}", PST_address[0], PST_address[1]);
                    sectionsToRead += "_PST";
                    numberOfBytesToRead += PST_address[1] - PST_address[0];

                    flashRangeListDST.Add(GenerateReadRange(DST_address[0], DST_address[1], 0));
                    Logger.LogInformation("Generating Read Range: 0x{0:X08} to 0x{1:X08}", DST_address[0], DST_address[1]);
                    sectionsToRead += "_DST";
                    numberOfBytesToRead += DST_address[1] - DST_address[0];

                    if (readcommandOption == (int)ReadCommandOption.READ_MODULE_EEPROM_BIN)
                    {
                        //flashRangeListEEPROM.Add(GenerateReadRange(EEPROM_address[0], EEPROM_address[1], 0));
                        //Logger.LogInformation("Generating Read Range: 0x{0:X08} to 0x{1:X08}", EEPROM_address[0], EEPROM_address[1]);
                        //numberOfSectionToRead = numberOfSectionToRead + 1;
                        //sectionsToRead += "_EEPROM";
                        //numberOfBytesToRead += EEPROM_address[1] - EEPROM_address[0];
                    }
                }
                FlashTotalBytesDetermined?.Invoke(typeof(MgFlasherCore), numberOfBytesToRead);

                BinaryWriter full_file;
                string fileNameStr_full = fileNameStr + sectionsToRead + ".bin";
                var _fileBuffer = new List<byte[]>();

                // create the partial file
                try
                {
                    full_file = new BinaryWriter(new FileStream(fileNameStr_full, FileMode.Create));
                }
                catch (IOException e)
                {
                    Logger.LogError(e.Message + "Cannot create file " + fileNameStr_full);
                    goto forced_exit;
                }

                for (int binFile = 0; binFile < numberOfSectionToRead; binFile++)
                {
                    if (readcommandOption == (int)ReadCommandOption.READ_MODULE_DST_BIN)
                    {
                        Logger.LogInformation("Reading only DST section");
                        flashRangeList = flashRangeListDST;
                    }
                    else if (readcommandOption == (int)ReadCommandOption.READ_MODULE_EEPROM_BIN)
                    {
                        Logger.LogInformation("Reading only EEPROM section");
                        flashRangeList = flashRangeListEEPROM;
                    }
                    else
                    {
                        switch (binFile)
                        {
                            case 0:
                                if (IsPpcArchitecture(moduleInfo))
                                {
                                    flashRangeList = flashRangeListBTLD;
                                }
                                else if (IsAurixArchitecture(moduleInfo))
                                {
                                    flashRangeList = flashRangeListPadding1;
                                }
                                break;

                            case 1:
                                if (IsPpcArchitecture(moduleInfo))
                                {
                                    flashRangeList = flashRangeListPST;
                                }
                                else if (IsAurixArchitecture(moduleInfo))
                                {
                                    flashRangeList = flashRangeListBTLD;
                                }
                                break;

                            case 2:
                                if (IsPpcArchitecture(moduleInfo))
                                {
                                    flashRangeList = flashRangeListDST;
                                }
                                else if (IsAurixArchitecture(moduleInfo))
                                {
                                    flashRangeList = flashRangeListPadding2;
                                }
                                break;

                            case 3:
                                if (IsPpcArchitecture(moduleInfo))
                                {
                                    flashRangeList = flashRangeListEEPROM;
                                }
                                else if (IsAurixArchitecture(moduleInfo))
                                {
                                    flashRangeList = flashRangeListPST;
                                }
                                break;

                            case 4:
                                if (IsPpcArchitecture(moduleInfo))
                                {
                                    Logger.LogInformation(" PPC shouldn't have any more sections to read...");
                                    goto forced_exit;
                                }
                                else if (IsAurixArchitecture(moduleInfo))
                                {
                                    flashRangeList = flashRangeListDST;
                                }
                                break;

                            case 5:
                                if (IsPpcArchitecture(moduleInfo))
                                {
                                    Logger.LogInformation(" PPC shouldn't have any more sections to read...");
                                    goto forced_exit;
                                }
                                else if (IsAurixArchitecture(moduleInfo))
                                {
                                    flashRangeList = flashRangeListEEPROM;
                                }
                                break;

                            default:
                                Logger.LogInformation(" Too many sections attempted to read...");
                                goto forced_exit;
                        }
                    }

                    if (readDataChunkSize >= (uint)_UDSDiagIf.ProtocolMaxPayload) // we are at (or over) limit, clamp and reduce by cmd response size
                    {
                        Logger.LogInformation(string.Format("Maximum total read block length (incl. cmd overhead) limited from modules max cmd 0x{0:X02} len {1} bytes to {2} bytes due to interface/bus-specific and module specific alignment restrictions", (byte)readMemoryCmdToUse, readDataChunkSize, _UDSDiagIf.ProtocolMaxPayload));
                        readDataChunkSize = (uint)(_UDSDiagIf.ProtocolMaxPayload - _UDSDiagIf.DiagResponseLeadingSize(readMemoryCmdToUse));
                    }

                    long readStartAddress = 0xffffffff;
                    long maxReadAddress = 0x0;
                    long totalTransfered = 0;

                    for (int i = 0; i < flashRangeList.Count; i++) // sectionsTotal blocks, update max and read start
                    {
                        if (flashRangeList[i].start < readStartAddress) readStartAddress = flashRangeList[i].start;
                        if (flashRangeList[i].end > maxReadAddress) maxReadAddress = flashRangeList[i].end;
                    }

                    Logger.LogInformation("  reading " + moduleName + " " + moduleInfo.ProcessorArchitectureType + " from " + string.Format("0x{0:X08} to 0x{1:X08}", readStartAddress, maxReadAddress));
                    Stopwatch stopWatch = new Stopwatch();
                    stopWatch.Start();

                    for (int i = 0; i < flashRangeList.Count; i++)  // sectionsTotal blocks, some might be ignored
                    {
                        uint start = flashRangeList[i].start;
                        uint end = flashRangeList[i].end;
                        uint size = flashRangeList[i].size;
                        uint source = flashRangeList[i].flash_method;

                        Logger.LogInformation(string.Format("  reading block #{0} of {1} 0x{2:X08} to 0x{3:X08}, source ", (i + 1), flashRangeList.Count, start, end) + ((source == 0) ? string.Format("{0} ", moduleId) : "SIM "));
                        {
                            long segmentDataChunkSize = readDataChunkSize; // reset segment datachunksize to default
                            for (long cnt = start; cnt <= end; cnt += segmentDataChunkSize)
                            {
                                // correct segment datachunksize for end off dataChunkSize boundry aligment
                                if ((end - cnt + 1) < segmentDataChunkSize) segmentDataChunkSize = (end - cnt + 1);
                                byte[] data; // for ZGW
                                byte[] _transferedData = Array.Empty<byte>();

                                if ((binFile == 0 || binFile == 2)
                                    && IsAurixArchitecture(moduleInfo)
                                    && readcommandOption != (int)ReadCommandOption.READ_MODULE_DST_BIN
                                    && readcommandOption != (int)ReadCommandOption.READ_MODULE_EEPROM_BIN)
                                {
                                    cnt = end;
                                    // writing into the file
                                    try
                                    {
                                        uint sizeOfPadding = 0;
                                        if (binFile == 0)
                                        {
                                            sizeOfPadding = (Padding1_address[1] - Padding1_address[0]) + 1;
                                        }
                                        else if (binFile == 2)
                                        {
                                            sizeOfPadding = (Padding2_address[1] - Padding2_address[0]) + 1;
                                        }

                                        var padding = new byte[sizeOfPadding];
                                        // for size of padding area:
                                        for (int j = 0; j < sizeOfPadding; j++)
                                        {
                                            padding[j] = 0x69;
                                        }
                                        _fileBuffer.Add(padding);
                                    }
                                    catch (IOException e)
                                    {
                                        Logger.LogInformation(e.Message + "Cannot write to buffer");
                                        goto forced_exit;
                                    }
                                }
                                else
                                {
                                    if (source == 0)  // not simulated read
                                    {
                                        if (readMemoryCmdToUse == UDS_BMW.UDS_DiagCmd.ReadMemoryByAddress)
                                        {
                                            if (_UDSDiagIf.ReadMemoryByAddress((uint)cnt, (uint)segmentDataChunkSize, out _transferedData) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                                            {
                                                Logger.LogInformation("-> error reading from " + string.Format("0x{0:X08}, chunksize {1}", cnt, segmentDataChunkSize));
                                                goto forced_exit;
                                            }
                                            else
                                            {
                                                totalTransfered += segmentDataChunkSize;
                                                Logger.LogInformation("Percentage complete: {percentage:0.0}%", ((double)totalTransfered / (double)numberOfBytesToRead) * 100.0);
                                                ProgressUpdated?.Invoke(typeof(MgFlasherCore), (double)totalTransfered / numberOfBytesToRead);
                                                ProgressSegmentBytesCompleted?.Invoke(typeof(MgFlasherCore), (int)totalTransfered);
                                            }
                                        }
                                        else // use UDS_BMW.UDS_DiagCmd.ZGWDebugService
                                        {
                                            uint r_address = (uint)cnt;
                                            uint r_size = (uint)segmentDataChunkSize;
                                            byte[] r_args = new byte[8];
                                            r_args[0] = (byte)(r_address >> 24);
                                            r_args[1] = (byte)(r_address >> 16);
                                            r_args[2] = (byte)(r_address >> 8);
                                            r_args[3] = (byte)(r_address);
                                            r_args[4] = (byte)(r_size >> 24);
                                            r_args[5] = (byte)(r_size >> 16);
                                            r_args[6] = (byte)(r_size >> 8);
                                            r_args[7] = (byte)(r_size);

                                            if (_UDSDiagIf.ZGWDebugService(0xFFFF, r_args, out data) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                                            {
                                                Logger.LogInformation("-> error reading from " + string.Format("0x{0:X08}, chunksize {1}", cnt, segmentDataChunkSize));
                                                goto forced_exit;
                                            }
                                            else
                                            {
                                                totalTransfered += segmentDataChunkSize;
                                                Logger.LogInformation("Percentage complete: {percentage:0.0}%", ((double)totalTransfered / (double)numberOfBytesToRead) * 100.0);
                                                ProgressUpdated?.Invoke(typeof(MgFlasherCore), (double)totalTransfered / numberOfBytesToRead);
                                                ProgressSegmentBytesCompleted?.Invoke(typeof(MgFlasherCore), (int)totalTransfered);
                                            }
                                        }
                                    }
                                    else // simulated data
                                    {
                                        data = new byte[segmentDataChunkSize];
                                        for (int t = 0; t < segmentDataChunkSize; t++) data[t] = 0xff; // fill the sim chunk with 0xff
                                    }

                                    // writing into the file
                                    try
                                    {
                                        _fileBuffer.Add(_transferedData);
                                    }
                                    catch (IOException e)
                                    {
                                        Logger.LogInformation(e.Message + "Cannot write to buffer");
                                        goto forced_exit;
                                    }
                                }
                            }
                        }
                    }
                    stopWatch.Stop();
                    PrintTransferStats(stopWatch.Elapsed, (uint)totalTransfered, true);
                }

                foreach (var item in _fileBuffer)
                {
                    full_file.Write(item);
                }
                full_file.Close();
                resultCode = 0;
                Logger.LogInformation("-> done");
            }
            else if (readcommandOption == (int)ReadCommandOption.READ_VIRTUAL_BIN)
            {
                // virtual read
                Logger.LogInformation("VIRTUAL READING FROM MODULE:");

                // BOOTMODE: do not virtual read, we no not know this SVK combo will actually run, special handling
                if (IsBootMode(activeDiagSession))
                {
                    Logger.LogInformation("  Virtual reading not available in UDS Boot-Mode for module {0}, aborting", moduleInfo.name);
                    goto forced_exit;
                }

                if (IsSupportedArchitecture(moduleInfo))
                {
                    Logger.LogInformation(" -> Unsupported module variant {0}, aborting", SGBDIndex);
                    goto forced_exit;
                }
            }
        }

        //
        // TASK WRITE TO MODULE
        //
        if ((requiredActions & (byte)ActionOption.WRITE_MODULE) == (byte)ActionOption.WRITE_MODULE)
        {
            Logger.LogInformation("WRITING TO MODULE:");
            OnMessage?.Invoke(typeof(MgFlasherCore), "preparing ecu...");

            CheckEcuForNewSecurityBtld(ref moduleInfo);
            var flashBlocks = new List<FlashBlock>();

            try
            {
                flashBlocks = GenerateFlashBlocksDumpInfo(inputModeSwflFiles, writecommandOption, inFiles, moduleInfo);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error");
                goto forced_exit;
            }

            if (flashBlocks.Count == 0) // No Flash blocks remaining after elimination (or none created) proceed to next action (usually coding)
            {
                // No coding data written unless coding file specified
                if (performCodingRestoreFromBackup == false) goto coding_start;
                else goto forced_exit;
            }

            //
            // PRECHECKS/INFO FETCH ROGRAMMING SESSION
            //
            Logger.LogInformation("PRECHECKS/INFO FETCH EXTENDED/PROGRAMMING SESSION:");

            if (_UDSDiagIf.ReadDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.FlashTimingParameter, out recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogError("Could not read FlashTimingParameters, exiting");
                goto forced_exit;
            }
            else
            {
                if (recordData.Length == 12)
                {
                    FTP_format = (ushort)Format_FTP.FP_6198;
                    eraseTime = ByteArrayParseUshortBigEndian(recordData, 0);
                    checkMemoryTime = ByteArrayParseUshortBigEndian(recordData, 2);
                    bootLoaderInstallTime = ByteArrayParseUshortBigEndian(recordData, 4);
                    authentificationTime = ByteArrayParseUshortBigEndian(recordData, 6);
                    resetTime = ByteArrayParseUshortBigEndian(recordData, 8);
                    transferDataTime = ByteArrayParseUshortBigEndian(recordData, 10);
                }
                else if (recordData.Length == 10)
                {
                    FTP_format = (ushort)Format_FTP.FP_5561;
                    eraseTime = ByteArrayParseUshortBigEndian(recordData, 0);
                    checkMemoryTime = ByteArrayParseUshortBigEndian(recordData, 2);
                    bootLoaderInstallTime = ByteArrayParseUshortBigEndian(recordData, 4);
                    authentificationTime = ByteArrayParseUshortBigEndian(recordData, 6);
                    resetTime = ByteArrayParseUshortBigEndian(recordData, 8);
                }
                else if (recordData.Length == 15)
                {
                    FTP_format = (ushort)Format_FTP.FP_4371;
                    eraseTimeFormat = recordData[0];
                    eraseTime = ByteArrayParseUshortBigEndian(recordData, 1);
                    checkMemoryTimeFormat = recordData[3];
                    checkMemoryTime = ByteArrayParseUshortBigEndian(recordData, 4);
                    authentificationTimeFormat = recordData[6];
                    authentificationTime = ByteArrayParseUshortBigEndian(recordData, 7);
                    resetTimeFormat = recordData[9];
                    resetTime = ByteArrayParseUshortBigEndian(recordData, 10);
                    bootLoaderInstallTimeFormat = recordData[12];
                    bootLoaderInstallTime = ByteArrayParseUshortBigEndian(recordData, 13);
                }

                Logger.LogInformation("  FlashTimingParameters {0}:", (Format_FTP)FTP_format);
                Logger.LogInformation("  EraseMemoryTime: {0} {1}", eraseTime, GetFormatString(eraseTimeFormat, FTP_format));
                Logger.LogInformation("  CheckMemoryTime: {0} {1}", checkMemoryTime, GetFormatString(checkMemoryTimeFormat, FTP_format));
                Logger.LogInformation("  BtLdInstallTime: {0} {1}", bootLoaderInstallTime, ((bootLoaderInstallTime == 0) ? "module does not support bootloader update" : GetResetFormatString(bootLoaderInstallTimeFormat, FTP_format)));
                Logger.LogInformation("  AuthenticationTime: {0} {1}", authentificationTime, GetFormatString(authentificationTimeFormat, FTP_format));
                Logger.LogInformation("  ResetTime: {0} {1}", resetTime, GetResetFormatString(resetTimeFormat, FTP_format));
                if (FTP_format == (ushort)Format_FTP.FP_6198)
                {
                    Logger.LogInformation("  FlashWriteSpeed: {0}", transferDataTime);
                }
            }

            //
            // read program counter (check only, no flashing logic)
            //
            if (_UDSDiagIf.ReadDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.ProgrammingCounter, out recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogError("Could not read ProgrammingCounter");
            }
            else
            {
                ushort programmingCounter = ByteArrayParseUshortBigEndian(recordData, 2), programmingCounterMax;
                byte ProgrammingCounterStatus = recordData[1];

                if (_UDSDiagIf.ReadDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.ProgrammingCounterMax, out recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    Logger.LogError("Could not read ProgrammingCounterMax");
                }

                programmingCounterMax = ByteArrayParseUshortBigEndian(recordData, 0);

                Logger.LogInformation("  ProgrammingCounter: {0}, programmingCounterMax: {1}, reprog allowed: {2}.", programmingCounter, programmingCounterMax, _UDSDiagIf.GetProgrammingCounterStatusDescription(ProgrammingCounterStatus));
            }

            //
            // read ProgrammingPreCondition from module
            //
            if (IsBootMode(activeDiagSession) == false)
            {
                UDS_BMW.UDS_ErrorCodes cmdStatus = _UDSDiagIf.StartRoutine_checkProgrammingPreCondition(out byte programmingPreCondition);
                if (cmdStatus == UDS_BMW.UDS_ErrorCodes.UDS_NRC_0031 || cmdStatus == UDS_BMW.UDS_ErrorCodes.UDS_NRC_0022) // requestOutOfRange, does not support this command
                {
                    Logger.LogInformation("  Module does not support ProgrammingPreCondition command, continuing without");
                }
                else if (cmdStatus != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    Logger.LogInformation("  Could not read ProgrammingPreCondition from module, exiting");
                    goto forced_exit;
                }
                else
                {
                    Logger.LogInformation("  ProgrammingPreConditions: " + _UDSDiagIf.GetProgrammingPreConditionDescription(programmingPreCondition));
                }
            }
            else
            {
                if (SGBDIndex == 4640 || SGBDIndex == 988336 || SGBDIndex == 989328 || SGBDIndex == 988344 || SGBDIndex == 991696 || SGBDIndex == 992720)
                {
                    Logger.LogInformation("  EGS module RESET hardReset to force clean slate");

                    // E70 8HP, EGS-GKEB23, EGS-GKEHY23, EGS-GKEB23TU, EGS-GKEB23TU 8HP51TU or ZFB_CE Supra/Z4) needs reset before entering ECUPM in boot mode, to reallow ERASE
                    if (ExecuteEcuHardReset(resetTime, resetTimeFormat, UDS_BMW.UDS_ResetMode.hardReset, moduleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE) goto forced_exit;
                }
            }

        //
        // PREPARE EXTENDED PRE PROGRAMMING SESSION
        //
        reInitiateFlashRemainder:

            Logger.LogInformation("PREPARING EXTENDED PRE PROGRAMMING SESSION:");

            if (SwitchSessionSecurityAccessAuthenticate(UDS_BMW.UDS_DiagMode.ECUEXTDIAG, moduleInfo.btldId, ref activeDiagSession) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogError("Could not switch Session Security, exiting");
                ExecuteEcuHardReset(resetTime, resetTimeFormat, UDS_BMW.UDS_ResetMode.hardReset, moduleId);
                goto forced_exit;
            }

            if (activeDiagSession != 0x0384) // not ExtDiagSessionFMA, request again
            {
                if (_UDSDiagIf.StartRoutine_SetEnergyMode(UDS_BMW.UDS_SetEnergyModeIdentifier.Flash) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    Logger.LogError("Could not set EnergyMode Flash, exiting");
                    ExecuteEcuHardReset(resetTime, resetTimeFormat, UDS_BMW.UDS_ResetMode.hardReset, moduleId);
                    goto forced_exit;
                }
                else
                {
                    Logger.LogInformation("  set EnergyMode Flash");
                }

                if (IsBootMode(activeDiagSession) == false)
                {
                    if (_UDSDiagIf.ControlDTCSetting(UDS_BMW.UDS_OnOff.Off) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                    {
                        Logger.LogError("Could not set ControlDTCSetting Off, exiting");
                        ExecuteEcuHardReset(resetTime, resetTimeFormat, UDS_BMW.UDS_ResetMode.hardReset, moduleId);
                        goto forced_exit;
                    }
                    else
                    {
                        Logger.LogInformation("  set ControlDTCSetting Off");
                    }

                    if (_UDSDiagIf.CommunicationControl(UDS_BMW.UDS_CommunicationMode.enableRxAndDisableTx, UDS_BMW.UDS_CommunicationType.application) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                    {
                        Logger.LogError("Could not set CommunicationControl enableRxAndDisableTx application, exiting");
                        ExecuteEcuHardReset(resetTime, resetTimeFormat, UDS_BMW.UDS_ResetMode.hardReset, moduleId);
                        goto forced_exit;
                    }
                    else
                    {
                        Logger.LogInformation("  set CommunicationControl enableRxAndDisableTx application");
                    }
                }

                if (_UDSDiagIf.StartRoutine_SetExtendedModeFlash() != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    Logger.LogError("Could not set ExtendedMode Flash, exiting");
                    ExecuteEcuHardReset(resetTime, resetTimeFormat, UDS_BMW.UDS_ResetMode.hardReset, moduleId);
                    goto forced_exit;
                }
                else
                {
                    Logger.LogInformation("  set ExtendedMode Flash");
                }
            }

            //
            // PREPARE UNLOCKED PROGRAMMING SESSION:
            //
            Logger.LogInformation("PREPARING UNLOCKED PROGRAMMING SESSION:");

            if (SwitchSessionSecurityAccessAuthenticate(UDS_BMW.UDS_DiagMode.ECUPM, moduleInfo.btldId, ref activeDiagSession) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                goto forced_exit;
            }

            //
            //  WRITE FINGERPRINT TO MODULE
            //
            Logger.LogInformation("WRITING FINGERPINT:");

            if (WriteFingerPrint(updateFingerPrint, moduleInfo.currentFingerprint) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                goto forced_exit;
            }

            //
            //  PROCESS FLASHBLOCKS -> ERASE, WRITE DATA TO MODULE, CHECK MEMORY (for each segment requested)
            //
            bool BootLoaderJustFlashed = false;
            bool BootLoaderUpgradePackJustFlashed = false;

#if BOSCH_MG1_UNLOCK_BYPASS

            uint DSTdescriptionTable = 0x00;
            uint PSTdescriptionTable = 0x00;
            uint BTLDdescriptionTable = 0x00;

            if (IsPpcArchitecture(moduleInfo)) // PPC
            {
                DSTdescriptionTable = 0x977FD00;
                PSTdescriptionTable = 0x967FD00;
                BTLDdescriptionTable = 0x903FD00;
            }
            else if (IsAurixArchitecture(moduleInfo)) // Aurix
            {
                DSTdescriptionTable = 0x807FFD00;
                PSTdescriptionTable = 0x806FFD00;
                BTLDdescriptionTable = 0x8005FD00;
            }
            else
            {
                Logger.LogInformation("  ERROR not supported processor type {0}, aborting", moduleInfo.ProcessorArchitectureType);
                goto forced_exit;
            }

            if (BoschMG1RsaBypassBootloaderFirstUpload == true)
            {
                Logger.LogInformation("MG1 PATCHED BTLD FIRST INSTALL, FULL ERASE:");

                //
                // ERASE FLASH POINT OF NO RETURN, erase DST, PST, BTLD
                //
                if (EraseFlashIndexed(PSTdescriptionTable, eraseTime) == false)
                {
                    Logger.LogInformation("  **** {0} erased ****", "PST");
                }
                else
                { // some error, prolly wrong code, abort
                    Logger.LogInformation("  **** {0} erase failed ****", "PST");
                    goto forced_exit;
                }
                if (EraseFlashIndexed(DSTdescriptionTable, eraseTime) == false)
                {
                    Logger.LogInformation("  **** {0} erased ****", "DST");
                }
                else
                { // some error, prolly wrong code, abort
                    Logger.LogInformation("  **** {0} erase failed ****", "DST");
                    goto forced_exit;
                }
                if (EraseFlashIndexed(BTLDdescriptionTable, eraseTime) == false)
                {
                    Logger.LogInformation("  **** {0} erased ****", "BTLD");
                }
                else
                { // some error, prolly wrong code, abort
                    Logger.LogInformation("  **** {0} erase failed ****", "BTLD");
                    goto forced_exit;
                }
            }
            else if (BoschMG1RsaPatchedBootloaderPresent == true) // pre erase PST/DST if they are requested
            {
                bool dstEraseNeeded = false, pstEraseNeeded = false;
                for (int i = 0; i < flashBlocks.Count; i++)
                {
                    if (flashBlocks[i].SweVersion.SweArt == SweArtType.PST) pstEraseNeeded = true;
                    if (flashBlocks[i].SweVersion.SweArt == SweArtType.DST) dstEraseNeeded = true;
                }

                if (pstEraseNeeded || dstEraseNeeded)
                {
                    Logger.LogInformation("MG1 PATCHED BTLD PRESENT, PRE-ERASE PST/DST BLOCKS IF NEEDED:");

                    if (dstEraseNeeded == true)
                    {
                        if (EraseFlashIndexed(DSTdescriptionTable, eraseTime) == false)
                        {
                            Logger.LogInformation("  **** DST erased ****");
                        }
                        else
                        { // some error, prolly wrong code, abort
                            Logger.LogInformation("  **** DST erase failed ****");
                            goto forced_exit;
                        }
                    }
                    if (pstEraseNeeded)
                    {
                        if (EraseFlashIndexed(PSTdescriptionTable, eraseTime) == false)
                        {
                            Logger.LogInformation("  **** PST erased ****");
                        }
                        else
                        { // some error, prolly wrong code, abort
                            Logger.LogInformation("  **** PST erase failed ****");
                            goto forced_exit;
                        }
                    }
                }
            }
#endif

            FlashBlocksDetermined?.Invoke(typeof(MgFlasherCore), flashBlocks.Select(fb => fb.flashSegments.Sum(s => s.sourceLength)).ToArray());
            var totalBytes = flashBlocks.Sum(fb => fb.flashSegments.Sum(s => s.sourceLength));
            FlashTotalBytesDetermined?.Invoke(typeof(MgFlasherCore), totalBytes);

            for (int i = 0; i < flashBlocks.Count; i++)
            {
                string segmentName = GetSegmentName(flashBlocks[i].SweVersion.SweArt);
                uint SWEDescriptionTableStartAddress = flashBlocks[i].SWEDescriptionTableStartAddress;

                Logger.LogInformation("PROGRAMMING {0}:", segmentName);
                OnMessage?.Invoke(typeof(MgFlasherCore), $"flashing {segmentName}...");

#if BOSCH_MG1_UNLOCK_BYPASS
                if ((BoschMG1RsaBypassBootloaderFirstUpload == false && BoschMG1RsaPatchedBootloaderPresent == true && flashBlocks[i].SweVersion.SweArt == SweArtType.BTL) ||
                    (BoschMG1RsaBypassBootloaderFirstUpload == false && BoschMG1RsaPatchedBootloaderPresent == false))
#endif
                {
                    //
                    // ERASE FLASH POINT OF NO RETURN
                    //
                    if (EraseFlashIndexed(SWEDescriptionTableStartAddress, eraseTime) == false)
                    {
                        Logger.LogInformation("  **** {0} erased ****", segmentName);
                    }
                    else
                    { // some error, prolly wrong code, abort
                        Logger.LogInformation("  **** {0} erase failed ****", segmentName);
                        goto forced_exit;
                    }
                }

                // write flash segments with auto resume on transfer errors
                if (CreateAndExecuteOptimizedFlashWriteSegments(flashBlocks[i].flashData, flashBlocks[i].flashSegments) == false)
                {
                    Logger.LogInformation("  **** {0} program error ****", segmentName);
                    goto forced_exit;
                }
                else
                {
                    Logger.LogInformation("  **** {0} programmed ****", segmentName);
                }

#if BOSCH_MG1_UNLOCK_BYPASS
                if (flashBlocks[i].SweVersion.SweArt == SweArtType.BTL && BoschMG1RsaBypassBootloaderFirstUpload == true) // BTLD last block in first upload, exit early
                {
                    goto unlockedProgSessionExit;
                }
                bool WrapperPSTSkipChecks = false;
                if (flashBlocks[i].SweVersion.SweArt == SweArtType.PST && BoschMG1RsaBypassBootloaderFirstUpload == true && BTLD_old_aurix_install) // Aurix PST wrapper flashing, exit early
                {
                    WrapperPSTSkipChecks = true;
                }

                if (!WrapperPSTSkipChecks && ((BoschMG1RsaPatchedBootloaderPresent == false) || (flashBlocks[i].SweVersion.SweArt == SweArtType.BTL && BoschMG1RsaPatchedBootloaderPresent == true) || (IsNewBtldSecurity(SecurityWave) && BoschMG1RsaPatchedBootloaderPresent == true) || IsThirdPartyUnlocked(moduleInfo.unlockCompany)))
#endif
                {
                    if (ShouldSkipCheckMemoryIndexed(moduleInfo.unlockCompany))
                    {
                        Logger.LogInformation("  Femto requires us to skip CheckMemoryIndexed, skipping...");
                    }
                    else
                    {
                        // CheckMemoryIndexed requires SWEDescTableStartAddress as parameter
                        _UDSDiagIf.SetMaxExecutionTimeSeconds(checkMemoryTime);
                        if (_UDSDiagIf.StartRoutine_CheckMemoryIndexed(SWEDescriptionTableStartAddress, out byte checkResult) == UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                        {
                            if (checkResult == 0x00) Logger.LogInformation("  CheckMemoryIndexed, SWEDescTableStartAddress 0x{0:X06} -> {1} OK", SWEDescriptionTableStartAddress, checkResult);
                            else // bad prog check result
                            {
                                Logger.LogInformation("  CheckMemoryIndexed, SWEDescTableStartAddress 0x{0:X06} -> {1} Incorrect", SWEDescriptionTableStartAddress, checkResult);
                                goto forced_exit;
                            }
                        }
                        else // command initiate or execute fail, abort
                        {
                            Logger.LogInformation("  CheckMemoryIndexed, SWEDescTableStartAddress 0x{0:X06} failed", SWEDescriptionTableStartAddress);
                            goto forced_exit;
                        }
                    }
                }

                if (flashBlocks[i].SweVersion.SweArt == SweArtType.BTL) // Bootloader Flashed to real location, CheckProgrammingDependencies initiates Copy over, direct proceed to next block NOT allowed
                {
                    BootLoaderJustFlashed = true;
                    if (flashBlocks.Count > 1) break; // more blocks requested, DENY (now)
                }

                if (flashBlocks[i].SweVersion.SweArt == SweArtType.BLUP) // Bootloader Flashed to PST location, CheckMemoryIndexed initiates Copy over, direct proceed to next block allowed
                {
                    BootLoaderUpgradePackJustFlashed = true;
                    if (flashBlocks.Count > 1) break; // more blocks requested, DENY (now)
                }
            }

#if BOSCH_MG1_UNLOCK_BYPASS
            if ((BoschMG1RsaPatchedBootloaderPresent == false) || (BootLoaderJustFlashed && BoschMG1RsaPatchedBootloaderPresent == true) || (IsNewBtldSecurity(SecurityWave) && BoschMG1RsaPatchedBootloaderPresent == true) || IsThirdPartyUnlocked(moduleInfo.unlockCompany))
#endif
            {
                if (ShouldSkipCheckProgrammingDependencies(moduleInfo.unlockCompany))
                {
                    Logger.LogInformation("  Femto requires us to skip CheckProgrammingDependencies, skipping...");
                }
                else
                {
                    // startRoutine_CheckProgrammingDependencies for BTLD Install or Full program check
                    string CheckProgrammingDependenciesType = (BootLoaderJustFlashed || BootLoaderUpgradePackJustFlashed) ? "Install BTLD" : "Full Program";
                    if (_UDSDiagIf.StartRoutine_CheckProgrammingDependencies(out byte ProgrammingDependenciesFull) == UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                    {
                        Logger.LogInformation("  {0} CheckProgrammingDependencies -> {1} {2}", CheckProgrammingDependenciesType, ProgrammingDependenciesFull, _UDSDiagIf.GetCheckProgrammingDependenciesDescription(ProgrammingDependenciesFull));
                        if (!BTLD_old_aurix_install)
                        {
                            if (ProgrammingDependenciesFull != 1)
                            {
                                goto forced_exit;
                            }
                        }
                    }
                    else // command initiate or execute fail, abort
                    {
                        if (!BTLD_old_aurix_install)
                        {
                            Logger.LogInformation("  {0} CheckProgrammingDependencies failed {1} {2}", CheckProgrammingDependenciesType, ProgrammingDependenciesFull, _UDSDiagIf.GetCheckProgrammingDependenciesDescription(ProgrammingDependenciesFull));
                            goto forced_exit;
                        }
                    }
                }
            }

            if (BootLoaderJustFlashed == true && (FTP_format != (ushort)Format_FTP.FP_4371 || FTP_format == (ushort)Format_FTP.FP_4371 && bootLoaderInstallTimeFormat == 2)) _UDSDiagIf.IdleUDSSleepMs(bootLoaderInstallTime * 1000);
            else if (BootLoaderUpgradePackJustFlashed == false)
            {
#if UDS_WRITE_VIN
                //
                // WDBI - WriteDataByIdentifier VIN, Only after Non-BTLD flashing session
                //
                string VINtoApply = string.IsNullOrEmpty(alternateVIN) ? moduleInfo.VIN : alternateVIN;
                if (VINtoApply.Length == 17) // VIN length seems plausible, try to apply
                {
                    UDS_BMW.UDS_ErrorCodes cmdStatus = _UDSDiagIf.WriteDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.VIN, Encoding.ASCII.GetBytes(VINtoApply));
                    if (cmdStatus == UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                    {
                        Logger.LogInformation("  writing VIN '{0}' to module -> OK", VINtoApply);
                    }
                    else if (cmdStatus == UDS_BMW.UDS_ErrorCodes.UDS_NRC_0022 && moduleInfo.timeLimitedCodingRecordIds.Length > 0) // module has timeLimitedCodingRecordIds, than likely time delay expired for VIN write
                    {
                        Logger.LogInformation("  writing VIN '{0}' to module -> TIME DELAY EXPIRED, OK", VINtoApply);
                    }
                    else
                    {
                        Logger.LogInformation("  writing VIN '{0}' to module failed, exiting", VINtoApply);
                        goto forced_exit;
                    }
                }
#endif
            }

#if BOSCH_MG1_UNLOCK_BYPASS
        unlockedProgSessionExit:
#endif

            Logger.LogInformation("UNLOCKED PROGRAMMING SESSION EXIT:");

            int activeResetTime = resetTime;
            byte activeResetTimeFormat = resetTimeFormat;

            if (BootLoaderUpgradePackJustFlashed)
            {
                activeResetTime = bootLoaderInstallTime;
                activeResetTimeFormat = bootLoaderInstallTimeFormat;
            }

            OnMessage?.Invoke(typeof(MgFlasherCore), $"resetting ECU...");
            if (ExecuteEcuHardReset(activeResetTime, activeResetTimeFormat, UDS_BMW.UDS_ResetMode.hardReset, moduleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE) goto forced_exit;

#if BOSCH_MG1_UNLOCK_BYPASS
            if (BoschMG1RsaBypassBootloaderFirstUpload == true)
            {
                resultCode = 0;
                goto forced_exit;
            }
#endif

            if (FetchModuleActiveDiagnosticSession(ref activeDiagSession) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                goto forced_exit;
            }

            if (BootLoaderJustFlashed == true || BootLoaderUpgradePackJustFlashed == true) // Flash the Remaining Blocks if present
            {
                flashBlocks.RemoveAt(0);

                //
                // TODO when just written a different bootloader, we need the refresh the current bootloader Id here (to the new)
                //
                if (flashBlocks.Count > 0)
                {
                    goto reInitiateFlashRemainder;
                }
                else if (BootLoaderUpgradePackJustFlashed == true)
                {
                    resultCode = 0;
                    goto forced_exit; // After BLUP flash, APP/DST write is manditory
                }
            }

            if (SwitchSessionSecurityAccessAuthenticate(UDS_BMW.UDS_DiagMode.ECUEXTDIAG, moduleInfo.btldId, ref activeDiagSession) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                goto forced_exit;
            }

            if (_UDSDiagIf.StartRoutine_SetEnergyMode(UDS_BMW.UDS_SetEnergyModeIdentifier.Default) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogError("Could not set EnergyMode Default, exiting");
                goto forced_exit;
            }
            else
            {
                Logger.LogInformation("  set EnergyMode Default");
            }

            if (SwitchSessionSecurityAccessAuthenticate(UDS_BMW.UDS_DiagMode.DEFAULT, moduleInfo.btldId, ref activeDiagSession) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                goto forced_exit;
            }
        }

    coding_start:

        //
        // TASK WRITE CODING TO MODULE
        //
        if (ShouldSkipWritingCodingToModule(moduleInfo.unlockCompany) && ((requiredActions & (byte)ActionOption.WRITE_MODULE) == (byte)ActionOption.WRITE_MODULE))
        {
            Logger.LogInformation("  Femto requires us to skip writing coding to module, skipping...");
        }
        else if ((requiredActions & (byte)ActionOption.WRITE_CODING) == (byte)ActionOption.WRITE_CODING)
        {
            Logger.LogInformation("WRITING CODING TO MODULE:");
            OnMessage?.Invoke(typeof(MgFlasherCore), $"writing coding...");

            if (moduleInfo.moduleHasCoding == true)
            {
                if (string.IsNullOrWhiteSpace(codingFile))
                {
                    var moduleName = (string.IsNullOrEmpty(moduleInfo.name) ? string.Format("{0}", moduleId) : moduleInfo.name);
                    var codingFileName = $"{moduleName}_{SGBDIndex}_{moduleInfo.serialNo}_{moduleInfo.VIN}_CAFD.srec";
                    codingFile = Path.Combine(externalBackupPath, codingFileName);
                }

                if (string.IsNullOrWhiteSpace(codingFile))
                {
                    Logger.LogInformation("  ERROR Invalid input file name specified for coding srec '{0}', aborting", codingFile);
                    goto forced_exit;
                }
                else if (!(File.Exists(codingFile)))
                {
                    Logger.LogInformation("  ERROR input file '{0}' does not exist, aborting", codingFile);
                    goto forced_exit;
                }

                if ((activeDiagSession & 0xfff0) != 0x0180) // NOT ApplicationDefaultSessionXXX, coding restore NOT allowed
                {
                    Logger.LogInformation("  ERROR Coding NOT allowed in session {0}, coding data NOT {1}stored to module", _UDSDiagIf.GetDiagSessionDescription(activeDiagSession), performCodingRestoreFromBackup ? "re" : "");
                    resultCode = 10;
                    goto forced_exit;
                }

                //
                // PRECHECKS/INFO FETCH CODING SESSION
                //
                Logger.LogInformation("PRECHECKS/INFO FETCH EXTENDED/CODING SESSION:");

                if (_UDSDiagIf.ReadDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.FlashTimingParameter, out recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    Logger.LogError("Could not read FlashTimingParameters, exiting");
                    goto forced_exit;
                }
                else
                {
                    if (recordData.Length == 12)
                    {
                        FTP_format = (ushort)Format_FTP.FP_6198;
                        eraseTime = ByteArrayParseUshortBigEndian(recordData, 0);
                        checkMemoryTime = ByteArrayParseUshortBigEndian(recordData, 2);
                        bootLoaderInstallTime = ByteArrayParseUshortBigEndian(recordData, 4);
                        authentificationTime = ByteArrayParseUshortBigEndian(recordData, 6);
                        resetTime = ByteArrayParseUshortBigEndian(recordData, 8);
                        transferDataTime = ByteArrayParseUshortBigEndian(recordData, 10);
                    }
                    else if (recordData.Length == 10)
                    {
                        FTP_format = (ushort)Format_FTP.FP_5561;
                        eraseTime = ByteArrayParseUshortBigEndian(recordData, 0);
                        checkMemoryTime = ByteArrayParseUshortBigEndian(recordData, 2);
                        bootLoaderInstallTime = ByteArrayParseUshortBigEndian(recordData, 4);
                        authentificationTime = ByteArrayParseUshortBigEndian(recordData, 6);
                        resetTime = ByteArrayParseUshortBigEndian(recordData, 8);
                    }
                    else if (recordData.Length == 15)
                    {
                        FTP_format = (ushort)Format_FTP.FP_4371;
                        eraseTimeFormat = recordData[0];
                        eraseTime = ByteArrayParseUshortBigEndian(recordData, 1);
                        checkMemoryTimeFormat = recordData[3];
                        checkMemoryTime = ByteArrayParseUshortBigEndian(recordData, 4);
                        authentificationTimeFormat = recordData[6];
                        authentificationTime = ByteArrayParseUshortBigEndian(recordData, 7);
                        resetTimeFormat = recordData[9];
                        resetTime = ByteArrayParseUshortBigEndian(recordData, 10);
                        bootLoaderInstallTimeFormat = recordData[12];
                        bootLoaderInstallTime = ByteArrayParseUshortBigEndian(recordData, 13);
                    }

                    Logger.LogInformation("  FlashTimingParameters {0}:", (Format_FTP)FTP_format);
                    Logger.LogInformation("  EraseMemoryTime: {0} {1}", eraseTime, GetFormatString(eraseTimeFormat, FTP_format));
                    Logger.LogInformation("  CheckMemoryTime: {0} {1}", checkMemoryTime, GetFormatString(checkMemoryTimeFormat, FTP_format));
                    Logger.LogInformation("  BtLdInstallTime: {0} {1}", bootLoaderInstallTime, ((bootLoaderInstallTime == 0) ? "module does not support bootloader update" : GetResetFormatString(bootLoaderInstallTimeFormat, FTP_format)));
                    Logger.LogInformation("  AuthenticationTime: {0} {1}", authentificationTime, GetFormatString(authentificationTimeFormat, FTP_format));
                    Logger.LogInformation("  ResetTime: {0} {1}", resetTime, GetResetFormatString(resetTimeFormat, FTP_format));
                    if (FTP_format == (ushort)Format_FTP.FP_6198)
                    {
                        Logger.LogInformation("  FlashWriteSpeed: {0}", transferDataTime);
                    }
                }

                //
                // PREPARE UNLOCKED CODING SESSION:
                //
                Logger.LogInformation("AQUIRING UNLOCKED CODING SESSION:");

                if (SwitchSessionSecurityAccessAuthenticate(UDS_BMW.UDS_DiagMode.ECUEXTDIAG, moduleInfo.btldId, ref activeDiagSession) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    goto forced_exit;
                }

                if (SwitchSessionSecurityAccessAuthenticate(UDS_BMW.UDS_DiagMode.ECUCODE, moduleInfo.btldId, ref activeDiagSession) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    goto forced_exit;
                }

                //
                // RESTORE CODING
                //
                string restoredReString = performCodingRestoreFromBackup ? "re" : "";
                string restoredBackupString = performCodingRestoreFromBackup ? "backup " : "";

                Logger.LogInformation("{0}STORING CODING:", restoredReString.ToUpper());

                if (FetchCodingRecordsFromSREC(codingFile, out byte[] cafdIdReadFile, out List<CodingRecord> CodingDatasetReadFile) == 0)
                {
                    if (PrepareCheckSignatureCodingRecords(ref CodingDatasetReadFile, moduleInfo.btldId, true) == true)
                    {
                        if (RestoreCodingRecords(CodingDatasetReadFile, string.IsNullOrEmpty(alternateVIN) ? moduleInfo.VIN : alternateVIN, moduleInfo.timeLimitedCodingRecordIds) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                        {
                            Logger.LogInformation("  Could NOT {0}store coding to module from coding data {1}file: {2}", restoredReString, restoredBackupString, codingFile);
                        }
                        else
                        {
                            Logger.LogInformation("  Coding data {0} {1}stored to module from {2}file: {3} -> OK", new SweVersion(cafdIdReadFile).GetXweString(), restoredReString, restoredBackupString, codingFile);
                        }
                    }
                    else // problem during signature correction, cannot restore coding
                    {
                        Logger.LogInformation("  Could NOT {0}store coding to module from coding data {1}file: {2}, Signature Correction FAILED", restoredReString, restoredBackupString, codingFile);
                    }
                }
                else
                {
                    Logger.LogInformation("  Error opening coding file or coding file contains sanity check errors, coding {0}store aborted!", restoredReString);
                }

                //
                // DONE back to default, TODO CHECK transfer to normal diagnostic session might be enough here (some modules only allow code clearing after RESET)
                //
                Logger.LogInformation("CODING SESSION EXIT:");

                if (ExecuteEcuHardReset(resetTime, resetTimeFormat, UDS_BMW.UDS_ResetMode.hardReset, moduleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE) goto forced_exit;

                if (FetchModuleActiveDiagnosticSession(ref activeDiagSession) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    goto forced_exit;
                }
            }
            else
            {
                Logger.LogInformation("  Module does not have coding, coding data NOT stored to module");
            }
        }

        //
        // TASK CLEAR DTC ON ALL MODULES
        //
        if ((requiredActions & (byte)ActionOption.CLEAR_DTCS) == (byte)ActionOption.CLEAR_DTCS)
        {
            Logger.LogInformation("CLEARING FAULTCODES:");
            OnMessage?.Invoke(typeof(MgFlasherCore), $"clearing DTCs...");

            if (_UDSDiagIf.ClearDiagnosticInformation((byte)UDS_BMW.FunktionalAddressingIds.UDS_ALL, UDS_BMW.UDS_DtcGroup.AllDTC) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogError("Could not ClearDiagnosticInformation funktional ALL modules AllDTC, exiting");
                goto forced_exit;
            }
            else
            {
                Logger.LogInformation("  module ClearDiagnosticInformation funktional ALL modules AllDTC -> OK");

                if (_UDSDiagIf.StartRoutine_ClearDTCShadowMemory((byte)UDS_BMW.FunktionalAddressingIds.UDS_ALL) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    Logger.LogError("Could not ClearDTCShadowMemory funktional ALL modules, exiting");
                    resultCode = 0;
                }
                else // next clear Shadow codes in leftover modules directly (non-funktional, dont care about response; but only try once)
                {
                    byte[] leftOverIds = { 0x10, 0x1C, 0x63, 0x78, 0x29, 0x61, 0x19 }; // ZGW, ICMQL, HEADUNIT, IHKA, DSC, ECALL, VGS/LMV

                    _UDSDiagIf.SetRepeatCount(0);

                    for (int i = 0; i < leftOverIds.Length; i++)
                    {
                        _UDSDiagIf.StartRoutine_ClearDTCShadowMemory(leftOverIds[i]);
                    }

                    _UDSDiagIf.ResetRepeatCount();

                    resultCode = 0;

                    Logger.LogInformation("  module ClearDTCShadowMemory funktional ALL modules -> OK");
                }
            }
        }
        //
        // TASK READ DTC (this MODULE)
        //
        if ((requiredActions & (byte)ActionOption.READ_DTCS) == (byte)ActionOption.READ_DTCS)
        {
            byte[] dtcSelectMask = { (byte)(UDS_BMW.UDS_DtcStatusMask.pendingDTC | UDS_BMW.UDS_DtcStatusMask.confirmedDTC) };
            if (_UDSDiagIf.ReadDiagnosticInformation(UDS_BMW.UDS_DtcReadGroup.reportDTCByStatusMask, dtcSelectMask, out byte[] dtcdatablock) == UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogInformation("DTC INFO:");

                if ((dtcdatablock.Length - 1) % 4 != 0)
                {
                    Logger.LogInformation("Not a valid ReadDTC response, length is wrong ({0}): {1}", dtcdatablock.Length, StringFunctions.ConvertBytesToHexString(dtcdatablock));
                }
                else
                {
                    int dtcCount = (dtcdatablock.Length - 1) / 4;
                    byte statusAvailabilityMask = dtcdatablock[0];

                    Logger.LogInformation("  DtcCount: {0}", dtcCount);
                    Logger.LogInformation("  StatusAvailabilityMask: {0:X02}", statusAvailabilityMask);

                    for (int index = 0; index < dtcCount; index++)
                    {
                        int startByte = 1 + index * 4;
                        byte dtcHSB = dtcdatablock[startByte];
                        byte dtcMSB = dtcdatablock[startByte + 1];
                        byte dtcLSB = dtcdatablock[startByte + 2];

                        int dtc = ((dtcHSB & 0xFF) << 16) + ((dtcMSB & 0xFF) << 8) + (dtcLSB & 0xFF);
                        byte status = dtcdatablock[startByte + 3];

                        Logger.LogInformation("  Dtc #{0:D02}: {1:X06} Status: {2:X02}", index, dtc, status);
                    }
                }
            }
        }
#endif

    forced_exit:
        return resultCode;
    }

    private static bool IsThirdPartyUnlocked(UnlockCompanyName unlockCompany)
    {
        return unlockCompany != UnlockCompanyName.MG_Flasher
            && unlockCompany != UnlockCompanyName.Undefined;
    }

    public static bool IsNewBtldSecurity(UnlockWaveType securityWave)
    {
        return securityWave != UnlockWaveType.Undefined && securityWave != UnlockWaveType.Wave1;
    }

    private static bool ShouldWriteFeedaffeZone(UnlockCompanyName unlockCompany)
    {
        return unlockCompany == UnlockCompanyName.FEMTO || unlockCompany == UnlockCompanyName.TAS;
    }

    private static bool ShouldSkipWritingCodingToModule(UnlockCompanyName unlockCompany)
    {
        return unlockCompany == UnlockCompanyName.FEMTO;
    }

    private static bool ShouldSkipCheckMemoryIndexed(UnlockCompanyName unlockCompany)
    {
        return unlockCompany == UnlockCompanyName.FEMTO || unlockCompany == UnlockCompanyName.TAS;
    }

    private static bool ShouldSkipCheckProgrammingDependencies(UnlockCompanyName unlockCompany)
    {
        return unlockCompany == UnlockCompanyName.FEMTO || unlockCompany == UnlockCompanyName.TAS;
    }

    public static bool GetWave3UnlockStatus(UnlockCompanyName unlockCompany)
    {
        return unlockCompany == UnlockCompanyName.FEMTO || unlockCompany == UnlockCompanyName.TAS || unlockCompany == UnlockCompanyName._1769;
    }

    public static UnlockWaveType CheckForSecurityWave(ref ModuleInfo moduleInfo)
    {
        try
        {
            if (SupportedBootctrlVersions(moduleInfo, checkWave3Support: true))
            {
                return UnlockWaveType.Wave3;
            }
            return CheckEcuForNewSecurityBtld(ref moduleInfo) ? UnlockWaveType.Wave2 : UnlockWaveType.Wave1;
        }
        catch (Exception ex)
        {
            Logger.LogError("Failed to determine security wave!");
            return UnlockWaveType.Undefined;
        }
    }

    public static bool CheckForTasOr1769Unlock(ref ModuleInfo moduleInfo)
    {
        return CheckFor1769Unlock(ref moduleInfo) || CheckForTasUnlock(ref moduleInfo);
    }

    private static bool CheckFor1769Unlock(ref ModuleInfo moduleInfo)
    {
        if (string.IsNullOrWhiteSpace(moduleInfo.serialNo)
            || moduleInfo.serialNo.Length < 5
            || !moduleInfo.serialNo.StartsWith("1769"))
        {
            return false;
        }
        moduleInfo.unlockCompany = UnlockCompanyName._1769;
        moduleInfo.unlocked = "1769";
        return true;
    }

    private static bool CheckForTasUnlock(ref ModuleInfo moduleInfo)
    {
        // Can only be checked through dif variante tags
        return moduleInfo.unlockCompany == UnlockCompanyName.TAS;
    }

    public static bool IsPpcArchitecture(ModuleInfo moduleInfo) => IsPpcArchitecture(moduleInfo.ProcessorArchitectureType);

    public static bool IsAurixArchitecture(ModuleInfo moduleInfo) => IsAurixArchitecture(moduleInfo.ProcessorArchitectureType);

    public static bool IsSupportedArchitecture(ModuleInfo moduleInfo) => IsPpcArchitecture(moduleInfo) || IsAurixArchitecture(moduleInfo);

    public static bool IsPpcArchitecture(ProcessorArchitectureType type)
    {
        return type.Equals(ProcessorArchitectureType.PPC);
    }

    public static bool IsAurixArchitecture(ProcessorArchitectureType type)
    {
        return type.Equals(ProcessorArchitectureType.AURIX);
    }

    public static bool IsSupportedArchitecture(ProcessorArchitectureType type)
    {
        return IsPpcArchitecture(type) || IsAurixArchitecture(type);
    }

    private static Range GenerateReadRange(uint sa, uint ea, SegmentOption segment, uint sim)
    {
        byte segmentB = (byte)segment;
        return GenerateReadRange((uint)(((uint)segmentB << 24) + (sa & 0xffffff)), (uint)(((uint)segmentB << 24) + (ea & 0xffffff)), sim);
    }

    private static Range GenerateReadRange(uint sa, uint ea, uint sim)
    {
        Range flashRangeItem = new Range
        {
            start = sa,
            end = ea
        };
        flashRangeItem.size = flashRangeItem.end - flashRangeItem.start + 1;
        flashRangeItem.flash_method = sim;
        return flashRangeItem;
    }

    private static FlashSegment GenerateFlashSegment(uint sa, uint ss, uint ta, string ns)
    {
        FlashSegment flashSegmentItem = new FlashSegment
        {
            sourceStartAddress = sa,
            sourceLength = ss,
            targetStartAddress = ta,
            targetLength = ss,
            nameShort = ns
        };
        return flashSegmentItem;
    }

#if LMV_XCP_OVER_FLEX_UNLOCK

    private static String BitArray6BitDecode(byte[] pBytes, int pBitOffset, int pDigits, bool pCheckEOL)
    {
        StringBuilder sb = new StringBuilder(pDigits);
        int bitLength = pDigits * 6;
        int sixbit = 0;
        for (int i = 0; i < bitLength; i++)
        {
            if ((i > 0) && (i % 6 == 0))
            {
                AppendDigit(sb, sixbit);
            }
            byte b = pBytes[((pBitOffset + i) / 8)];
            int bitPos = 7 - (pBitOffset + i) % 8;
            int mask = 1 << bitPos;
            int bit = b & mask;
            sixbit <<= 1;
            sixbit |= (bit > 0 ? 1 : 0);
            if ((pCheckEOL) && (i == 1) &&
                (sixbit == 0))
            {
                return null;
            }
        }
        AppendDigit(sb, sixbit);

        return sb.ToString();
    }

    private static void AppendDigit(StringBuilder pSB, int pSixBit)
    {
        int prefix = (pSixBit & 0x30) >> 4;

        int lowNibble = pSixBit & 0xF;
        int highNibble;
        if (prefix == 1)
        {
            highNibble = 48;
        }
        else
        {
            if (prefix == 2)
            {
                highNibble = 64;
            }
            else
            {
                if (prefix == 3)
                {
                    highNibble = 80;
                }
                else
                {
                    highNibble = 0;
                }
            }
        }
        int digit = highNibble + lowNibble;

        pSB.Append((char)digit);
    }

#endif

    public static uint StoreCodingRecordsToSREC(string filename, byte[] cafdId, List<CodingRecord> CodingDataset)
    {
        StreamWriter writeStream = File.CreateText(filename);
        var ncdLineCnt = StoreCodingRecordsToSREC(writeStream, cafdId, CodingDataset);
        OnCodingStored?.Invoke(typeof(MgFlasherCore), filename);
        return ncdLineCnt;
    }

    public static uint StoreCodingRecordsToSREC(StreamWriter writeStream, byte[] cafdId, List<CodingRecord> CodingDataset)
    {
        const byte bytesPerRow = 32;
        ushort ncdLineCnt = 0;

        if (writeStream != null)
        {
            // write S0 header
            if (cafdId.Length > 2) writeStream.WriteLine(CreateSRECLineNCD(0, 0, cafdId, 1, 7));
            else writeStream.WriteLine(CreateSRECLineNCD(0, 0, cafdId, 0, (byte)cafdId.Length));

            // write S2 data CodingDataset
            for (int i = 0; i < CodingDataset.Count; i++)
            {
                uint segmentA = (uint)(CodingDataset[i].index << 8);

                // fragment
                int segments = (CodingDataset[i].size / bytesPerRow);
                int remainder = (CodingDataset[i].size % bytesPerRow);

                for (int t = 0; t < segments; t++)
                {
                    ushort offset = (ushort)(t * bytesPerRow);
                    writeStream.WriteLine(CreateSRECLineNCD(2, segmentA + offset, CodingDataset[i].data, offset, bytesPerRow)); ncdLineCnt++;
                }

                if (remainder > 0)
                {
                    ushort offsetRemain = (ushort)(segments * bytesPerRow);
                    writeStream.WriteLine(CreateSRECLineNCD(2, segmentA + offsetRemain, CodingDataset[i].data, (ushort)offsetRemain, (byte)remainder)); ncdLineCnt++;
                }
            }

            // write S5 count record
            writeStream.WriteLine(CreateSRECLineNCD(5, ncdLineCnt, Array.Empty<byte>(), 0, 0));
        }
        writeStream.Flush();
        writeStream.Close();

        return ncdLineCnt;
    }

    private static string CreateSRECLineNCD(byte type, uint address, byte[] data, ushort offset, byte length)
    {
        string ncdLine = "S";
        byte checksum = 0;

        ncdLine += type.ToString("X1");

        // add count
        if (type == 5) { checksum += 3; ncdLine += "03"; }
        else { byte count = (byte)(length + ((type == 0) ? 3 : 4)); checksum += count; ncdLine += count.ToString("X2"); }

        // and address
        byte ab;
        if (type != 0 && type != 5) { ab = (byte)(address >> 16); checksum += ab; ncdLine += ab.ToString("X2"); }
        ab = (byte)(address >> 8); checksum += ab; ncdLine += ab.ToString("X2");
        ab = (byte)(address); checksum += ab; ncdLine += ab.ToString("X2");

        for (int i = 0; i < length; i++) { checksum += data[offset + i]; ncdLine += data[offset + i].ToString("X2"); }
        ncdLine += ((byte)~checksum).ToString("X2");

        return ncdLine;
    }

    public static uint FetchCodingRecordsFromSREC(string filename, out byte[] cafdId, out List<CodingRecord> CodingDataset)
    {
        ushort ncdS2LineCnt = 0;
        ushort checksumOrLineErrorCount = 0;

        cafdId = Array.Empty<byte>();
        CodingDataset = new List<CodingRecord>();

        try
        {
            string[] SRECLines = System.IO.File.ReadAllLines(filename);

            for (int i = 0; i < SRECLines.Length; i++)
            {
                if (SRECLines[i][0] != 'S') continue; // Not a valid SREC line start, skip it

                byte[] SRECLineBytes = StringFunctions.ConvertHexStringToBytes(SRECLines[i].Substring(2)); // SREC Line -> bytes, strip leading 'SX'

                // verify checksum
                byte checksum = 0;
                for (int t = 0; t < (SRECLineBytes.Length - 1); t++)
                {
                    checksum += SRECLineBytes[t];
                }
                if (checksum != (byte)~SRECLineBytes[^1]) checksumOrLineErrorCount++;

                switch (SRECLines[i][1])
                {
                    case '0': // hdr (header record, defines CAFD SWE version)
                        {
                            if (SRECLineBytes.Length > 10)
                            {
                                cafdId = new byte[8];
                                cafdId[0] = 0x05; // CAFD
                                System.Array.Copy(SRECLineBytes, 3, cafdId, 1, 7);
                            }
                            else // codingChangeIndex KWP2000
                            {
                                cafdId = new byte[2];
                                System.Array.Copy(SRECLineBytes, 3, cafdId, 0, 2);
                            }
                        }
                        break;

                    case '2': // data
                        {
                            ushort index = ByteArrayParseUshortBigEndian(SRECLineBytes, 1);
                            ushort size = (ushort)(SRECLineBytes[0] - 4); // data size

                            var itemIndex = CodingDataset.FindIndex(x => x.index == index);
                            if (itemIndex != -1) // found it, update
                            {
                                var item = CodingDataset.ElementAt(itemIndex);
                                CodingDataset.RemoveAt(itemIndex);

                                byte[] datanew = new byte[item.data.Length + size];

                                item.data.CopyTo(datanew, 0);
                                System.Array.Copy(SRECLineBytes, 4, datanew, item.data.Length, size);

                                item.size += size;
                                item.data = datanew;

                                CodingDataset.Insert(itemIndex, item);
                            }
                            else // not found, add it
                            {
                                byte[] data = new byte[size];
                                System.Array.Copy(SRECLineBytes, 4, data, 0, size);

                                CodingRecord codingRecord = new CodingRecord
                                {
                                    index = index,
                                    data = data,
                                    size = size
                                };
                                CodingDataset.Add(codingRecord);
                            }
                            ncdS2LineCnt++;
                        }
                        break;

                    case '5':
                        {
                            ushort count = ByteArrayParseUshortBigEndian(SRECLineBytes, 1);
                            if (count != ncdS2LineCnt) checksumOrLineErrorCount++;
                        }
                        break; // count record
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogInformation($"Cannot open \"{filename}\" for reading, CodingDataset import aborted. Exception: \"{ex}\"");
            Logger.LogError(ex, "Error");
            return 0xff;
        }
        return checksumOrLineErrorCount;
    }

    public static ushort ByteArrayParseUshortBigEndian(byte[] data, int offset)
    {
        return _UDSDiagIf.ByteArrayParseUshortBigEndian(data, offset);
    }

    public static ushort ByteArrayParseUshortLittleEndian(byte[] data, int offset)
    {
        return _UDSDiagIf.ByteArrayParseUshortLittleEndian(data, offset);
    }

    public static void Serialize(ModuleConnectInfo[] input)
    {
        var filename = "moduleConnectInfo.xml";
        if (File.Exists(filename))
        {
            var serializer = new XmlSerializer(input.GetType());
            var sw = new StreamWriter(filename);
            serializer.Serialize(sw, input);
            sw.Close();
        }
        else
        {
            throw new Exception($"File [{filename}] doesn't exist!");
        }
    }

    public static ModuleConnectInfo[] Deserialize()
    {
        var filename = "moduleConnectInfo.xml";
        if (File.Exists(filename))
        {
            var stream = new StreamReader(filename);
            var ser = new XmlSerializer(typeof(ModuleConnectInfo[]));
            object obj = ser.Deserialize(stream);
            stream.Close();
            return (ModuleConnectInfo[])obj;
        }
        else
        {
            throw new Exception($"File [{filename}] doesn't exist!");
        }
    }

    public static void PrintTransferStats(TimeSpan ts, uint transferredBytes, bool isUpload = false)
    {
        // Format and display the TimeSpan value.
        string elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}", ts.Hours, ts.Minutes, ts.Seconds, ts.Milliseconds / 10);

        if (transferredBytes > 0)
        {
            double milliSec = ts.TotalMilliseconds;
            int kbSizeBytes = 1024;
            double msPerKb = ((double)milliSec / ((double)transferredBytes / kbSizeBytes));
            double kbPerSecond = 1000 / msPerKb;
            string complete_runtime = String.Format("  complete; total runtime: ");
            Logger.LogInformation(complete_runtime + elapsedTime + String.Format(", {0} speed: {1:F2} ms/kb or {2:F2} kb/sec", (isUpload ? "upload" : "download"), msPerKb, kbPerSecond));
        }
        else
        {
            Logger.LogInformation("  no data transferred, total runtime: " + elapsedTime);
        }
    }

#if USE_UDS_PROTOCOL

    private static string GetSegmentName(SweArtType segmentType) => segmentType switch
    {
        SweArtType.BTL => "BTLD",
        SweArtType.PST => "PST",
        SweArtType.DST => "DST",
        SweArtType.SWFL => "PST OR DST",
        SweArtType.BLUP => "BLUP",
        SweArtType.HW => "HWAP OR HWEL",
        SweArtType.UNKNOWN => "UNKNOWN",
        _ => "INVALID",
    };

    private static string GetCompressionMethodName(CompressionMethod compressionMethod) => compressionMethod switch
    {
        CompressionMethod.NONE => "RAW",
        CompressionMethod.NRV => "NRV",
        _ => "INVALID",
    };

    public enum Format_FTP
    { FP_6198, FP_5561, FP_4371 };

    private static string GetResetFormatString(byte timeFormat, ushort format)
    {
        if (format == (ushort)Format_FTP.FP_4371)
        {
            return timeFormat switch
            {
                1 => "with method B1 (ResponsePending, $78)",
                2 => "with waiting method (FP_5582/FP_5557)",
                _ => "",
            };
        }

        return "";
    }

    private static string GetFormatString(byte timeFormat, ushort format)
    {
        if (format == (ushort)Format_FTP.FP_4371)
        {
            return timeFormat == 1 ? "with method B1 (ResponsePending, $78)" : "";
        }
        return "";
    }

    public static ModuleInfo DecodeModuleInfoFromSGBDIndex(int SGBDIndex, bool skipFallbackSgbd = false)
    {
        ModuleInfo moduleInfo = new ModuleInfo
        {
            SGBDIndex = SGBDIndex,
            name = string.Empty,
            serialNo = string.Empty,
            supplierTypeName = string.Empty,
            ProcessorArchitectureType = ProcessorArchitectureType.UNKNOWN,
            unlockCompany = UnlockCompanyName.Undefined,
            unlocked = string.Empty,
            moduleHasCoding = true,
            readMemoryByAddressAvailableInBoot = false,
            readMemoryByAddressChunkSizeInclCmdRsp = 128,
            fullBinSize = 0,

            codingRecordIds = Array.Empty<ushort>(),
            timeLimitedCodingRecordIds = Array.Empty<ushort>(),
            btldId = new byte[8],
            cafdId = new byte[8],
#if BOSCH_MG1_UNLOCK_BYPASS
            swfkId = new byte[8],
            swflId = new byte[8],
#endif
            sweVersions = new List<SweVersion>(),
            bdcSweVersions = new List<SweVersion>(),
            sweBackupList = new List<EcuSvkHistoryItem>(),
            difVarianteList = new List<DifVariante>(),
            A2Lstring = string.Empty,
            Logistikbereich = string.Empty,
            calid = string.Empty,
            cvn = string.Empty,
            fullEngineCode = string.Empty,
            engineCode = string.Empty,
            engineConfiguration = string.Empty,
            engineProductionYears = string.Empty,
            engineDisplacment = string.Empty
        };

        if (!(SGBDIndex == 992294
              || SGBDIndex == 992295
              || SGBDIndex == 992298
              || SGBDIndex == 992296
              || SGBDIndex == 987920
              || SGBDIndex == 992948
              || SGBDIndex == 992674
              || SGBDIndex == 993584
              || SGBDIndex == 992992
              || SGBDIndex == 993000
              || SGBDIndex == 992352
              || SGBDIndex == 696901
              || SGBDIndex == 696902
              || SGBDIndex == 696903
              || SGBDIndex == 696904
                ) && !skipFallbackSgbd)
        {
            SGBDIndex = DetermineGenericFallbackSgbdIndexViaDmeArchitecture();
        }

        switch (SGBDIndex)
        {
            case 992294:
                {
                    moduleInfo.name = "DME840";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.PPC;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x780000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;

            case 992295:
                {
                    moduleInfo.name = "DME841";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.PPC;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x780000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;

            case 992298:
                {
                    moduleInfo.name = "DME860";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.PPC;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x780000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;

            case 992296:
                {
                    moduleInfo.name = "DME861";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.PPC;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x780000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;

            case 987920: // BDC ZGW Bench
                {
                    moduleInfo.name = "ZGW-FEM-GATEWAY";
                    moduleInfo.supplierTypeName = "ZGW_FEM";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.MPC5567_MVR132;
                    moduleInfo.moduleHasCoding = false;
                    moduleInfo.fullBinSize = 0x200000;
                }
                break;

            case 992948:
                {
                    moduleInfo.name = "DME86T0";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.AURIX;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x800000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;

            case 992674:
                {
                    moduleInfo.name = "DME84T0";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.AURIX;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x800000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;

            case 993584:
                {
                    moduleInfo.name = "DME860TH";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.AURIX;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x800000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;

            case 992992:
                {
                    moduleInfo.name = "DME84T0";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.AURIX;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x800000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;

            case 993000:
                {
                    moduleInfo.name = "DME86T0";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.AURIX;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x800000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;

            case 992352:
                {
                    moduleInfo.name = "DME88T0";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.AURIX;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x800000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;

            case 696901: // (SK_CUSTOM)
                {
                    moduleInfo.name = "GenericAurix";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.AURIX;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x800000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;

            case 696902: // (SK_CUSTOM)
                {
                    moduleInfo.name = "GenericPPC";
                    moduleInfo.supplierTypeName = "MG1";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.PPC;

                    /*
                     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
                     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
                     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
                     * 0x3032 Codierdaten Signatur schreiben
                     * 0x37FE Codierprüfstempel (CPS) schreiben
                     */

                    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
                    moduleInfo.fullBinSize = 0x780000;
                    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
                    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
                }
                break;
            //case 696903: // (SK_CUSTOM)
            //{
            //    moduleInfo.name = "GenericMEVD17";
            //    moduleInfo.supplierTypeName = "xxxx";
            //    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.MEVD;

            //    /*
            //     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
            //     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
            //     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
            //     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
            //     * 0x3032 Codierdaten Signatur schreiben
            //     * 0x37FE Codierprüfstempel (CPS) schreiben
            //     */

            //    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
            //    moduleInfo.fullBinSize = 0x780000;
            //    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
            //    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
            //}
            //    break;
            //case 696904: // (SK_CUSTOM)
            //{
            //    moduleInfo.name = "GenericMED_EDC";
            //    moduleInfo.supplierTypeName = "xxxx";
            //    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.MED_EDC;

            //    /*
            //     * 0x3010 Codierdatenblock mit Zeitbegrenzung schreiben
            //     * 0x3020 Codierdatenblock ohne Zeitbegrenzung schreiben
            //     * 0x3030 Codierdatenblock ohne Zeitbegrenzung schreiben
            //     * 0x3031 SGBM−Identifikatoren (CAF−ID) schreiben
            //     * 0x3032 Codierdaten Signatur schreiben
            //     * 0x37FE Codierprüfstempel (CPS) schreiben
            //     */

            //    moduleInfo.readMemoryByAddressChunkSizeInclCmdRsp = 0xFF;
            //    moduleInfo.fullBinSize = 0x780000;
            //    moduleInfo.codingRecordIds = new ushort[] { 0x3010, 0x3020, 0x3030, 0x3031, 0x3032, 0x37FE };
            //    moduleInfo.timeLimitedCodingRecordIds = new ushort[] { 0x3010 };
            //}
            //    break;
            default:
                {
                    moduleInfo.name = "UNKNOWN";
                    moduleInfo.supplierTypeName = "UNKNOWN";
                    moduleInfo.ProcessorArchitectureType = ProcessorArchitectureType.UNKNOWN;
                }
                break;
        }
        return moduleInfo;
    }

    public static bool IsBootMode(ushort activeDiagSession)
    {
        return (activeDiagSession == 0x0100 || activeDiagSession == 0x0300);
    }

    private static bool CafdCoded(byte[] xWebytes)
    {
        bool CafdCoded = true;
        xWebytes = StringFunctions.ExtractSectionArray(xWebytes, 1, 7);
        if (xWebytes.All(b => b == 0) || xWebytes.All(b => b == 0xff)) CafdCoded = false;
        return CafdCoded;
    }

    private static int SparseBitcount(int n)
    {
        int count = 0;
        while (n != 0)
        {
            count++;
            n &= (n - 1);
        }
        return count;
    }

    private static List<FlashBlock> GenerateFlashBlocksDumpInfo(bool inputModeSwflFiles, int writecommandOption, List<string> inFiles, ModuleInfo moduleInfo)
    {
        var flashBlockList = new List<FlashBlock>();

        if (inputModeSwflFiles == true) // needs multiple (>= N) input files (depending on N amount of 1 bits in writecommandOption)
        {
            flashBlockList = GenerateFlashBlocksSWFL(writecommandOption, inFiles);
        }
        else // needs one input file, used in multiple flashBlocks
        {
            flashBlockList = GenerateFlashBlocksRawBin(writecommandOption, inFiles, moduleInfo.SGBDIndex, moduleInfo.fullBinSize, moduleInfo);
        }

        // some error during flashblocks creation, goto exit.
        if (flashBlockList.Count < 1) throw new System.InvalidOperationException("ERROR: flashBlocks.Count < 1");

        Logger.LogInformation("CHECKING FLASHBLOCK ORDER:");
        flashBlockList.Sort(delegate (FlashBlock x, FlashBlock y) // simple flashBlocks by sweArt; reversed upload order in hacked mode
        {
            if ((byte)x.SweVersion.SweArt == (byte)y.SweVersion.SweArt) return 0;
#if BOSCH_MG1_UNLOCK_BYPASS
            else if ((byte)x.SweVersion.SweArt > (byte)y.SweVersion.SweArt) return (BoschMG1RsaBypassBootloaderFirstUpload == true) ? 1 : -1;
            else return (BoschMG1RsaBypassBootloaderFirstUpload == true) ? -1 : 1;
#else
                else if ((byte)x.SweVersion.SweArt > (byte)y.SweVersion.SweArt) return -1;
                else return 1;
#endif
        });

        Logger.LogInformation("CHECKING COMPATIBILITY/REFERENCES:");

        var runningSweVersionList = new List<SweVersion>();
        foreach (var sweVersion in moduleInfo.sweVersions)
        {
            runningSweVersionList.Add(sweVersion);
        }
        if (flashBlockList.All(x => (x.SweVersion.References.Count > 0))) // compatibility info filled for all flashBlocks
        {
            var allReferencesOk = true;
            var previousFlashBlock = new FlashBlock?();
            foreach (var entry in flashBlockList.Select((FlashBlock, Index) => (FlashBlock, Index)))
            {
                var flashBlockReferenceList = entry.FlashBlock.SweVersion.References.ToList();

                var flashBlockGroupedReferencesByProcessClass = flashBlockReferenceList.GroupBy(x => x.ProcessClass)
                    .OrderBy(x => x.Key)
                    .Select(x => (x.Key, x.Select(x => x).ToList()))
                    .Select(reference => new { ProcessClass = reference.Key, SweVersionList = reference.Item2 })
                    .ToList();

                var referencesOk = flashBlockGroupedReferencesByProcessClass.All(reference => CheckSweVersionListForReference(reference.ProcessClass, reference.SweVersionList, runningSweVersionList));
                if (referencesOk)
                {
                    runningSweVersionList = UpdateSweVersionListForGivenProcessClass(entry.FlashBlock.SweVersion, runningSweVersionList);
                }
                else
                {
                    allReferencesOk = false;
                }

                var againstName = entry.Index == 0
                    ? "MODULE"
                    : $"FLASHBLOCK #{entry.Index - 1}";

                var againstProcessClass = entry.Index == 0 ?
                    entry.FlashBlock.SweVersion.References[0].ToString().ToUpper()
                    : $"{previousFlashBlock.Value.SweVersion.ToString().ToUpper()}";

                Logger.LogInformation($"  [Flashblock #{entry.Index} - {entry.FlashBlock.SweVersion.ToString().ToUpper()}] against [{againstName} - {againstProcessClass}] -> REFERENCE {((referencesOk) ? "OK" : "ERROR")}");
                previousFlashBlock = entry.FlashBlock;
            }

#if BOSCH_MG1_UNLOCK_BYPASS
            if (BoschMG1RsaBypassBootloaderFirstUpload == true || BTLD_old_aurix_install)
            {
                Logger.LogInformation("  WARNING Compatibility/References chain check info ignored for BoschMG1RsaBypassBootloaderFirstUpload mode, continuing");
            }
            else
#endif
            if (allReferencesOk == false)
            {
                Logger.LogInformation("  ERROR Compatibility/References chain check failed for one or multiple sections, aborting");
                OnMessage?.Invoke(typeof(MgFlasherCore), "Incompatible software...");
                throw new System.InvalidOperationException("");
            }
        }
        else // compatibility/references info NOT filled for ALL flashBlocks, skip check
        {
            Logger.LogInformation("  WARNING Compatibility/References chain check skipped, compatility/references info not provided for all sections");
        }

        // AUTO OR FORCED ELIMINATION OF FLASHBLOCKS
        if (writecommandOption == (int)WriteCommandOption.AUTO)
        {
            int eliminatedCnt = 0;
            bool bootLoaderFlashEnabled = false;
            Logger.LogInformation("AUTO ELIMINATING FLASHBLOCKS:");
            foreach (var flashBlock in flashBlockList)
            {
                var sweVersion = flashBlock.SweVersion;

                if (moduleInfo.sweVersions.Any(x => !x.IsEmpty
                                                    && !sweVersion.IsEmpty
                                                    && sweVersion.Id == x.Id
                                                    && sweVersion.Major == x.Major
                                                    && sweVersion.Minor == x.Minor
                                                    && sweVersion.Patch == x.Patch))
                {
                    if (!(bootLoaderFlashEnabled && sweVersion.SweArt == SweArtType.DST))
                    {
                        Logger.LogInformation("  eliminating {0} {1}", sweVersion.SweArt, sweVersion.GetXweString());
                        flashBlockList.Remove(flashBlock);
                        eliminatedCnt++;
                    }
                }
                else if (sweVersion.SweArt == SweArtType.BTL) bootLoaderFlashEnabled = true;
            }
            if (eliminatedCnt == 0) Logger.LogInformation("  No Flash Blocks Eliminated");
        }
        else if (inputModeSwflFiles == true)
        {
            int eliminatedCnt = 0;
            Logger.LogInformation("FORCED ELIMINATING FLASHBLOCKS (by writecommandOption = {0})", (WriteCommandOption)writecommandOption);
            foreach (var flashBlock in flashBlockList)
            {
                if (((int)flashBlock.SweVersion.SweArt & writecommandOption) == 0)
                {
                    Logger.LogInformation("  eliminating {0} {1}", flashBlock.SweVersion.SweArt, flashBlock.SweVersion.GetXweString());
                    flashBlockList.Remove(flashBlock);
                    eliminatedCnt++;
                }
            }
            if (eliminatedCnt == 0) Logger.LogInformation("  No Flash Blocks Eliminated");
            else Logger.LogInformation("  ** TAKE CARE, after forced elimination it is possible that the compatibility/references chain is broken **");
        }

        Logger.LogInformation("FINAL FLASHBLOCKS:");

        if (flashBlockList.Count == 0) // No Flash blocks remaining after elimination, proceed to next action (usually coding)
        {
            Logger.LogInformation("  No FlashBlocks remaining for execution, skipping module Flash");
            return new List<FlashBlock>();
        }

        bool finalbootLoaderFlashEnabled = false;

        for (int i = 0; i < flashBlockList.Count; i++)
        {
            var referenceSweInfoConcat = "ReferenceSweInfo ";
            for (int t = 0; t < flashBlockList[i].SweVersion.References.Count; t++) referenceSweInfoConcat += flashBlockList[i].SweVersion.References[t].GetXweString() + " ";
            if (flashBlockList[i].SweVersion.References.Count == 0) referenceSweInfoConcat += "NA";

            Logger.LogInformation("  {0} {1,-4} {2} SWEDescriptionTableStartAddress 0x{3:X06} SWEDevelopmentInfoFieldAddress {4} {5}",
                i, GetSegmentName(flashBlockList[i].SweVersion.SweArt), flashBlockList[i].SweVersion.GetXweString(), flashBlockList[i].SWEDescriptionTableStartAddress,
                (flashBlockList[i].SWEDevelopmentInfoFieldAddress > 0) ? string.Format("0x{0:X06}", flashBlockList[i].SWEDevelopmentInfoFieldAddress) : "NA",
                referenceSweInfoConcat);

            for (int t = 0; t < flashBlockList[i].flashSegments.Count; t++)
            {
                FlashSegment flashSegment = flashBlockList[i].flashSegments[t];
                Logger.LogInformation("    {0, 2} {1} {2} {3} ta 0x{4:X06} tl 0x{5:X06} sa 0x{6:X06} sl 0x{7:X06} checksum 0x{8:X04}",
                    t, flashSegment.nameShort, flashSegment.compressed, GetCompressionMethodName(flashSegment.compressionMethod), flashSegment.targetStartAddress,
                    flashSegment.targetLength, flashSegment.sourceStartAddress, flashSegment.sourceLength, flashSegment.checksum);
            }

            if (flashBlockList[i].SweVersion.SweArt == SweArtType.BTL) finalbootLoaderFlashEnabled = true;
        }

#if BOSCH_MG1_UNLOCK_BYPASS
        if (finalbootLoaderFlashEnabled && BoschMG1RsaPatchedBootloaderPresent) // mg1BmwExplorerStyleRsaPatchedBootloaderDetected, BTLD flash warning
        {
            Logger.LogInformation("  WARNING MG1 SPECIAL MODE BoschMG1RsaPatchedBootloaderPresent only supports OEM BTLD flash");
        }
#endif

        return flashBlockList;
    }

    private static List<SweVersion> UpdateSweVersionListForGivenProcessClass(SweVersion newSweVersion, List<SweVersion> sweVersionList)
    {
        var result = new List<SweVersion>();
        var itemAdded = false;
        foreach (var oldSweVersion in sweVersionList)
        {
            if ((oldSweVersion.SweArt == newSweVersion.SweArt
                 || oldSweVersion.ProcessClass == newSweVersion.ProcessClass
                 || oldSweVersion.ProcessClass == ProcessClassType.UNKNOWN)
                && !itemAdded)
            {
                result.Add(newSweVersion);
                itemAdded = true;
            }
            else
            {
                result.Add(oldSweVersion);
            }
        }
        return result;
    }

    private static bool CheckSweVersionListForReference(ProcessClassType processClass, List<SweVersion> referenceSweVersionList, List<SweVersion> moduleSweVersionList)
    {
        var moduleSweVersion = moduleSweVersionList.Where(x => x.ProcessClass == processClass).FirstOrDefault();
        if (moduleSweVersion != null && !moduleSweVersion.IsEmpty)
        {
            if (processClass == ProcessClassType.BTLD
                || processClass == ProcessClassType.SWFL
                || processClass == ProcessClassType.SWFK)
            {
                return referenceSweVersionList.Any(x => !x.IsEmpty
                                                        && !moduleSweVersion.IsEmpty
                                                        && x.Id == moduleSweVersion.Id
                                                        && x.Major == moduleSweVersion.Major
                                                        && x.Minor == moduleSweVersion.Minor
                                                        && x.Patch == moduleSweVersion.Patch);
            }
            return referenceSweVersionList.Any(x => !x.IsEmpty
                                                    && !moduleSweVersion.IsEmpty
                                                    && x.Id == moduleSweVersion.Id
                                                    && x.Major == moduleSweVersion.Major
                                                    && x.Minor == moduleSweVersion.Minor);
        }
        return false;
    }

    private static List<FlashBlock> GenerateFlashBlocksSWFL(int writecommandOption, List<string> inFiles)
    {
        var flashBlocks = new List<FlashBlock>();

        int requestedSegmentCnt = SparseBitcount(writecommandOption & (int)WriteCommandOption.BTL_HWL_PST_DST);
        int segmentsTouched = 0;

        if (inFiles.Count == 0 || (inFiles.Any(b => string.IsNullOrWhiteSpace(b))) || inFiles.Distinct().Count() != inFiles.Count())
        {
            Logger.LogInformation("  ERROR Invalid (or duplicate) input file name(s) specified for SWFL binary flash, aborting");
        }
        else if (writecommandOption != (int)WriteCommandOption.AUTO && inFiles.Count < requestedSegmentCnt)
        {
            Logger.LogInformation("  ERROR SWFL binary flash requires at least the same amount of unique input files ({0}) as requested write segments ({1}), aborting", inFiles.Count, requestedSegmentCnt);
        }
        else if (!(inFiles.All(b => b.Contains(".bin."))))
        {
            Logger.LogInformation("  ERROR not all input files names are in format xxx.bin.yyy, aborting");
        }
        else if (!(inFiles.All(b => (File.Exists(b) && File.Exists(b.Replace("bin", "xml"))))))
        {
            Logger.LogInformation("  ERROR not all input bin file names or their xml counterparts exist, aborting");
        }
        else if (!(inFiles.All(b =>
                 {
                     string filename = Path.GetFileName(b);
                     if (filename.StartsWith("SWFL", StringComparison.OrdinalIgnoreCase) || filename.StartsWith("SWFK", StringComparison.OrdinalIgnoreCase) ||
                         filename.StartsWith("BTLD", StringComparison.OrdinalIgnoreCase) || filename.StartsWith("BLUP", StringComparison.OrdinalIgnoreCase)) return true;
                     else return false;
                 })))
        {
            Logger.LogInformation("  ERROR not all input files are of a known processClass, aborting");
        }
        else
        {
            Crc16 crcGen = new Crc16();

            // always use ascending order in file names, when input file are SWFL name order specifies to-Flash order
            inFiles.Sort();

            for (int i = 0; i < inFiles.Count; i++) // parse input files, create flashblocks
            {
                string inFile = inFiles[i];
                FlashBlock flashBlock;

                byte[] fileBytes;

                try  // try to open the binary file for reading
                {
                    fileBytes = File.ReadAllBytes(inFile);
                }
                catch (Exception ex)
                {
                    Logger.LogInformation($"  Cannot open '{inFile}' for reading, SWFL flashdata import aborted. Exception: \"{ex}\"");
                    Logger.LogError(ex, "Error");
                    return new List<FlashBlock>();
                }

                Logger.LogInformation("  opening SWFL binfile for reading '{0}'", inFile);
                Logger.LogInformation("  reading {0} bytes from file to memory buffer", fileBytes.Length);

                flashBlock = ParseSwflXmlFlashBlock(inFile.Replace("bin", "xml"));

                // CRC16 check against reference to check file validity (we cannot check NRV compressed for sanity beforehand otherwise)
                for (int t = 0; t < flashBlock.flashSegments.Count; t++)
                {
                    FlashSegment flashSegment = flashBlock.flashSegments[t];
                    ushort computedCrc16 = crcGen.ComputeChecksum(fileBytes, (int)flashSegment.sourceStartAddress, (int)flashSegment.sourceLength);
                    if (flashSegment.checksum != computedCrc16)
                    {
                        Logger.LogInformation("  ERROR Invalid CRC16 on {0} segment {1} calcCRC 0x{2:X04} refCRC 0x{3:X04}, aborting", flashBlock.SweVersion.SweArt, t, computedCrc16, flashSegment.checksum);
                        return new List<FlashBlock>();
                    }
                }

                flashBlock.flashData = fileBytes;

                // set bit in segmentsTouched of processed xml section so we can verify if we have data for all sections at the end
                segmentsTouched |= ((byte)flashBlock.SweVersion.SweArt);

                flashBlocks.Add(flashBlock);
            }

            if (writecommandOption != (int)WriteCommandOption.AUTO)
            {
                if ((segmentsTouched & (int)SweArtType.UNKNOWN) > 0)
                {
                    Logger.LogInformation("  ERROR could not determine exact flashblock type for all flashblocks, cannot proceed in -w {0}, try Auto, aborting", writecommandOption);
                    return new List<FlashBlock>();
                }

                if ((segmentsTouched & writecommandOption) != writecommandOption)
                {
                    Logger.LogInformation("  ERROR inconsistency between write actions and input files, aborting");
                    return new List<FlashBlock>();
                }
            }
        }
        return flashBlocks;
    }

    private static FlashBlock ParseSwflXmlFlashBlock(string xmlFile)
    {
        FlashBlock flashBlock = new FlashBlock
        {
            flashSegments = new List<FlashSegment>(),
            SweVersion = new SweVersion(),
        };

        XmlDocument doc = new XmlDocument();

        try  // try to open the XML file for reading
        {
            doc.Load(xmlFile);
        }
        catch (Exception ex)
        {
            Logger.LogInformation($"  Cannot open '{xmlFile}' for reading, SWFL XML import aborted. Exception: \"{ex}\"");
            Logger.LogError(ex, "Error");
            return flashBlock;
        }

        Logger.LogInformation("  parsing SWFL context XML from '{0}'", xmlFile);

        var processClass = string.Empty;
        var identifier = string.Empty;
        var version = string.Empty;
        XmlNodeList childs = doc.GetElementsByTagName("SD");
        for (int t = 0; t < childs.Count; t++)
        {
            if (childs[t].Attributes != null && childs[t].Attributes["SI"] != null)
            {
                if (childs[t].Attributes["SI"].Value == "ProcessClass")
                {
                    processClass = childs[t].InnerText; // swfk
                }
                if (childs[t].Attributes["SI"].Value == "Identifier")
                {
                    identifier = childs[t].InnerText; // 00003430
                }
                if (childs[t].Attributes["SI"].Value == "Version")
                {
                    version = childs[t].InnerText; // 080_021_221
                }
                var name = processClass + "-" + identifier + "-" + version.Replace("_", "."); // swfk-00003430-080.021.221
                flashBlock.SweVersion = new SweVersion(name);
            }
        }

        if (!flashBlock.SweVersion.IsEmpty)
        {
            XmlNodeList elemList = doc.GetElementsByTagName("DESC");
            for (int i = 0; i < elemList.Count; i++)
            {
                flashBlock.SweVersion.References = ParseReferenceSweInfo(elemList[i].OuterXml);
                flashBlock.SweVersion.SweArt = ParseSweArtFromXml(elemList[i].OuterXml);
            }
        }

        // give sweArt determine another chance if we could not find it before
        if (flashBlock.SweVersion.SweArt == SweArtType.UNKNOWN)
        {
            flashBlock.SweVersion.SweArt = flashBlock.SweVersion.ParseSweArtFromProcessClass();
        }

        childs = doc.GetElementsByTagName("SDG");
        for (int t = 0; t < childs.Count; t++)
        {
            if (childs[t]["SDG-CAPTION"]["SHORT-NAME"].InnerText == "SWEDevelopmentInfoFieldAddress")
            {
                flashBlock.SWEDevelopmentInfoFieldAddress = UInt32.Parse(childs[t]["SD"].InnerText, System.Globalization.NumberStyles.HexNumber);
            }

            if (childs[t]["SDG-CAPTION"]["SHORT-NAME"].InnerText == "SWEDescriptionTableStartAddress")
            {
                flashBlock.SWEDescriptionTableStartAddress = UInt32.Parse(childs[t]["SD"].InnerText, System.Globalization.NumberStyles.HexNumber);
            }
        }

        childs = doc.GetElementsByTagName("FLASH-SEGMENT");
        for (int t = 0; t < childs.Count; t++)
        {
            FlashSegment flashSegment = new FlashSegment
            {
                compressed = (CompressionStatus)Enum.Parse(typeof(CompressionStatus), ((XmlElement)childs[t]).GetAttributeNode("COMPRESSION-STATUS").InnerText),
                nameShort = childs[t]["SHORT-NAME"].InnerText,
                sourceStartAddress = UInt32.Parse(childs[t]["SOURCE-START-ADDRESS"].InnerText, System.Globalization.NumberStyles.HexNumber)
            };
            flashSegment.sourceLength = UInt32.Parse(childs[t]["SOURCE-END-ADDRESS"].InnerText, System.Globalization.NumberStyles.HexNumber) - flashSegment.sourceStartAddress + 1;
            flashSegment.targetStartAddress = UInt32.Parse(childs[t]["TARGET-START-ADDRESS"].InnerText, System.Globalization.NumberStyles.HexNumber);
            flashSegment.targetLength = UInt32.Parse(childs[t]["TARGET-END-ADDRESS"].InnerText, System.Globalization.NumberStyles.HexNumber) - flashSegment.targetStartAddress + 1;

            if (flashSegment.compressed == CompressionStatus.COMPRESSED) flashSegment.compressionMethod = (CompressionMethod)Enum.Parse(typeof(CompressionMethod), childs[t]["COMPRESSION-METHOD"].InnerText);
            else flashSegment.compressionMethod = CompressionMethod.NONE;

            string checksumString = childs[t]["CHECKSUM"].InnerText;
            if (checksumString.Length > 0) flashSegment.checksum = UInt16.Parse(checksumString, System.Globalization.NumberStyles.HexNumber);
            else flashSegment.checksum = 0x0;

            flashBlock.flashSegments.Add(flashSegment);
        }
        return flashBlock;
    }

    private static List<FlashBlock> GenerateFlashBlocksRawBin(int writecommandOption, List<string> inFiles, int SGBDIndex, int fullBinSize, ModuleInfo moduleInfo)
    {
        var flashBlocks = new List<FlashBlock>();

        if (inFiles.Count == 0 || (inFiles.Count > 0 && string.IsNullOrWhiteSpace(inFiles[0])))
        {
            Logger.LogInformation("  ERROR Invalid input file name specified for RAW binary uncompressed flash, aborting");
        }
        else if (inFiles.Count > 1)
        {
            Logger.LogInformation("  ERROR RAW binary uncompressed flash mode requires only a single input file, not ({0}) files. aborting", inFiles.Count);
        }
        else if (!(inFiles.All(b => File.Exists(b) || PreloadedInFiles.ContainsKey(b))))
        {
            Logger.LogInformation("  ERROR input file '{0}' does not exist, aborting", inFiles[0]);
        }
        else
        {
            string inFile = inFiles[0];
            byte[] fileBytes = Array.Empty<byte>();
            bool firstBlock = true;

            try  // try to open the binary file for reading
            {
                fileBytes = File.ReadAllBytes(inFile);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error");
                if (PreloadedInFiles.ContainsKey(inFile))
                {
                    fileBytes = PreloadedInFiles[inFile];
                }
                else
                {
                    Logger.LogInformation($"  Cannot open '{inFile}' for reading, RAW binary uncompressed flashdata import aborted. Exception: \"{ex}\"");
                    return new List<FlashBlock>();
                }
            }

            if (fileBytes.Length != fullBinSize)
            {
                Logger.LogInformation("  ERROR '{0}' is an odd size ({1} bytes instead of {2} bytes). Are you sure this is a full dump ? Aborting", inFile, fileBytes.Length, fullBinSize);
                return new List<FlashBlock>();
            }

            Logger.LogInformation("  opening RAW uncompressed binfile for reading '{0}'", inFile);
            Logger.LogInformation("  reading {0} bytes from file to memory buffer", fileBytes.Length);

            while (fileBytes.Length > 0 && (writecommandOption & (int)WriteCommandOption.BTL_HWL_PST_DST) > 0)
            {
                var flashBlock = new FlashBlock
                {
                    flashSegments = new List<FlashSegment>(),
                    SweVersion = new SweVersion()
                };
                var targetSweArt = SweArtType.UNKNOWN;

                if (IsPpcArchitecture(moduleInfo))
                {
                    if ((writecommandOption & (byte)SweArtType.BTL) == (byte)SweArtType.BTL) // BTLD
                    {
                        targetSweArt = SweArtType.BTL;

#if BOSCH_MG1_UNLOCK_BYPASS
                        if (BoschMG1RsaBypassBootloaderFirstUpload == true)
                        {
                            // generate special patched bootloader segment for flashing to btld alt location; supplied bin needs to be full stock
                            // written to btld alt location 0x09680100 to 0x097004FF
                            flashBlock.SWEDescriptionTableStartAddress = 0x903FD00;
                            flashBlock.SWEDevelopmentInfoFieldAddress = 0x903FE0C;
                            flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x680100, 0x7004FF, 0x09680100, 0x097004FF));

                            Logger.LogInformation("MG1 PREPARING BTLD ALT SEGMENT:");

                            Logger.LogInformation("  preparing special bootloader segment for bltd alt upload, supplied bin should be full stock");

                            Logger.LogInformation("  * clearing (fill with 0xff) section 0x{0:X08} to 0x{1:X08}", 0x680100, (0x680100 + 0x7ff00 - 1));

                            for (int t = 0; t < 0x7ff00; t++)
                            {
                                fileBytes[0x680100 + t] = 0xff;
                            }

                            Logger.LogInformation("  * copying btld snippet from 0x{0:X08} to 0x{1:X08} to section 0x{2:X08} to 0x{3:X08}", 0, 0x4ff, 0x700000, 0x700000 + 0x4ff);

                            for (int t = 0; t < 0x500; t++)
                            {
                                fileBytes[0x700000 + t] = fileBytes[0 + t];
                            }

                            byte[] btld_RSA_struct_orig = { 0x00, 0x00, 0x8B, 0x6C, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x01, 0x09, 0x00, 0x08, 0x14 };
                            byte[] btld_RSA_struct_patched = { 0x00, 0x00, 0x8B, 0x6C, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x01, 0x09, 0x00, 0x08, 0x04 };
                            int matchLocation = -1;

                            Logger.LogInformation("  * searching for btld_RSA_struct_orig match in range 0x{0:X08} to 0x{1:X08}", 0x700000, 0x700000 + 0x4ff);

                            for (int t = 0; t < 0x500; t++)
                            {
                                bool fullmatch = true;
                                for (int z = 0; z < btld_RSA_struct_orig.Length; z++)
                                {
                                    if (fileBytes[0x700000 + t + z] != btld_RSA_struct_orig[z]) fullmatch = false;
                                }

                                if (fullmatch == true)
                                {
                                    matchLocation = t; break;
                                }
                            }

                            if (matchLocation != -1) Logger.LogInformation(" match found @ 0x{0:X08}", 0x700000 + matchLocation);
                            else
                            {
                                Logger.LogInformation(" no match found, aborting!"); return new List<FlashBlock>();
                            }

                            Logger.LogInformation("  * patching btld_RSA_struct_orig @ 0x{0:X08} with btld_RSA_struct_patched data, len {1} bytes, done!", 0x700000 + matchLocation, btld_RSA_struct_patched.Length);

                            for (int t = 0; t < btld_RSA_struct_patched.Length; t++)
                            {
                                fileBytes[0x700000 + matchLocation + t] = btld_RSA_struct_patched[t];
                            }
                        }
                        else // normal btld upload
#endif
                        {
                            flashBlock.SWEDescriptionTableStartAddress = 0x903FD00;
                            flashBlock.SWEDevelopmentInfoFieldAddress = 0x903FE0C;
                            if (IsNewBtldSecurity(SecurityWave))
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x100, 0x3FFDF, 0x09000100, 0x0903FFDF)); // same as low security?
                            }
                            else
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x100, 0x3FFDF, 0x09000100, 0x0903FFDF));
                            }
                        }
                    }
                    else if ((writecommandOption & (byte)SweArtType.PST) == (byte)SweArtType.PST) // PST
                    {
                        targetSweArt = SweArtType.PST;
                        flashBlock.SWEDescriptionTableStartAddress = 0x967FD00;
                        flashBlock.SWEDevelopmentInfoFieldAddress = 0x967FE0C;
#if BOSCH_MG1_UNLOCK_BYPASS
                        if (BoschMG1RsaBypassBootloaderFirstUpload == true || BoschMG1RsaPatchedBootloaderPresent == true)
                        {
                            if (IsNewBtldSecurity(SecurityWave) || IsThirdPartyUnlocked(moduleInfo.unlockCompany))
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x40100, 0x67FFDF, 0x09040100, 0x0967FFDF));
                            }
                            else
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x40100, 0x67FFFF, 0x09040100, 0x0967FFFF));
                            }
                        }
                        else // normal pst upload
#endif
                        {
                            flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x40100, 0x67FFDF, 0x09040100, 0x0967FFDF));
                        }
                    }
                    else // DST
                    {
                        targetSweArt = SweArtType.DST;
                        flashBlock.SWEDescriptionTableStartAddress = 0x977FD00;
                        flashBlock.SWEDevelopmentInfoFieldAddress = 0x977FE0C;
#if BOSCH_MG1_UNLOCK_BYPASS
                        if (BoschMG1RsaBypassBootloaderFirstUpload == true || BoschMG1RsaPatchedBootloaderPresent == true)
                        {
                            if (!ShouldWriteFeedaffeZone(moduleInfo.unlockCompany) && (IsNewBtldSecurity(SecurityWave) || IsThirdPartyUnlocked(moduleInfo.unlockCompany)))
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x680100, 0x77FFDF, 0x09680100, 0x0977FFDF));
                            }
                            else
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x680100, 0x77FFFF, 0x09680100, 0x0977FFFF));
                            }
                        }
                        else // normal dst upload
#endif
                        {
                            flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x680100, 0x77FFDF, 0x09680100, 0x0977FFDF));
                        }
                    }
                }
                else if (IsAurixArchitecture(moduleInfo))
                {
                    if ((writecommandOption & (byte)SweArtType.BTL) == (byte)SweArtType.BTL) // BTLD
                    {
                        targetSweArt = SweArtType.BTL;
#if BOSCH_MG1_UNLOCK_BYPASS
                        if (BoschMG1RsaBypassBootloaderFirstUpload == true)
                        {
                            // generate special patched bootloader segment for flashing to btld alt location; supplied bin needs to be full stock
                            flashBlock.SWEDescriptionTableStartAddress = 0x8005FD00;
                            flashBlock.SWEDevelopmentInfoFieldAddress = 0x8005FE0C;
                            flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x700100, 0x7808FF, 0x80700100, 0x807808FF));

                            Logger.LogInformation("MG1 PREPARING BTLD ALT SEGMENT:");

                            Logger.LogInformation("  preparing special bootloader segment for bltd alt upload, supplied bin should be full stock");

                            Logger.LogInformation("  * clearing (fill with 0x00) section 0x{0:X08} to 0x{1:X08}", 0x700100, (0x700100 + 0x7FF00 - 1));

                            for (int t = 0; t < 0x7FF00; t++)
                            {
                                fileBytes[0x700100 + t] = 0x00;
                            }
                            Logger.LogInformation("  * copying btld snippet from 0x{0:X08} to 0x{1:X08} to section 0x{2:X08} to 0x{3:X08}", 0x28000, 0x288ff, 0x780000, 0x780000 + 0x8FF);

                            for (int t = 0; t < 0x900; t++)
                            {
                                fileBytes[0x780000 + t] = fileBytes[0x28000 + t];
                            }

                            byte[] btld_RSA_struct_orig = { 0x00, 0xFC, 0x67, 0x00, 0x00, 0x01, 0x70, 0x80, 0x00, 0xFC, 0x0F, 0x00, 0x00, 0x81, 0x02, 0x80 };
                            byte[] btld_RSA_struct_patched = { 0x00, 0xFC, 0x67, 0x00, 0x00, 0x01, 0x70, 0x80, 0x00, 0xFC, 0x0F, 0x00, 0x00, 0x81, 0x0A, 0x80 };
                            int matchLocation = -1;

                            Logger.LogInformation("  * searching for btld_RSA_struct_orig match in range 0x{0:X08} to 0x{1:X08}", 0x780000, 0x780000 + 0x8FF);

                            for (int t = 0; t < 0x900; t++)
                            {
                                bool fullmatch = true;
                                for (int z = 0; z < btld_RSA_struct_orig.Length; z++)
                                {
                                    if (fileBytes[0x780000 + t + z] != btld_RSA_struct_orig[z]) fullmatch = false;
                                }

                                if (fullmatch == true)
                                {
                                    matchLocation = t; break;
                                }
                            }

                            if (matchLocation != -1) Logger.LogInformation(" match found @ 0x{0:X08}", 0x780000 + matchLocation);
                            else
                            {
                                Logger.LogInformation(" no match found, aborting!"); return new List<FlashBlock>();
                            }

                            Logger.LogInformation("  * patching btld_RSA_struct_orig @ 0x{0:X08} with btld_RSA_struct_patched data, len {1} bytes, done!", 0x780000 + matchLocation, btld_RSA_struct_patched.Length);

                            for (int t = 0; t < btld_RSA_struct_patched.Length; t++)
                            {
                                fileBytes[0x780000 + matchLocation + t] = btld_RSA_struct_patched[t];
                            }
                        }
                        else // normal btld upload
#endif
                        {
                            flashBlock.SWEDescriptionTableStartAddress = 0x8005FD00;
                            flashBlock.SWEDevelopmentInfoFieldAddress = 0x8005FE0C;
                            if (IsNewBtldSecurity(SecurityWave))
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x28100, 0x5FFDF, 0x80028100, 0x8005FFDF));
                            }
                            else
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x28100, 0x5FFFF, 0x80028100, 0x8005FFFF));
                            }
                        }
                    }
                    else if ((writecommandOption & (byte)SweArtType.PST) == (byte)SweArtType.PST) // PST
                    {
                        targetSweArt = SweArtType.PST;
                        flashBlock.SWEDescriptionTableStartAddress = 0x806FFD00;
                        flashBlock.SWEDevelopmentInfoFieldAddress = 0x806FFE0C;
#if BOSCH_MG1_UNLOCK_BYPASS
                        if (BoschMG1RsaBypassBootloaderFirstUpload == true || BoschMG1RsaPatchedBootloaderPresent == true)
                        {
                            if (IsNewBtldSecurity(SecurityWave) || IsThirdPartyUnlocked(moduleInfo.unlockCompany))
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x80100, 0x6FFFDF, 0x80080100, 0x806FFFDF));
                            }
                            else
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x80100, 0x6FFFFF, 0x80080100, 0x806FFFFF));
                            }
                        }
                        else // normal pst upload
#endif
                        {
                            flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x80100, 0x6FFFDF, 0x80080100, 0x806FFFDF));
                        }
                    }
                    else // DST
                    {
                        targetSweArt = SweArtType.DST;
                        flashBlock.SWEDescriptionTableStartAddress = 0x807FFD00;
                        flashBlock.SWEDevelopmentInfoFieldAddress = 0x807FFE0C;
#if BOSCH_MG1_UNLOCK_BYPASS
                        if (BoschMG1RsaBypassBootloaderFirstUpload == true || BoschMG1RsaPatchedBootloaderPresent == true)
                        {
                            if (!ShouldWriteFeedaffeZone(moduleInfo.unlockCompany) && (IsNewBtldSecurity(SecurityWave) || IsThirdPartyUnlocked(moduleInfo.unlockCompany)))
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x700100, 0x7FFFDF, 0x80700100, 0x807FFFDF));
                            }
                            else
                            {
                                flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x700100, 0x7FFFFF, 0x80700100, 0x807FFFFF));
                            }
                        }
                        else // normal dst upload
#endif
                        {
                            flashBlock.flashSegments.Add(GenerateFlashSegmentRawBin(0x700100, 0x7FFFDF, 0x80700100, 0x807FFFDF));
                        }
                    }
                }
                else
                {
                    Logger.LogInformation("  ERROR RAW binary uncompressed flash mode not supported for processor type {0}, aborting", moduleInfo.ProcessorArchitectureType); return new List<FlashBlock>();
                }
                Logger.LogInformation("  flash Blocks prepared");

                string segmentName = GetSegmentName(targetSweArt);

                // check if opened bin file covers source ranges requested for all segments in this flash-block
                for (int i = 0; i < flashBlock.flashSegments.Count; i++)
                {
                    uint sourceSegmentBinEnd = flashBlock.flashSegments[i].sourceStartAddress + flashBlock.flashSegments[i].sourceLength;
                    if (fileBytes.Length < sourceSegmentBinEnd)
                    {
                        Logger.LogInformation("  ERROR {0} source binary length 0x{1:X06} is smaller than requested segment end 0x{2:X06}, aborting",
                            segmentName, fileBytes.Length, sourceSegmentBinEnd);
                        return new List<FlashBlock>();
                    }
                }

                int lastflashSegmentsIdx = flashBlock.flashSegments.Count() - 1;
                uint flashBlockOffsetSrcToTarget = flashBlock.flashSegments[lastflashSegmentsIdx].targetStartAddress - flashBlock.flashSegments[lastflashSegmentsIdx].sourceStartAddress;

                if (flashBlock.SWEDescriptionTableStartAddress != 0xFFFFFFFF)
                {
                    var sweVersion = new SweVersion(StringFunctions.ExtractSectionArray(fileBytes, (int)(flashBlock.SWEDescriptionTableStartAddress - flashBlockOffsetSrcToTarget), 8)) { SweArt = targetSweArt };
                    if (sweVersion.GetXweString().StartsWith("ERASED") || sweVersion.IsEmpty)
                    {
                        Logger.LogInformation("  ERROR {0} SWEDescriptionTable appears to be empty, aborting", segmentName);
                        return new List<FlashBlock>();
                    }
                    flashBlock.SweVersion = sweVersion;
                }

                if (flashBlock.SWEDevelopmentInfoFieldAddress != 0xFFFFFFFF)
                {
                    flashBlock.SweVersion.References = ParseReferenceSweInfo(fileBytes, (flashBlock.SWEDevelopmentInfoFieldAddress - flashBlockOffsetSrcToTarget));
                }
                else
                {
                    flashBlock.SweVersion.References = new List<SweVersion>();
                }

                if (NrvCompressSegments) // NRV compress ALL segments in flashBlock to facilitate fast download
                {
                    byte[] compressed_section_total = Array.Empty<byte>();

                    for (int t = 0; t < flashBlock.flashSegments.Count; t++)
                    {
                        FlashSegment FlashSegmentUpdate = flashBlock.flashSegments[t];
                        if (Ucl_Nrv.NRV_compress(fileBytes, (int)FlashSegmentUpdate.sourceStartAddress, (int)FlashSegmentUpdate.sourceLength, out byte[] compressed_section))
                        { // compression successfull, update segment data
                            double deflatePercent = 100 - (((double)compressed_section.Length / (double)FlashSegmentUpdate.sourceLength) * 100);
                            if (firstBlock) Logger.LogInformation("PERFORMING NRV COMPRESSION ON SEGMENTS:");
                            Logger.LogInformation("  {0,-4} 0x{1:X08} segment NRV compression OK, reduced from size 0x{2:X06} to size 0x{3:X06}, deflated by {4:F2} %",
                                segmentName, FlashSegmentUpdate.targetStartAddress, FlashSegmentUpdate.sourceLength, compressed_section.Length, deflatePercent);
                            FlashSegmentUpdate.sourceStartAddress = (uint)compressed_section_total.Length;
                            FlashSegmentUpdate.sourceLength = (uint)compressed_section.Length;
                            FlashSegmentUpdate.compressed = CompressionStatus.COMPRESSED;
                            FlashSegmentUpdate.compressionMethod = CompressionMethod.NRV;
                            flashBlock.flashSegments[t] = FlashSegmentUpdate;
                            compressed_section_total = Combine(compressed_section_total, compressed_section);
                            firstBlock = false;
                        }
                        else // compress failed, abort
                        {
                            return new List<FlashBlock>();
                        }
                    }
                    flashBlock.flashData = compressed_section_total;
                }
                else
                {
                    flashBlock.flashData = fileBytes;
                }
                // add flashBlock to list
                Logger.LogInformation(" adding flashBlock to list");
                flashBlocks.Add(flashBlock);
                writecommandOption &= ~((byte)targetSweArt);
            }
        }
        return flashBlocks;
    }

    private static FlashSegment GenerateFlashSegmentRawBin(uint startAddress, uint endAddress)
    {
        FlashSegment flashSegment = new FlashSegment
        {
            compressed = CompressionStatus.UNCOMPRESSED,
            targetStartAddress = startAddress
        };
        flashSegment.targetLength = endAddress - flashSegment.targetStartAddress + 1;
        flashSegment.sourceStartAddress = flashSegment.targetStartAddress;
        flashSegment.sourceLength = flashSegment.targetLength;
        flashSegment.compressionMethod = CompressionMethod.NONE;
        return flashSegment;
    }

    private static FlashSegment GenerateFlashSegmentRawBin(uint sourceStartAddress, uint sourceEndAddress, uint targetStartAddress, uint targetEndAddress)
    {
        FlashSegment flashSegment = new FlashSegment
        {
            compressed = CompressionStatus.UNCOMPRESSED,
            targetStartAddress = targetStartAddress
        };
        flashSegment.targetLength = targetEndAddress - flashSegment.targetStartAddress + 1;
        flashSegment.sourceStartAddress = sourceStartAddress;
        flashSegment.sourceLength = sourceEndAddress - flashSegment.sourceStartAddress + 1;
        flashSegment.compressionMethod = CompressionMethod.NONE;
        return flashSegment;
    }

    private static List<SweVersion> ParseReferenceSweInfo(string xmlDesc)
    {
        var referenceSweInfo = new List<SweVersion>();

        int start, at, end, count;

        end = xmlDesc.Length;
        start = 0;
        at = 0;
        while ((start <= end) && (at > -1))
        {
            count = end - start;
            at = xmlDesc.IndexOf("$DIF_Ref", start, count);
            if (at == -1) break;
            var referenceSwe = xmlDesc.Substring(at + "$DIF_Ref".Length + 3, 25);
            referenceSweInfo.Add(new SweVersion(referenceSwe));
            start = at + 1;
        }
        return referenceSweInfo;
    }

    private static SweArtType ParseSweArtFromXml(string xmlDesc)
    {
        int at = xmlDesc.IndexOf("$DIF_SweArt: ");
        if (at == -1) return SweArtType.UNKNOWN; // not found
        string sweArt = xmlDesc.Substring(at + "$DIF_SweArt: ".Length, 3);
        if (!(Enum.IsDefined(typeof(SweArtType), sweArt))) return SweArtType.UNKNOWN; // found, but not in SweArt Enum
        return (SweArtType)Enum.Parse(typeof(SweArtType), sweArt);
    }

    private static List<SweVersion> ParseReferenceSweInfo(byte[] fileBytes, uint SWEDevelopmentInfoFieldAddress)
    {
        var referenceSweInfo = new List<SweVersion>();

        int infoheader_n = fileBytes[SWEDevelopmentInfoFieldAddress + 1];
        for (int t = 0; t < infoheader_n; t++)
        {
            var referenceSweInfoElement = new SweVersion(StringFunctions.ExtractSectionArray(fileBytes, (int)(SWEDevelopmentInfoFieldAddress + 2 + t * 8), 8));
            referenceSweInfo.Add(referenceSweInfoElement);
        }
        return referenceSweInfo;
    }

    public static bool FetchCodingRecords(ushort[] recordIds, out List<CodingRecord> CodingDataset)
    {
        CodingDataset = new List<CodingRecord>();

        UDS_BMW.UDS_ErrorCodes cmdStatus = UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE;

        for (int i = 0; i < recordIds.Length; i++)
        {
            cmdStatus = _UDSDiagIf.ReadDataByIdentifier(recordIds[i], out byte[] recordData);
            if (cmdStatus != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogError("Could not read codingRecord with id 0x{0:X04} from module, exiting", (ushort)recordIds[i]);
                return false;
            }
            else
            {
                CodingRecord codingRecord = new CodingRecord
                {
                    index = (ushort)recordIds[i],
                    data = recordData,
                    size = (ushort)recordData.Length
                };
                CodingDataset.Add(codingRecord);
            }
        }
        return true;
    }

    public static bool FetchCodingRecordsBruteForce(out List<CodingRecord> CodingDataset)
    {
        CodingDataset = new List<CodingRecord>();

        UDS_BMW.UDS_ErrorCodes cmdStatus = UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE;

        for (ushort i = (ushort)UDS_BMW.UDS_RecordIdentifier.CodierDatenIdxFirst; i <= (ushort)UDS_BMW.UDS_RecordIdentifier.Codierpruefstempel; i++)
        {
            uint responseTesterId = 0;
            uint responseModuleId = 0;
            cmdStatus = _UDSDiagIf.ReadDataByIdentifier((UDS_BMW.UDS_RecordIdentifier)i, out byte[] recordData, ref responseTesterId, ref responseModuleId);
            if (cmdStatus == UDS_BMW.UDS_ErrorCodes.UDS_NRC_0031)
            {
                // requestOutOfRange, no problem; proceed to next
            }
            else if (cmdStatus != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                Logger.LogError("Could not read codingRecord with id 0x{0:X04} from module, exiting", i);
                return false;
            }
            else
            {
                CodingRecord codingRecord = new CodingRecord
                {
                    index = (ushort)i,
                    data = recordData,
                    size = (ushort)recordData.Length
                };
                CodingDataset.Add(codingRecord);

                if (IsValidCodingSignature(codingRecord)) break; // when we have the signature, we have the last record -> break brute force loop
            }
        }

        return true;
    }

    public static byte[] Combine(byte[] first, byte[] second)
    {
        byte[] ret = new byte[first.Length + second.Length];
        Buffer.BlockCopy(first, 0, ret, 0, first.Length);
        Buffer.BlockCopy(second, 0, ret, first.Length, second.Length);
        return ret;
    }

    public static bool IsValidCodingSignature(CodingRecord codingRecord)
    {
        bool codingSignatureMatch = false;
        if (codingRecord.size == 132 &&
            (codingRecord.data.All(b => b == 0) || codingRecord.data.All(b => b == 0xFF) ||
             (codingRecord.data[0] == 0 && codingRecord.data[1] == 0 && codingRecord.data[2] == 0 && codingRecord.data[3] == 0x20))) codingSignatureMatch = true;
        return codingSignatureMatch;
    }

    public static bool PrepareCheckSignatureCodingRecords(ref List<CodingRecord> CodingDataset, byte[] btldId, bool correctSignature)
    {
        CodingSignature_UDS codingSigCalulatorUDS = new CodingSignature_UDS();

        byte[] codingDataArray = Array.Empty<byte>();
        int signatureCodingRecordIndex = -1;

        for (int i = 0; i < CodingDataset.Count; i++)
        {
            if (CodingDataset[i].index != (ushort)UDS_BMW.UDS_RecordIdentifier.Codierpruefstempel && CodingDataset[i].size != 132) // add up all data but pruefstamp and the signature itself
            {
                codingDataArray = Combine(codingDataArray, CodingDataset[i].data);
            }
            else if (IsValidCodingSignature(CodingDataset[i])) // found the coding record
            {
                signatureCodingRecordIndex = i;
            }
        }

        if (signatureCodingRecordIndex == -1) // we could find the signature record, abort mission
        {
            Logger.LogInformation("  Could not find the coding signature record in list, signature NOT corrected");
        }
        else
        {
            byte[] codingSignature = codingSigCalulatorUDS.ComputeCodingSignatureBigIntegerModPow(btldId, codingDataArray);

            if (codingSignature.Length != 132) // error no new signature created, not replaced; output in siggen routine
            {
                return false;
            }
            else // valid coding signature created with proper length
            {
                if (correctSignature == true) // correct signature
                {
                    Logger.LogInformation("  Correcting coding signature -> OK");

                    CodingRecord signatureRecord = CodingDataset[signatureCodingRecordIndex];
                    signatureRecord.data = codingSignature;
                    CodingDataset[signatureCodingRecordIndex] = signatureRecord;
                }
                else // verify signature
                {
                    if (CodingDataset[signatureCodingRecordIndex].data.All(b => b == 0) || CodingDataset[signatureCodingRecordIndex].data.All(b => b == 0xFF))
                    {
                        Logger.LogInformation("  Coding signature EMPTY, cannot verify, recalculate it before writeback");
                    }
                    else
                    {
                        bool signatureMatch = true;
                        for (int i = 0; i < codingSignature.Length; i++) if (CodingDataset[signatureCodingRecordIndex].data[i] != codingSignature[i]) signatureMatch = false;
                        Logger.LogInformation("  Verifying coding signature -> {0}", (signatureMatch ? "OK" : "Incorrect"));
                        return signatureMatch;
                    }
                }
            }
        }
        return true;
    }

    public static UDS_BMW.UDS_ErrorCodes RestoreCodingRecords(List<CodingRecord> CodingDataset, string VIN, ushort[] timeLimitedCodingRecordIds)
    {
        // NEEDS coding Authorize before entry and valid coding dataset in CodingDataset
        // writes all coding data and coding signature
        // performs coding signature check
        // writes coding pfuefstempel
        UDS_BMW.UDS_ErrorCodes cmdStatus;

        // WRITE all coding data back to module, except Codierpruefstempel
        for (int i = 0; i < CodingDataset.Count; i++)
        {
            bool isTimeLimitedCodingRecordId = false;
            for (int t = 0; t < timeLimitedCodingRecordIds.Length; t++) if (timeLimitedCodingRecordIds[t] == CodingDataset[i].index) isTimeLimitedCodingRecordId = true;

            if (CodingDataset[i].index != (ushort)UDS_BMW.UDS_RecordIdentifier.Codierpruefstempel)
            {
                cmdStatus = _UDSDiagIf.WriteDataByIdentifier((UDS_BMW.UDS_RecordIdentifier)CodingDataset[i].index, CodingDataset[i].data);

                if (isTimeLimitedCodingRecordId && cmdStatus == UDS_BMW.UDS_ErrorCodes.UDS_NRC_0022) // time delay expired for coding record
                {
                    Logger.LogInformation("  writing time limited coding record with id 0x{0:X04} to module -> TIME DELAY EXPIRED, OK", CodingDataset[i].index);
                }
                else if (cmdStatus != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    Logger.LogInformation("  writing coding record with id 0x{0:X04} to module -> FAILED", CodingDataset[i].index);
                    return cmdStatus;
                }
                else
                {
                    Logger.LogInformation("  writing coding record with id 0x{0:X04} to module -> OK", CodingDataset[i].index);
                }
            }
        }

        // EXECUTE CODING SIGNATURE CHECK
        cmdStatus = _UDSDiagIf.RoutineControl(UDS_BMW.UDS_RoutineAction.startRoutine, UDS_BMW.UDS_RoutineIdentifier.CheckCodingSignature, out byte[] codingCheckResult);
        if (cmdStatus != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogInformation("  executing coding signature check -> FAILED");
            return cmdStatus;
        }
        else
        {
            if (codingCheckResult[0] == 0x00) Logger.LogInformation("  executing coding signature check -> OK");
            else // bad prog check result
            {
                Logger.LogInformation("  executing coding signature check -> Incorrect Signature");
                return UDS_BMW.UDS_ErrorCodes.UDS_NRC_0010; // general reject just to indicate NOT okay
            }
        }

        //  WRITE Codierpruefstempel -> 7 last bytes of VIN, not from backup but from CAR
        byte[] VinBytes = Encoding.ASCII.GetBytes(VIN);
        VinBytes = StringFunctions.ExtractSectionArray(VinBytes, VinBytes.Length - 7, 7);

        cmdStatus = _UDSDiagIf.WriteDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.Codierpruefstempel, VinBytes);
        if (cmdStatus != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogInformation("  writing last 7 bytes of vin to coding teststamp with id 0x{0:X04} to module -> FAILED", (ushort)UDS_BMW.UDS_RecordIdentifier.Codierpruefstempel);
            var shortVinBackupCodingRecords = CodingDataset.Where(x => x.index == (ushort)UDS_BMW.UDS_RecordIdentifier.Codierpruefstempel);
            if (shortVinBackupCodingRecords.Any() && shortVinBackupCodingRecords.FirstOrDefault().data.Length == 7)
            {
                Logger.LogInformation($"  short vin backup: [0x{string.Join(", 0x", shortVinBackupCodingRecords.FirstOrDefault().data.Select(x => x.ToString("X02")))}]");
                cmdStatus = _UDSDiagIf.WriteDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.Codierpruefstempel, shortVinBackupCodingRecords.FirstOrDefault().data);
                if (cmdStatus != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    Logger.LogInformation("  writing short vin backup to coding teststamp with id 0x{0:X04} to module -> FAILED", (ushort)UDS_BMW.UDS_RecordIdentifier.Codierpruefstempel);
                    return cmdStatus;
                }
                else
                {
                    Logger.LogInformation("  writing short vin backup to coding teststamp with id 0x{0:X04} to module -> OK", (ushort)UDS_BMW.UDS_RecordIdentifier.Codierpruefstempel);
                }
            }
            else
            {
                Logger.LogInformation("  No short vin backup for coding teststamp is available!");
                return UDS_BMW.UDS_ErrorCodes.UDS_TRANSPORT_ERR_SND_IF;
            }
        }
        else
        {
            Logger.LogInformation("  writing last 7 bytes of vin to coding teststamp with id 0x{0:X04} to module -> OK", (ushort)UDS_BMW.UDS_RecordIdentifier.Codierpruefstempel);
        }

        return cmdStatus;
    }

    public static UDS_BMW.UDS_ErrorCodes SwitchSessionSecurityAccessAuthenticate(UDS_BMW.UDS_DiagMode diagMode, byte[] btldId, ref ushort activeDiagSession)
    {
        UDS_BMW.UDS_ErrorCodes cmdStatus;
        UDS_BMW.UDS_SecurityAccessType SecurityAccesType;

        switch (diagMode)
        {
            case UDS_BMW.UDS_DiagMode.ECUCODE: SecurityAccesType = UDS_BMW.UDS_SecurityAccessType.Coding; break;
            case UDS_BMW.UDS_DiagMode.ECUPM:
                {
                    SecurityAccesType = UDS_BMW.UDS_SecurityAccessType.Prog;

                    // reset time with method B1 (ResponsePending, $78)
                    if (FTP_format == (ushort)Format_FTP.FP_4371 && resetTimeFormat == 1 && _UDSDiagIf.GetMaxExecutionTimeSeconds() < resetTime) _UDSDiagIf.SetMaxExecutionTimeSeconds(resetTime);
                    break;
                }
            default: SecurityAccesType = UDS_BMW.UDS_SecurityAccessType.None; break;
        }

        //
        // change DiagnosticSession to requested type, update module session state after
        // when transitioning to ECUPM, reset time-like cmd execution time is to be expected (adjusted above, depending on wait method used)
        //
        if ((cmdStatus = SetActiveDiagnosticSession(diagMode)) == UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            // idleUDSSleep reset time wait with waiting method (FP_5582/FP_5557) after switch to ECUPM command issued
            if (diagMode == UDS_BMW.UDS_DiagMode.ECUPM && (FTP_format != (ushort)Format_FTP.FP_4371 || FTP_format == (ushort)Format_FTP.FP_4371 && resetTimeFormat == 2))
            {
                _UDSDiagIf.IdleUDSSleepMs(resetTime * 1000);
            }

            if ((cmdStatus = FetchModuleActiveDiagnosticSession(ref activeDiagSession)) == UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
            {
                // perform securityAccessAuthenticate to fully unlock the session (if needed)
                if (SecurityAccesType != UDS_BMW.UDS_SecurityAccessType.None)
                {
                    if ((cmdStatus = SecurityAccessAuthenticate(SecurityAccesType, btldId)) == UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                    {
                        // request the new ActiveDiagnosticSession (after securityAccessAuthenticate change)
                        cmdStatus = FetchModuleActiveDiagnosticSession(ref activeDiagSession);
                    }
                }
            }
        }
        return cmdStatus;
    }

    private static UDS_BMW.UDS_ErrorCodes SetActiveDiagnosticSession(UDS_BMW.UDS_DiagMode diagMode)
    {
        UDS_BMW.UDS_ErrorCodes cmdStatus;
        ushort p2Max = 0;
        uint p2Star = 0;

        if ((cmdStatus = _UDSDiagIf.DiagnosticSessionControl(diagMode, ref p2Max, ref p2Star)) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogError("Could not set DiagnosticSessionControl {0} session, exiting", diagMode);
            return cmdStatus;
        }
        else
        {
            Logger.LogInformation("  set DiagnosticSessionControl {0} session, P2: {1}, P2*: {2}", diagMode, p2Max, p2Star);
            _UDSDiagIf.SetTimingParameters(p2Max, p2Star);
        }
        return cmdStatus;
    }

    public static UDS_BMW.UDS_ErrorCodes FetchModuleActiveDiagnosticSession(ref ushort activeDiagSession)
    {
        return _UDSDiagIf.FetchModuleActiveDiagnosticSession(ref activeDiagSession);
    }

    private static UDS_BMW.UDS_ErrorCodes ExecuteEcuHardReset(int activeResetTime, byte activeResetTimeFormat, UDS_BMW.UDS_ResetMode resetMode, UDS_BMW.ModuleIds moduleId)
    {
        UDS_BMW.UDS_ErrorCodes cmdStatus;

        // reset time with method B1 (ResponsePending, $78)
        if (FTP_format == (ushort)Format_FTP.FP_4371 && activeResetTimeFormat == 1 && _UDSDiagIf.GetMaxExecutionTimeSeconds() < activeResetTime) _UDSDiagIf.SetMaxExecutionTimeSeconds(activeResetTime);

        if ((cmdStatus = _UDSDiagIf.ECUReset(resetMode, moduleId)) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogError("Could not {0} module {1}, exiting", _UDSDiagIf.GetResetModeName(resetMode), _UDSDiagIf.GetModuleName(moduleId));
            return cmdStatus;
        }
        else
        {
            Logger.LogInformation("  Module {0}, {1}", _UDSDiagIf.GetModuleName(moduleId), _UDSDiagIf.GetResetModeName(resetMode));
        }

        // idleUDSSleep reset time with waiting method (FP_5582/FP_5557); afterwards we should have reverted to default session
        if (FTP_format != (ushort)Format_FTP.FP_4371 || FTP_format == (ushort)Format_FTP.FP_4371 && activeResetTimeFormat == 2) _UDSDiagIf.IdleUDSSleepMs(activeResetTime * 1000);

        return cmdStatus;
    }

    private static UDS_BMW.UDS_ErrorCodes SecurityAccessAuthenticate(UDS_BMW.UDS_SecurityAccessType type, byte[] btldId)
    {
        //
        // SEED/KEY 'Secret' Handshake
        //
        byte[] testerId = { 0xff, 0xff, 0xff, 0xff };

        UDS_BMW.UDS_ErrorCodes cmdStatus;

        if ((cmdStatus = _UDSDiagIf.SecurityAccessRequestSeed(type, testerId, out byte[] seed)) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogError(string.Format("Failed to complete SecurityAccess RequestSeed [{0}], exiting", type.ToString().ToUpper()));
            return cmdStatus;
        }
        else
        {
            Logger.LogInformation("  SecurityAccess RequestSeed; SEED [{0}] ({1}): {2}", type.ToString().ToUpper(), seed.Length, StringFunctions.ConvertBytesToHexString(seed));
        }

        KeyExchange_UDS keyExchangeUDS = new KeyExchange_UDS();

        byte[] acceskey = keyExchangeUDS.ComputeRSAKeyBigIntegerModPow(testerId, btldId, seed);

        cmdStatus = _UDSDiagIf.SecurityAccessSendKey(type, acceskey);
        if (cmdStatus == UDS_BMW.UDS_ErrorCodes.UDS_NRC_0035) // Invalid Key
        {
            Logger.LogInformation("  SecurityAccess SendKey [{0}] -> {1}", type.ToString().ToUpper(), _UDSDiagIf.GetErrorDescription(cmdStatus));
        }
        else if (cmdStatus != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE) // Other error
        {
            Logger.LogError(string.Format("Failed to complete SecurityAccess SendKey [{0}], exiting", type.ToString().ToUpper()));
        }
        else // All OK
        {
            Logger.LogInformation("  SecurityAccess SendKey [{0}] -> OK", type.ToString().ToUpper());
        }
        return cmdStatus;
    }

    private static UDS_BMW.UDS_ErrorCodes WriteFingerPrint(bool updateFingerPrint, byte[] currentFingerPrint)
    {
        byte[] fingerPrint;
        if (updateFingerPrint)
        {
            fingerPrint = new byte[] { 0x00, 0x00, 0x00, 0x00, 0x04, 0xD2, 0x01, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00 };
            byte[] dateBCD = GetLocalDateInBCD();

            dateBCD.CopyTo(fingerPrint, 0);
            fingerPrint[3] = 0x8F; // top nibble FPlen, bottom nibble tek
        }
        else
        {
            fingerPrint = currentFingerPrint;
        }

        Logger.LogInformation("  {0} FingerPrint: {1}", (updateFingerPrint ? "New " : "Keeping identical"), StringFunctions.ConvertBytesToHexString(fingerPrint));

        UDS_BMW.UDS_ErrorCodes cmdStatus;
        if ((cmdStatus = _UDSDiagIf.WriteDataByIdentifier(UDS_BMW.UDS_RecordIdentifier.FingerPrint, fingerPrint)) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogError("Could not WriteDataByIdentifier FingerPrint, exiting");
            return cmdStatus;
        }
        else Logger.LogInformation("  WriteDataByIdentifier FingerPrint -> OK");
        return cmdStatus;
    }

    public static byte ConvertToBcd(byte x)
    {
        int msb = x / 10;
        int lsb = x - (msb * 10);
        msb <<= 4;
        return (byte)(msb | lsb);
    }

    public static byte[] GetLocalDateInBCD()
    {
        DateTime now = DateTime.Now;
        byte[] Data = new byte[3];
        Data[0] = (byte)(now.Year % 100);
        Data[1] = (byte)now.Month;
        Data[2] = (byte)now.Day;
        for (int i = 0; i < 3; i++)
        {
            Data[i] = ConvertToBcd(Data[i]);
        }
        return Data;
    }

    public static byte[] GetLocalTimeInBCD()
    {
        DateTime now = DateTime.Now;
        byte[] Data = new byte[6];
        Data[0] = (byte)(now.Year % 100);
        Data[1] = (byte)now.Month;
        Data[2] = (byte)now.Day;
        Data[3] = (byte)now.Hour;
        Data[4] = (byte)now.Minute;
        Data[5] = (byte)now.Second;
        for (int i = 0; i < 6; i++)
        {
            Data[i] = ConvertToBcd(Data[i]);
        }
        return Data;
    }

    private static bool EraseFlashAddressed(uint eraseStartA, uint eraseLength, int FlashEraseTime)
    {
        Logger.LogInformation("  erasing addressed, start address " + string.Format("0x{0:X06}", eraseStartA) + " length " + string.Format("0x{0:X06}", eraseLength));

        _UDSDiagIf.SetMaxExecutionTimeSeconds(FlashEraseTime);
        if (_UDSDiagIf.StartRoutine_EraseMemoryAddressed(eraseStartA, eraseLength) == UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogInformation(" -> OK");
        }
        else
        { // some error, prolly wrong code, abort
            Logger.LogInformation(" -> failed");
            return true;
        }
        return false;
    }

    private static bool EraseFlashIndexed(uint SWEDescTableStartAddress, int FlashEraseTime)
    {
        Logger.LogInformation("  erasing indexed, SWEDescTableStartAddress " + string.Format("0x{0:X06}", SWEDescTableStartAddress));

        _UDSDiagIf.SetMaxExecutionTimeSeconds(FlashEraseTime);
        if (_UDSDiagIf.StartRoutine_EraseMemoryIndexed(SWEDescTableStartAddress) == UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogInformation(" -> OK");
        }
        else
        { // some error, prolly wrong code, abort
            Logger.LogInformation(" -> failed");
            return true;
        }
        return false;
    }

    private static uint ExecuteWriteSegments(byte[] flashData, List<FlashSegment> flashSegments, out TimeSpan downloadConcatTs)
    {
        uint bytes_written_total = 0;
        downloadConcatTs = TimeSpan.Zero;

        for (int i = 0; i < flashSegments.Count; i++) // execute flash plan
        {
            uint bytes_written_block = WriteFlashSequentialAutoResume(flashData, flashSegments[i], i + 1, flashSegments.Count, out TimeSpan downloadTs);
            bytes_written_total += bytes_written_block;
            downloadConcatTs += downloadTs;

            // some write error occured we could not recover from, abort remainder
            if (bytes_written_block != flashSegments[i].sourceLength) break;
        }
        return bytes_written_total;
    }

    private static bool CreateAndExecuteOptimizedFlashWriteSegments(byte[] flashData, List<FlashSegment> flashSegments)
    {
        uint total_skipped = 0;
        uint totalWriteLength = 0;

        for (int i = 0; i < flashSegments.Count; i++) totalWriteLength += flashSegments[i].sourceLength;

        Stopwatch stopWatch = new Stopwatch();
        stopWatch.Start();
        uint bytes_written_total = ExecuteWriteSegments(flashData, flashSegments, out _);

        stopWatch.Stop();
        PrintTransferStats(stopWatch.Elapsed, bytes_written_total, false);

        if (bytes_written_total > 0) bytes_written_total += total_skipped; // add the skipped size to total

        return (bytes_written_total == totalWriteLength);
    }

    private static byte BlockSequenceCntNext(byte blockSeqCnt)
    {
        blockSeqCnt++;
        if (blockSeqCnt > 255) blockSeqCnt = 1;
        return blockSeqCnt;
    }

    private const int writeFlashAutoResumeMaxRetryPerBlock = 5; // increasing higher than 5 with 1sec readTimeout will most likely case session timeout/drop, handle with care
#if DOWNLOAD_BACKSTEP_AUTOFAIL
        const int writeFlashAutoResumeMaxBackStepPerBlock = 10; // not actualy backstepping but resending with same BSC, backstepping only needed for 35UP ecu's
#endif

    private static uint WriteFlashSequentialAutoResume(byte[] flashData, FlashSegment flashSegment, int segment_current, int segments_max, out TimeSpan downloadTs)
    {
        uint block_bytes_completed = 0;
        uint block_retry_cnt = 0;

        byte block_seqcnt = 1; // first block is always 1

#if DOWNLOAD_BACKSTEP_AUTOFAIL
            Random rnd = new Random();
            int debugBackStepForceRandomEnabled = 0;
#endif

        Stopwatch stopWatch = new Stopwatch();
        downloadTs = TimeSpan.Zero;

        ushort writeLengthBlockInterfaceMax = (ushort)_UDSDiagIf.ProtocolMaxPayload;

        Logger.LogInformation("  flashing block #{0:D02} of {1:D02} ", segment_current, segments_max);
        Logger.LogInformation("source {0} 0x{1:X06}-0x{2:X06} to ", GetCompressionMethodName(flashSegment.compressionMethod), flashSegment.sourceStartAddress, flashSegment.sourceStartAddress + flashSegment.sourceLength - 1);
        Logger.LogInformation("target 0x{0:X06}-0x{1:X06} ", flashSegment.targetStartAddress, flashSegment.targetStartAddress + flashSegment.targetLength - 1);

        if (_UDSDiagIf.RequestDownload(flashSegment.targetStartAddress, flashSegment.targetLength, (flashSegment.compressed == CompressionStatus.COMPRESSED), out ushort writeLengthBlock) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogInformation(" requestDownload failed, NRC received; aborting");
            return block_bytes_completed;
        }
        else
        {
            Logger.LogInformation(" requestDownload succeeded!");
        }

        if (writeLengthBlock > writeLengthBlockInterfaceMax) // TAKE CARE: alignment needs to cover the payload only
        {
            int alignmentSize = 1;

            int alignmentAdjustSize = ((writeLengthBlockInterfaceMax - 2) % alignmentSize);
            writeLengthBlockInterfaceMax -= (ushort)alignmentAdjustSize;

            Logger.LogTrace("Maximum total write block length (incl. cmd overhead) limited from modules max transfer len " + writeLengthBlock + " bytes to " + writeLengthBlockInterfaceMax + " bytes due to interface/bus-specific and module specific alignment restrictions");
            writeLengthBlock = writeLengthBlockInterfaceMax;
        }
        else
        {
            Logger.LogTrace("Maximum total write block length (incl. cmd overhead) = modules max transfer len " + writeLengthBlock + " bytes");
        }

        // reduce writeLengthBlock by TransferData cmd and blocksequence size
        writeLengthBlock -= 2;

        // ?? let the framework handle the repeats (even for transferData) ?? -> YES but with custom RepeatCount, can be higher for UDS
        // because each block included the block_seqcnt, we can therefore just retransmit the same even on loosing response without overrunning
        // the backstep buffer beyond the point we cant backstep from, TODO ?? different backstep needed when transferDataTime != 0 ??
        _UDSDiagIf.SetRepeatCount(writeFlashAutoResumeMaxRetryPerBlock);

        stopWatch.Reset();
        stopWatch.Start();

        // we have work todo, execute:
        {
            while (block_bytes_completed != flashSegment.sourceLength)
            {
                ProgressUpdated?.Invoke(typeof(MgFlasherCore), (double)block_bytes_completed / flashSegment.sourceLength);
                ProgressSegmentBytesCompleted?.Invoke(typeof(MgFlasherCore), (int)block_bytes_completed);

                if ((flashSegment.sourceLength - block_bytes_completed) < writeLengthBlock) writeLengthBlock = (ushort)(flashSegment.sourceLength - block_bytes_completed);

                // extract subsection from bin file to local dataBlock buffer
                byte[] dataBlock = StringFunctions.ExtractSectionArray(flashData, (int)(flashSegment.sourceStartAddress + block_bytes_completed), (int)writeLengthBlock);

                UDS_BMW.UDS_ErrorCodes cmdStatus = _UDSDiagIf.TransferData(block_seqcnt, dataBlock, out byte sequenceCnt);
                if (cmdStatus == UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE && block_seqcnt == sequenceCnt) // ALL GOOD
                {
#if DOWNLOAD_BACKSTEP_AUTOFAIL
                        if ((rnd.Next(30) == 5) && (++debugBackStepForceRandomEnabled < writeFlashAutoResumeMaxBackStepPerBlock))
                        {
                            Logger.LogError("AUTOFAIL resending block #{0} transferData; Cnt {1}/{2}", block_seqcnt, debugBackStepForceRandomEnabled, writeFlashAutoResumeMaxBackStepPerBlock);
                            continue;
                        }
#endif
                    block_seqcnt = BlockSequenceCntNext(block_seqcnt);
                    block_bytes_completed += writeLengthBlock;
                }
                else // some error:
                {
                    if (cmdStatus >= UDS_BMW.UDS_ErrorCodes.UDS_TRANSPORT_ERR_SND_IF) // TRANSPORT ERROR or Timeout
                    {
                        // Transfer errors are auto recovered by the framework (or at least attempted), it we get here it failed on recover, abort
                        Logger.LogError("Aborting block #{0} transferData, Transport NRC '{1}' received", segment_current, _UDSDiagIf.GetErrorDescription(cmdStatus));
                        return block_bytes_completed;
                    }
                    else
                    {
                        if (block_seqcnt != sequenceCnt) // PROGRAM ERROR, try to recover with backstep (only on 35UP ecu's not implemented, aborting)
                        {
                            Logger.LogError("Aborting block #{0} transferData, PROGRAM ERROR", segment_current);
                            return block_bytes_completed;
                        }
                        else // some other NRC; can only recover from this by performing ERASE, abort
                        {
                            Logger.LogError("Aborting block #{0} transferData, NRC '{1}' received", segment_current, _UDSDiagIf.GetErrorDescription(cmdStatus));
                            return block_bytes_completed;
                        }
                    }
                }
            }

            ProgressUpdated?.Invoke(typeof(MgFlasherCore), (double)block_bytes_completed / flashSegment.sourceLength);
            ProgressSegmentBytesCompleted?.Invoke(typeof(MgFlasherCore), (int)block_bytes_completed);

            stopWatch.Stop();
            downloadTs += stopWatch.Elapsed;
        }

        _UDSDiagIf.ResetRepeatCount();

        if (_UDSDiagIf.RequestTransferExit() != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            // some error, might be some transmit error (but we failed to get a response RepeatCount times), abort mission
            Logger.LogInformation(" requestTransferExit failed, NRC received; aborting");
            return block_bytes_completed;
        }

        if (block_bytes_completed != flashSegment.sourceLength)  // we excited blocks loops prematurely, some programming error ?
        {
            Logger.LogInformation("incomplete transfer, transferred bytes 0x{0:X05}", block_bytes_completed);
        }
        else // all good! block written completely, done
        {
            Logger.LogInformation("-> OK{0}", (block_retry_cnt == 0) ? "" : string.Format(", retry_cnt: {0}", block_retry_cnt));
        }

        return block_bytes_completed;
    }

#endif

    private static void ShowHelp(MultiOptionSet p)
    {
        Logger.LogInformation("Usage: " + Path.GetFileNameWithoutExtension(AppDomain.CurrentDomain.FriendlyName) + " [OPTIONS]");
        Logger.LogInformation("");
        Logger.LogInformation("Options:");
        p.WriteOptionDescriptions();
    }

    public static string TrimRepeated(string str)
    {
        str = Regex.Replace(str, "(_){3,}", "$1", RegexOptions.Compiled);
        str = Regex.Replace(str, "( ){3,}", "$1", RegexOptions.Compiled);
        return str;
    }

    private static void ReadDifVariante(ref ModuleInfo moduleInfo)
    {
        if (moduleInfo.sweVersions is null)
        {
            var message = $"SVK_recordId is empty? not able to read Dif Variante";
            Logger.LogError(message);
            throw new Exception(message);
        }

        bool receivedHewl, receivedHwap, receivedBtld, receivedPst, receivedDst;
        receivedHewl = receivedHwap = receivedBtld = receivedPst = receivedDst = false;

        Logger.LogInformation("Reading Dif Variante from available modules...");
        foreach (var xweListItem in moduleInfo.sweVersions)
        {
            if (xweListItem != null)
            {
                var xweBytes = xweListItem.GetXweBytes();
                var xweListItemUpper = (String)xweListItem.GetXweString().ToUpper();
                var xweEmptyUpper = (String)("ERASED -> EMPTY EMPTY").ToUpper();

                if (xweListItemUpper.IndexOf(xweEmptyUpper, StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    Logger.LogInformation("Xwe string shows empty, not reading Dif Variante '{xweListItem}'.", xweListItem);
                }
                else
                {
                    // Check for programming index
                    if (xweBytes[0] == 0x01)
                    {
                        if (receivedHewl)
                        {
                            Logger.LogInformation("Already received xweBytes[0] == {xweBytes[0]}, continue", xweBytes[0]);
                            continue;
                        }
                        else
                        {
                            receivedHewl = true;
                        }
                    }
                    else if (xweBytes[0] == 0x02)
                    {
                        if (receivedHwap)
                        {
                            Logger.LogInformation("Already received xweBytes[0] == {xweBytes[0]}, continue", xweBytes[0]);
                            continue;
                        }
                        else
                        {
                            receivedHwap = true;
                        }
                    }
                    else if (xweBytes[0] == 0x06)
                    {
                        if (receivedBtld)
                        {
                            Logger.LogInformation("Already received xweBytes[0] == {xweBytes[0]}, continue", xweBytes[0]);
                            continue;
                        }
                        else
                        {
                            receivedBtld = true;
                        }
                    }
                    else if (xweBytes[0] == 0x08)
                    {
                        if (receivedPst)
                        {
                            Logger.LogInformation("Already received xweBytes[0] == {xweBytes[0]}, continue", xweBytes[0]);
                            continue;
                        }
                        else
                        {
                            receivedPst = true;
                        }
                    }
                    else if (xweBytes[0] == 0x0D)
                    {
                        if (receivedDst)
                        {
                            Logger.LogInformation("Already received xweBytes[0] == {xweBytes[0]}, continue", xweBytes[0]);
                            continue;
                        }
                        else
                        {
                            receivedDst = true;
                        }
                    }
                    else
                    {
                        Logger.LogInformation("string '{xweListItem}' doesn't have Dif Variante available to read", xweListItem);
                        continue;
                    }

                    // Read Dif Variante
                    if (MgFlasherCore._UDSDiagIf.RoutineControl(UDS_BMW.UDS_RoutineAction.startRoutine, UDS_BMW.UDS_RoutineIdentifier.ReadDevelopmentInfo, xweBytes, out byte[] difVarianteRaw) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
                    {
                        var message = $"string '{xweListItem}' doesn't have Dif Variante available to read";
                        Logger.LogError(message);
                        //throw new Exception(message);
                    }
                    else
                    {
                        var difVarianteParsed = new byte[difVarianteRaw.Length];
                        Array.Copy(difVarianteRaw, difVarianteParsed, difVarianteParsed.Length);

                        for (int j = 0; j < difVarianteParsed.Length; j++)
                        {
                            if (difVarianteParsed[j] < 0x20 || difVarianteParsed[j] > 0x7F)
                            {
                                //Logger.LogInformation("found invalid character '0x{0:X08}' at '0x{1:X08}', corrected to '_' (0x5F) ...", _difVariante[j], j);
                                difVarianteParsed[j] = 0x5F;
                            }
                        }
                        var _difVarianteString = Encoding.ASCII.GetString(difVarianteParsed, 0, difVarianteParsed.Length);

                        int index = _difVarianteString.IndexOf("#");
                        if (index > 0)
                        { // remove everything before first '#'
                            _difVarianteString = _difVarianteString[index..];
                        }
                        _difVarianteString = TrimRepeated(_difVarianteString);

                        //parse dif variante:
                        if (xweBytes[0] == 0x06)
                        {
                            if (_difVarianteString.IndexOf("ATAT", StringComparison.OrdinalIgnoreCase) >= 0
                                || MgFlasherCore.ByteArrayContains(difVarianteParsed, "ATAT"))
                            {
                                moduleInfo.unlockCompany = UnlockCompanyName.AutoTuner;
                                moduleInfo.unlocked = "AutoTuner";
                            }
                            else if (_difVarianteString.IndexOf("MGF_3PU", StringComparison.OrdinalIgnoreCase) >= 0
                                     || MgFlasherCore.ByteArrayContains(difVarianteParsed, "MGF_3PU"))
                            {
                                moduleInfo.unlockCompany = UnlockCompanyName.Unknown;
                                moduleInfo.unlocked = "PreviousThirdPartyUnlock";
                            }
                            if (_difVarianteString.IndexOf("#TAS#MGF", StringComparison.OrdinalIgnoreCase) >= 0
                                     || MgFlasherCore.ByteArrayContains(difVarianteParsed, "#TAS#MGF"))
                            {
                                moduleInfo.unlockCompany = UnlockCompanyName.TAS;
                                moduleInfo.unlocked = "TAS";
                            }
                            if (_difVarianteString.IndexOf("1769 Performance & KM Engineers", StringComparison.OrdinalIgnoreCase) >= 0
                                || MgFlasherCore.ByteArrayContains(difVarianteParsed, "1769 Performance & KM Engineers"))
                            {
                                moduleInfo.unlockCompany = UnlockCompanyName._1769;
                                moduleInfo.unlocked = "1769";
                            }
                            if (_difVarianteString.IndexOf("FEMTO", StringComparison.OrdinalIgnoreCase) >= 0
                                || MgFlasherCore.ByteArrayContains(difVarianteParsed, "FEMTO"))
                            {
                                moduleInfo.unlockCompany = UnlockCompanyName.FEMTO;
                                moduleInfo.unlocked = "FEMTO";
                            }

                            if (!string.IsNullOrEmpty(moduleInfo.unlocked))
                            {
                                Logger.LogInformation($"moduleInfo.unlocked: {moduleInfo.unlocked}");
                            }
                        }
                        else if (xweBytes[0] == 0x08)
                        {
                            if (_difVarianteString.IndexOf("MGF_3PU", StringComparison.OrdinalIgnoreCase) >= 0
                                || MgFlasherCore.ByteArrayContains(difVarianteParsed, "MGF_3PU"))
                            {
                                moduleInfo.unlockCompany = UnlockCompanyName.Unknown;
                                moduleInfo.unlocked = "PreviousThirdPartyUnlock";
                            }
                            if (_difVarianteString.IndexOf("#TAS#MGF", StringComparison.OrdinalIgnoreCase) >= 0
                                     || MgFlasherCore.ByteArrayContains(difVarianteParsed, "#TAS#MGF"))
                            {
                                moduleInfo.unlockCompany = UnlockCompanyName.TAS;
                                moduleInfo.unlocked = "TAS";
                            }
                            if (_difVarianteString.IndexOf("FEMTO", StringComparison.OrdinalIgnoreCase) >= 0
                                || MgFlasherCore.ByteArrayContains(difVarianteParsed, "FEMTO"))
                            {
                                moduleInfo.unlockCompany = UnlockCompanyName.FEMTO;
                                moduleInfo.unlocked = "FEMTO";
                            }
                            if (_difVarianteString.IndexOf("1769 Performance & KM Engineers", StringComparison.OrdinalIgnoreCase) >= 0
                                || MgFlasherCore.ByteArrayContains(difVarianteParsed, "1769 Performance & KM Engineers"))
                            {
                                moduleInfo.unlockCompany = UnlockCompanyName._1769;
                                moduleInfo.unlocked = "1769";
                            }

                            if (!string.IsNullOrEmpty(moduleInfo.unlocked))
                            {
                                Logger.LogInformation($"moduleInfo.unlocked: {moduleInfo.unlocked}");
                            }

                            if (moduleInfo.unlockCompany != UnlockCompanyName.Unknown)
                            {
                                Logger.LogInformation($"moduleInfo.unlockCompany: {moduleInfo.unlockCompany}");
                            }
                        }

                        Logger.LogInformation("Dif Variante: {_difVarianteString} {difVarianteParsed}", _difVarianteString, difVarianteParsed);
                        moduleInfo.difVarianteList.Add(new DifVariante()
                        {
                            SweArt = xweListItem.ProcessClass.ToString(),
                            Data = difVarianteRaw
                        });
                    }
                }
            }
        }
    }

    public static bool ByteArrayContains(byte[] haystack, string needle)
    {
        if (!string.IsNullOrEmpty(needle))
        {
            byte[] _needle = Encoding.ASCII.GetBytes(needle);
            //Logger.LogInformation("ByteArrayContains: haystack = '{haystack}' and _needle = '{_needle}'", BitConverter.ToString(haystack).Replace("-", ""), BitConverter.ToString(_needle).Replace("-", ""));

            if (haystack.Length >= _needle.Length)
            {
                for (int i = 0; i < haystack.Length - _needle.Length; i++)
                {
                    int runningMatches = 0;
                    for (int j = 0; j <= _needle.Length; j++)
                    {
                        if (haystack[i + j] == _needle[j])
                        {
                            runningMatches++;
                            if (runningMatches == _needle.Length)
                            {
                                Logger.LogInformation(string.Format("ByteArrayContains: Found match for '{0}'!", needle));
                                return true; // found our needle
                            }
                        }
                        else
                        {
                            break; // no match, continue with next set
                        }
                    }
                }
            }
        }
        Logger.LogInformation(string.Format("ByteArrayContains: failed to find a match for '{0}'...", needle));
        return false;
    }

    public static int DetermineGenericFallbackSgbdIndexViaDmeArchitecture()
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        UDS_BMW.UDS_RecordIdentifier SVK_recordId = UDS_BMW.UDS_RecordIdentifier.SVK_AKTUELL;

        Logger.LogInformation(string.Format("Identifying Generic SGBD Index using DME Architecture by comparing BTLD via {0} info:", SVK_recordId));
        if (_UDSDiagIf.ReadDataByIdentifier(SVK_recordId, out byte[] recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            var message = $"Could not read {SVK_recordId} from module, exiting";
            Logger.LogError(message);
            throw new Exception(message);
        }

        // parse SVK_AKTUELL
        byte nrOfRecords = recordData[3];
        byte blockOffset = 17;

        for (byte i = 0; i < nrOfRecords; i++)
        {
            // check only bootloader
            if (recordData[blockOffset] == 6)
            {
                var ecuBootloader = new SweVersion(StringFunctions.ExtractSectionArray(recordData, blockOffset, 8)) { SweArt = SweArtType.BTLD };
                Logger.LogInformation($"BTLD: {ecuBootloader.GetXweString()}");

                var processorArchitectureType = GetArchitectureFromBtldDifVariante(ecuBootloader);
                Logger.LogInformation($"Detected Architecture: {processorArchitectureType}");

                if (processorArchitectureType.Equals(ProcessorArchitectureType.PPC))
                {
                    return 696902; // SK's Generic PPC fallback SGBD index
                }
                else if (processorArchitectureType.Equals(ProcessorArchitectureType.AURIX))
                {
                    return 696901; // SK's Generic AURIX fallback SGBD index
                }
                else if (processorArchitectureType.Equals(ProcessorArchitectureType.MEVD))
                {
                    return 696903; // SK's Generic MEVD fallback SGBD index
                }
                else if (processorArchitectureType.Equals(ProcessorArchitectureType.MED_EDC))
                {
                    return 696904; // SK's Generic MED_EDC fallback SGBD index
                }
            }
            blockOffset += 8;
        }
        Logger.LogInformation("Error: Architecture unknown!");
        return 0;
    }

    private static ProcessorArchitectureType GetArchitectureFromBtldDifVariante(SweVersion btld)
    {
        if (btld.ProcessClass != ProcessClassType.BTLD)
        {
            return ProcessorArchitectureType.UNKNOWN;
        }

        var difVariante = GetDifVarianteFromSweVersion(btld);
        if (string.IsNullOrWhiteSpace(difVariante))
        {
            return ProcessorArchitectureType.UNKNOWN;
        }

        return GetProcessorArchitectureFromDifVariante(difVariante);
    }

    // Using chip names "MG1CS003", "MG1CS024", etc. to determine architecture
    public static ProcessorArchitectureType GetProcessorArchitectureFromDifVariante(string difVarianteString)
    {
        var arch = SupportedEcuService.GetArchitectureFromDifVariante(difVarianteString);
        return MapArch(arch);
    }

    private static ProcessorArchitectureType MapArch(ArchitectureType arch) =>
        arch switch
        {
            ArchitectureType.PPC => ProcessorArchitectureType.PPC,
            ArchitectureType.AURIX => ProcessorArchitectureType.AURIX,
            ArchitectureType.MED_EDC => ProcessorArchitectureType.MED_EDC,
            ArchitectureType.MEVD => ProcessorArchitectureType.MEVD,
            ArchitectureType.MPC5567_MVR132 => ProcessorArchitectureType.MPC5567_MVR132,
            ArchitectureType.UNKNOWN => ProcessorArchitectureType.UNKNOWN,
            _ => ProcessorArchitectureType.UNKNOWN,
        };

    public static string ReadBootctrlVersion()
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        var svk_recordId = UDS_BMW.UDS_RecordIdentifier.StartupBlockId;
        if (_UDSDiagIf.ReadDataByIdentifier(svk_recordId, out byte[] recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogInformation("Could not read {svk_recordId} from module, exiting", svk_recordId);
            return string.Empty;
        }

        var recordDataString = Encoding.ASCII.GetString(recordData);
        var bootctrlVersionSplit = new Regex("BOOTCTRL_V([0-9]){2}.([0-9]){2}.([0-9]){2}", RegexOptions.Compiled);
        // bootctrl Version: (BOOTCTRL_V03.00.08, BOOTCTRL_V04.00.01, etc) == BOOTCTRL_V([0-9]){2}.([0-9]){2}.([0-9]){2}
        var bootctrlVersionMatches = bootctrlVersionSplit.Matches(recordDataString);
        var bootctrlVersionString = bootctrlVersionMatches.Count > 0 ? bootctrlVersionMatches[0].Value.Replace("BOOTCTRL_V", "") : string.Empty;

        Logger.LogInformation("  Boot Control Version: " + bootctrlVersionString);
        return bootctrlVersionString;
    }

    public static bool SupportedBootctrlVersions(ModuleInfo moduleInfo, bool checkWave3Support = false)
    {
        if (Version.TryParse(moduleInfo.bootctrlVersion, out Version bootctrlVersion))
        {
            return SupportedBootctrlVersions(bootctrlVersion, moduleInfo.ProcessorArchitectureType, checkWave3Support);
        }
        Logger.LogWarning($"Unable to parse bootctrl version [{moduleInfo.bootctrlVersion}], defaulting to unsupported!");
        return false;
    }

    public static bool SupportedBootctrlVersions(string versionString, ProcessorArchitectureType type, bool checkWave3Support = false)
    {
        if (Version.TryParse(versionString, out Version bootctrlVersion))
        {
            return SupportedBootctrlVersions(bootctrlVersion, type, checkWave3Support);
        }
        Logger.LogWarning($"Unable to parse bootctrl version [{versionString}], defaulting to unsupported!");
        return false;
    }

    public static bool SupportedBootctrlVersions(Version version, ProcessorArchitectureType type, bool checkWave3Support = false)
    {
        var newEcuSecurityVersion = new Version(99, 99, 99);
        if (IsAurixArchitecture(type))
        {
            newEcuSecurityVersion = new Version(04, 00, 03); // v04.00.03 aurix
        }
        else if (IsPpcArchitecture(type))
        {
            newEcuSecurityVersion = new Version(03, 00, 81); // v03.00.81 ppc
        }

        if (version >= newEcuSecurityVersion)
        {
            Logger.LogInformation($"Bootctrl version is newer or equal to the Wave3 restricted version.");
            if (checkWave3Support)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        Logger.LogInformation($"Bootctrl version is older than the Wave3 restricted version.");
        return !checkWave3Support;
    }

    public static bool BdcRockerComboSupported(List<SweVersion> bdcSweVersions, List<IntegrationStepInfo> integrationStepInfoList)
    {
        if (bdcSweVersions == null
            || !bdcSweVersions.Any())
        {
            Logger.LogInformation($"{nameof(bdcSweVersions)} is null or empty, returning false");
            return false;
        }

        var targetIntegrationStepInfo = integrationStepInfoList?.Find(x => x.Type.Equals(IntegrationStepType.Current));
        if (targetIntegrationStepInfo == null)
        {
            Logger.LogInformation($"Failed to find target IStep type ({IntegrationStepType.Current}), checking RockerCombo support via BDC swe list only...");
            return BdcSweSupportsRockerCombo(bdcSweVersions);
        }
        return BdcRockerComboSupported(bdcSweVersions, targetIntegrationStepInfo?.Version);
    }

    public static bool BdcRockerComboSupported(List<SweVersion> bdcSweVersions, Version version)
    {
        if (bdcSweVersions == null
            || !bdcSweVersions.Any()
            || version == null)
        {
            Logger.LogInformation($"{nameof(bdcSweVersions)} or {nameof(version)} is null or empty, returning false");
            return false;
        }

        var newBdcIStepVersion = new Version(21, 11, 0); // I-Step which blocked cruise control data over CAN Bus
        if (version >= newBdcIStepVersion)
        {
            Logger.LogInformation($"IStep (I-Stufe) version in BDC is newer or equal to the unsupported version which requires a BDC downgrade.");
            return false;
        }
        Logger.LogInformation($"IStep (I-Stufe) version in module is older and can use RockerCombo cruise control buttons.");
        return BdcSweSupportsRockerCombo(bdcSweVersions);
    }

    public static bool BdcSweSupportsRockerCombo(List<SweVersion> bdcSweVersions)
    {
        if (bdcSweVersions == null
            || !bdcSweVersions.Any())
        {
            Logger.LogInformation($"{nameof(bdcSweVersions)} is null or empty, returning false");
            return false;
        }

        if (!bdcSweVersions.Any(x => x.ProcessClass == ProcessClassType.GWTB))
        {
            Logger.LogInformation($"No GTWB detected, assuming FEM, returning true");
            return true;
        }

        var maxAllowableSweVersionList = new List<SweVersion> // Latest SWE which still allow cruise control data over CAN Bus
        {
            new SweVersion("GWTB-00001800-007.019.101"), // unconfirmed
            new SweVersion("GWTB-00001801-008.042.000"), // unconfirmed
            new SweVersion("GWTB-00001802-006.027.101"), // unconfirmed
            new SweVersion("GWTB-00001803-010.027.101"), // unconfirmed
            new SweVersion("GWTB-00001804-011.036.004"),
            new SweVersion("GWTB-00005E49-014.030.201"), // unconfirmed
            new SweVersion("GWTB-0000413B-012.030.005"),
            new SweVersion("GWTB-0000413C-013.044.001"), // unconfirmed
            new SweVersion("GWTB-0000913E-015.042.050"), // unconfirmed
            new SweVersion("GWTB-0000A90B-013.044.050"), // unconfirmed
        };

        var tableSweList = bdcSweVersions?.Where(x => x.ProcessClass == ProcessClassType.GWTB).ToList();
        foreach (var tableSwe in tableSweList)
        {
            var matchingMaxAllowableSweVersion = maxAllowableSweVersionList.Find(x => x.Id == tableSwe.Id);
            if (matchingMaxAllowableSweVersion == null)
            {
                continue;
            }

            var maxAllowedVersion = new Version(matchingMaxAllowableSweVersion.Major, matchingMaxAllowableSweVersion.Minor, matchingMaxAllowableSweVersion.Patch);
            var currentVersion = new Version(tableSwe.Major, tableSwe.Minor, tableSwe.Patch);
            if (currentVersion <= maxAllowedVersion)
            {
                Logger.LogInformation($"BDC SWE is older and can use RockerCombo cruise control buttons.");
                return true;
            }
        }
        Logger.LogInformation($"BDC SWE is newer or equal to the unsupported version which requires a BDC downgrade.");
        return false;
    }

    public static string ReadEcuManufacturingDate()
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        var svk_recordId = UDS_BMW.UDS_RecordIdentifier.EcuManufacturingData;
        if (_UDSDiagIf.ReadDataByIdentifier(svk_recordId, out byte[] recordData, ref responseTesterId, ref responseModuleId) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogInformation("Could not read {svk_recordId} from module, exiting", svk_recordId);
        }
        else
        {
            int year = 2000 + int.Parse(recordData[0].ToString("X02"), System.Globalization.NumberStyles.Integer);
            int month = int.Parse(recordData[1].ToString("X02"), System.Globalization.NumberStyles.Integer);
            int day = int.Parse(recordData[2].ToString("X02"), System.Globalization.NumberStyles.Integer);
            DateTime manufacturingDate = new DateTime(year, month, day, 0, 0, 0);

            var manufacturingDateString = manufacturingDate.ToString("MMMM dd yyyy", CultureInfo.InvariantCulture);
            Logger.LogInformation("  Ecu Manufacturing Date: " + manufacturingDateString);
            return manufacturingDateString;
        }
        return string.Empty;
    }

    public static bool SupportedEcuManufacturingDate(string dateString)
    {
        var manufacturingDate = DateTime.ParseExact(dateString, "MMMM dd yyyy", CultureInfo.InvariantCulture);
        return SupportedEcuManufacturingDate(manufacturingDate);
    }

    public static bool SupportedEcuManufacturingDate(DateTime date)
    {
        DateTime newEcuSecurityManufacturingDate = new DateTime(2020, 07, 1, 0, 0, 0); // July 1 2020

        if (DateTime.Compare(date, newEcuSecurityManufacturingDate) >= 0)
        {
            var newEcuSecurityManufacturingDateString = newEcuSecurityManufacturingDate.ToString("MMMM dd yyyy", CultureInfo.InvariantCulture);
            Logger.LogInformation("Ecu unsupported, may require Wave3 unlock! It was manufactured after [{newEcuSecurityManufacturingDateString}]!", newEcuSecurityManufacturingDateString);
            return false;
        }
        return true;
    }

    public static string BootloaderCrcPatchLookup(ref ModuleInfo moduleInfo, uint btldCrcPatch)
    {
        uint btldIdMayorUint = ProtocolUtils.btldMayorIdOf(moduleInfo.btldId);

        string appliedPatchNames = string.Format("UNKNOWN BTLD 0x{0:X08} CRCPATCH 0x{1:X08} combo", btldIdMayorUint, btldCrcPatch);
        switch (btldIdMayorUint)
        {
            case 0x00003072: // BTLD-00003072-000.xxx.xxx
            case 0x00003075: // BTLD-00003075-000.xxx.xxx
            case 0x0000306F: // BTLD-0000306f-000.xxx.xxx
                if (moduleInfo.btldId[6] == 1 && moduleInfo.btldId[7] == 32) // BTLD-000.001.032
                {
                    if (btldCrcPatch == 0x6A11111E || btldCrcPatch == 0x22F12403) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0x852BF86C) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                    else if (btldCrcPatch == 0x27BE35FF) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-c}";
                    else if (btldCrcPatch == 0xC884DC8D) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-cp}";
                }
                else if (moduleInfo.btldId[6] == 10 && moduleInfo.btldId[7] == 34) // BTLD-000.010.034
                {
                    if (btldCrcPatch == 0x2C9DFDC2) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0x1CD610EA) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}";
                    else if (btldCrcPatch == 0xF6B01C01) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                    else if (btldCrcPatch == 0xC6FBF129) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}"; // app default
                }
                break;

            case 0x00007226: // BTLD-00007226-000.xxx.xxx
            case 0x0000722A: // BTLD-0000722A-000.xxx.xxx
                if (moduleInfo.btldId[6] == 1 && moduleInfo.btldId[7] == 3) // BTLD-000.001.003
                {
                    if (btldCrcPatch == 0x2C9DFDC2) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0x1CD610EA) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                    else if (btldCrcPatch == 0xF6B01C01) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-c}";
                    else if (btldCrcPatch == 0xC6FBF129) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-cp}";
                }
                break;

            case 0x00003080: // BTLD-00003080-000.xxx.xxx
            case 0x00003084: // BTLD-00003084-000.xxx.xxx
            case 0x00003087: // BTLD-00003087-000.xxx.xxx
                if (moduleInfo.btldId[6] == 1 && moduleInfo.btldId[7] == 32) // BTLD-000.001.032
                {
                    if (btldCrcPatch == 0x6A11111E || btldCrcPatch == 0x22F12403) appliedPatchNames = "RSA_BYPASS PST/DST {-v}"; // !! TODO check BmwExplorer style for DME840/860 !!
                    else if (btldCrcPatch == 0xE5BFA95C) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                    else if (btldCrcPatch == 0x8403E04C) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-c}";
                    else if (btldCrcPatch == 0x0BAD580E) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-cp}";
                }
                else if (moduleInfo.btldId[6] == 10 && moduleInfo.btldId[7] == 34) // BTLD-000.010.034
                {
                    if (btldCrcPatch == 0x2C9DFDC2) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0xA03AD9DB) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}";
                    else if (btldCrcPatch == 0x7928AE68) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                    else if (btldCrcPatch == 0xF58F8A71) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}"; // app default
                }
                break;

            case 0x000048DC: // BTLD-000048DC
            case 0x00004295: // BTLD-00004295
            case 0x00003E4B: // BTLD-00003E4B
            case 0x00006198: // BTLD-00006198
            case 0x00005D53: // BTLD-00005D53
                if (moduleInfo.btldId[6] == 7) // BTLD-001.007.xxx
                {
                    if (moduleInfo.btldId[7] == 1) // BTLD-001.007.001
                    {
                        if (btldCrcPatch == 0x34D1D528) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                        else if (btldCrcPatch == 0x3005DAFD) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                        else if (btldCrcPatch == 0x5A11F0AB) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                        else if (btldCrcPatch == 0x5EC5FF7E) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}";
                    }
                    else if (moduleInfo.btldId[7] == 3) // BTLD-001.007.003
                    {
                        if (btldCrcPatch == 0x43FE3668) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                        else if (btldCrcPatch == 0x472A39BD) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                        else if (btldCrcPatch == 0x3B2EC699) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                        else if (btldCrcPatch == 0x3FFAC94C) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}";
                    }
                }
                else if (moduleInfo.btldId[6] == 9) // BTLD-001.009.003
                {
                    if (btldCrcPatch == 0x1747F7E4) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0x1CD18CE4) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                    else if (btldCrcPatch == 0x0E588A52) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                    else if (btldCrcPatch == 0x05CEF152) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}";
                }
                else if (moduleInfo.btldId[6] == 11)
                {
                    if (moduleInfo.btldId[7] == 1) // BTLD-001.011.001
                    {
                        if (btldCrcPatch == 0x1747F7E4) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                        else if (btldCrcPatch == 0x1CD18CE4) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                        else if (btldCrcPatch == 0x796CF56A) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                        else if (btldCrcPatch == 0x72FA8E6A) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}";
                    }
                    else if (moduleInfo.btldId[7] == 2) // BTLD-001.011.002
                    {
                        //if (btldCrcPatch == 0x1747F7E4) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                        //else if (btldCrcPatch == 0x1CD18CE4) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}";
                        //else if (btldCrcPatch == 0x796CF56A) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                        //else if (btldCrcPatch == 0x72FA8E6A) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}"; // app default
                    }
                }
                else if (moduleInfo.btldId[6] == 12 || moduleInfo.btldId[6] == 14 || moduleInfo.btldId[6] == 16) // BTLD-001.012.xxx || BTLD-001.014.xxx || BTLD-001.016.xxx
                {
                    if (moduleInfo.btldId[7] == 1) // BTLD-001.012.001 || BTLD-001.014.001 || BTLD-001.016.001
                    {
                        if (btldCrcPatch == 0xB3153D4A) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                        else if (btldCrcPatch == 0xB883464A) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}";
                        else if (btldCrcPatch == 0x406E907F) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                        else if (btldCrcPatch == 0x4BF8EB7F) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}"; // app default
                    }
                    else if (moduleInfo.btldId[7] == 2) // BTLD-001.012.002 || BTLD-001.014.002 || BTLD-001.016.002
                    {
                        if (btldCrcPatch == 0xB3153D4A) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                        else if (btldCrcPatch == 0xB883464A) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}";
                        else if (btldCrcPatch == 0x8A227B4A) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                        else if (btldCrcPatch == 0x81B4004A) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}"; // app default
                    }
                }
                else if (moduleInfo.btldId[6] == 20 && moduleInfo.btldId[7] == 1) // BTLD-001.020.001
                {
                    if (btldCrcPatch == 0x1747F7E4) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0x1CD18CE4) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                    else if (btldCrcPatch == 0x023A7EFA) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                    else if (btldCrcPatch == 0x09AC05FA) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}";
                }
                else if ((moduleInfo.btldId[6] == 20 && moduleInfo.btldId[7] == 2) // BTLD-001.020.002
                         || (moduleInfo.btldId[6] == 25 && moduleInfo.btldId[7] == 3) // BTLD-001.025.003
                         || (moduleInfo.btldId[6] == 35 && moduleInfo.btldId[7] == 5) // BTLD-001.035.005
                         || (moduleInfo.btldId[6] == 45 && moduleInfo.btldId[7] == 1)) // BTLD-001.045.001
                {
                    if (btldCrcPatch == 0xB3153D4A) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0xB883464A) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}";
                    else if (btldCrcPatch == 0x1380FC49) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                    else if (btldCrcPatch == 0x18168749) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}"; // app default
                }
                else if ((moduleInfo.btldId[5] == 1 && moduleInfo.btldId[6] == 17 && moduleInfo.btldId[7] == 1)) // BTLD-001.017.001
                {
                    if (btldCrcPatch == 0x43FE3668) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0x472A39BD) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}";
                    else if (btldCrcPatch == 0x3B2EC699) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                    else if (btldCrcPatch == 0x3FFAC94C) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}"; // app default
                }
                else if ((moduleInfo.btldId[5] == 1 && moduleInfo.btldId[6] == 21 && moduleInfo.btldId[7] == 1)) // BTLD-001.021.001
                {
                    if (btldCrcPatch == 0xB3153D4A) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0xB883464A) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}";
                    else if (btldCrcPatch == 0x8A227B4A) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                    else if (btldCrcPatch == 0x81B4004A) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}"; // app default
                }
                else if ((moduleInfo.btldId[5] == 1 && moduleInfo.btldId[6] == 30 && moduleInfo.btldId[7] == 1)) // BTLD-001.030.001
                {
                    if (btldCrcPatch == 0xB3153D4A) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0xB883464A) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}";
                    else if (btldCrcPatch == 0x9A69378E) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-vc}";
                    else if (btldCrcPatch == 0x91FF4C8E) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-vcp}"; // app default
                }
                break;

            case 0x000048D9: // BTLD-000048D9
                if (moduleInfo.btldId[6] == 2 && moduleInfo.btldId[7] == 11) // BTLD-000.002.011
                {
                    if (btldCrcPatch == 0x34D1D528) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0xFE7FF50F) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                    else if (btldCrcPatch == 0x187F3E17) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-c}";
                    else if (btldCrcPatch == 0xD2D11E30) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-cp}";
                }
                else if (moduleInfo.btldId[6] == 10 && moduleInfo.btldId[7] == 1) // BTLD-000.010.002
                {
                    if (btldCrcPatch == 0x1747F7E4) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0x1CD18CE4) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                    else if (btldCrcPatch == 0x023A7EFA) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-c}";
                    else if (btldCrcPatch == 0x09AC05FA) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-cp}";
                }
                else if ((moduleInfo.btldId[6] == 10 && moduleInfo.btldId[7] == 2) // BTLD-000.010.002
                         || (moduleInfo.btldId[6] == 35 && moduleInfo.btldId[7] == 5)) //  BTLD-001.035.005
                {
                    if (btldCrcPatch == 0xB3153D4A) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0xB883464A) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                    else if (btldCrcPatch == 0x1380FC49) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-c}";
                    else if (btldCrcPatch == 0x18168749) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-cp}";
                }
                break;

            case 0x000048DB: // BTLD-000048DB
                if (moduleInfo.btldId[6] == 3) // BTLD-000.003.xxx
                {
                    if (moduleInfo.btldId[7] == 3) // BTLD-000.003.003
                    {
                        if (btldCrcPatch == 0x1747F7E4) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                        else if (btldCrcPatch == 0x1CD18CE4) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                        else if (btldCrcPatch == 0xEEA70B7E) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-c}";
                        else if (btldCrcPatch == 0xE531707E) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-cp}";
                    }
                    else if (moduleInfo.btldId[7] == 6) // BTLD-000.003.006
                    {
                        if (btldCrcPatch == 0x1747F7E4) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                        else if (btldCrcPatch == 0x1CD18CE4) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                        else if (btldCrcPatch == 0x023A7EFA) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-c}";
                        else if (btldCrcPatch == 0x09AC05FA) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-cp}";
                    }
                }
                else if ((moduleInfo.btldId[6] == 5 && moduleInfo.btldId[7] == 1) // BTLD-000.005.001
                         || (moduleInfo.btldId[6] == 10 && moduleInfo.btldId[7] == 4) // BTLD-000.010.004
                         || (moduleInfo.btldId[6] == 35 && moduleInfo.btldId[7] == 4)) // BTLD-001.035.005
                {
                    if (btldCrcPatch == 0xB3153D4A) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0xB883464A) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                    else if (btldCrcPatch == 0x1380FC49) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-c}";
                    else if (btldCrcPatch == 0x18168749) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-cp}";
                }
                break;

            case 0x000078E8: // BTLD-000078E8
                if ((moduleInfo.btldId[6] == 5 && moduleInfo.btldId[7] == 1) // BTLD-000.005.001
                    || (moduleInfo.btldId[6] == 35 && moduleInfo.btldId[7] == 4)) // BTLD-001.035.005
                {
                    if (btldCrcPatch == 0xB3153D4A) appliedPatchNames = "RSA_BYPASS PST/DST {-v}";
                    else if (btldCrcPatch == 0xB883464A) appliedPatchNames = "RSA_BYPASS PST/DST + PROGCNT_FREEZE {-vp}"; // app default
                    else if (btldCrcPatch == 0x1380FC49) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST {-c}";
                    else if (btldCrcPatch == 0x18168749) appliedPatchNames = "RSA_BYPASS CODE BTL/PST/DST + PROGCNT_FREEZE {-cp}";
                }
                break;

            default:
                Logger.LogInformation("add case for other ecus!");
                break;
        }
        Logger.LogInformation("bootloaderCrcPatchLookup: {0}", appliedPatchNames);
        return appliedPatchNames;
    }

    public static bool CheckEcuForNewSecurityBtld(ref ModuleInfo moduleInfo)
    {
        var btldSweVersion = moduleInfo.SegmentSweVersion(SweArtType.BTLD);
        var result = CheckForNewSecurity(btldSweVersion);
        if (result)
        {
            Logger.LogInformation("  ECU has high security!");
            if (BTLD_old_aurix_install)
            {
                Logger.LogInformation("  New bootloader security requirements are to check programming dependencies after each swe upload, 'ba|bltd_old_aurix_install' is disabled! May need to be reworked with new obd unlock solution");
                BTLD_old_aurix_install = false;
            }
        }
        else
        {
            Logger.LogInformation("  ECU has low security.");
        }
        return result;
    }

    public static bool CheckForNewSecurity(SweVersion btld)
    {
        var securityLevel = SupportedEcuService.GetSecurityLevelFromBtldSweVersion(btld.GenerateMgfFileName());
        return securityLevel == SweSecurityLevel.HighSecurity || securityLevel == SweSecurityLevel.Undefined;
    }

    private static string GetCbVersionStringFromBtld(string btldDifVariante)
    {
        var cbVersionPattern = @"(C|CB)_011_([a-zA-Z0-9._]+)";
        var matchCbVersion = Regex.Match(btldDifVariante, cbVersionPattern);
        if (!matchCbVersion.Success)
        {
            throw new Exception($"Failed to find CB Version in this string [{btldDifVariante}]");
        }
        return matchCbVersion.Value;
    }

    public static Version GetMgfUnlockVersion(ModuleInfo moduleInfo, uint btldCrcPatch)
    {
        var groupMarker = "Version";
        var mgfUnlockPattern = @$"#MGF-Unlock-v(?<{groupMarker}>[0-9.]+)";

        var btldDifVariante = GetDifVariante(moduleInfo, SweArtType.BTLD);
        var matchMgfUnlock = Regex.Match(btldDifVariante, mgfUnlockPattern);
        if (!matchMgfUnlock.Success)
        {
            return IsBootloaderCrcPatchDefined(moduleInfo, btldCrcPatch) ? new Version(1, 0) : new Version(0, 0);
        }
        if (!Version.TryParse(matchMgfUnlock.Groups[groupMarker].Value, out var result))
        {
            return IsBootloaderCrcPatchDefined(moduleInfo, btldCrcPatch) ? new Version(1, 0) : new Version(0, 0);
        }
        return result;
    }

    private static bool IsBootloaderCrcPatchDefined(ModuleInfo moduleInfo, uint btldCrcPatch)
    {
        var patchName = BootloaderCrcPatchLookup(ref moduleInfo, btldCrcPatch);
        return !patchName.ToUpper().Contains("unknown".ToUpper());
    }

    private static string GetDifVariante(ModuleInfo moduleInfo, SweArtType sweArt)
    {
        var result = moduleInfo.SegmentDifVariante(sweArt.ToString());
        if (string.IsNullOrWhiteSpace(result))
        {
            var sweVersion = moduleInfo.SegmentSweVersion(sweArt);
            if (!sweVersion.IsValid)
            {
                throw new ArgumentNullException(nameof(sweVersion));
            }
            result = GetDifVarianteFromSweVersion(sweVersion);
        }
        return result;
    }

    private static string GetDifVarianteFromSweVersion(SweVersion sweVersion)
    {
        if (attachedCable is not BmwEnet)
        {
            throw new PlatformNotSupportedException("Connection unsupported (not ENET)!");
        }

        if (!sweVersion.IsValid)
        {
            Logger.LogError($"Unable to read Dif Variante for [{sweVersion}], it is invalid!");
            return string.Empty;
        }

        var xweBytes = sweVersion.GetXweBytes();
        if (MgFlasherCore._UDSDiagIf.RoutineControl(UDS_BMW.UDS_RoutineAction.startRoutine, UDS_BMW.UDS_RoutineIdentifier.ReadDevelopmentInfo, xweBytes, out byte[] difVarianteRaw) != UDS_BMW.UDS_ErrorCodes.UDS_NRC_NONE)
        {
            Logger.LogError($"Failed to get read Dif Variante for [{sweVersion}]!");
            return string.Empty;
        }

        var difVarianteParsed = new byte[difVarianteRaw.Length];
        Array.Copy(difVarianteRaw, difVarianteParsed, difVarianteParsed.Length);

        for (int j = 0; j < difVarianteParsed.Length; j++)
        {
            if (difVarianteParsed[j] < 0x20 || difVarianteParsed[j] > 0x7F)
            {
                //Logger.LogInformation("found invalid character '0x{0:X08}' at '0x{1:X08}', corrected to '_' (0x5F) ...", _difVariante[j], j);
                difVarianteParsed[j] = 0x5F;
            }
        }
        var difVarianteString = Encoding.ASCII.GetString(difVarianteParsed, 0, difVarianteParsed.Length);

        int index = difVarianteString.IndexOf("#");
        if (index > 0)
        { // remove everything before first '#'
            difVarianteString = difVarianteString[index..];
        }
        var result = TrimRepeated(difVarianteString);
        Logger.LogInformation($"[{sweVersion.ProcessClass}]: {result} {difVarianteParsed}");
        return result;
    }
}