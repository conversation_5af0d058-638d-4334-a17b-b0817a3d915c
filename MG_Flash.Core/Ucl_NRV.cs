﻿namespace UNI_Flash;

public class Ucl_NRV
{
    /* Error codes for the compression/decompression functions. Negative
     * values are errors, positive values will be used for special but
     * normal events.
     */
    public const int UCL_E_OK = 0;
    public const int UCL_E_ERROR = (-1);
    public const int UCL_E_INVALID_ARGUMENT = (-2);
    public const int UCL_E_OUT_OF_MEMORY = (-3);
    /* compression errors */
    public const int UCL_E_NOT_COMPRESSIBLE = (-101);
    /* decompression errors */
    public const int UCL_E_INPUT_OVERRUN = (-201);
    public const int UCL_E_OUTPUT_OVERRUN = (-202);
    public const int UCL_E_LOOKBEHIND_OVERRUN = (-203);
    public const int UCL_E_EOF_NOT_FOUND = (-204);
    public const int UCL_E_INPUT_NOT_CONSUMED = (-205);
    public const int UCL_E_OVERLAP_OVERRUN = (-206);

    public bool NRV_compress(byte[] src, int startidx, int len, out byte[] dst)
    {
        var input = src[startidx..(startidx + len)];
        dst = TurboCraft.UCL.BMW.Compression.UclBMW.doUCLcompression(input, 5);
        return true;
    }
}