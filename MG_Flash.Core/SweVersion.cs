﻿using EdiabasTest;
using System;
using System.Collections.Generic;
using System.Linq;

namespace UNI_Flash;

public class SweVersion
{
    public string Id { get; set; } = "0000";
    public byte Major { get; set; } = 0x00;
    public byte Minor { get; set; } = 0x00;
    public byte Patch { get; set; } = 0x00;
    public ProcessClassType ProcessClass { get; set; } = ProcessClassType.UNKNOWN;
    public SweArtType SweArt { get; set; } = SweArtType.UNKNOWN;
    public bool IsEmpty => Id == "0000";
    public List<SweVersion> References { get; set; }

    public bool IsValid => isIdValid
                           && !(Major == 0
                                && Minor == 0
                                && Patch == 0);

    private bool isIdValid => !string.IsNullOrWhiteSpace(Id) && Id.Replace("0x", "").Length == 4;

    public SweVersion()
    {
    }

    public SweVersion(string id, byte major, byte minor, byte patch)
    {
        Id = id;
        Major = major;
        Minor = minor;
        Patch = patch;
    }

    public SweVersion(byte[] xweBytes)
    {
        if (xweBytes != null && xweBytes.Length == 8)
        {
            // only check last 7 bytes for for empty
            var checkEmpty = StringFunctions.ExtractSectionArray(xweBytes, 1, 7);
            if (!checkEmpty.All(b => b == 0) && !checkEmpty.All(b => b == 0xff))
            {
                ProcessClass = ParseProcessClassStringFromByte(xweBytes[0]);
                SweArt = ParseSweArtFromProcessClass();
                Id = StringFunctions.ConvertBytesPartToString(xweBytes, 3, 2);
                Major = xweBytes[5];
                Minor = xweBytes[6];
                Patch = xweBytes[7];
            }
        }
    }

    public SweVersion(string xweString)
    {
        // example BTLD-00003075-000.001.032
        if (!string.IsNullOrWhiteSpace(xweString) && xweString.Length == 25)
        {
            ProcessClass = ParseProcessClassFromProcessClassString(xweString.Substring(0, 4));
            SweArt = ParseSweArtFromProcessClass();
            Id = xweString.Substring(9, 4);
            Major = Convert.ToByte(xweString.Substring(14, 3), 10);
            Minor = Convert.ToByte(xweString.Substring(18, 3), 10);
            Patch = Convert.ToByte(xweString.Substring(22, 3), 10);
        }
    }

    public bool IsIdentical(SweVersion sweVersion)
    {
        if (string.IsNullOrWhiteSpace(Id))
        {
            throw new ArgumentNullException("Unable to check if identical with invalid Id");
        }
        if (sweVersion == null || string.IsNullOrWhiteSpace(sweVersion.Id))
        {
            throw new ArgumentNullException("Unable to check if identical with invalid input");
        }
        var result = Id.ToUpper() == sweVersion.Id.ToUpper()
                     && Major == sweVersion.Major
                     && Minor == sweVersion.Minor
                     && Patch == sweVersion.Patch;
        return result;
    }

    #region BTLD IDs

    public bool Is306F(string swflId) => swflId.ToUpper().Contains("306F"); // B-Series engine

    public bool Is3072(string swflId) => swflId.ToUpper().Contains("3072"); // B-Series engine

    public bool Is3075(string swflId) => swflId.ToUpper().Contains("3075"); // B-Series engine

    public bool Is3080(string swflId) => swflId.ToUpper().Contains("3080"); // B-Series engine

    public bool Is3084(string swflId) => swflId.ToUpper().Contains("3084"); // B-Series engine

    public bool Is3087(string swflId) => swflId.ToUpper().Contains("3087"); // B-Series engine

    public bool Is3124(string swflId) => swflId.ToUpper().Contains("3124"); // N63TU2

    public bool Is31B3(string swflId) => swflId.ToUpper().Contains("31B3"); // N74

    public bool Is3E4B(string swflId) => swflId.ToUpper().Contains("3E4B"); // B-Series engine

    public bool Is3E72(string swflId) => swflId.ToUpper().Contains("3E72"); // B-Series engine

    public bool Is4294(string swflId) => swflId.ToUpper().Contains("4294"); // B-Series engine

    public bool Is4295(string swflId) => swflId.ToUpper().Contains("4295"); // B-Series engine

    public bool Is48D8(string swflId) => swflId.ToUpper().Contains("48D8"); // N74T2

    public bool Is48D9(string swflId) => swflId.ToUpper().Contains("48D9"); // S63

    public bool Is48DB(string swflId) => swflId.ToUpper().Contains("48DB"); // S58

    public bool Is48DC(string swflId) => swflId.ToUpper().Contains("48DC"); // B-Series engine

    public bool Is5C11(string swflId) => swflId.ToUpper().Contains("5C11"); // N74T2

    public bool Is5D53(string swflId) => swflId.ToUpper().Contains("5D53"); // B-Series engine

    public bool Is6198(string swflId) => swflId.ToUpper().Contains("6198"); // B-Series engine

    public bool Is7226(string swflId) => swflId.ToUpper().Contains("7226"); // B-Series engine

    public bool Is722A(string swflId) => swflId.ToUpper().Contains("722A"); // B-Series engine

    public bool Is7702(string swflId) => swflId.ToUpper().Contains("7702"); // B-Series engine

    public bool Is78E8(string swflId) => swflId.ToUpper().Contains("78E8"); // S58 Alpina

    public bool Is306F() => Id.ToUpper().Contains("306F"); // B-Series engine

    public bool Is3072() => Id.ToUpper().Contains("3072"); // B-Series engine

    public bool Is3075() => Id.ToUpper().Contains("3075"); // B-Series engine

    public bool Is3080() => Id.ToUpper().Contains("3080"); // B-Series engine

    public bool Is3084() => Id.ToUpper().Contains("3084"); // B-Series engine

    public bool Is3087() => Id.ToUpper().Contains("3087"); // B-Series engine

    public bool Is3124() => Id.ToUpper().Contains("3124"); // N63TU2

    public bool Is31B3() => Id.ToUpper().Contains("31B3"); // N74

    public bool Is3E4B() => Id.ToUpper().Contains("3E4B"); // B-Series engine

    public bool Is3E72() => Id.ToUpper().Contains("3E72"); // B-Series engine

    public bool Is4294() => Id.ToUpper().Contains("4294"); // B-Series engine

    public bool Is4295() => Id.ToUpper().Contains("4295"); // B-Series engine

    public bool Is48D8() => Id.ToUpper().Contains("48D8"); // N74T2

    public bool Is48D9() => Id.ToUpper().Contains("48D9"); // S63

    public bool Is48DB() => Id.ToUpper().Contains("48DB"); // S58

    public bool Is48DC() => Id.ToUpper().Contains("48DC"); // B-Series engine

    public bool Is5C11() => Id.ToUpper().Contains("5C11"); // N74T2

    public bool Is5D53() => Id.ToUpper().Contains("5D53"); // B-Series engine

    public bool Is6198() => Id.ToUpper().Contains("6198"); // B-Series engine

    public bool Is7226() => Id.ToUpper().Contains("7226"); // B-Series engine

    public bool Is722A() => Id.ToUpper().Contains("722A"); // B-Series engine

    public bool Is7702() => Id.ToUpper().Contains("7702"); // B-Series engine

    public bool Is78E8() => Id.ToUpper().Contains("78E8"); // S58 Alpina

    #endregion BTLD IDs

    #region SWFL IDs

    public bool Is3076(string swflId) => swflId.ToUpper().Contains("3076"); // B-Series engine

    public bool Is3081(string swflId) => swflId.ToUpper().Contains("3081"); // B-Series engine

    public bool Is3125(string swflId) => swflId.ToUpper().Contains("3125"); // N63TU2

    public bool Is31B4(string swflId) => swflId.ToUpper().Contains("31B4"); // N74

    public bool Is3428(string swflId) => swflId.ToUpper().Contains("3428"); // PPC Elf

    public bool Is3E7D(string swflId) => swflId.ToUpper().Contains("3E7D"); // B-Series engine

    public bool Is4963(string swflId) => swflId.ToUpper().Contains("4963"); // N74T2

    public bool Is4ACF(string swflId) => swflId.ToUpper().Contains("4ACF"); // S63

    public bool Is5C64(string swflId) => swflId.ToUpper().Contains("5C64"); // S58

    public bool Is5D55(string swflId) => swflId.ToUpper().Contains("5D55"); // B-Series engine

    public bool Is7706(string swflId) => swflId.ToUpper().Contains("7706"); // B-Series engine

    public bool Is78F7(string swflId) => swflId.ToUpper().Contains("78F7"); // S58 Alpina

    public bool Is7972(string swflId) => swflId.ToUpper().Contains("7972"); // B-Series engine

    public bool IsAED2(string swflId) => swflId.ToUpper().Contains("AED2"); // S58 G82 GT4

    public bool Is3076() => Id.ToUpper().Contains("3076"); // B-Series engine

    public bool Is3081() => Id.ToUpper().Contains("3081"); // B-Series engine

    public bool Is3125() => Id.ToUpper().Contains("3125"); // N63TU2

    public bool Is31B4() => Id.ToUpper().Contains("31B4"); // N74

    public bool Is3428() => Id.ToUpper().Contains("3428"); // PPC Elf

    public bool Is3E7D() => Id.ToUpper().Contains("3E7D"); // B-Series engine

    public bool Is4963() => Id.ToUpper().Contains("4963"); // N74T2

    public bool Is4ACF() => Id.ToUpper().Contains("4ACF"); // S63

    public bool Is5C64() => Id.ToUpper().Contains("5C64"); // S58

    public bool Is5D55() => Id.ToUpper().Contains("5D55"); // B-Series engine

    public bool Is7706() => Id.ToUpper().Contains("7706"); // B-Series engine

    public bool Is78F7() => Id.ToUpper().Contains("78F7"); // S58 Alpina

    public bool Is7972() => Id.ToUpper().Contains("7972"); // B-Series engine

    public bool IsAED2() => Id.ToUpper().Contains("AED2"); // S58 G82 GT4

    #endregion SWFL IDs

    public bool IsFxxHybrid_Gen1()
    {
        if (SweArt != SweArtType.PST)
        {
            throw new Exception("Unable to check if not SweArtType.PST");
        }
        var result = Is3076()
            && Major == 50
            && Minor == 6
            && Patch == 2;
        return result;
    }

    public bool IsFxx_Gen1()
    {
        if (SweArt != SweArtType.PST)
        {
            throw new Exception("Unable to check if not SweArtType.PST");
        }
        var result = Is3428() // PPC Elf
            || Is3076() // B-Series engine
            || Is7972() // B-Series engine
            ;
        return result;
    }

    // Gen 1 designation refers to architecture, PPC
    public bool IsGxx_Gen1()
    {
        if (SweArt != SweArtType.PST)
        {
            throw new Exception("Unable to check if not SweArtType.PST");
        }
        var result = Is3081() // B-Series engine
            || Is3125() // N63TU2
            || Is31B4() // N74
            || Is7706() // B-Series engine
            ;
        return result;
    }

    // Gen 2 designation refers to architecture, Aurix
    public bool IsGxx_Gen2() => IsAurix();

    public bool IsBtldIdPpc(string btldId) =>
        Is306F(btldId) // B-Series engine
        || Is3072(btldId) // B-Series engine
        || Is3075(btldId) // B-Series engine
        || Is3080(btldId) // B-Series engine
        || Is3084(btldId) // B-Series engine
        || Is3087(btldId) // B-Series engine
        || Is3124(btldId) // N63TU2
        || Is31B3(btldId) // N74
        || Is7702(btldId) // B-Series engine
        || Is7226(btldId) // B-Series engine
        || Is722A(btldId) // B-Series engine
    ;

    public bool IsSwflIdPpc(string swflId) =>
        Is3076(swflId) // B-Series engine
        || Is3081(swflId) // B-Series engine
        || Is3125(swflId) // N63TU2
        || Is31B4(swflId) // N74
        || Is3428(swflId) // PPC Elf
        || Is7706(swflId) // B-Series engine
        || Is7972(swflId) // B-Series engine
    ;

    public bool IsPpc()
    {
        if (SweArt == SweArtType.BTLD)
        {
            return IsBtldIdPpc(Id);
        }
        else if (SweArt == SweArtType.PST)
        {
            return IsSwflIdPpc(Id);
        }
        throw new Exception("Unable to check if SweArt is not BTLD or PST");
    }

    public bool IsBtldIdAurix(string btldId) =>
        Is3E72(btldId) // B-Series engine
        || Is3E4B(btldId) // B-Series engine
        || Is48D8(btldId) // N74T2
        || Is5C11(btldId) // N74T2
        || Is48D9(btldId) // S63
        || Is48DB(btldId) // S58
        || Is4294(btldId) // B-Series engine
        || Is4295(btldId) // B-Series engine
        || Is48DC(btldId) // B-Series engine
        || Is5D53(btldId) // B-Series engine
        || Is6198(btldId) // B-Series engine
        || Is78E8(btldId) // S58 Alpina
    ;

    public bool IsSwflIdAurix(string swflId) =>
        Is3E7D(swflId) // B-Series engine
        || Is4963(swflId) // N74T2
        || Is4ACF(swflId) // S63
        || Is5C64(swflId) // S58
        || Is5D55(swflId) // B-Series engine
        || Is78F7(swflId) // S58 Alpina
        || IsAED2(swflId) // S58 G82 GT4
        ;

    public bool IsAurix()
    {
        if (SweArt == SweArtType.BTLD)
        {
            return IsBtldIdAurix(Id);
        }
        else if (SweArt == SweArtType.PST)
        {
            return IsSwflIdAurix(Id);
        }
        throw new Exception("Unable to check if SweArt is not BTLD or SWFL");
    }

    public bool IsAurix(string swflId)
    {
        return IsSwflIdAurix(swflId);
    }

    public bool IsDualEcuSwfl(string swflId) =>
        Is3125(swflId) // N63TU2
        || Is31B4(swflId) // N74
        || Is4963(swflId) // N74T2
        || Is4ACF(swflId) // S63
    ;

    public bool IsDualEcuBtld(string btldId) =>
        Is3124(btldId) // N63TU2
        || Is31B3(btldId) // N74
        || Is48D8(btldId) // N74T2
        || Is48D9(btldId) // S63
        || Is5C11(btldId) // N74T2
    ;

    public bool IsDualEcu()
    {
        if (SweArt == SweArtType.BTLD)
        {
            return IsDualEcuBtld(Id);
        }
        else if (SweArt == SweArtType.PST)
        {
            return IsDualEcuSwfl(Id);
        }
        throw new Exception("Unable to check if SweArt is not BTLD or SWFL");
    }

    public string GetArchitectureFromSwflId(string swflId)
    {
        var result = IsSwflIdPpc(swflId) ? "PPC" : (IsSwflIdAurix(swflId) ? "Aurix" : throw new Exception($"Unable to determine architecture!"));
        return result;
    }

    public string GetArchitecture()
    {
        if (SweArt != SweArtType.PST)
        {
            throw new Exception("Unable to check if not SweArt.PST");
        }
        var result = IsPpc() ? "PPC" : (IsAurix() ? "Aurix" : throw new Exception($"Unable to determine architecture!"));
        return result;
    }

    public override string ToString()
    {
        // example swfl 3076 090 060 006
        var segment = ProcessClass.ToString().ToLower();
        if (!string.IsNullOrWhiteSpace(Id))
        {
            var result = (!string.IsNullOrWhiteSpace(segment) ? segment + " " : "") + (Id ?? "0000").ToLower() + " " + Major.ToString("d3") + " " + Minor.ToString("d3") + " " + Patch.ToString("d3");
            return result;
        }
        return string.Empty;
    }

    /// <summary>
    /// Example swfl_3076-090.060.006 as default
    /// With (dstAsSwfl == true) => swfk_367b.xml.080_017_005
    /// </summary>
    public string GenerateMgfFileName(bool dstAsSwfl = false)
    {
        var segment = GetSegmentFileName(dstAsSwfl: dstAsSwfl);
        var name = string.Empty;
        if (!string.IsNullOrWhiteSpace(Id))
        {
            name = segment + "_" + (Id ?? "0000").ToLower() + (segment.ToUpper().Contains("SWFK") ? ".xml." : "-") + Major.ToString("d3") + (segment.ToUpper().Contains("SWFK") ? "_" : ".") + Minor.ToString("d3") + (segment.ToUpper().Contains("SWFK") ? "_" : ".") + Patch.ToString("d3");
        }
        return name;
    }

    private string GetSegmentFileName(bool dstAsSwfl = false)
    {
        var result = SweArt switch
        {
            SweArtType.HWEL => "hwel",
            SweArtType.HWAP => "hwap",
            SweArtType.BTL or SweArtType.BTLD => "btld",
            SweArtType.PST or SweArtType.SWFL => "swfl",
            SweArtType.DST or SweArtType.SWFK => dstAsSwfl ? "swfl" : "swfk",
            SweArtType.CAFD => "cafd",
            SweArtType.GWTB => "gwtb",
            _ => string.Empty,
        };
        return result;
    }

    public string GenerateOemFileName()
    {
        // example swfl_00003076.bin.090_060_006
        if (!string.IsNullOrWhiteSpace(Id))
        {
            var result = ProcessClass.ToString().ToLower() + "_0000" + (Id ?? "0000").ToLower() + ".bin." + Major.ToString("d3") + "_" + Minor.ToString("d3") + "_" + Patch.ToString("d3");
            return result;
        }
        return string.Empty;
    }

    public string GenerateFolderName()
    {
        // example swfl-00003076-090.060.006
        if (!string.IsNullOrWhiteSpace(Id))
        {
            var result = ProcessClass.ToString().ToLower() + "-0000" + (Id ?? "0000").ToLower() + "-" + Major.ToString("d3") + "." + Minor.ToString("d3") + "." + Patch.ToString("d3");
            return result;
        }
        return string.Empty;
    }

    public string GetXweString()
    {
        // example BTLD-00003075-000.001.032
        if (!string.IsNullOrWhiteSpace(Id) && Id != "0000")
        {
            var result = ProcessClass.ToString().ToUpper() + "-0000" + (Id ?? "0000") + "-" + Major.ToString("d3") + "." + Minor.ToString("d3") + "." + Patch.ToString("d3");
            return result;
        }
        return "ERASED -> EMPTY EMPTY";
    }

    public byte[] GetXweBytes()
    {
        // Example byte[]: { 0x06, 0x00, 0x00, 0x30, 0x75, 0x00, 0x01, 0x20 };
        byte[] xweBytes = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
        if (Id != null && Id.Length >= 4)
        {
            xweBytes[0] = (byte)ProcessClass;
            xweBytes[3] = Convert.ToByte(Id.Substring(0, 2), 16);
            xweBytes[4] = Convert.ToByte(Id.Substring(2, 2), 16);
            xweBytes[5] = Major;
            xweBytes[6] = Minor;
            xweBytes[7] = Patch;
        }
        return xweBytes;
    }

    public SweArtType ParseSweArtFromSweArtString(string sweArt)
    {
        sweArt = sweArt.ToUpper();
        if (Enum.IsDefined(typeof(SweArtType), sweArt))
        {
            var result = (SweArtType)Enum.Parse(typeof(SweArtType), sweArt);
            return result;
        }
        return SweArtType.UNKNOWN;
    }

    public SweArtType ParseSweArtFromProcessClass()
    {
        var processClass = ProcessClass.ToString().ToUpper();
        if (processClass.Contains("SWFK"))
        {
            processClass = "DST";
        }
        if (Enum.IsDefined(typeof(SweArtType), processClass))
        {
            var result = (SweArtType)Enum.Parse(typeof(SweArtType), processClass);
            return result;
        }
        return SweArtType.UNKNOWN;
    }

    public ProcessClassType ParseProcessClassFromProcessClassString(string processClass)
    {
        processClass = processClass.ToUpper();
        if (Enum.IsDefined(typeof(ProcessClassType), processClass))
        {
            var result = (ProcessClassType)Enum.Parse(typeof(ProcessClassType), processClass);
            return result;
        }
        return ProcessClassType.UNKNOWN;
    }

    public ProcessClassType ParseProcessClassStringFromByte(byte input)
    {
        if (Enum.IsDefined(typeof(ProcessClassType), input))
        {
            var result = (ProcessClassType)Enum.ToObject(typeof(ProcessClassType), input);
            return result;
        }
        return ProcessClassType.UNKNOWN;
    }
}