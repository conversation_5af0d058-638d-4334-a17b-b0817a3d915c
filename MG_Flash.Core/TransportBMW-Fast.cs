﻿using CableInterface;
using System;
using System.Diagnostics;
using System.Threading;
using Microsoft.Extensions.Logging;

namespace DiagTransportInterface;
//
// TransportBMW_Fast
//

public class TransportBMW_Fast : DiagTransportInterfaceBase
{
    private readonly ILogger<TransportBMW_Fast> _logger;

    public override UInt32 TransportVersion
    {
        get
        {
            return 209;
        }
    }

    public override string TransportName
    {
        get
        {
            return "BMW-FAST";
        }
    }

    public override int TransportMaxPayloadSize
    {
        get
        {
            int transportPayloadSize = 0;
            int cableMaxFrameSize = CableInterface.InterfaceMaxFrameSize;

            // BMW fast (with longframe support) transport has max PayloadSize of 0xfff - 7 (isoTP max i transport overhead) bytes
            if (cableMaxFrameSize > (0xff + 5)) // Cable supports LONG-Frames
            {
                // clamp if needed, then reduce by max transport overhead (6 + 1 bytes)
                transportPayloadSize = ((cableMaxFrameSize > 0xfff) ? (0xfff - 7) : (cableMaxFrameSize - 7));
            }
            else // cable does not support long frames
            {
                transportPayloadSize = cableMaxFrameSize - 5; // reduce by max transport overhead (4 + 1 bytes)
            }
            return transportPayloadSize;
        }
    }

    public TransportBMW_Fast(ILogger<TransportBMW_Fast> logger) : base(logger)
    {
        _logger = logger;
        // base init variables
        ParTimeoutStd = 0;
        ParTimeoutTelEnd = 0;
        ParInterbyteTime = 0;

        ParRequestTimeNr21 = 0;
        ParRequestTimeNr23 = 0;
        ParRetryNr21 = 0;
        ParRetryNr23 = 0;
        ParTimeoutNr78 = 0;
        ParRetryNr78 = 0;

        Nr78Dict.Clear();

        ParTimeoutTelEnd = 100; // was 100msec

        ParTimeoutStd = 1093; // prime was 1000msec UDS, UDS will overwrite, KWP2000 can (optionally)
        ParTimeoutNr78 = 0x2710; // 10 seconds, UDS will overwrite, KWP2000 can (optionally)
        ParRetryNr78 = 0xffff; // -> disabled, use endtime based.
    }

    public override bool Initialize(string mode, int baudRate)
    {
        int dataBits = 8;
        CableInterfaceBase.SerialParity parity = CableInterfaceBase.SerialParity.None;
        return CableInterface.Initialize(mode, baudRate, dataBits, parity, out HasAdapterEcho);
    }

    private bool SendData(byte[] sendData, int length, bool setDtr, int interbyteTime, int timeout)
    {
        if (length <= 0)
        {
            return true;
        }
        try
        {
            if (!SendData(sendData, length, setDtr, timeout))
            {
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SendData error");
            return false;
        }
        return true;
    }

    private bool SendData(byte[] sendData, int length, bool setDtr, int timeout)
    {
        if (length <= 0) return true;

        return CableInterface.InterfaceSendData(sendData, length, setDtr, 0, timeout);
    }

    private bool ReceiveData(byte[] receiveData, int offset, int length, ref uint responseTesterId, ref uint responseModuleId, int timeout, int timeoutTelEnd)
    {
        return ReceiveData(receiveData, offset, length, ref responseTesterId, ref responseModuleId, timeout, timeoutTelEnd, false);
    }

    private bool ReceiveData(byte[] receiveData, int offset, int length, ref uint responseTesterId, ref uint responseModuleId, int timeout, int timeoutTelEnd, bool logResponse)
    {
        responseTesterId = 0;
        responseModuleId = 0;
        if (length <= 0) return true;

        return CableInterface.InterfaceReceiveData(receiveData, offset, length, ref responseTesterId, ref responseModuleId, timeout, timeoutTelEnd);
    }

    // telegram length without checksum
    protected int TelLengthBmwFast(byte[] dataBuffer)
    {
        int telLength = dataBuffer[0] & 0x3F;
        if (telLength == 0) // with length byte
        {
            if (dataBuffer[3] == 0)
            {
                telLength = (dataBuffer[4] << 8) + dataBuffer[5] + 6;
            }
            else
            {
                telLength = dataBuffer[3] + 4;
            }
        }
        else
        {
            telLength += 3;
        }
        return telLength;
    }

    protected byte CalcChecksumBmwFast(byte[] data, int length)
    {
        byte sum = 0;
        for (int i = 0; i < length; i++)
        {
            sum += data[i];
        }
        return sum;
    }

    protected byte[] FormatFrameBmwFast(byte deviceId, byte moduleId, byte[] data)
    {
        int dataLength = data.Length;
        int headerLength = ((dataLength > 255) ? 6 : (dataLength > 60 ? 4 : 3));
        byte sof = (byte)((moduleId == 0xDF || moduleId == 0xEF) ? 0xC0 : 0x80); // moduleId 0xDF/0xEF means functional adressing, not physical

        byte[] bmwfastframe = new byte[headerLength + dataLength + 1]; // 3/4 or 6 byte header, 1 byte for checksum, not filled
        if (headerLength == 6) // with 2 length bytes
        {
            bmwfastframe[0] = sof;
            bmwfastframe[3] = 0x00;
            bmwfastframe[4] = (byte)(dataLength >> 8);
            bmwfastframe[5] = (byte)dataLength;
        }
        else if (headerLength == 4) // with length byte
        {
            bmwfastframe[0] = sof;
            bmwfastframe[3] = (byte)dataLength;
        }
        else // without length byte
        {
            bmwfastframe[0] = (byte)(sof + (dataLength & 0x3f));
        }
        bmwfastframe[1] = moduleId;
        bmwfastframe[2] = deviceId;
        Buffer.BlockCopy(data, 0, bmwfastframe, headerLength, dataLength);
        return bmwfastframe;
    }

    protected byte[] UnFormatFrameBmwFast(byte[] data, int dataLength)
    {
        byte[] UnformattedFrame;

        if (data[0] == 0x80 && data[3] == 0)
        {
            UnformattedFrame = new byte[dataLength - 7];
            Buffer.BlockCopy(data, 6, UnformattedFrame, 0, UnformattedFrame.Length);
        }
        else if (data[0] == 0x80)
        {
            UnformattedFrame = new byte[dataLength - 5];
            Buffer.BlockCopy(data, 4, UnformattedFrame, 0, UnformattedFrame.Length);
        }
        else
        {
            UnformattedFrame = new byte[dataLength - 4];
            Buffer.BlockCopy(data, 3, UnformattedFrame, 0, UnformattedFrame.Length);
        }
        return UnformattedFrame;
    }

    public override TransportErrorCodes SendReceive(byte deviceId, byte moduleId, byte[] snddata, ref uint responseTesterId, ref uint responseModuleId, out byte[] rcvdata, bool responseExpected = true, int maxExecutionTime = maxExecutionTimeDefault, int maxWaitingTimeMS = maxWaitingTimeMSDefault, bool enableLogging = true)
    {
        byte[] BmwFastFrame = FormatFrameBmwFast(deviceId, moduleId, snddata);
        byte[] BmwFastResponse = new byte[6 + 0xfff + 1]; // max frame length = hdr (6) + payload (0xFFF) + crc (1) (long frame)

        TransportErrorCodes TransStatus = TransBMW_Fast(BmwFastFrame, BmwFastFrame.Length, ref BmwFastResponse, out int BmwFastResponseLength, ref responseTesterId, ref responseModuleId, responseExpected, maxExecutionTime, maxWaitingTimeMS, enableLogging);
        if (BmwFastResponseLength > 0)
        {
            rcvdata = UnFormatFrameBmwFast(BmwFastResponse, BmwFastResponseLength);
        }
        else
        {
            rcvdata = new byte[0];
        }
        return TransStatus;
    }

    protected TransportErrorCodes TransBMW_Fast(byte[] sendData, int sendDataLength, ref byte[] receiveData, out int receiveLength, ref uint responseTesterId, ref uint responseModuleId, bool responseExpected, int maxExecutionTime, int maxWaitingTimeMS, bool enableLogging)
    {
        long executionStartTick = Stopwatch.GetTimestamp();
        responseTesterId = 0;
        responseModuleId = 0;

        int nrSendCount = 0;
    restart:
        receiveLength = 0;

        if (sendDataLength > 0)
        {
            Nr78Dict.Clear();
            int sendLength = TelLengthBmwFast(sendData);
            sendData[sendLength] = CalcChecksumBmwFast(sendData, sendLength);

            sendLength++;
            if (enableLogging) _logger.LogData(sendData, 0, sendLength, "Send");

            if (!SendData(sendData, sendLength, false, ParInterbyteTime, maxWaitingTimeMS))
            {
                if (enableLogging) _logger.LogTrace("*** Sending failed");
                return TransportErrorCodes.TRANSPORT_ERR_SND_IF;
            }
            if (HasAdapterEcho)
            {
                // remove remote echo
                if (!ReceiveData(receiveData, 0, sendLength, ref responseTesterId, ref responseModuleId, CableInterface.CableEchoTimeoutExtend(EchoTimeout, sendLength), ParTimeoutTelEnd))
                {
                    if (enableLogging) _logger.LogTrace("*** No echo received");
                    return TransportErrorCodes.TRANSPORT_ERR_ECHO_NA;
                }
                if (enableLogging) _logger.LogData(receiveData, 0, sendLength, "Echo");
                for (int i = 0; i < sendLength; i++)
                {
                    if (receiveData[i] != sendData[i])
                    {
                        if (enableLogging) _logger.LogTrace("*** Echo incorrect");
                        return TransportErrorCodes.TRANSPORT_ERR_ECHO_IV;
                    }
                }
            }
        }

        if (responseExpected == true)
        {
            for (; ; )
            {
                int timeout = Math.Min((Nr78Dict.Count > 0) ? ParTimeoutNr78 : ParTimeoutStd, maxWaitingTimeMS);

                // header byte
                int headLength = 4;
                if (!ReceiveData(receiveData, 0, headLength, ref responseTesterId, ref responseModuleId, timeout, ParTimeoutTelEnd))
                {
                    if (enableLogging) _logger.LogTrace("*** No header received");
                    return TransportErrorCodes.TRANSPORT_ERR_RSP_NOHEAD;
                }

                if ((receiveData[0] & 0xC0) != 0x80)
                {
                    if (enableLogging) _logger.LogData(receiveData, 0, 4, "Head");
                    if (enableLogging) _logger.LogTrace("*** Invalid header");
                    return TransportErrorCodes.TRANSPORT_ERR_RSP_HEAD_IV;
                }

                if ((receiveData[0] & 0x3F) == 0x00 && receiveData[3] == 0)
                {   // 2 byte length
                    if (!ReceiveData(receiveData, headLength, 2, ref responseTesterId, ref responseModuleId, timeout, ParTimeoutTelEnd))
                    {
                        if (enableLogging) _logger.LogTrace("*** No length received");
                        return TransportErrorCodes.TRANSPORT_ERR_RSP_HEAD_IV;
                    }
                    headLength += 2;
                }

                int recLength = TelLengthBmwFast(receiveData);
                if (!ReceiveData(receiveData, headLength, recLength - headLength + 1, ref responseTesterId, ref responseModuleId, ParTimeoutTelEnd, ParTimeoutTelEnd))
                {
                    if (enableLogging) _logger.LogTrace("*** No tail received");
                    return TransportErrorCodes.TRANSPORT_ERR_RSP_NOTAIL;
                }
                if (enableLogging) _logger.LogData(receiveData, 0, recLength + 1, "Resp");
                if (CalcChecksumBmwFast(receiveData, recLength) != receiveData[recLength])
                {
                    if (enableLogging) _logger.LogTrace("*** Checksum incorrect");
                    return TransportErrorCodes.TRANSPORT_ERR_RSP_CHKS;
                }

                int dataLen = receiveData[0] & 0x3F;
                int dataStart = 3;
                if (dataLen == 0)
                {   // with length byte
                    dataLen = receiveData[3];
                    dataStart++;
                }
                if ((dataLen == 3) && (receiveData[dataStart] == 0x7F))
                {
                    int nrRequestTime = 0;
                    int nrRetries = 0;
                    if (receiveData[dataStart + 2] == 0x21)
                    {
                        nrRequestTime = ParRequestTimeNr21;
                        nrRetries = ParRetryNr21;
                    }
                    if (receiveData[dataStart + 2] == 0x23)
                    {
                        nrRequestTime = ParRequestTimeNr23;
                        nrRetries = ParRetryNr23;
                    }
                    if (nrRequestTime > 0 && nrRetries > 0)
                    {
                        if (nrSendCount >= nrRetries)
                        {
                            if (enableLogging) _logger.LogTrace("*** NR21/23 nrRetries exceeded");
                            break;
                        }

                        if ((Stopwatch.GetTimestamp() - executionStartTick) < maxExecutionTime * TickResolMs)
                        {
                            if (enableLogging) _logger.LogFormat("*** NR21/23 maxExecutionTime exceeded");
                            break;
                        }
                        if (enableLogging) _logger.LogTrace("NR21/23 request");

                        Thread.Sleep(nrRequestTime);

                        nrSendCount++;
                        goto restart;
                    }
                }

                if ((dataLen == 3) && (receiveData[dataStart] == 0x7F) && (receiveData[dataStart + 2] == 0x78))
                {   // negative response 0x78
                    Nr78DictAdd(receiveData[2], enableLogging, executionStartTick, maxExecutionTime);
                }
                else
                {
                    Nr78DictRemove(receiveData[2], enableLogging);
                    break;
                }
                if (Nr78Dict.Count == 0)
                {
                    break;
                }
            }
            receiveLength = TelLengthBmwFast(receiveData) + 1;
        }
        else // no reponse processing (apart from echo)
        {
            receiveLength = 0;
            receiveData = new byte[0];
        }

        return TransportErrorCodes.TRANSPORT_ERR_NONE;
    }
}