﻿namespace UNI_Flash;

public enum SweArtType : byte
{
    BTL = 0x04,
    PST = 0x02,
    DST = 0x01,
    BTLD = 0x04,
    HW = 0x08,
    SWFL = 0x10,
    BLUP = 0x20,
    UNKNOWN = 0xC0,

    // Added for completion, may not be correct:
    HWAP = 0xFF, // not real?
    HWEL = 0xFE, // not real?
    SWFK = 0xFD, // not real? Use DST where possible
    CAFD = 0xFC, // not real?
    GWTB = 0xFB, // not real?
    FAFP = 0xFA, // not real?
    FLSL = 0xF9, // not real?
    FLUP = 0xF8, // not real?
    IBAD = 0xF7, // not real?
    TLRT = 0xF6, // not real?
}