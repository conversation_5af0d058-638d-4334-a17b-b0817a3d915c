﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using Microsoft.Extensions.Logging;

namespace DiagTransportInterface;

public class TransportENET : DiagTransportInterfaceBase
{
    private readonly ILogger<TransportENET> _logger;

    public TransportENET(ILogger<TransportENET> logger) : base(logger)
    {
        _logger = logger;
        Nr78Dict.Clear();

        ParRequestTimeNr21 = 0;
        ParRequestTimeNr23 = 0;
        ParRetryNr21 = 0;
        ParRetryNr23 = 0;

        ParTimeoutTelEnd = 100; // 100msec
        ParTimeoutStd = 541; // prime was 500msec UDS, UDS will overwrite, KWP2000 can (optionally)
        ParTimeoutNr78 = 0x2710; // 10 seconds, UDS will overwrite, KWP2000 can (optionally)
        ParRetryNr78 = 0xffff; // -> disabled, use endtime based.
    }

    public override UInt32 TransportVersion
    {
        get
        {
            return 1;
        }
    }

    public override string TransportName
    {
        get
        {
            return "ENET_RAW";
        }
    }

    public override int TransportMaxPayloadSize
    {
        get
        {
            return CableInterface.InterfaceMaxFrameSize;
        }
    }

    public override bool Initialize(string mode, int canBitRate)
    {
        if (mode == "CAN") // CAN
        {
            return CableInterface.Initialize(mode, canBitRate);
        }
        else // K-Line
        {
            return false;
        }
    }

    protected bool SendData(byte testerId, byte moduleId, byte[] sendData, int length, int timeout)
    {
        if (length <= 0) return true;

        uint testerIdShort = testerId;
        uint moduleIdShort = moduleId;

        return CableInterface.InterfaceSendData(sendData, length, testerIdShort, moduleIdShort, timeout);
    }

    protected bool ReceiveData(ref uint responseTesterId, ref uint responseModuleId, out byte[] receiveData, int timeout, int timeoutTelEnd)
    {
        return CableInterface.InterfaceReceiveData(out receiveData, ref responseTesterId, ref responseModuleId, timeout, timeoutTelEnd);
    }

    public override TransportErrorCodes SendReceive(byte testerId, byte moduleId, byte[] snddata, ref uint responseTesterId, ref uint responseModuleId, out byte[] rcvdata, bool responseExpected = true, int maxExecutionTime = maxExecutionTimeDefault, int maxWaitingTimeMS = maxWaitingTimeMSDefault, bool enableLogging = true)
    {
        TransportErrorCodes TransStatus = TransENET(testerId, moduleId, snddata, snddata.Length, ref responseTesterId, ref responseModuleId, out rcvdata, responseExpected, maxExecutionTime, maxWaitingTimeMS, enableLogging);
        return TransStatus;
    }

    protected TransportErrorCodes TransENET(byte testerId, byte moduleId, byte[] sendData, int sendDataLength, ref uint responseTesterId, ref uint responseModuleId, out byte[] receiveData, bool responseExpected, int maxExecutionTime, int maxWaitingTimeMS, bool enableLogging)
    {
        receiveData = new byte[0];
        var responseTesterIds = new List<uint> { };
        var responseModuleIds = new List<uint> { };

        long executionStartTick = Stopwatch.GetTimestamp();
        int nrSendCount = 0;

    restart:

        if (sendDataLength > 0)
        {
            Nr78Dict.Clear();
            int sendLength = sendDataLength;

            if (enableLogging) _logger.LogData(sendData, 0, sendLength, "Send");

            if (!SendData(testerId, moduleId, sendData, sendLength, timeout: maxWaitingTimeMS))
            {
                if (enableLogging) _logger.LogTrace("*** Sending failed");
                return TransportErrorCodes.TRANSPORT_ERR_SND_IF;
            }
        }

        if (responseExpected == true)
        {
            for (; ; )
            {
                int timeout = Math.Min((Nr78Dict.Count > 0) ? ParTimeoutNr78 : ParTimeoutStd, maxWaitingTimeMS);
                responseTesterId = 0;
                responseModuleId = 0;

                // header byte
                if (!ReceiveData(ref responseTesterId, ref responseModuleId, out receiveData, timeout, ParTimeoutTelEnd) || receiveData.Length == 0)
                {
                    if (enableLogging) _logger.LogTrace("*** No header received");
                    return TransportErrorCodes.TRANSPORT_ERR_RSP_NOHEAD;
                }

                if (enableLogging) _logger.LogData(receiveData, 0, receiveData.Length, "Resp");
                responseTesterIds.Add(responseModuleId);
                responseModuleIds.Add(responseTesterId);

                int dataLen = receiveData.Length;
                int dataStart = 0;

                if ((receiveData.Length == 3) && (receiveData[dataStart + 0] == 0x7F))
                {
                    int nrRequestTime = 0;
                    int nrRetries = 0;
                    if (receiveData[dataStart + 2] == 0x21)
                    {
                        nrRequestTime = ParRequestTimeNr21;
                        nrRetries = ParRetryNr21;
                    }
                    if (receiveData[dataStart + 2] == 0x23)
                    {
                        nrRequestTime = ParRequestTimeNr23;
                        nrRetries = ParRetryNr23;
                    }
                    if (nrRequestTime > 0 && nrRetries > 0)
                    {
                        if (nrSendCount >= nrRetries)
                        {
                            if (enableLogging) _logger.LogTrace("*** NR21/23 nrRetries exceeded");
                            break;
                        }

                        if ((Stopwatch.GetTimestamp() - executionStartTick) < maxExecutionTime * TickResolMs)
                        {
                            if (enableLogging) _logger.LogTrace("*** NR21/23 maxExecutionTime exceeded");
                            break;
                        }
                        if (enableLogging) _logger.LogTrace("NR21/23 request");

                        Thread.Sleep(nrRequestTime);

                        nrSendCount++;
                        goto restart;
                    }
                }

                if ((dataLen == 3) && (receiveData[dataStart] == 0x7F) && (receiveData[dataStart + 2] == 0x78))
                {   // negative response 0x78
                    Nr78DictAdd(moduleId, enableLogging, executionStartTick, maxExecutionTime);
                }
                else
                {
                    Nr78DictRemove(moduleId, enableLogging);
                    break;
                }
                if (Nr78Dict.Count == 0)
                {
                    break;
                }
            }
        }
        else // no reponse processing (apart from echo)
        {
            // receiveData = new byte[0];
        }

        return TransportErrorCodes.TRANSPORT_ERR_NONE;
    }
}