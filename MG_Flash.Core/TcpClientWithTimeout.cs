﻿#define DISABLE_NETWORK_NAGLE_ALGORITMH // disable Nagle algorithm (application side)

using Microsoft.Extensions.Logging;
using System;
using System.Net.Sockets;
using System.Threading;

namespace CableInterface;

public class TcpClientWithTimeout
{
    private readonly string _host;
    private readonly int _port;
    private readonly int _timeoutMilliseconds;
    private readonly ILogger<TcpClientWithTimeout> _logger;
    private TcpClient _connection;
    private bool _connected;
    private Exception _exception;

    public TcpClientWithTimeout(string host, int port, int timeoutMilliseconds, ILogger<TcpClientWithTimeout> logger)
    {
        _host = host;
        _port = port;
        _timeoutMilliseconds = timeoutMilliseconds;
        _logger = logger;
    }

    public TcpClient Connect()
    {
        // kick off the thread that tries to connect
        _connected = false;
        _exception = null;
        Thread thread = new Thread(BeginConnect)
        {
            IsBackground = true
        };
        // So that a failed connection attempt
        // wont prevent the process from terminating while it does the long timeout
        thread.Start();

        // wait for either the timeout or the thread to finish
        thread.Join(_timeoutMilliseconds);

        if (_connected)
        {
            // it succeeded, so return the connection
            return _connection;
        }
        if (_exception != null)
        {
            // it crashed, so return the exception to the caller
            throw _exception;
        }
        else
        {
            throw new TimeoutException("Connect timeout");
        }
    }

    private void BeginConnect()
    {
        try
        {
            _connection = new TcpClient();
            _connection.NoDelay = true; // disable Nagle algorithm
            _connection.Client.NoDelay = true;
            _connection.Connect(_host, _port);
            // record that it succeeded, for the main thread to return to the caller
            _connected = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Begin Connect exception");
            // record the exception for the main thread to re-throw back to the calling code
            _exception = ex;
        }
    }
}