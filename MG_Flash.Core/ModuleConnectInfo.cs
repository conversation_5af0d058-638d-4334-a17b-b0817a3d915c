﻿/// OPTIONS FOR UDS BRANCH:
#define UDS_WRITE_VIN
#define USE_UDS_PROTOCOL
#define BOSCH_MG1_UNLOCK_BYPASS
// #define BOSCH_MG1_UNLOCK_BYPASS
#define LMV_XCP_OVER_FLEX_UNLOCK

/// GENERIC OPTIONS (Both Branches)
// #define DOWNLOAD_BACKSTEP_AUTOFAIL

using DiagProtocolInterface;

namespace UNI_Flash;

public struct ModuleConnectInfo
{
    public DiagProtocolBase.ModuleIds moduleId;
    public string serialNo;
#if USE_UDS_PROTOCOL
    public int SGBDIndex;
#else
            public int transportComboIdxLastUsed;
            public string BMWPartNo;
            public string VariantIndexS;
#endif
}