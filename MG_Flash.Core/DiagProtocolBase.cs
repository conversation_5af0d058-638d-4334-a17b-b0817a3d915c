﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using CableInterface;
using DiagTransportTranslationWrapIf;
using Microsoft.Extensions.Logging;

namespace DiagProtocolInterface;

public class DiagProtocolBase : IDisposable
{
    private readonly ILogger _logger;
    protected static byte _testerId = (byte)TesterInterfaceIds.D_CAN; // default (D_CAN)
    protected static byte _moduleId = (byte)ModuleIds.DME_MASTER; // default (DME_MASTER)
    protected static string _modulename;
    protected static string _transportName;
    protected static string _interfaceType;
    protected static BaudRates _baudRate;
    protected const int defaultCmdResendMaxRetries = 3;
    protected int cmdResendMaxRetries = defaultCmdResendMaxRetries;

    protected bool baudRateHasBeenChangedToNonStandard;

    // TP handling related
    protected static readonly long TickResolMs = Stopwatch.Frequency / 1000;

    protected static long LastTPCmdTick = DateTime.MinValue.Ticks;
    protected static readonly int cycleTimeTPmsg = 2000; // default 2s < P3 time, P3 typical = 5 seconds
    // EOF TP handling related

    protected delegate DiagTransportTranslationWrapIfBase.TransportWrapperErrorCodes SendReceiveDelegate(byte deviceId, byte moduleId, byte[] snddata, ref uint responseTesterId, ref uint responseModuleId, out byte[] rcvdata, bool responseExpected = true, int maxExecutionTime = DiagTransportTranslationWrapIfBase.maxExecutionTimeDefault, int maxWaitingTimeMS = DiagTransportTranslationWrapIfBase.maxWaitingTimeMSDefault, bool enableLogging = true);

    // all frames should contain data and should not have dependance on eachother
    protected delegate DiagTransportTranslationWrapIfBase.TransportWrapperErrorCodes SendReceivePipelined(byte testerId, List<DiagTransportTranslationWrapIfBase.PipelineSlot> cmdPipeline, out List<DiagTransportTranslationWrapIfBase.PipelineSlot> rspPipeline, bool responseExpected = true, int maxExecutionTime = DiagTransportTranslationWrapIfBase.maxExecutionTimeDefault, int maxWaitingTimeMS = DiagTransportTranslationWrapIfBase.maxWaitingTimeMSDefault, bool enableLogging = true);

    protected DiagTransportTranslationWrapIfBase DiagTransportInterface;
    protected SendReceiveDelegate SendReceiveFunc;
    protected SendReceivePipelined SendReceivePipelinedFunc;

    public DiagProtocolBase(ILogger logger)
    {
        _logger = logger;
    }

    public enum ModuleIds : byte
    {
        ZGW = 0x10, // FEM-GW, BDC15, D_CAN/Ethernet
        DME_MASTER = 0x12, // A_FlexRay
        DME = DME_MASTER, // A_FlexRay
        DME_SLAVE = 0x13, // A_FlexRay
        CAS = 0x40, // FEM-Body, ZSG_CAN
        DME861_BOOTCNTRL = 0xA0,
        All = 0xDF, // Every module sees this message
        EGS = 0x18 // Likely the TCU
    }

    public enum FunktionalAddressingIds : byte
    {
        KWP2000_ALL = 0xEF,
        KWP2000_MOST_ALL = 0xEC,
        UDS_ALL = 0xDF,
        ALL_K_CAN = 0xE9,
        ALL_PT_CAN = 0xEA,
        ALL_MOST = 0xEC,
    }

    public enum TesterInterfaceIds : byte
    {
        D_CAN = 0xF1, // D-CAN Tester
        TELETESTER = 0xF2, // Teletester
        ETHERNET = 0xF4, // Ethernet Tester
        OBD = 0xF5, // on Ethernet
        FLEXRAY = 0xF9, // Diagnosetool FlexRay
        MOST = 0xFA, // Diagnosetool MOST
        FA_CAN = 0xFB, // Diagnosetool FA-CAN
        K_CAN = 0xFC, // Diagnosetool B-CAN
        BODY_CAN = 0xFD, // Diagnosetool K-CAN
    }

    public enum BaudRates : uint
    {
        CAN_500K = 500000,
        CAN_100K = 100000,
        KLINE_115200 = 115200,
        KLINE_57600 = 57600,
        KLINE_38400 = 38400,
        KLINE_19200 = 19200,
        KLINE_9600 = 9600,
    }

    public ModuleIds ModuleId
    {
        get { return (ModuleIds)_moduleId; }
    }

    public string ModuleName
    {
        get { return _modulename; }
    }

    public string InterfaceMode
    {
        get { return DiagTransportInterface.CableInterface.InterfaceMode; }
    }

    public string InterfaceName
    {
        get { return DiagTransportInterface.CableInterface.InterfaceName; }
    }

    public string TransportName
    {
        get { return DiagTransportInterface.TransportName; }
    }

    public int ProtocolMaxPayload
    {
        get { return DiagTransportInterface.TransportMaxPayloadSize; }
    }

    public int GetRepeatCount()
    {
        return cmdResendMaxRetries;
    }

    public void SetRepeatCount(int repeatCount)
    {
        cmdResendMaxRetries = repeatCount;
        _logger.LogTrace("setRepeatCount - repeatCount changed to {0}", repeatCount);
    }

    public static int GetDefaultRepeatCount()
    {
        return defaultCmdResendMaxRetries;
    }

    public void ResetRepeatCount()
    {
        cmdResendMaxRetries = defaultCmdResendMaxRetries;
        _logger.LogTrace("resetRepeatCount - repeatCount reverted to default {0}", defaultCmdResendMaxRetries);
    }

    public static BaudRates GetBaudrate()
    {
        return _baudRate;
    }

    public static string GetTransportName()
    {
        return _transportName;
    }

    // set to non standard baudrate, does not change for CAN
    public bool SetBaudrate(BaudRates baudRate)
    {
        baudRateHasBeenChangedToNonStandard = false;

        if (InterfaceMode.Contains("K-Line"))
        {
            baudRateHasBeenChangedToNonStandard = DiagTransportInterface.CableInterface.SetBaudrate((int)baudRate);
        }
        else
        {
            _logger.LogWarning("Change baudrate not allowed for {0} cable using phy {1}", InterfaceName, InterfaceMode);
        }
        return baudRateHasBeenChangedToNonStandard;
    }

    // resets baudrate back to default
    public bool ResetBaudrate()
    {
        if (InterfaceMode.Contains("K-Line") && baudRateHasBeenChangedToNonStandard)
        {
            _logger.LogTrace("Baudrate reset for {0} cable using phy {1} to {2}", InterfaceName, InterfaceMode, _baudRate);
            baudRateHasBeenChangedToNonStandard = !(DiagTransportInterface.CableInterface.SetBaudrate((int)_baudRate));
            return !baudRateHasBeenChangedToNonStandard;
        }
        else
        {
            return true;
        }
    }

    public BmwEnet AttachCable(string host, int port, ILoggerFactory loggerFactory, bool ignoreLogs = false)
    {
        BmwEnet result;

        try
        {
            result = new BmwEnet(host, port, loggerFactory, ignoreLogs);
        }
        catch (Exception e)
        {
            _logger.LogWarning(e.Message, ignoreLogs);
            return null;
        }

        if (!result.Connect()) // error, no cable present;
        {
            _logger.LogWarning($"Unable to connect to {result.InterfaceName} Central-Gateway, exiting", ignoreLogs);
            return null;
        }

        return result;
    }

    public bool AttachCableToTransportWrapper(CableInterfaceBase CableInterface, ILoggerFactory loggerFactory, string transportName = "BMW-FAST", BaudRates baudRate = BaudRates.CAN_500K, string mode = "CAN", string translation = "PASSTHROUGH")
    {
        _testerId = (byte)(CableInterface.InterfaceName.StartsWith("ENET") ? TesterInterfaceIds.ETHERNET : TesterInterfaceIds.D_CAN);
        _interfaceType = mode;
        _baudRate = baudRate;
        _transportName = (CableInterface.InterfaceName.StartsWith("ENET") ? "ENET_RAW" : transportName);

        baudRateHasBeenChangedToNonStandard = false;

        switch (translation)
        {
            case "PASSTHROUGH": DiagTransportInterface = new TransportTranslationWrapPassThrough(); break;
            default: return false;
        }

        if (!DiagTransportInterface.AttachTransportToTransportWrapper(_transportName, loggerFactory)) return false;

        SendReceiveFunc = DiagTransportInterface.SendReceive;
        SendReceivePipelinedFunc = DiagTransportInterface.SendReceivePipelined;

        _transportName += (" " + translation);

        DiagTransportInterface.AttachCableToTransport(CableInterface);

        if (!DiagTransportInterface.Initialize(_interfaceType, (int)_baudRate)) // error could not initialize cable
        {
            _logger.LogWarning("Could not initialize {0} cable for phy {1} {2:F1}kb/s", InterfaceName, mode, (double)baudRate / 1000);
            return false;
        }
        return true;
    }

    public int GetInterfaceResetTimeout()
    {
        return DiagTransportInterface.CableInterface.InterfaceResetTimeout;
    }

    public bool CablePollUntilAvailable()
    {
        for (int i = 0; i < 15; i++) // poll for 1.5 seconds
        {
            DiagTransportInterface.CableInterface.FlushBuffers();
            if (DiagTransportInterface.CableInterface.CableConnectedAndAlive()) return true;
            // no need for additonal delay, the echo poll of internal command already waits 100msec
        }
        return false;
    }

    public bool CableResetExecute(bool disableTimeoutPrompt)
    {
        if (!disableTimeoutPrompt && DiagTransportInterface.CableInterface.InterfaceReadTimeout > 0)
        {
            // if cable gets lost it has an internal InterfaceReadTimeout second timeout in which it waits for reponse from module (during which it is frozen to us)
            // wait until it has passed, then reset it (port only not the cable itself)
            _logger.LogWarning("{0} Sleep until InterfaceReadTimeout (cable internal) is expired {1}ms", InterfaceName, DiagTransportInterface.CableInterface.InterfaceReadTimeout);
            System.Threading.Thread.Sleep(DiagTransportInterface.CableInterface.InterfaceReadTimeout);
        }

        if (DiagTransportInterface.CableInterface.CableReset() && DiagTransportInterface.CableInterface.FlushBuffers())
        {
            _logger.LogInformation("{0} CableReset and flushBuffers successfull", InterfaceName);
            return true;
        }
        _logger.LogWarning("{0} CableReset or flushBuffers failed", InterfaceName);
        return false;
    }

    protected static bool CommandStatusOk(byte[] snddata, byte[] rcvdata)
    {
        return (rcvdata.Length >= 1 && rcvdata[0] == (snddata[0] + 0x40) && rcvdata[0] != 0x7f);
    }

    protected static bool CommandStatusNRC(byte[] rcvdata)
    {
        return (rcvdata.Length == 3 && rcvdata[0] == 0x7f);
    }

    protected static bool CommandStatusCustomCodeWDBIResponse(byte[] rcvdata)
    {
        return rcvdata[2] == 0x69;
    }

    protected static byte[] ResponseStripLeading(byte[] data, byte leadingToStrip)
    {
        if (leadingToStrip > data.Length) return Array.Empty<byte>(); // error cannot strip more than available, return zero byte[]

        int strippedLength = data.Length - leadingToStrip;
        byte[] strippedFrame = new byte[strippedLength];
        Buffer.BlockCopy(data, leadingToStrip, strippedFrame, 0, strippedLength);
        return strippedFrame;
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            // DiagTransportInterface?.CableInterface?.Dispose();
            DiagTransportInterface?.Dispose();
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}