﻿/// OPTIONS FOR UDS BRANCH:
#define UDS_WRITE_VIN
#define USE_UDS_PROTOCOL
// #define FULL_COMPAT_CHECK // if not defined -> Compat check skip last 3 digits (minor version/revision)
#define LMV_XCP_OVER_FLEX_UNLOCK

/// OPTIONS FOR KWP2000 BRANCH:
#define KWP2000_WRITE_VIN
// #define UPLOAD_METHOD_CMD35_CMD36_CMD37
// #define GRACEFULL_READ_MODULE_EXIT

/// GENERIC OPTIONS (Both Branches)
// #define DOWNLOAD_BACKSTEP_AUTOFAIL

using NDesk.Options;

namespace UNI_Flash;

// from https://stackoverflow.com/questions/14741217/how-can-i-get-ndesk-to-parse-multi-arg-lists-at-the-command-line
public class MultiOptionSet : OptionSet
{
    private string lastArg;
    private Option lastOption;

    protected override bool Parse(string argument, OptionContext c)
    {
        // based on example in http://www.ndesk.org/doc/ndesk-options/NDesk.Options/Option.html#M:NDesk.Options.Option.OnParseComplete(NDesk.Options.OptionContext)

        bool haveParts = GetOptionParts(argument, out string f, out string n, out string s, out string v);

        // reset placeholder for next multiple if we are looking at a flagged argument name
        if (haveParts)
        {
            lastArg = f + n;
        }
        // are we after a flagged argument name, without parts (meaning a value)
        else
        {
            // remember for next time, in case it's another value
            if (null != c.Option) lastOption = c.Option;
            // but if the 'last arg' wasn't already provided, we reuse the one we set last time
            else
            {
                c.Option = lastOption;
                c.OptionName = lastArg;
            }

            c.OptionValues.Add(argument); // add the value to be invoked
            c.Option.Invoke(c); // perform the 'setter'
            return true;
        }

        return base.Parse(argument, c);
    }
}