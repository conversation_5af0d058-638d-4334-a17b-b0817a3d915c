﻿using System;
using System.Collections.Generic;

namespace DiagTransportTranslationWrapIf;

public class TransportTranslationWrapPassThrough : DiagTransportTranslationWrapIfBase
{
    public override uint TransportVersion
    { get { return _DiagTransportInterface.TransportVersion; } }

    public override string TransportName
    { get { return _DiagTransportInterface.TransportName; } }

    public override int TransportMaxPayloadSize
    { get { return _DiagTransportInterface.TransportMaxPayloadSize; } }

    public override ushort GetP2max()
    {
        return _DiagTransportInterface.GetP2max();
    }

    public override ushort GetP2Starmax()
    {
        return _DiagTransportInterface.GetP2Starmax();
    }

    public override bool Initialize(string mode, int baudRate)
    {
        return _DiagTransportInterface.Initialize(mode, baudRate); ;
    }

    public override TransportWrapperErrorCodes SendReceive(byte deviceId, byte moduleId, byte[] snddata, ref uint responseTesterId, ref uint responseModuleId, out byte[] rcvdata, bool responseExpected = true, int maxExecutionTime = 10000, int maxWaitingTimeMS = 20000, bool enableLogging = true)
    {
        return (TransportWrapperErrorCodes)_DiagTransportInterface.SendReceive(deviceId, moduleId, snddata, ref responseTesterId, ref responseModuleId, out rcvdata, responseExpected, maxExecutionTime, maxWaitingTimeMS, enableLogging);
    }

    public override TransportWrapperErrorCodes SendReceivePipelined(byte testerId, List<PipelineSlot> cmdPipeline, out List<PipelineSlot> rspPipeline, bool responseExpected = true, int maxExecutionTime = maxExecutionTimeDefault, int maxWaitingTimeMS = maxWaitingTimeMSDefault, bool enableLogging = true)
    {
        throw new NotImplementedException();
    }

    public override void SetTimingParameters(ushort P2max, uint P2star)
    {
        _DiagTransportInterface.SetTimingParameters(P2max, P2star);
    }
}