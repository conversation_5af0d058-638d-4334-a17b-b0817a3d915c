﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<StringEncryption>hash</StringEncryption>
		<ResourceEncryption>true</ResourceEncryption>
		<ControlFlowObfuscation>if=on;switch=on;case=on;call=on</ControlFlowObfuscation>
		<ControlFlowIterations>3</ControlFlowIterations>		
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="MgFlasher.Changesets.SupportedEcu" Version="1.0.4" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.1" />		
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Condition="'$(Configuration)' == 'Release' " Include="Babel.Obfuscator" Version="10.9.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Turbocraft.UCL.BMW.Compression" Version="1.0.2" />
	</ItemGroup>

</Project>
