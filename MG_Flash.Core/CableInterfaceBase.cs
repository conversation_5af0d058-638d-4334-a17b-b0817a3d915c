﻿using System;
using System.Text;
using System.Globalization;
using Microsoft.Extensions.Logging;

namespace CableInterface;

public abstract class CableInterfaceBase
{
    private readonly ILogger _logger;
    private static readonly CultureInfo Culture = CultureInfo.CreateSpecificCulture("en");

    public CableInterfaceBase(ILogger logger)
    {
        _logger = logger;
    }

    public abstract string InterfaceMode { get; }

    public abstract UInt32 InterfaceVersion { get; }

    public abstract string InterfaceName { get; }

    public abstract int InterfaceMaxFrameSize { get; }

    public abstract int InterfaceReadTimeout { get; }

    public abstract int InterfaceResetTimeout { get; }

    public abstract bool Connect();

    public abstract bool CableReset();

    public abstract bool CableConnectedAndAlive();

    public virtual int CableEchoTimeoutExtend(int echoStandard, int echoLength)
    {
        return echoStandard;
    }

    public virtual bool Initialize(string mode, int baudRate, int dataBits, SerialParity parity, out bool cableHasEcho)
    {
        cableHasEcho = true;
        return false;
    }

    public virtual bool Initialize(string mode, int canBitRate)
    {
        return false;
    }

    public virtual bool SetBaudrate(int baudRate)
    {
        return false;
    }

    public abstract bool FlushBuffers();

    public virtual bool InterfaceSendData(byte[] sendData, int length, bool setDtr, double dtrTimeCorr, int timeout)
    {
        return false;
    }

    public virtual bool InterfaceReceiveData(byte[] receiveData, int offset, int length, ref uint responseTesterId, ref uint responseModuleId, int timeout, int timeoutTelEnd)
    {
        responseTesterId = 0;
        responseModuleId = 0;
        return false;
    }

    public virtual bool InterfaceSendData(byte[] sendData, int length, uint sendId, uint responseId, int timeout)
    {
        return false;
    }

    public virtual bool InterfaceReceiveData(out byte[] receiveData, ref uint responseTesterId, ref uint responseModuleId, int timeout, int timeoutTelEnd)
    {
        responseTesterId = 0;
        responseModuleId = 0;
        receiveData = new byte[0];
        return false;
    }

    public enum SerialParity
    {
        None = 0,
        Odd = 1,
        Even = 2,
        Mark = 3,
        Space = 4,
    }

    public void LogFormat(string format, params object[] args)
    {
        for (int i = 0; i < args.Length; i++)
        {
            if (args[i] == null)
            {
                continue;
            }
            if (args[i] is string)
            {
                args[i] = "'" + (string)args[i] + "'";
            }
            if (args[i] is byte[] argArray)
            {
                StringBuilder stringBuilder = new StringBuilder(argArray.Length);
                foreach (byte arg in argArray)
                {
                    stringBuilder.Append(string.Format(Culture, "{0:X02} ", arg));
                }

                args[i] = "[" + stringBuilder + "]";
            }
        }
        _logger.LogInformation(string.Format(format, args));
    }

    public void LogData(byte[] data, int offset, int length, string info)
    {
        StringBuilder stringBuilder = new StringBuilder(length);
        for (int i = 0; i < length; i++)
        {
            stringBuilder.Append(string.Format(Culture, "{0:X02} ", data[offset + i]));
        }

        _logger.LogInformation(string.Format(" (" + info + "): ({0}) {1}", length, stringBuilder));
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}