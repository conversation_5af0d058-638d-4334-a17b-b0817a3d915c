﻿namespace UNI_Flash;

public enum ProcessClassType : byte
{
    HWEL = 0x01, // Hardware (Elektronik)
    HWAP = 0x02, // Hardwareauspraegung
    HWFR = 0x03, // Hardwarefarbe
    CAFD = 0x05, // Codierdaten
    BTLD = 0x06, // Bootloader
    SWFL = 0x08, // Software ECU Speicherimage
    SWFF = 0x09, // Flash File Software
    SWPF = 0x0A, // Pruefsoftware
    ONPS = 0x0B, // Onboard Programmiersystem
    FAFP = 0x0F, // FA2FP
    TLRT = 0x1A, // Temporaere Loeschroutine
    TPRG = 0x1B, // Temporaere Programmierroutine
    FLSL = 0x07, // Flashloader Slave
    IBAD = 0x0C, // Interaktive Betriebsanleitung Daten
    FCFA = 0x10, // Freischaltcode Fahrzeug-Auftrag
    BLUP = 0x1C, // Bootloader-Update Applikation
    FLUP = 0x1D, // Flashloader-Update Applikation
    SWUP = 0xC0, // Software-Update Package
    SWIP = 0xC1, // Index Software-Update Package
    ENTD = 0xA0, // Entertainment Daten
    NAVD = 0xA1, // Navigation Daten
    FCFN = 0xA2, // Freischaltcode Funktion
    GWTB = 0x04, // Gateway-Tabelle
    SWFK = 0x0D, // BEGU: Detaillierung auf SWE-Ebene
    UNKNOWN = 0xFF, // ungueltig
}