﻿namespace DiagProtocolUtils;

public class ProtocolUtils
{
    public static uint btldMayorIdOf(byte[] btldId)
    {
        return (uint)((btldId[1] << 24) + (btldId[2] << 16) + (btldId[3] << 8) + btldId[4]);
    }

    public static byte[] btldMayorIdBytesOf(byte[] btldId)
    {
        byte[] btldIdMayor = new byte[4];
        System.Array.Copy(btldId, 1, btldIdMayor, 0, 4);
        return btldIdMayor;
    }
}