﻿/// OPTIONS FOR UDS BRANCH:
#define UDS_WRITE_VIN
#define USE_UDS_PROTOCOL
#define BOSCH_MG1_UNLOCK_BYPASS
// #define BOSCH_MG1_UNLOCK_BYPASS
#define LMV_XCP_OVER_FLEX_UNLOCK

/// GENERIC OPTIONS (Both Branches)
// #define DOWNLOAD_BACKSTEP_AUTOFAIL

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace UNI_Flash;

public struct ModuleInfo
{
    public bool bootMode;
    public string progDate;
    public UnlockCompanyName unlockCompany;
    public string unlocked;
    public int SGBDIndex;
    public string name;
    public string supplierTypeName;
    public ProcessorArchitectureType ProcessorArchitectureType;
    public string serialNo;
    public bool master;
    public bool fakeEcuData;
    public bool dualEcus;
    public DiagProtocolInterface.DiagProtocolBase.ModuleIds moduleId;
    public string VIN;
    public IntegrationStepInfo IStep;
    public List<IntegrationStepInfo> BdcIStepList;
    public string A2Lstring;
    public string Logistikbereich;
    public string customCodeVersionInstalled;
    public string Region;
    public bool moduleHasCoding;
    public bool readMemoryByAddressAvailableInBoot;
    public byte readMemoryByAddressChunkSizeInclCmdRsp;
    public int fullBinSize;

    public ushort[] codingRecordIds;
    public ushort[] timeLimitedCodingRecordIds;
    public byte[] btldId;
    public byte[] cafdId;
    public byte[] swflId;
    public byte[] swfkId;
    public byte[] currentFingerprint;
    public List<SweVersion> sweVersions;
    public List<SweVersion> bdcSweVersions;
    public List<EcuSvkHistoryItem> sweBackupList;
    public List<DifVariante> difVarianteList;
    public string calid;
    public string cvn;
    public string fullEngineCode;
    public string engineCode;
    public string engineConfiguration;
    public string engineProductionYears;
    public string engineDisplacment;
    public string bootctrlVersion;
    public string manufacturingDate;
    public string flashHistoryItemId;

    public string SegmentDifVariante(string sweArt)
    {
        if (string.IsNullOrWhiteSpace(sweArt))
        {
            return string.Empty;
        }
        if (difVarianteList == null || !difVarianteList.Any())
        {
            return string.Empty;
        }

        var difVariante = difVarianteList.FirstOrDefault(x => x.SweArt.ToUpper().Equals(sweArt.ToUpper()));
        if (difVariante == null || !difVariante.Data.Any())
        {
            return string.Empty;
        }

        return Encoding.Default.GetString(difVariante.Data);
    }

    public SweVersion SegmentSweVersion(SweArtType sweArt)
    {
        if (sweArt.Equals(SweArtType.UNKNOWN))
        {
            return new SweVersion();
        }
        if (sweVersions == null || !sweVersions.Any())
        {
            return new SweVersion();
        }

        var sweVersion = sweVersions.FirstOrDefault(x => x.SweArt.Equals(sweArt));
        if (sweVersion == null)
        {
            return new SweVersion();
        }

        return sweVersion;
    }

    [Obsolete]
    public List<string> svkActualXweList;

    [Obsolete]
    public string processorType;
}