﻿// OPTIONS FOR UDS BRANCH:
#define UPDATE_TRANSPORT_TIMING_PARAMETERS // enabled, cable delays factored in

using DiagTransportTranslationWrapIf;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using DiagTransportInterface;
using Microsoft.Extensions.Logging;
using System.Linq;

namespace DiagProtocolInterface;

//
// BMW specific UDS protocol, many parts in common with generic UDS from
// ISO 15765-3 and 4 Unified diagnostic services (UDS on CAN) but not everything
//
// TODO: locate the BMW specific documentation
//
public class UDS_BMW : DiagProtocolBase
{
    private readonly ILogger<UDS_BMW> _logger;
    private static long totalRoundtripDelayMs = DateTime.MinValue.Ticks;
    private const long totalRoundtripDelayMinMs = 500; // [500 ms]

    private UDS_DiagMode activeDiagSession = UDS_DiagMode.DEFAULT;

    private int udsMaxExecutionTimeMs = DiagTransportTranslationWrapIfBase.maxExecutionTimeDefault;
    private int maxWaitingTimeMS = DiagTransportTranslationWrapIfBase.maxWaitingTimeMSDefault;
    private const int moduleNonResponseCmdMicroSnoozeTimeMs = 100; // non response cmd, some microsnooze is needed before transmit of next (using 2 * default P2)

    private const bool enableLogging = true;

    public UDS_BMW(ILogger<UDS_BMW> logger) : base(logger)
    {
        _logger = logger;
    }

    public bool SimpleModuleInitialization(ModuleIds moduleId, bool ignoreLogs = false, bool sendTesterPresent = false)
    {
        _logger.LogInfo($"Simple Initialization of {moduleId}", ignoreLogs);

        _modulename = string.Format("{0}", moduleId);
        UpdateActiveModuleId(moduleId);

        if (sendTesterPresent)
        {
            if (TesterPresent(UDS_suppressPositiveResponse.No) != UDS_ErrorCodes.UDS_NRC_NONE)
            {
                var connectMsgNok = $"Could not initiate module {_modulename} 0x{_moduleId:X02} {_interfaceType} communication (UDS_BMW transport {_transportName}), trying full module initialization...";
                _logger.LogInfo(connectMsgNok, ignoreLogs);
                return FullModuleInitialization(moduleId, ignoreLogs: ignoreLogs, sendTesterPresent: sendTesterPresent);
            }
            else
            {
                var connectMsgOk = $"{DiagTransportInterface?.CableInterface?.InterfaceMode} communication with module {_modulename} 0x{_moduleId:X02} initiated successfully ({DiagTransportInterface?.CableInterface?.InterfaceName} UDS_BMW transport {_transportName})";
                _logger.LogInfo(connectMsgOk, ignoreLogs);
            }
        }
        else
        {
            var moduleIdChanged = $"Changed active module to {_modulename} 0x{_moduleId:X02} with {DiagTransportInterface?.CableInterface?.InterfaceMode} communication ({DiagTransportInterface?.CableInterface?.InterfaceName} UDS_BMW transport {_transportName})";
            _logger.LogInfo(moduleIdChanged, ignoreLogs);
        }

        _logger.LogInfo($"Module [{moduleId}] successfully (simply) initialized", ignoreLogs);
        return true;
    }

    public bool FullModuleInitialization(ModuleIds moduleId, bool ignoreLogs = false, bool sendTesterPresent = true)
    {
        _logger.LogInfo($"Full Initialization of {moduleId}", ignoreLogs);

        _modulename = string.Format("{0}", moduleId);
        UpdateActiveModuleId(moduleId);

        ushort referencedActiveDiagSession = 0;

        if (FetchModuleActiveDiagnosticSession(ref referencedActiveDiagSession) != UDS_ErrorCodes.UDS_NRC_NONE)
        {
            _logger.LogError($"Error: Could not read active diag session from module");
            activeDiagSession = UDS_DiagMode.UNGUELTIG;
        }

        var connectMsgOk = $"{DiagTransportInterface?.CableInterface?.InterfaceMode} communication with module {_modulename} 0x{_moduleId:X02} initiated successfully ({DiagTransportInterface?.CableInterface?.InterfaceName} UDS_BMW transport {_transportName})";
        var connectMsgNok = $"Could not initiate module {_modulename} 0x{_moduleId:X02} {_interfaceType} communication (UDS_BMW transport {_transportName}), exiting";

        if (moduleId != ModuleIds.DME861_BOOTCNTRL
            && activeDiagSession != UDS_DiagMode.DEFAULT)
        {
            ushort p2Max = 0;
            uint p2Star = 0;

            long diagSessionControlStartTime = Stopwatch.GetTimestamp();

            if (DiagnosticSessionControl(UDS_DiagMode.DEFAULT, ref p2Max, ref p2Star) != UDS_ErrorCodes.UDS_NRC_NONE)
            {
                _logger.LogInfo(connectMsgNok, ignoreLogs);
                return false;
            }
            else
            {
                totalRoundtripDelayMs = (Stopwatch.GetTimestamp() - diagSessionControlStartTime) / TickResolMs;
                _logger.LogInfo(string.Format("UDS Measured RoundtripDelay {0}ms", totalRoundtripDelayMs), ignoreLogs);
                _logger.LogInfo(connectMsgOk, ignoreLogs);
                SetTimingParameters(p2Max, p2Star);
            }
        }
        else if (sendTesterPresent)
        {
            if (TesterPresent(UDS_suppressPositiveResponse.No) != UDS_ErrorCodes.UDS_NRC_NONE)
            {
                _logger.LogInfo(connectMsgNok, ignoreLogs);
                return false;
            }
            else
            {
                _logger.LogInfo(connectMsgOk, ignoreLogs);
            }
        }
        else
        {
            var moduleIdChanged = $"Changed active module to {_modulename} 0x{_moduleId:X02} with {DiagTransportInterface?.CableInterface?.InterfaceMode} communication ({DiagTransportInterface?.CableInterface?.InterfaceName} UDS_BMW transport {_transportName})";
            _logger.LogInfo(moduleIdChanged, ignoreLogs);
        }

        _logger.LogInfo($"Module [{moduleId}] successfully (fully) initialized", ignoreLogs);
        return true;
    }

    public void SetTimingParameters(ushort p2Max, uint p2Star)
    {
        ushort p2MaxPlusRoundtrip = (ushort)(totalRoundtripDelayMinMs + p2Max);
        uint p2StarPlusRoundtrip = (uint)(totalRoundtripDelayMinMs + p2Star);
        _logger.LogTrace(string.Format("UDS setTimingParameters P2 {0}ms P2* {1}ms +RoundTripDelayMin P2 {2}ms P2* {3}ms", p2Max, p2Star, p2MaxPlusRoundtrip, p2StarPlusRoundtrip));
        DiagTransportInterface.SetTimingParameters(p2MaxPlusRoundtrip, p2StarPlusRoundtrip);
        _logger.LogTrace(string.Format("UDS module sessionTimingParameters P2 {0}ms, P2* {1}ms", p2Max, p2Star));
    }

    public void UpdateActiveModuleId(ModuleIds moduleId)
    {
        _moduleId = (byte)moduleId;
    }

    public UDS_ErrorCodes FetchModuleActiveDiagnosticSession(ref ushort _activeDiagSession)
    {
        UDS_ErrorCodes cmdStatus;

        uint responseTesterId = 0;
        uint responseModuleId = 0;
        if ((cmdStatus = ReadDataByIdentifier(UDS_RecordIdentifier.ActiveDiagnosticSession, out byte[] recordData, ref responseTesterId, ref responseModuleId)) != UDS_ErrorCodes.UDS_NRC_NONE)
        {
            _logger.LogError("Could not read ActiveDiagnosticSession, exiting");
            return cmdStatus;
        }
        else
        {
            if (recordData.Length == 2) // module responds in the 2 byte format, parse it
            {
                _activeDiagSession = ByteArrayParseUshortBigEndian(recordData, 0);
            }
            else if (recordData.Length == 1) // module responds in the 1 byte format, request more info
            {
                /* ActiveDiagnosticSession on some controllers only returns one byte, meaning:
                 *
                 *   01,DefaultSession
                 *   02,ProgrammingSession
                 *   03,ExtDiagSession
                 *   41,CodingSessionSession
                 */

                if ((cmdStatus = ReadDataByIdentifier(UDS_RecordIdentifier.ActiveSessionState, out recordData, ref responseTesterId, ref responseModuleId)) != UDS_ErrorCodes.UDS_NRC_NONE)
                {
                    _logger.LogError("Could not read ActiveSessionState, exiting");
                    return cmdStatus;
                }
                else
                {
                    _activeDiagSession = ByteArrayParseUshortBigEndian(recordData, 0);
                }
            }
            _logger.LogInformation("  ActiveDiagnosticSession: " + GetDiagSessionDescription(_activeDiagSession));
            UpdateActiveDiagSession(_activeDiagSession);
        }
        return cmdStatus;
    }

    public ushort ByteArrayParseUshortBigEndian(byte[] data, int offset)
    {
        if (data.Length < (offset + 2)) return 0xffff;
        return (ushort)((data[offset] << 8) + data[offset + 1]);
    }

    public ushort ByteArrayParseUshortLittleEndian(byte[] data, int offset)
    {
        if (data.Length < (offset + 2)) return 0xffff;
        return (ushort)((data[offset + 1] << 8) + data[offset]);
    }

    public void UpdateActiveDiagSession(ushort DiagMode)
    {
        activeDiagSession = (UDS_DiagMode)(DiagMode >> 8);
    }

    public bool CheckCarResponding()
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;

        DiagTransportTranslationWrapIfBase.TransportWrapperErrorCodes transportStatus = SendReceiveFunc(_testerId,
            (byte)FunktionalAddressingIds.UDS_ALL, UDSFormatCmd(UDS_DiagCmd.TesterPresent, 0x00, UDS_suppressPositiveResponse.Yes), ref responseTesterId, ref responseModuleId, out byte[] TpResponse,
            false, udsMaxExecutionTimeMs, maxWaitingTimeMS, enableLogging);

        if (transportStatus == DiagTransportTranslationWrapIfBase.TransportWrapperErrorCodes.TRANSPORT_ERR_NONE)
        {
            DiagTransportInterface.CableInterface.FlushBuffers();

            return true;
        }

        return false;
    }

    public void HandleTesterPresent()
    {
        if (activeDiagSession != UDS_DiagMode.DEFAULT && ((Stopwatch.GetTimestamp() - LastTPCmdTick) >= cycleTimeTPmsg * TickResolMs))
        {
            _logger.LogTrace(string.Format("UDS P3 Timeout, Sending TP command 0x{0:X02} ", (byte)UDS_DiagCmd.TesterPresent));

            uint responseTesterId = 0;
            uint responseModuleId = 0;
            DiagTransportTranslationWrapIfBase.TransportWrapperErrorCodes transportStatus = SendReceiveFunc(_testerId,
                (byte)FunktionalAddressingIds.UDS_ALL, UDSFormatCmd(UDS_DiagCmd.TesterPresent, 0x00, UDS_suppressPositiveResponse.Yes), ref responseTesterId, ref responseModuleId, out byte[] TpResponse,
                false, udsMaxExecutionTimeMs, maxWaitingTimeMS, enableLogging);

            if (transportStatus == DiagTransportTranslationWrapIfBase.TransportWrapperErrorCodes.TRANSPORT_ERR_NONE)
            {
                LastTPCmdTick = Stopwatch.GetTimestamp();
                _logger.LogTrace(string.Format("UDS {0} - No Response, as directed", UDS_DiagCmd.TesterPresent));
                // consumes any NRC, if present
                System.Threading.Thread.Sleep(moduleNonResponseCmdMicroSnoozeTimeMs);
                DiagTransportInterface.CableInterface.FlushBuffers();
            }
            else if (transportStatus == DiagTransportTranslationWrapIfBase.TransportWrapperErrorCodes.TRANSPORT_ERR_ECHO_NA)
            {
                // in most cases where the wifi does not repond; it magicly resets (pic18f side), give it some time to recover and POLL for complete.
                CablePollUntilAvailable();
            }
        }
    }

    private UDS_ErrorCodes ExecuteCommand(byte testerId, byte moduleId, byte[] snddata, ref uint responseTesterId, ref uint responseModuleId, bool responseExpected = true)
    {
        return ExecuteCommand(testerId, moduleId, snddata, out _, ref responseTesterId, ref responseModuleId, responseExpected);
    }

    public UDS_ErrorCodes ExecuteCommand(byte testerId, byte moduleId, byte[] snddata, out byte[] rcvdata, ref uint responseTesterId, ref uint responseModuleId, bool responseExpected = true, bool disableTimeoutPrompt = false)
    {
        int cmdResendCnt = 0;
        responseTesterId = 0;
        responseModuleId = 0;
        string DiagCmdInfo = ((UDS_DiagCmd)snddata[0]).ToString();

    resendCmdTransportOrTimeoutError:

        UDS_ErrorCodes cmdStatus = UDS_ErrorCodes.UDS_NRC_NONE;

        _logger.LogTrace(string.Format("UDS {0}tarting command 0x{1:X02} ", cmdResendCnt == 0 ? "S" : "Res", (byte)snddata[0]) + DiagCmdInfo);

        DiagTransportTranslationWrapIfBase.TransportWrapperErrorCodes transportStatus = SendReceiveFunc(testerId,
            moduleId, snddata, ref responseTesterId, ref responseModuleId, out rcvdata, responseExpected, udsMaxExecutionTimeMs, maxWaitingTimeMS, enableLogging);

        if (transportStatus != DiagTransportTranslationWrapIfBase.TransportWrapperErrorCodes.TRANSPORT_ERR_NONE) // ISO 14229:2006(E) Annex A or see ISO 15765-3:2004(E) page#28, table7:
        {
            cmdStatus = (UDS_ErrorCodes)((uint)UDS_ErrorCodes.UDS_TRANSPORT_ERR_NONE + (byte)transportStatus);
            string transportStatusMsg = string.Format("UDS {0} - Transmission Error 0x{1:X02} ", DiagCmdInfo, (byte)cmdStatus) + GetErrorDescription(cmdStatus);
            if (++cmdResendCnt < cmdResendMaxRetries)
            {
                _logger.LogWarning(transportStatusMsg);
                if ((uint)cmdStatus >= (uint)UDS_ErrorCodes.UDS_TRANSPORT_ERR_RSP_NOHEAD) // module did not respond or not in time, just repeat
                {
                    if (CableResetExecute(disableTimeoutPrompt))
                    {
                        _logger.LogWarning("Repeat request allowed, Cnt {0}/{1}", cmdResendCnt, cmdResendMaxRetries);
                        goto resendCmdTransportOrTimeoutError;
                    }
                    else // cable reset or buffer flush failed
                    {
                        _logger.LogError("Repeat request denied, cable reinit failed Cnt {0}/{1}", cmdResendCnt, cmdResendMaxRetries);
                    }
                }
                else // Cable stopped responding or send/recieve error, reset it and hope for the best
                {
                    if (CableResetExecute(disableTimeoutPrompt))
                    {
                        _logger.LogWarning("Repeat request allowed, Cnt {0}/{1}", cmdResendCnt, cmdResendMaxRetries);
                        goto resendCmdTransportOrTimeoutError;
                    }
                    else // cable reset or buffer flush failed
                    {
                        _logger.LogError("Repeat request denied, cable reinit failed Cnt {0}/{1}", cmdResendCnt, cmdResendMaxRetries);
                    }
                }
            }
            else // tries exceeded, abort mission
            {
                if (cmdResendMaxRetries > 0) _logger.LogError("Repeat request denied, tries exceeded Cnt {0}/{1}", cmdResendCnt, cmdResendMaxRetries);
                _logger.LogError(transportStatusMsg);
            }
        }
        else if (responseExpected == false)
        {
            _logger.LogTrace(string.Format("UDS {0} - No Response, as directed", DiagCmdInfo));
            // consumes any NRC, if present
            System.Threading.Thread.Sleep(moduleNonResponseCmdMicroSnoozeTimeMs);
            DiagTransportInterface.CableInterface.FlushBuffers();
        }
        else // Transport successfull
        {
            if (CommandStatusNRC(rcvdata)) // Negative command Response
            {
                if (CommandStatusCustomCodeWDBIResponse(rcvdata))
                {
                    _logger.LogTrace(string.Format("UDS {0} - Custom Code WDBI Positive Response 0x{1:X02} ", DiagCmdInfo, (byte)rcvdata[0]));
                    rcvdata = ResponseStripLeading(rcvdata, DiagResponseLeadingSize((UDS_DiagCmd)snddata[0]));
                }
                else
                {
                    cmdStatus = (UDS_ErrorCodes)rcvdata[2];
                    _logger.LogWarning(string.Format("UDS {0} - Negative Response 0x{1:X02} ", DiagCmdInfo, (byte)cmdStatus) + GetErrorDescription(cmdStatus));
                }
            }
            else if (CommandStatusOk(snddata, rcvdata)) // Positive command Response
            {
                _logger.LogTrace(string.Format("UDS {0} - Positive Response 0x{1:X02} ", DiagCmdInfo, (byte)rcvdata[0]));
                rcvdata = ResponseStripLeading(rcvdata, DiagResponseLeadingSize((UDS_DiagCmd)snddata[0]));
            }
            else // We recieved something, but its garbage (is this even possible ?)
            {
                _logger.LogTrace(string.Format("UDS {0} - Garbage Response, retrying", DiagCmdInfo));
                goto resendCmdTransportOrTimeoutError;
            }
        }

        // restore back to default
        udsMaxExecutionTimeMs = DiagTransportTranslationWrapIfBase.maxExecutionTimeDefault;
        maxWaitingTimeMS = DiagTransportTranslationWrapIfBase.maxWaitingTimeMSDefault;

        if (cmdStatus == UDS_ErrorCodes.UDS_NRC_NONE) HandleTesterPresent();

        return cmdStatus;
    }

    public void IdleUDSSleepMs(int sleepTimeMs)
    {
        long startTime = Stopwatch.GetTimestamp();
        while ((Stopwatch.GetTimestamp() - startTime) < sleepTimeMs * TickResolMs)
        {
            HandleTesterPresent();
            System.Threading.Thread.Sleep(1);
        }
    }

    public int GetMaxExecutionTimeSeconds()
    {
        return udsMaxExecutionTimeMs / 1000;
    }

    public void SetMaxExecutionTimeSeconds(int maxExecutionTimeSeconds)
    {
        udsMaxExecutionTimeMs = maxExecutionTimeSeconds * 1000;
        _logger.LogTrace(string.Format("UDS Non-default Max execution time for next command {0}s", udsMaxExecutionTimeMs));
    }

    public void SetMaxWaitingTimeMilliSeconds(int _maxWaitingTimeMS)
    {
        maxWaitingTimeMS = _maxWaitingTimeMS;
        _logger.LogTrace(string.Format("UDS Non-default Max waiting time for response {0}ms", maxWaitingTimeMS));
    }

    private byte[] UDSFormatCmd(UDS_DiagCmd serviceId)
    {
        byte[] Cmd = new byte[1];
        Cmd[0] = (byte)serviceId;
        return Cmd;
    }

    private byte[] UDSFormatCmd(UDS_DiagCmd serviceId, byte subCommand, UDS_suppressPositiveResponse suppressPositiveResponse = UDS_suppressPositiveResponse.No)
    {
        byte[] Cmd = new byte[2];
        Cmd[0] = (byte)serviceId;
        Cmd[1] = (byte)((byte)subCommand | (byte)suppressPositiveResponse);
        return Cmd;
    }

    private byte[] FormatAddressSize(uint startAddress, uint size, UDS_AddressFormat format)
    {
        if (format == UDS_AddressFormat.COMPACT)
        {
            byte[] memBlock = new byte[6];
            memBlock[0] = 0x14; // length size 1 byte, length address 4 bytes
            memBlock[1] = (byte)(startAddress >> 24);
            memBlock[2] = (byte)(startAddress >> 16);
            memBlock[3] = (byte)(startAddress >> 8);
            memBlock[4] = (byte)startAddress;
            memBlock[5] = (byte)size;
            return memBlock;
        }
        else if (format == UDS_AddressFormat.MID)
        {
            byte[] memBlock = new byte[6 + 1];
            memBlock[0] = 0x24; // length size 2 byte, length address 4 bytes
            memBlock[1] = (byte)(startAddress >> 24);
            memBlock[2] = (byte)(startAddress >> 16);
            memBlock[3] = (byte)(startAddress >> 8);
            memBlock[4] = (byte)startAddress;
            memBlock[5] = (byte)(size >> 8); // take care, BMW fast transport cannot handle more than 255 payload)
            memBlock[6] = (byte)size;
            return memBlock;
        }
        else
        {
            byte[] memBlock = new byte[6 + 3];
            memBlock[0] = 0x44; // length size 4 byte, length address 4 bytes
            memBlock[1] = (byte)(startAddress >> 24);
            memBlock[2] = (byte)(startAddress >> 16);
            memBlock[3] = (byte)(startAddress >> 8);
            memBlock[4] = (byte)startAddress;
            memBlock[5] = (byte)(size >> 24);
            memBlock[6] = (byte)(size >> 16);
            memBlock[7] = (byte)(size >> 8);
            memBlock[8] = (byte)size;
            return memBlock;
        }
    }

    public enum UDS_ErrorCodes : uint
    {
        UDS_NRC_NONE = 0, // postiveResponse
        UDS_NRC_0010 = 0x10, // generalReject
        UDS_NRC_0011 = 0x11, // serviceNotSupported
        UDS_NRC_0012 = 0x12, // subFunctionNotSupported
        UDS_NRC_0013 = 0x13, // incorrectMessageLengthOrInvalidFormat
        UDS_NRC_0014 = 0x14, // responseTooLong
        UDS_NRC_0021 = 0x21, // busyRepeatRequest
        UDS_NRC_0022 = 0x22, // conditionsNotCorrect
        UDS_NRC_0024 = 0x24, // requestSequenceError
        UDS_NRC_0031 = 0x31, // requestOutOfRange
        UDS_NRC_0033 = 0x33, // securityAccessDenied
        UDS_NRC_0035 = 0x35, // invalidKey
        UDS_NRC_0036 = 0x36, // exceedNumberOfAttempts
        UDS_NRC_0037 = 0x37, // requiredTimeDelayNotExpired
        UDS_NRC_0070 = 0x70, // uploadDownloadNotAccepted
        UDS_NRC_0071 = 0x71, // transferDataSuspended
        UDS_NRC_0072 = 0x72, // generalProgrammingFailure
        UDS_NRC_0073 = 0x73, // wrongBlockSequenceCounter
        UDS_NRC_0078 = 0x78, // requestCorrectlyRecieved-ResponsePending
        UDS_NRC_007E = 0x7E, // subFunctionNotSupportedInActiveSession
        UDS_NRC_007F = 0x7F, // serviceNotSupportedInActiveSession
        UDS_NRC_0081 = 0x81, // rpmTooHigh
        UDS_NRC_0082 = 0x82, // rpmTooLow
        UDS_NRC_0083 = 0x83, // engineIsRunning
        UDS_NRC_0084 = 0x84, // engineIsNotRunning
        UDS_NRC_0085 = 0x85, // engineRunTimeTooLow
        UDS_NRC_0086 = 0x86, // temperatureTooHigh
        UDS_NRC_0087 = 0x87, // temperatureTooLow
        UDS_NRC_0088 = 0x88, // vehicleSpeedTooHigh
        UDS_NRC_0089 = 0x89, // vehicleSpeedTooLow
        UDS_NRC_008A = 0x8A, // throttle/PedalTooHigh
        UDS_NRC_008B = 0x8B, // throttle/PedalTooLow
        UDS_NRC_008C = 0x8C, // transmissionRangeNotInNeutral
        UDS_NRC_008D = 0x8D, // transmissionRangeNotInGear
        UDS_NRC_008F = 0x8F, // breakeSwitch(es)NotClosed (Brake Pedal not pressed or not applied)
        UDS_NRC_0090 = 0x90, // shifterLevelNotInPark
        UDS_NRC_0091 = 0x91, // torqueConverterClutchLocked
        UDS_NRC_0092 = 0x92, // voltageTooHigh
        UDS_NRC_0093 = 0x93, // voltageTooLow
        UDS_TRANSPORT_ERR_NONE = 0x100, // "postiveResponse"
        UDS_TRANSPORT_ERR_SND_IF = 0x101, // "could not send data to interface"
        UDS_TRANSPORT_ERR_ECHO_NA = 0x102, // "no echo recieved from interface"
        UDS_TRANSPORT_ERR_ECHO_IV = 0x103, // "invalid echo recieved from interface"
        UDS_TRANSPORT_ERR_RSP_NOHEAD = 0x104, // "no header recieved from module"
        UDS_TRANSPORT_ERR_RSP_HEAD_IV = 0x105, // "invalid header recieved from module"
        UDS_TRANSPORT_ERR_RSP_NOTAIL = 0x106, // "no tail recieved from module"
        UDS_TRANSPORT_ERR_RSP_CHKS = 0x107, // "invalid checksum on data received from interface"
        CustomCode_WBDI_Incorrect_Checksum = 0x9696, // "Custom Code incorrect checksum for WDBI"
        CustomCode_WBDI_Invalid_DID_or_Data = 0x6969, // "Custom Code invalid DID or Data for WDBI"
    }

    public string GetErrorDescription(UDS_ErrorCodes errorCode) => errorCode switch
    {
        UDS_ErrorCodes.UDS_NRC_NONE => "postiveResponse",
        UDS_ErrorCodes.UDS_NRC_0010 => "generalReject",
        UDS_ErrorCodes.UDS_NRC_0011 => "serviceNotSupported",
        UDS_ErrorCodes.UDS_NRC_0012 => "subFunctionNotSupported",
        UDS_ErrorCodes.UDS_NRC_0013 => "incorrectMessageLengthOrInvalidFormat",
        UDS_ErrorCodes.UDS_NRC_0014 => "responseTooLong",
        UDS_ErrorCodes.UDS_NRC_0021 => "busyRepeatRequest",
        UDS_ErrorCodes.UDS_NRC_0022 => "conditionsNotCorrect",
        UDS_ErrorCodes.UDS_NRC_0024 => "requestSequenceError",
        UDS_ErrorCodes.UDS_NRC_0031 => "requestOutOfRange",
        UDS_ErrorCodes.UDS_NRC_0033 => "securityAccessDenied",
        UDS_ErrorCodes.UDS_NRC_0035 => "invalidKey",
        UDS_ErrorCodes.UDS_NRC_0036 => "exceedNumberOfAttempts",
        UDS_ErrorCodes.UDS_NRC_0037 => "requiredTimeDelayNotExpired",
        UDS_ErrorCodes.UDS_NRC_0070 => "uploadDownloadNotAccepted",
        UDS_ErrorCodes.UDS_NRC_0071 => "transferDataSuspended",
        UDS_ErrorCodes.UDS_NRC_0072 => "generalProgrammingFailure",
        UDS_ErrorCodes.UDS_NRC_0073 => "wrongBlockSequenceCounter",
        UDS_ErrorCodes.UDS_NRC_0078 => "requestCorrectlyRecieved-ResponsePending",
        UDS_ErrorCodes.UDS_NRC_007E => "subFunctionNotSupportedInActiveSession",
        UDS_ErrorCodes.UDS_NRC_007F => "serviceNotSupportedInActiveSession",
        UDS_ErrorCodes.UDS_NRC_0081 => "rpmTooHigh",
        UDS_ErrorCodes.UDS_NRC_0082 => "rpmTooLow",
        UDS_ErrorCodes.UDS_NRC_0083 => "engineIsRunning",
        UDS_ErrorCodes.UDS_NRC_0084 => "engineIsNotRunning",
        UDS_ErrorCodes.UDS_NRC_0085 => "engineRunTimeTooLow",
        UDS_ErrorCodes.UDS_NRC_0086 => "temperatureTooHigh",
        UDS_ErrorCodes.UDS_NRC_0087 => "temperatureTooLow",
        UDS_ErrorCodes.UDS_NRC_0088 => "vehicleSpeedTooHigh",
        UDS_ErrorCodes.UDS_NRC_0089 => "vehicleSpeedTooLow",
        UDS_ErrorCodes.UDS_NRC_008A => "throttle/PedalTooHigh",
        UDS_ErrorCodes.UDS_NRC_008B => "throttle/PedalTooLow",
        UDS_ErrorCodes.UDS_NRC_008C => "transmissionRangeNotInNeutral",
        UDS_ErrorCodes.UDS_NRC_008D => "transmissionRangeNotInGear",
        UDS_ErrorCodes.UDS_NRC_008F => "breakeSwitch(es)NotClosed (Brake Pedal not pressed or not applied)",
        UDS_ErrorCodes.UDS_NRC_0090 => "shifterLevelNotInPark",
        UDS_ErrorCodes.UDS_NRC_0091 => "torqueConverterClutchLocked",
        UDS_ErrorCodes.UDS_NRC_0092 => "voltageTooHigh",
        UDS_ErrorCodes.UDS_NRC_0093 => "voltageTooLow",
        UDS_ErrorCodes.UDS_TRANSPORT_ERR_NONE => "postiveResponse",
        UDS_ErrorCodes.UDS_TRANSPORT_ERR_SND_IF => "could not send data to interface",
        UDS_ErrorCodes.UDS_TRANSPORT_ERR_ECHO_NA => "no echo recieved from interface",
        UDS_ErrorCodes.UDS_TRANSPORT_ERR_ECHO_IV => "invalid echo recieved from interface",
        UDS_ErrorCodes.UDS_TRANSPORT_ERR_RSP_NOHEAD => "no header recieved from module",
        UDS_ErrorCodes.UDS_TRANSPORT_ERR_RSP_HEAD_IV => "invalid header recieved from module",
        UDS_ErrorCodes.UDS_TRANSPORT_ERR_RSP_NOTAIL => "no tail recieved from module",
        UDS_ErrorCodes.UDS_TRANSPORT_ERR_RSP_CHKS => "invalid checksum on data received from interface",
        UDS_ErrorCodes.CustomCode_WBDI_Incorrect_Checksum => "Custom Code incorrect checksum for WDBI",
        UDS_ErrorCodes.CustomCode_WBDI_Invalid_DID_or_Data => "Custom Code invalid DID or Data for WDBI",
        _ => string.Format("unknown Error Code 0x{0:X04}", (uint)errorCode),
    };

    public enum UDS_DiagCmd : byte
    {
        DiagnosticSessionControl = 0x10, // 50
        ECUReset = 0x11, // 51
        SecurityAccess = 0x27, // 67
        CommunicationControl = 0x28, // 68
        TesterPresent = 0x3E, // 7E
        SecuredDataTransmission = 0x84, // C4
        ControlDTCSetting = 0x85, // C5
        ResponseOnEvent = 0x86, // C6
        LinkControl = 0x87, // C7
        ReadDataByIdentifier = 0x22, // 64
        ReadMemoryByAddress = 0x23, // 63
        ReadScalingDataByIdentifier = 0x24, // 64
        ReadDataByPeriodicIdentifier = 0x2A, // 6A
        DynamicallyDefineDataIdentifier = 0x2C, // 6C
        WriteDataByIdentifier = 0x2E, // 6E
        WriteMemoryByAddress = 0x3D, // 7D
        ReadDTCInformation = 0x19, // 59
        ClearDiagnosticInformation = 0x14, // 54
        InputOutputControlByIdentifier = 0x2F, // 6F
        RoutineControl = 0x31, // 71
        RequestDownload = 0x34, // 74
        RequestUpload = 0x35, // 75
        TransferData = 0x36, // 76
        RequestTransferExit = 0x37, // 77
        ZGWDebugService = 0xBF,
        escCode = 0x80, // (Not part of Diagnostic Services Specification; KWP 2000 only) C0
    }

    //
    // TODO: check and fill for other commands
    //
    public byte DiagResponseLeadingSize(UDS_DiagCmd diagCmd) => diagCmd switch
    {
        UDS_DiagCmd.DiagnosticSessionControl => 2,
        UDS_DiagCmd.ReadDTCInformation => 2,
        UDS_DiagCmd.ReadDataByIdentifier => 3,
        UDS_DiagCmd.SecurityAccess => 2,
        UDS_DiagCmd.RoutineControl => 4,
        UDS_DiagCmd.ZGWDebugService => 3,
        _ => 1,
    };

    public enum UDS_ResetMode : byte
    {
        hardReset = 0x01, // Power On Reset
        keyOffOnReset = 0x02, // keyOffOnReset Reset
        softReset = 0x03, // Nonvolatile Memory Reset
        enableRapidPowerShutDown = 0x04, // enableRapidPowerShutDown ??
        disableRapidPowerShutDown = 0x05, // disableRapidPowerShutDown ??
        jumpToEcuControlDme861 = 0x83, // jumps to ecuControl UDS mini state machine
    }

    public string GetResetModeName(UDS_ResetMode _mode) => _mode switch
    {
        UDS_ResetMode.hardReset => "power on reset (0x01)",
        UDS_ResetMode.keyOffOnReset => "key off/on reset (0x02)",
        UDS_ResetMode.softReset => "non-volatile memory reset (0x03)",
        UDS_ResetMode.enableRapidPowerShutDown => "enable rapid power shutdown (0x04)",
        UDS_ResetMode.disableRapidPowerShutDown => "disable rapid power shutdown (0x05)",
        UDS_ResetMode.jumpToEcuControlDme861 => "jump to ecuControl UDS mini state machine (0x83)",
        _ => string.Format("reset mode (unknown) 0x{0:X02}", (byte)_mode),
    };

    public string GetModuleName(ModuleIds _id) => _id switch
    {
        ModuleIds.CAS => "CAS [FEM-Body, ZSG_CAN] (0x40)",
        //ModuleIds.DME => "DME [A_FlexRay] (0x12)",
        ModuleIds.DME_MASTER => "DME_MASTER [A_FlexRay] (0x12)",
        ModuleIds.DME_SLAVE => "DME_SLAVE [A_FlexRay] (0x13)",
        ModuleIds.ZGW => "ZGW [FEM-GW, BDC15, D_CAN/Ethernet] (0x10)",
        ModuleIds.DME861_BOOTCNTRL => "DME861_BOOTCNTRL (0xA0)",
        _ => string.Format("id (unknown) 0x{0:X02}", (byte)_id),
    };

    public enum UDS_DiagMode : byte
    {
        UNGUELTIG = 0x00, // "DefaultMode"
        DEFAULT = 0x01, // "DefaultMode"
        ECUPM = 0x02, // "ECUProgrammingMode"
        ECUEXTDIAG = 0x03, // "ECUExtendedDiagnosticSession"
        ECUSSDS = 0x04, // "ECUSafetySystemDiagnosticSession"
        ECUEOL = 0x40, // "ECUEndOfLineSession"
        ECUCODE = 0x41, // "ECUCodingSession"
        ECUSWT = 0x42, // "ECUSwtSession"
        ECUHDD = 0x43, // "ECUHDDDownloadSession"
        ECUDEVELOP = 0x4F, // "ECUDevelopmentSession"
        ECUGDM = 0x5F, // "ECUGarageDiagnoseMode"
    }

    public string GetDiagSessionDescription(ushort DiagMode) => DiagMode switch
    {
        0x0100 => "BootloaderDefaultSession",
        0x0181 => "ApplicationDefaultSessionAA",
        0x0182 => "ApplicationDefaultSessionFMA",
        0x0201 => "ProgSessionLocked",
        0x0202 => "ProgSessionUnlocked",
        0x0203 => "ProgSessionFingerprintWritten",
        0x0204 => "ProgSessionMemoryErased",
        0x0244 => "ProgSessionMemoryEraseFailed",
        0x0205 => "ProgSessionDownloadStarted",
        0x0206 => "ProgSessionTransferData",
        0x0246 => "ProgSessionTransferDataFailed",
        0x0207 => "ProgSessionDownloadFinished",
        0x0208 => "ProgSessionMemoryChecked",
        0x0248 => "ProgSessionMemoryCheckFailed",
        0x0209 => "ProgSessionProgDepsChecked",
        0x0249 => "ProgSessionProgDepsCheckFailed",
        0x0300 => "BootloaderExtDiagSession",
        0x0381 => "ExtDiagSessionStarted",
        0x0382 => "ExtDiagSessionDTCOff_1",
        0x0383 => "ExtDiagSessionNDCDisabled",
        0x0384 => "ExtDiagSessionFMA",
        0x0385 => "ExtDiagSessionDTCOff_2",
        0x0386 => "ExtDiagSessionStarted",
        0x4101 => "CodingSessionLocked",
        0x4102 => "CodingSessionUnlocked",
        _ => "---",
    };

    public enum UDS_SecurityAccessType : byte
    {
        None = 0x00,
        Coding = 0x01,
        Msm_03 = 0x03,
        Prog = 0x11,
        Msm_61 = 0x61,
    }

    public enum UDS_BaudRate : byte
    {
        PC9600 = 0x01, // "Baudrate 9.6 kBaud"
        PC19200 = 0x02, // "Baudrate 19.2 kBaud"
        PC38400 = 0x03, // "Baudrate 38.4 kBaud"
        PC57600 = 0x04, // "Baudrate 57.6 kBaud"
        PC115200 = 0x05 // "Baudrate 115.2 kBaud"
    }

    public enum UDS_CommunicationMode : byte
    {
        enableRxAndTx = 0x00,
        enableRxAndDisableTx = 0x01,
        disableRxAndEnableTx = 0x02,
        disableRxAndTx = 0x03,
    }

    public enum UDS_CommunicationType : byte
    {
        application = 0x01,
        networkManagement = 0x02,
    }

    public enum UDS_DtcGroup : uint
    {
        AllPowertrainDTC = 0x000000, // Clear all powertrain related DTC
        AllChassisDTC = 0x400000, // Clear all chassis related DTC
        AllBodyDTC = 0x800000, // Clear all body related DTC
        AllNetworkDTC = 0xC00000, // Clear all network related DTC
        AllDTC = 0xFFFFFF // Clear All DTC
    }

    public enum UDS_DtcReadGroup : byte
    {
        reportNumberOfDTCByStatusMask = 0x01,
        reportDTCByStatusMask = 0x02,
        reportDTCSnapshotIdentification = 0x03,
        reportDTCSnapshotRecordByDTCNumber = 0x04,
        reportDTCSnapshotRecordByRecordNumber = 0x05,
        reportDTCExtendedDataRecordByDTCNumber = 0x06,
        reportNumberOfDTCBySeverityMaskRecord = 0x07,
        reportDTCBySeverityMaskRecord = 0x08,
        reportSeverityInformationOfDTC = 0x09,
        reportSupportedDTC = 0x0A,
        reportFirstTestFailedDTC = 0x0B,
        reportFirstConfirmedDTC = 0x0C,
        reportMostRecentTestFailedDTC = 0x0D,
        reportMostRecentConfirmedDTC = 0x0E,
        reportMirrorMemoryDTCByStatusMaskr = 0x0F,
        reportMirrorMemoryDTCExtendedDataRecordByDTCNumber = 0x10,
        reportNumberOfMirrorMemoryDTCByStatusMask = 0x11,
        reportNumberOfEmissionsRelatedOBDDTCByStatusMask = 0x12,
        reportEmissionsRelatedOBDDTCByStatusMask = 0x13,
    }

    public enum UDS_DtcStatusMask : byte
    {
        testFailed = 0x01,
        testFailedThisMonitoringCycle = 0x02,
        pendingDTC = 0x04,
        confirmedDTC = 0x08,
        testNotCompletedSinceLastClear = 0x10,
        testFailedSinceLastClear = 0x20,
        testNotCompletedThisMonitoringCycle = 0x40,
        warningIndicatorRequested = 0x80
    }

    /// <summary>
    /// [$22/$2E/$2F] DataIdentifier, Starts on page 6130 of PPC_SWCalDoc.pdf
    /// </summary>
    public enum UDS_RecordIdentifier : ushort
    {
        TestStamp = 0x1000, // TestStamp -> Fingerprint
        EnergySavingMode = 0x100A, // EnergySavingMode
        IntegrationSteps = 0x100B, // I-Step -> I-Stuffe (BDC/ZGW only)
        Betriebsmode = 0x100E, // Betriebsmode/ExtendedMode
        NumberofSubbusMembers = 0x1600, // NumberofSubbusMembers -> Anzahl der intelligenten Subbussensoren lesen

        StdCoreVersion = 0x1720, // StdCoreVersion
        StdCoreModules = 0x1726, // StdCoreModules
        IpConfig = 0x172A, // IPConfig
        IndivDataIDTable = 0x1734, // ReadIndivDataIDTable
        StatusLifeCycle = 0x1735, // StatusLifeCycle

        SekundaererFehlerspeicher = 0x2000, // Sekundaerer Fehlerspeicher lesen (alle Fehler / Ort und Art), Secondary errors

        MemorySegmentationTable = 0x2501, // ReadMemorySegmentationTable
        ProgrammingCounter = 0x2502, // ProgrammingCounter
        ProgrammingCounterMax = 0x2503, // ProgrammingCounterMax
        FlashTimingParameter = 0x2504, // FlashTimingParameter
        MemoryAddress = 0x2506, // ReadMemoryAddress
        EcuSupportsDeleteSwe = 0x2507, // EcuSupportsDeleteSwe
        GWRoutingStatus = 0x2508, // GWRoutingStatus

        CALIdCVN = 0x2541, // Calibration ID, CVN Calibration verification number (pairs)

        CodierDatenIdxFirst = 0x3000,
        CodierDatenIdxMax = 0x37FD,
        Codierpruefstempel = 0x37FE, // Codierpruefstempel 7-stellig (Short VIN)

        Fahrzeugauftrag = 0x3F06, // (ZGW) Read vehicle order, returns SALAPA elements
        VcmGetEcuListAll = 0x3F07, // (ZGW) List of all SGs stored in the SVT target, could use this to see if 0x13 exists (dual ecus)
        SVTVersion = 0x3F1A, // SVT Version

        Logistikbereich = 0x401F, // ecu_sw_bmw, a2l version for development?

        RBSachNummer = 0x4020, // 8hp Robert Bosch Sachnummer
        ZFSachNummer = 0x4021, // 8hp only

        StartupBlockId = 0x4098, // StartupBlockId (bootctrl)

        VGSG_F_SysInfo = 0x40F0, // Read catalyst exhaust temperature history

        Codierung = 0x4500,
        StatCodierBytes = 0x4501,
        StatCodierBytesMod = 0x4502,

        StatusAZGErkennung = 0x4630, // STAT_AZG_KENNUNG_DST, STAT_AZG_KENNUNG_PST, STAT_AZG_KENNUNG_S_E_A

        Motordrehzahl = 0x4807, // Engine RPM

        MG1_HSM_Status = 0x4896, // HSM Status

        Bordnetzspannung_von_ECU = 0x586A, // ECU, battery voltage (multiply result by 20 to get mV)
        SoundTuning = 0x5FFD, // Codierung Sound−Tuning Off: "BasSvrAppl_stSTOff" [00 = Soundtuning aktiv] [01 = Soundtuning deaktiviert]

        StatusSpannungKlemmen = 0xDAB3, // CAS, battery voltage
        StatusArgDme1 = 0xDABC, // CAS
        StatusKlemmen = 0xDC56, // CAS

        ActiveSessionState = 0xF100, // ActiveSessionState

        SVK_AKTUELL = 0xF101, // SVK_AKTUELL
        SVK_SUPPLIER = 0xF102, // SVK_SUPPLIER/ReadSysSuppSVK
        SVK_WERK = 0xF103, // SVK_WERK/PlantSVK
        SVK_BACKUP_01 = 0xF104, // SVK last backup. 0xF104 -> 0xF10D = SVK−Backup−Lesen (0xF104 = SVK_Backup_1, ... 0xF10D = SVK_Backup_10)
        SVK_BACKUP_02 = 0xF105,
        SVK_BACKUP_03 = 0xF106,
        SVK_BACKUP_04 = 0xF107,
        SVK_BACKUP_05 = 0xF108,
        SVK_BACKUP_06 = 0xF109,
        SVK_BACKUP_07 = 0xF10A,
        SVK_BACKUP_08 = 0xF10B,
        SVK_BACKUP_09 = 0xF10C,
        SVK_BACKUP_10 = 0xF10D,

        SGBDIndex = 0xF150, //  Sub-Parameter SGBD-Index, A2L characteristic: CustDiag_dSGBDIdx_CA
        FingerPrint = 0xF15A,

        bootSoftwareIdentification = 0xF180, // ???
        applicationSoftwareIdentification = 0xF181,
        applicationDataIdentificatioin = 0xF182,
        bootSoftwareFingerprint = 0xF183,
        applicationSoftwareFingerprint = 0xF184,
        applicationDataFingerprint = 0xF185,

        ActiveDiagnosticSession = 0xF186, // ActiveDiagnosticSession
        SystemSupplierIdentifier = 0xF18A, // SystemSupplierIdentifier, Hersteller Info auslesen (0x000008 stands for Bosch)
        EcuManufacturingData = 0xF18B, // EcuManufacturingData, Herstellungsdatum, Manuf Date
        ECUSerialNumber = 0xF18C, // ECUSerialNumber
        VIN = 0xF190, // VIN

        MG1_EEPROM_xx = 0xF198, // xx, len 8 bytes (not allowed in default session)
        MG1_EEPROM_xy = 0xF199, // xy, len 10 bytes (not allowed in default session)

        MG1_CB_id = 0xF1F0, // Customer Block Id string
        MG1_SB_id = 0xF1F1, // Startup Block Id string
        MG1_SWE_OCB = 0xF1F2, // Swe/Ocb Data per block
        MG1_CHECK_STRUCT = 0xF1F3, // Check status data per block

        MG1_DDDI_0 = 0xF300,
        MG1_DDDI_1 = 0xF301,
        MG1_DDDI_2 = 0xF302,
        MG1_DDDI_3 = 0xF303,

        CustomCode_SwitchableMapSlot = 0x6945,               // Custom code WDBI id for active switchable map slot
        CustomCode_BurbleSlot = 0x6946,                      // Custom code WDBI id for active Burble slot
        CustomCode_MaxCoolingStatus = 0x6947,                // Custom code WDBI id for active Max Cooling status
        CustomCode_AntilagStandingRpmSetpoint = 0x6948,      // Custom code WDBI id for Antilag Standing RPM Setpoint
        CustomCode_ExhaustFlapOtfStatus = 0x6949,            // Custom code WDBI id for Exhaust Flap On-The-Fly status
        CustomCode_ValetModeStatus = 0x694A,                 // Custom code WDBI id for Valet Mode status
        CustomCode_EthanolOverrideStatus = 0x694B,           // Custom code WDBI id for Ethanol Override status
        CustomCode_EthanolOverrideValue = 0x694C,            // Custom code WDBI id for Ethanol Override value
        CustomCode_ResetCustomCodeDtcs = 0x694D,             // Custom code WDBI id for resetting Custom DTCs
        CustomCode_RadiatorFlapOtfStatus = 0x694E,           // Custom code WDBI id for Radiator Flap On-The-Fly status
    }

    public enum UDS_MemorySegment : byte
    {
        LAR = 0x00, // "linearAdressRange"
        ROMI = 0x01, // "ROM / EPROM, internal"
        ROMX = 0x02, // "ROM / EPROM, external"
        NVRAM = 0x03, // "NV-RAM (characteristic zones, DTC memory"
        RAMIS = 0x04, // "RAM, internal (short MOV)"
        RAMXX = 0x05, // "RAM, external (x data MOV)"
        FLASH = 0x06, // "Flash EPROM, internal"
        UIFM = 0x07, // "User Info Field Memory"
        VODM = 0x08, // "Vehicle Order Data Memory"
        FLASHX = 0x09, // "Flash EPROM, external"
        RAMIL = 0x0B, // "RAM, internal (long MOV / Register)"
    }

    /*
     * only allowed on services: 22,23,24,2A,2E,3D,2F,34,35,36,37,84
     */

    public enum UDS_suppressPositiveResponse : byte
    {
        Yes = 0x80,
        No = 0x00,
    }

    public enum UDS_OnOff : byte
    {
        On = 0x01,
        Off = 0x02,
    }

    public enum UDS_DynamicallyDefineDataIdentifierAction : byte
    {
        defineByIdentifier = 0x01,
        defineByMemoryAddress = 0x02,
        clearDynamicallyDefinedDataIdentifier = 0x03,
    }

    public enum UDS_IOControlAction : byte
    {
        returnControlToECU = 0x00, // The device must get back controls of the mentioned signals.
        resetToDefault = 0x01, // The tester prompts to reset signals to the system wide default value.
        freezeCurrentState = 0x02, // The device shall freeze the current signal value.
        shortTermAdjustment = 0x03, // The device shall use the provided value for the signal
    }

    public enum UDS_IOControlIdentifier : ushort
    {
        ElectronicWastegate = 0x6078, // Elektrisches Wastegate ansteuern ==> arguments can be values between 0x0000 − 0x00FF (0 − 100%) 0% = 0x00, 25% = 0x40, 50% = 0x80, 75% = 0xC0, 99% = 0xFF
        ElectronicExhaustFlap = 0x60FD, // Elektrische Abgasklappe ansteuern/ beenden ==> arguments can be values between 0x0000 - 0x2710 (0 - 100%), we use 10% - 90% => factor == 100x, close and open values set with maps: TVEAKRCLOSE, TVEAKROPEN & TVEAKRDIAG
        PnumaticExhaustFlap = 0x60C1, //Pneumatische Abgasklappe ansteuern/ beenden
        RadiatorFlapTop = 0x6013, //Aktive Kuehlklappe 1 SP18 über LIN − AKKS Master−Master RADSHT_AKKSTOP_ATS
        RadiatorFlapBottom = 0x6014, //Aktive Kuehlklappe 1 SP18 über LIN − AKKS Master−Master RADSHT_AKKSBOTM_ATS
    }

    public enum UDS_RoutineAction : byte
    {
        startRoutine = 0x01,
        stopRoutine = 0x02,
        requestRoutineResults = 0x03,
    }

    public enum UDS_RoutineIdentifier : ushort
    {
        RequestListEntry = 0x0200,
        CheckMemory = 0x0202,
        checkProgrammingPreCondition = 0x0203,
        ReadSweProgrammingStatus = 0x0204, // neds logicalBlock address as parameter format 31 01 02 04 00 40 00 00 00 00
        ReadDevelopmentInfo = 0x0205, // ?? needs bootloader id as parameter ?? like -> 31 01 02 05 01 00 00 30 74 03 13 09 for IST_REFERENZ 01-00003074-003.019.009

        // TODO confirm:
        EchoTest = 0x0303,

        OtherTestInfo = 0x0304,

        CheckCodingSignature = 0x0F01,
        EraseInfoSpeicher = 0x0F06,
        ControlROE = 0x0F0B, // DeactivateROE;31 01 0F 0B DF 00 03 86 40 02, ActivateROE;31 01 0F 0B DF 00 03 86 45 02
        SetEnergyMode = 0x0F0C, // SetEnergyModeDefault;31 01 0F 0C 00, SetEnergyModeFlash;31 01 0F 0C 03, SetEnergyModeProduction;31 01 0F 0C 01, SetEnergyModeTransport;31 01 0F 0C 02

        IgnitionPower = 0x1001, // module id 0x40 (CAS) only! will turn on/off iginition if you pass the argument (0x0a 0x0a 0x43 [ON] or 0x06 0x06 0xa8 [OFF]) => 0x40 (CAS) 0x31 0x01 0x10 0x01 0x0a 0x0a 0x43
        RequestListEntryByIndex = 0x1002,
        SetExtendedMode = 0x1003, // SetExtendedModeFlash;31 01 10 03 01
        ActivateParallelFlashMode = 0x100E,
        ActivateApplicationMode = 0x100F,
        SetDefaultBusState = 0x1010, // SetDefaultBusDeActivate;31 01 10 10 00, SetDefaultBusActivate;31 01 10 10 01
        GetActualConfig = 0x1011,
        EraseIDRData = 0x101A, // ????
        RequestPreferredProtocol = 0x1021, // ???

        ControlUpdateMode = 0x4007, // ???

        UpDownloadPrePostProcessing = 0x7000, // 04 downloadPostProcessing

        MG1_ADAP_SELEKTIV_LOESCHEN = 0xF030, // argument 3 bytes mask, everything 0xff ff ff
        MG1_ADAP2_SELEKTIV_LOESCHEN = 0xF031,  // argument 3 bytes mask, everything 0xff ff ff

        DiagnosticElectronicWastegateTest = 0xF045,  // EWG, electronic wastegate test

        ResetHUAktivierungsLeitung = 0xF760,
        EraseMemory = 0xFF00, //
        CheckProgDeps = 0xFF01,
    }

    public enum UDS_SetEnergyModeIdentifier : byte
    {
        Default = 0x00,
        Production = 0x01,
        Transport = 0x02,
        Flash = 0x03,
    }

    public enum UDS_AddressFormat : byte
    {
        COMPACT = 0x00, // 4 byte address, 1 byte size
        MID = 0x01, // 4 byte address, 2 byte size
        LONG = 0x02, // 4 byte address, 4 byte size
    }

    public enum UDS_WriteStatus : byte
    {
        OK = 0x01,
        NOT_OK = 0x02,
    }

    public UDS_ErrorCodes DiagnosticSessionControl(UDS_DiagMode diagMode, ref ushort p2Max, ref uint p2Star)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;

        UDS_ErrorCodes cmdstatus = ExecuteCommand(_testerId, _moduleId, UDSFormatCmd(UDS_DiagCmd.DiagnosticSessionControl, (byte)diagMode), out byte[] returnvalues, ref responseTesterId, ref responseModuleId);
        if (cmdstatus == UDS_ErrorCodes.UDS_NRC_NONE && returnvalues.Length >= 4)
        {
            p2Max = (ushort)((returnvalues[0] << 8) + returnvalues[1]); // resolution 1ms
            p2Star = (uint)(((returnvalues[2] << 8) + returnvalues[3]) * 10); // resolution 10ms
            activeDiagSession = diagMode;
            LastTPCmdTick = Stopwatch.GetTimestamp();
        }
        return cmdstatus;
    }

    public UDS_ErrorCodes ECUReset(UDS_ResetMode resetMode, ModuleIds moduleId)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        UDS_ErrorCodes cmdstatus = ExecuteCommand(_testerId, (byte)moduleId, UDSFormatCmd(UDS_DiagCmd.ECUReset, (byte)resetMode), ref responseTesterId, ref responseModuleId);
        if (cmdstatus == UDS_ErrorCodes.UDS_NRC_NONE)
        {
            activeDiagSession = UDS_DiagMode.DEFAULT;
            LastTPCmdTick = Stopwatch.GetTimestamp();
        }
        return cmdstatus;
    }

    public UDS_ErrorCodes ReadDtc(out int[] dtcs_core, out int[] dtcs_shadow)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        var tmp_dtc_core = new List<int>();
        var tmp_dtc_shadow = new List<int>();

        // R4C9J758B_SWCalDoc.pdf pg 1721:
        // Core errors
        var cmd_core = new byte[3];
        cmd_core[0] = (byte)UDS_DiagCmd.ReadDTCInformation;
        cmd_core[1] = (byte)UDS_DtcReadGroup.reportDTCByStatusMask; // read dtc by filter
        cmd_core[2] = (byte)(UDS_DtcStatusMask.pendingDTC | UDS_DtcStatusMask.confirmedDTC);
        UDS_ErrorCodes cmdStatus_core = ExecuteCommand(_testerId, _moduleId, cmd_core, out byte[] returnValues_core, ref responseTesterId, ref responseModuleId);
        if (cmdStatus_core == UDS_ErrorCodes.UDS_NRC_NONE)
        {
            int dtcCount = (returnValues_core.Length - 1) / 4;
            byte statusAvailabilityMask = returnValues_core[0];

            _logger.LogInformation("  Core DTC Count: {0}", dtcCount);
            _logger.LogInformation("  StatusAvailabilityMask: {0:X02}\n", statusAvailabilityMask);

            for (int index = 0; index < dtcCount; index++)
            {
                int startByte = 1 + index * 4;
                byte dtcHSB = returnValues_core[startByte];
                byte dtcMSB = returnValues_core[startByte + 1];
                byte dtcLSB = returnValues_core[startByte + 2];

                int dtc = ((dtcHSB & 0xFF) << 16) + ((dtcMSB & 0xFF) << 8) + (dtcLSB & 0xFF);
                byte status = returnValues_core[startByte + 3];

                _logger.LogInformation("  Dtc #{0:D02}: {1:X06} Status: {2:X02}", index, dtc, status);
                tmp_dtc_core.Add(dtc);
            }
        }
        else
        {
            _logger.LogWarning("Could not read {0} from module", UDS_DiagCmd.ReadDTCInformation);
        }

        // Try reading secondary codes too: (0x2000) R4C9J758B_SWCalDoc.pdf pg 8229
        // Shadow errors
        UDS_ErrorCodes cmdStatus_shadow = ReadDataByIdentifier(UDS_RecordIdentifier.SekundaererFehlerspeicher, out byte[] returnValues_shadow, ref responseTesterId, ref responseModuleId);
        if (cmdStatus_shadow == UDS_ErrorCodes.UDS_NRC_NONE)
        {
            int dtcCount = returnValues_shadow.Length / 4;
            _logger.LogInformation("  Shadow DTC Count: {0}", dtcCount);

            for (int index = 0; index < dtcCount; index++)
            {
                int startByte = index * 4;
                byte dtcHSB = returnValues_shadow[startByte];
                byte dtcMSB = returnValues_shadow[startByte + 1];
                byte dtcLSB = returnValues_shadow[startByte + 2];

                int dtc = ((dtcHSB & 0xFF) << 16) + ((dtcMSB & 0xFF) << 8) + (dtcLSB & 0xFF);
                byte status = returnValues_shadow[startByte + 3];

                _logger.LogInformation("  Shadow Dtc #{0:D02}: {1:X06} Status: {2:X02}", index, dtc, status);
                tmp_dtc_shadow.Add(dtc);
            }
        }
        else
        {
            _logger.LogWarning("Could not read {0} from module", UDS_RecordIdentifier.SekundaererFehlerspeicher);
        }

        dtcs_core = tmp_dtc_core.Count > 0 ? tmp_dtc_core.OrderBy(x => x).ToArray() : new int[0];
        dtcs_shadow = tmp_dtc_shadow.Count > 0 ? tmp_dtc_shadow.OrderBy(x => x).ToArray() : new int[0];

        return cmdStatus_core;
    }

    public UDS_ErrorCodes SecurityAccessRequestSeed(UDS_SecurityAccessType type, byte[] testerid, out byte[] seed)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[6];
        Cmd[0] = (byte)UDS_DiagCmd.SecurityAccess;
        Cmd[1] = (byte)type;
        Cmd[2] = testerid[0];
        Cmd[3] = testerid[1];
        Cmd[4] = testerid[2];
        Cmd[5] = testerid[3];
        return ExecuteCommand(_testerId, _moduleId, Cmd, out seed, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes SecurityAccessRequestSeedEcuControl(UDS_SecurityAccessType type, out byte[] seed)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[2];
        Cmd[0] = (byte)UDS_DiagCmd.SecurityAccess;
        Cmd[1] = (byte)type;
        return ExecuteCommand(_testerId, _moduleId, Cmd, out seed, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes SecurityAccessSendKey(UDS_SecurityAccessType type, byte[] key)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[2 + key.Length];
        Cmd[0] = (byte)UDS_DiagCmd.SecurityAccess;
        Cmd[1] = (byte)((byte)(type) + 1);
        key.CopyTo(Cmd, 2);
        return ExecuteCommand(_testerId, _moduleId, Cmd, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes CommunicationControl(UDS_CommunicationMode mode, UDS_CommunicationType type)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[3];
        Cmd[0] = (byte)UDS_DiagCmd.CommunicationControl;
        Cmd[1] = (byte)mode;
        Cmd[2] = (byte)type;
        return ExecuteCommand(_testerId, _moduleId, Cmd, ref responseTesterId, ref responseModuleId);
    }

    /* After the successful execution of the CommunicationControl with the subfunction disableRxAndTx in the application, a
     * functional addressed TesterPresent message with suppressPosRspMsgIndicationBit (bit 7 of subfunction) = TRUE (1) (no response) is
     * sent approx. every 2 s to keep all servers in this state in order to not send normal communication messages.
     */

    public UDS_ErrorCodes TesterPresent(UDS_suppressPositiveResponse suppressPositiveResponse)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        return ExecuteCommand(_testerId, _moduleId, UDSFormatCmd(UDS_DiagCmd.TesterPresent, 0x00, suppressPositiveResponse), ref responseTesterId, ref responseModuleId, suppressPositiveResponse == UDS_suppressPositiveResponse.No);
    }

    // SecuredDataTransmission

    public UDS_ErrorCodes ControlDTCSetting(UDS_OnOff state)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        return ExecuteCommand(_testerId, _moduleId, UDSFormatCmd(UDS_DiagCmd.ControlDTCSetting, (byte)state), ref responseTesterId, ref responseModuleId);
    }

    // ResponseOnEvent

    // LinkControl

    public UDS_ErrorCodes ReadDataByIdentifier(UDS_RecordIdentifier recordIdentifier, out byte[] recorddata, ref uint responseTesterId, ref uint responseModuleId, bool disableTimeoutPrompt = false)
    {
        var recordCommonIdentifier = (ushort)recordIdentifier;
        byte[] Cmd = new byte[3];
        Cmd[0] = (byte)UDS_DiagCmd.ReadDataByIdentifier;
        Cmd[1] = (byte)(recordCommonIdentifier >> 8);
        Cmd[2] = (byte)recordCommonIdentifier;
        return ExecuteCommand(_testerId, _moduleId, Cmd, out recorddata, ref responseTesterId, ref responseModuleId, disableTimeoutPrompt: disableTimeoutPrompt);
    }

    public UDS_ErrorCodes ReadDataByIdentifier(UDS_RecordIdentifier recordIdentifier, out byte[] recorddata)
    {
        var recordCommonIdentifier = (ushort)recordIdentifier;

        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[3];
        Cmd[0] = (byte)UDS_DiagCmd.ReadDataByIdentifier;
        Cmd[1] = (byte)(recordCommonIdentifier >> 8);
        Cmd[2] = (byte)recordCommonIdentifier;
        return ExecuteCommand(_testerId, _moduleId, Cmd, out recorddata, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes ReadDataByIdentifier(ushort recordCommonIdentifier, out byte[] recorddata)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[3];
        Cmd[0] = (byte)UDS_DiagCmd.ReadDataByIdentifier;
        Cmd[1] = (byte)(recordCommonIdentifier >> 8);
        Cmd[2] = (byte)recordCommonIdentifier;
        return ExecuteCommand(_testerId, _moduleId, Cmd, out recorddata, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes ReadMemoryByAddress(uint startAddress, byte size, out byte[] memorydata, UDS_AddressFormat format = UDS_AddressFormat.COMPACT)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] memBlock = FormatAddressSize(startAddress, size, format);
        byte[] Cmd = new byte[1 + memBlock.Length];
        Cmd[0] = (byte)UDS_DiagCmd.ReadMemoryByAddress;
        memBlock.CopyTo(Cmd, 1);
        return ExecuteCommand(_testerId, _moduleId, Cmd, out memorydata, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes ReadMemoryByAddress(uint startAddress, uint size, out byte[] memorydata, UDS_AddressFormat format = UDS_AddressFormat.LONG)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] memBlock = FormatAddressSize(startAddress, size, format);
        byte[] Cmd = new byte[1 + memBlock.Length];
        Cmd[0] = (byte)UDS_DiagCmd.ReadMemoryByAddress;
        memBlock.CopyTo(Cmd, 1);
        return ExecuteCommand(_testerId, _moduleId, Cmd, out memorydata, ref responseTesterId, ref responseModuleId);
    }

    // ReadScalingDataByIdentifier

    // ReadDataByPeriodicIdentifier

    public UDS_ErrorCodes DynamicallyDefineDataIdentifier_defineByIdentifier(ushort dynamicallyDefinedDataIdentifier, ushort[] sourceDataIdentifier, byte[] positionInSourceDataRecord, byte[] memorySize)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[4 + sourceDataIdentifier.Length * 4];
        Cmd[0] = (byte)UDS_DiagCmd.DynamicallyDefineDataIdentifier;
        Cmd[1] = (byte)UDS_DynamicallyDefineDataIdentifierAction.defineByIdentifier;
        Cmd[2] = (byte)(dynamicallyDefinedDataIdentifier >> 8);
        Cmd[3] = (byte)dynamicallyDefinedDataIdentifier;

        for (int ddi = 0; ddi < sourceDataIdentifier.Length; ddi++)
        {
            int ddi_base = ddi * 4 + 4;
            Cmd[ddi_base + 0] = (byte)(sourceDataIdentifier[ddi] >> 8);
            Cmd[ddi_base + 1] = (byte)sourceDataIdentifier[ddi];
            Cmd[ddi_base + 2] = positionInSourceDataRecord[ddi];
            Cmd[ddi_base + 3] = memorySize[ddi];
        }
        return ExecuteCommand(_testerId, _moduleId, Cmd, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes DynamicallyDefineDataIdentifier_defineByMemoryAddress(ushort dynamicallyDefinedDataIdentifier, uint[] memoryAddress, uint[] memorySize, UDS_AddressFormat format = UDS_AddressFormat.COMPACT)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte recordSize = 5, memFormat = 0x14;
        switch (format)
        {
            case UDS_AddressFormat.COMPACT: recordSize = 5; memFormat = 0x14; break;
            case UDS_AddressFormat.MID: recordSize = 6; memFormat = 0x24; break;
            case UDS_AddressFormat.LONG: recordSize = 8; memFormat = 0x44; break;
        }

        byte[] Cmd = new byte[5 + memoryAddress.Length * recordSize];
        Cmd[0] = (byte)UDS_DiagCmd.DynamicallyDefineDataIdentifier;
        Cmd[1] = (byte)UDS_DynamicallyDefineDataIdentifierAction.defineByMemoryAddress;
        Cmd[2] = (byte)(dynamicallyDefinedDataIdentifier >> 8);
        Cmd[3] = (byte)dynamicallyDefinedDataIdentifier;
        Cmd[4] = memFormat;

        for (int ddi = 0; ddi < memoryAddress.Length; ddi++)
        {
            int ddi_base = ddi * recordSize + 5;
            byte[] memBlock = ResponseStripLeading(FormatAddressSize(memoryAddress[ddi], memorySize[ddi], format), 1);
            memBlock.CopyTo(Cmd, ddi_base);
        }
        return ExecuteCommand(_testerId, _moduleId, Cmd, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes DynamicallyDefineDataIdentifier_clearDynamicallyDefinedDataIdentifier(ushort dynamicallyDefinedDataIdentifier)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[4];
        Cmd[0] = (byte)UDS_DiagCmd.DynamicallyDefineDataIdentifier;
        Cmd[1] = (byte)UDS_DynamicallyDefineDataIdentifierAction.clearDynamicallyDefinedDataIdentifier;
        Cmd[2] = (byte)(dynamicallyDefinedDataIdentifier >> 8);
        Cmd[3] = (byte)dynamicallyDefinedDataIdentifier;
        return ExecuteCommand(_testerId, _moduleId, Cmd, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes WriteDataByIdentifier(UDS_RecordIdentifier recordIdentifier, byte[] recorddata)
    {
        return WriteDataByIdentifier((ushort)recordIdentifier, recorddata);
    }

    public UDS_ErrorCodes WriteDataByIdentifier(ushort recordIdentifier, byte[] recorddata)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[3 + recorddata.Length];
        Cmd[0] = (byte)UDS_DiagCmd.WriteDataByIdentifier;
        Cmd[1] = (byte)(recordIdentifier >> 8);
        Cmd[2] = (byte)recordIdentifier;
        recorddata.CopyTo(Cmd, 3);
        return ExecuteCommand(_testerId, _moduleId, Cmd, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes WriteMemoryByAddress(uint startAddress, byte size, byte[] memorydata, UDS_AddressFormat format = UDS_AddressFormat.COMPACT)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] memBlock = FormatAddressSize(startAddress, size, format);
        byte[] Cmd = new byte[1 + memBlock.Length + memorydata.Length];
        Cmd[0] = (byte)UDS_DiagCmd.WriteMemoryByAddress;
        memBlock.CopyTo(Cmd, 1);
        memorydata.CopyTo(Cmd, 1 + memBlock.Length);
        return ExecuteCommand(_testerId, _moduleId, Cmd, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes ReadDiagnosticInformation(UDS_DtcReadGroup DtcReadGroup, byte[] selectMask, out byte[] returnvalues)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[2 + selectMask.Length];
        Cmd[0] = (byte)UDS_DiagCmd.ReadDTCInformation;
        Cmd[1] = (byte)DtcReadGroup;
        selectMask.CopyTo(Cmd, 2);
        return ExecuteCommand(_testerId, _moduleId, Cmd, out returnvalues, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes ReadDiagnosticInformation(UDS_DtcReadGroup DtcReadGroup, UDS_DtcGroup groupOfDtc, out byte[] returnvalues)
    {
        byte[] selectMask = new byte[3];
        selectMask[0] = (byte)((uint)groupOfDtc >> 16);
        selectMask[1] = (byte)((uint)groupOfDtc >> 8);
        selectMask[2] = (byte)groupOfDtc;
        return ReadDiagnosticInformation(DtcReadGroup, selectMask, out returnvalues);
    }

    public UDS_ErrorCodes ClearDiagnosticInformation(UDS_DtcGroup DtcClearGroup)
    {
        return ClearDiagnosticInformation(_moduleId, DtcClearGroup);
    }

    public UDS_ErrorCodes ClearDiagnosticInformation(uint DtcClearGroup)
    {
        return ClearDiagnosticInformation(_moduleId, DtcClearGroup);
    }

    public UDS_ErrorCodes ClearDiagnosticInformation(byte moduleId, UDS_DtcGroup DtcClearGroup)
    {
        return ClearDiagnosticInformation(moduleId, (uint)DtcClearGroup);
    }

    public UDS_ErrorCodes ClearDiagnosticInformation(byte moduleId, uint DtcClearGroup)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[4];
        Cmd[0] = (byte)UDS_DiagCmd.ClearDiagnosticInformation;
        Cmd[1] = (byte)(DtcClearGroup >> 16);
        Cmd[2] = (byte)(DtcClearGroup >> 8);
        Cmd[3] = (byte)DtcClearGroup;

        UDS_ErrorCodes cmdstatus = ExecuteCommand(_testerId, moduleId, Cmd, ref responseTesterId, ref responseModuleId);

        if (moduleId == (byte)UDS_BMW.FunktionalAddressingIds.UDS_ALL) // functional ALL modules, can take upto extended NRC78 time extra
        {
            // consumes any NRC (or other response), if present
            var sleeptime = DiagTransportInterface.GetP2Starmax() + 1000;
            _logger.LogInformation("Sleeping for: [{sleeptime}]", sleeptime);
            System.Threading.Thread.Sleep(sleeptime);
            DiagTransportInterface.CableInterface.FlushBuffers();
        }

        return cmdstatus;
    }

    // InputOutputControlByIdentifier

    // set control, no additional arguments, no data back (just status)
    public UDS_ErrorCodes InputOutputControlByIdentifier(UDS_IOControlAction iocontrolAction, UDS_IOControlIdentifier iocontrolIdentifier)
    {
        return InputOutputControlByIdentifier(iocontrolAction, (ushort)iocontrolIdentifier);
    }

    public UDS_ErrorCodes InputOutputControlByIdentifier(UDS_IOControlAction iocontrolAction, ushort iocontrolIdentifier)
    {
        return InputOutputControlByIdentifier(iocontrolAction, iocontrolIdentifier, new byte[0]);
    }

    // set control with arguments, no data back (just status)
    public UDS_ErrorCodes InputOutputControlByIdentifier(UDS_IOControlAction iocontrolAction, UDS_IOControlIdentifier iocontrolIdentifier, byte[] arguments)
    {
        return InputOutputControlByIdentifier(iocontrolAction, (ushort)iocontrolIdentifier, arguments);
    }

    public UDS_ErrorCodes InputOutputControlByIdentifier(UDS_IOControlAction iocontrolAction, ushort iocontrolIdentifier, byte[] arguments)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[4 + arguments.Length];
        Cmd[0] = (byte)UDS_DiagCmd.InputOutputControlByIdentifier;
        Cmd[1] = (byte)((ushort)iocontrolIdentifier >> 8);
        Cmd[2] = (byte)((ushort)iocontrolIdentifier);
        Cmd[3] = (byte)iocontrolAction;
        arguments.CopyTo(Cmd, 4);
        return ExecuteCommand(_testerId, _moduleId, Cmd, ref responseTesterId, ref responseModuleId);
    }

    // set control with arguments, optional data back additonal to status
    public UDS_ErrorCodes InputOutputControlByIdentifier(UDS_IOControlAction iocontrolAction, UDS_IOControlIdentifier iocontrolIdentifier, byte[] arguments, out byte[] returnvalues)
    {
        return InputOutputControlByIdentifier(iocontrolAction, (ushort)iocontrolIdentifier, arguments, out returnvalues);
    }

    public UDS_ErrorCodes InputOutputControlByIdentifier(UDS_IOControlAction iocontrolAction, ushort iocontrolIdentifier, byte[] arguments, out byte[] returnvalues)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[4 + arguments.Length];
        Cmd[0] = (byte)UDS_DiagCmd.InputOutputControlByIdentifier;
        Cmd[1] = (byte)((ushort)iocontrolIdentifier >> 8);
        Cmd[2] = (byte)((ushort)iocontrolIdentifier);
        Cmd[3] = (byte)iocontrolAction;
        arguments.CopyTo(Cmd, 4);
        return ExecuteCommand(_testerId, _moduleId, Cmd, out returnvalues, ref responseTesterId, ref responseModuleId);
    }

    // RoutineControl

    // start routine, no additional arguments, no data back (just status)
    public UDS_ErrorCodes RoutineControl(UDS_RoutineAction routineAction, UDS_RoutineIdentifier routineIdentifier)
    {
        return RoutineControl(routineAction, (ushort)routineIdentifier);
    }

    public UDS_ErrorCodes RoutineControl(UDS_RoutineAction routineAction, ushort routineIdentifier)
    {
        return RoutineControl(routineAction, routineIdentifier, new byte[0]);
    }

    // start routine, no additional arguments, optional data back additonal to status
    public UDS_ErrorCodes RoutineControl(UDS_RoutineAction routineAction, UDS_RoutineIdentifier routineIdentifier, out byte[] returnvalues)
    {
        return RoutineControl(routineAction, (ushort)routineIdentifier, out returnvalues);
    }

    public UDS_ErrorCodes RoutineControl(UDS_RoutineAction routineAction, ushort routineIdentifier, out byte[] returnvalues)
    {
        return RoutineControl(routineAction, routineIdentifier, new byte[0], out returnvalues);
    }

    // start routine with arguments, no data back (just status)
    public UDS_ErrorCodes RoutineControl(UDS_RoutineAction routineAction, UDS_RoutineIdentifier routineIdentifier, byte[] arguments)
    {
        return RoutineControl(routineAction, (ushort)routineIdentifier, arguments);
    }

    public UDS_ErrorCodes RoutineControl(UDS_RoutineAction routineAction, ushort routineIdentifier, byte[] arguments)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[4 + arguments.Length];
        Cmd[0] = (byte)UDS_DiagCmd.RoutineControl;
        Cmd[1] = (byte)routineAction;
        Cmd[2] = (byte)((ushort)routineIdentifier >> 8);
        Cmd[3] = (byte)((ushort)routineIdentifier);
        arguments.CopyTo(Cmd, 4);
        return ExecuteCommand(_testerId, _moduleId, Cmd, ref responseTesterId, ref responseModuleId);
    }

    // start routine with arguments, optional data back additonal to status
    public UDS_ErrorCodes RoutineControl(UDS_RoutineAction routineAction, UDS_RoutineIdentifier routineIdentifier, byte[] arguments, out byte[] returnvalues)
    {
        return RoutineControl(routineAction, (ushort)routineIdentifier, arguments, out returnvalues);
    }

    public UDS_ErrorCodes RoutineControl(UDS_RoutineAction routineAction, ushort routineIdentifier, byte[] arguments, out byte[] returnvalues)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[4 + arguments.Length];
        Cmd[0] = (byte)UDS_DiagCmd.RoutineControl;
        Cmd[1] = (byte)routineAction;
        Cmd[2] = (byte)((ushort)routineIdentifier >> 8);
        Cmd[3] = (byte)((ushort)routineIdentifier);
        arguments.CopyTo(Cmd, 4);
        return ExecuteCommand(_testerId, _moduleId, Cmd, out returnvalues, ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes RequestUploadDownload(UDS_DiagCmd updown, uint startAddress, uint size, bool compressed, out ushort blockMaxLength, UDS_AddressFormat format = UDS_AddressFormat.LONG)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        blockMaxLength = 0;
        byte[] memBlock = FormatAddressSize(startAddress, size, format);
        byte[] Cmd = new byte[2 + memBlock.Length];
        Cmd[0] = (byte)updown;
        Cmd[1] = (byte)(compressed ? 0x10 : 0x00);
        memBlock.CopyTo(Cmd, 2);

        UDS_ErrorCodes cmdstatus = ExecuteCommand(_testerId, _moduleId, Cmd, out byte[] returnvalues, ref responseTesterId, ref responseModuleId);
        if (cmdstatus == UDS_ErrorCodes.UDS_NRC_NONE)
        {
            byte length = (byte)(returnvalues[0] >> 4); // normally 2
            if (length == 2 && returnvalues.Length >= 3) blockMaxLength = (ushort)((returnvalues[1] << 8) + returnvalues[2]);
            else if (length == 3 && returnvalues.Length >= 4) blockMaxLength = (ushort)((returnvalues[1] << 16) + (returnvalues[2] << 8) + returnvalues[3]);
            else if (length == 4 && returnvalues.Length >= 5) blockMaxLength = (ushort)((returnvalues[1] << 24) + (returnvalues[2] << 16) + (returnvalues[3] << 8) + returnvalues[4]);
            else blockMaxLength = 255;
        }
        return cmdstatus;
    }

    public UDS_ErrorCodes RequestDownload(uint startAddress, uint size, bool compressed, out ushort blockMaxLength, UDS_AddressFormat format = UDS_AddressFormat.LONG)
    {
        return RequestUploadDownload(UDS_DiagCmd.RequestDownload, startAddress, size, compressed, out blockMaxLength, format);
    }

    public UDS_ErrorCodes RequestUpload(uint startAddress, uint size, bool compressed, out ushort blockMaxLength, UDS_AddressFormat format = UDS_AddressFormat.LONG)
    {
        return RequestUploadDownload(UDS_DiagCmd.RequestUpload, startAddress, size, compressed, out blockMaxLength, format);
    }

    // transferData:Download
    public UDS_ErrorCodes TransferData(byte blockSequenceCounter, byte[] transferdata, out byte blockSequenceCounterRSP)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        blockSequenceCounterRSP = 0;
        byte[] Cmd = new byte[2 + transferdata.Length]; //  transferdata.Length = max 254
        Cmd[0] = (byte)UDS_DiagCmd.TransferData;
        Cmd[1] = blockSequenceCounter;
        transferdata.CopyTo(Cmd, 2);

        UDS_ErrorCodes cmdstatus = ExecuteCommand(_testerId, _moduleId, Cmd, out byte[] returnvalues, ref responseTesterId, ref responseModuleId);
        if (cmdstatus == UDS_ErrorCodes.UDS_NRC_NONE && returnvalues.Length >= 1)
        {
            blockSequenceCounterRSP = returnvalues[0];
        }
        return cmdstatus;
    }

    // transferData:Upload
    public UDS_ErrorCodes TransferData(byte blockSequenceCounter, out byte[] transferdata, out byte blockSequenceCounterRSP)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        //  transferdata.Length = max 254
        transferdata = new byte[0]; blockSequenceCounterRSP = 0;

        UDS_ErrorCodes cmdstatus = ExecuteCommand(_testerId, _moduleId, UDSFormatCmd(UDS_DiagCmd.TransferData, blockSequenceCounter), out byte[] returnvalues, ref responseTesterId, ref responseModuleId);
        if (cmdstatus == UDS_ErrorCodes.UDS_NRC_NONE && returnvalues.Length >= 1)
        {
            blockSequenceCounterRSP = returnvalues[0];
            transferdata = ResponseStripLeading(returnvalues, 1);
        }
        return cmdstatus;
    }

    public UDS_ErrorCodes RequestTransferExit()
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        return ExecuteCommand(_testerId, _moduleId, UDSFormatCmd(UDS_DiagCmd.RequestTransferExit), ref responseTesterId, ref responseModuleId);
    }

    public UDS_ErrorCodes StartRoutine_checkProgrammingPreCondition(out byte programmingPreCondition)
    {
        programmingPreCondition = 0xff;
        UDS_ErrorCodes cmdstatus = RoutineControl(UDS_RoutineAction.startRoutine, UDS_RoutineIdentifier.checkProgrammingPreCondition, out byte[] returnvalues);
        if (cmdstatus == UDS_ErrorCodes.UDS_NRC_NONE && returnvalues.Length >= 1)
        {
            programmingPreCondition = returnvalues[0];
        }
        return cmdstatus;
    }

    public string GetProgrammingPreConditionDescription(byte programmingPreCondition) => programmingPreCondition switch
    {
        1 => "engine speed is not null",
        2 => "immobilizer system is not unlocked",
        3 => "gear box input speed is not null",
        4 => "gear box output speed is not null",
        5 => "vehicle speed is not null",
        6 => "control active",
        7 => "ignition off-on required",
        8 => "no programming voltage",
        9 => "ignition not activated (clamp 15 off)",
        10 => "vehicle electrical system voltage too low",
        11 => "temperature too high",
        12 => "temperature too low",
        _ => "OK",
    };

    public UDS_ErrorCodes StartRoutine_SetEnergyMode(UDS_SetEnergyModeIdentifier EnergyMode)
    {
        byte[] arguments = new byte[1];
        arguments[0] = (byte)EnergyMode;
        return RoutineControl(UDS_RoutineAction.startRoutine, UDS_RoutineIdentifier.SetEnergyMode, arguments);
    }

    public UDS_ErrorCodes StartRoutine_SetExtendedModeFlash()
    {
        byte[] arguments = new byte[1];
        arguments[0] = 0x01; // hardcoded
        return RoutineControl(UDS_RoutineAction.startRoutine, UDS_RoutineIdentifier.SetExtendedMode, arguments);
    }

    public UDS_ErrorCodes StartRoutine_EraseMemoryAddressed(uint startAddress, uint size, UDS_AddressFormat format = UDS_AddressFormat.LONG)
    {
        byte[] memBlock = FormatAddressSize(startAddress, size, format);
        byte[] arguments = new byte[2 + memBlock.Length];
        arguments[0] = 0x01; // Erase Addressed
        arguments[1 + memBlock.Length] = 0x06; // Activation Command
        memBlock.CopyTo(arguments, 1);

        return RoutineControl(UDS_RoutineAction.startRoutine, UDS_RoutineIdentifier.EraseMemory, arguments);
    }

    public UDS_ErrorCodes StartRoutine_EraseMemoryIndexed(uint logicalBlockNumber)
    {
        byte[] arguments = new byte[7];
        arguments[0] = 0x02; // Erase Indexed
        arguments[1] = 0x40; // Format/Size specifier ?
        arguments[2] = (byte)(logicalBlockNumber >> 24);
        arguments[3] = (byte)(logicalBlockNumber >> 16);
        arguments[4] = (byte)(logicalBlockNumber >> 8);
        arguments[5] = (byte)logicalBlockNumber;
        arguments[6] = 0x06; // Activation Command
        return RoutineControl(UDS_RoutineAction.startRoutine, UDS_RoutineIdentifier.EraseMemory, arguments);
    }

    //
    // VERIFY THIS COMMAND
    //
    public UDS_ErrorCodes StartRoutine_CheckMemoryAddressed(uint startAddress, uint size, out byte checkResult, UDS_AddressFormat format = UDS_AddressFormat.LONG)
    {
        byte[] memBlock = FormatAddressSize(startAddress, size, format);
        checkResult = 1; // inCorrect default
        byte[] arguments = new byte[3 + memBlock.Length];
        arguments[0] = 0x11; // CheckMemory Addressed ??
        arguments[1 + memBlock.Length] = 0x00; // ??
        arguments[2 + memBlock.Length] = 0x00; // ??
        memBlock.CopyTo(arguments, 1);

        UDS_ErrorCodes cmdstatus = RoutineControl(UDS_RoutineAction.startRoutine, UDS_RoutineIdentifier.CheckMemory, arguments, out byte[] returnvalues);
        if (cmdstatus == UDS_ErrorCodes.UDS_NRC_NONE && returnvalues.Length >= 1)
        {
            checkResult = returnvalues[0];
        }
        return cmdstatus;
    }

    public UDS_ErrorCodes StartRoutine_CheckMemoryIndexed(uint logicalBlockNumber, out byte checkResult)
    {
        byte[] arguments = new byte[8];
        checkResult = 69; // inCorrect default
        arguments[0] = 0x12; // CheckMemory Indexed
        arguments[1] = 0x40; // Format/Size specifier ?
        arguments[2] = (byte)(logicalBlockNumber >> 24);
        arguments[3] = (byte)(logicalBlockNumber >> 16);
        arguments[4] = (byte)(logicalBlockNumber >> 8);
        arguments[5] = (byte)logicalBlockNumber;
        arguments[6] = 0x00; // ??
        arguments[7] = 0x00; // ??

        UDS_ErrorCodes cmdstatus = RoutineControl(UDS_RoutineAction.startRoutine, UDS_RoutineIdentifier.CheckMemory, arguments, out byte[] returnvalues);
        if (cmdstatus == UDS_ErrorCodes.UDS_NRC_NONE && returnvalues.Length >= 1)
        {
            checkResult = returnvalues[0];
        }
        return cmdstatus;
    }

    public UDS_ErrorCodes StartRoutine_ReadSweProgrammingStatus(uint logicalBlockNumber, out byte sweProgrammingStatus)
    {
        byte[] arguments = new byte[6];
        sweProgrammingStatus = 0; // default zero
        arguments[0] = 0x00;
        arguments[1] = 0x40; // Format/Size specifier ?
        arguments[2] = (byte)(logicalBlockNumber >> 24);
        arguments[3] = (byte)(logicalBlockNumber >> 16);
        arguments[4] = (byte)(logicalBlockNumber >> 8);
        arguments[5] = (byte)logicalBlockNumber;
        UDS_ErrorCodes cmdstatus = RoutineControl(UDS_RoutineAction.startRoutine, UDS_RoutineIdentifier.ReadSweProgrammingStatus, arguments, out byte[] returnvalues);
        if (cmdstatus == UDS_ErrorCodes.UDS_NRC_NONE && returnvalues.Length >= 1)
        {
            sweProgrammingStatus = returnvalues[0];
        }
        return cmdstatus;
    }

    public UDS_ErrorCodes StartRoutine_CheckProgrammingDependencies(out byte ProgrammingDependencies)
    {
        ProgrammingDependencies = 0;
        UDS_ErrorCodes cmdstatus = RoutineControl(UDS_RoutineAction.startRoutine, UDS_RoutineIdentifier.CheckProgDeps, out byte[] returnvalues);
        if (cmdstatus == UDS_ErrorCodes.UDS_NRC_NONE && returnvalues.Length >= 1)
        {
            ProgrammingDependencies = returnvalues[0];
        }
        return cmdstatus;
    }

    public UDS_ErrorCodes StartRoutine_ClearDTCShadowMemory()
    {
        return StartRoutine_ClearDTCShadowMemory(_moduleId);
    }

    public UDS_ErrorCodes StartRoutine_ClearDTCShadowMemory(byte moduleId)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[4];
        Cmd[0] = (byte)UDS_DiagCmd.RoutineControl;
        Cmd[1] = (byte)UDS_RoutineAction.startRoutine;
        Cmd[2] = (byte)((ushort)UDS_RoutineIdentifier.EraseInfoSpeicher >> 8);
        Cmd[3] = (byte)((ushort)UDS_RoutineIdentifier.EraseInfoSpeicher & 0xff);

        UDS_ErrorCodes cmdstatus = ExecuteCommand(_testerId, moduleId, Cmd, ref responseTesterId, ref responseModuleId);

        if (moduleId == (byte)UDS_BMW.FunktionalAddressingIds.UDS_ALL) // functional ALL modules, can take upto extended NRC78 time extra
        {
            // consumes any NRC (or other response), if present
            var sleeptime = DiagTransportInterface.GetP2Starmax() + 1000;
            _logger.LogInformation("Sleeping for: [{sleeptime}]", sleeptime);
            System.Threading.Thread.Sleep(sleeptime);
            DiagTransportInterface.CableInterface.FlushBuffers();
        }

        return cmdstatus;
    }

    public UDS_ErrorCodes ZGWDebugService(ushort debugCmd, byte[] arguments, out byte[] returnvalues)
    {
        uint responseTesterId = 0;
        uint responseModuleId = 0;
        byte[] Cmd = new byte[3 + arguments.Length];
        Cmd[0] = (byte)UDS_DiagCmd.ZGWDebugService;
        Cmd[1] = (byte)(debugCmd >> 8);
        Cmd[2] = (byte)debugCmd;
        arguments.CopyTo(Cmd, 3);
        return ExecuteCommand(_testerId, _moduleId, Cmd, out returnvalues, ref responseTesterId, ref responseModuleId);
    }

    public string GetCheckProgrammingDependenciesDescription(byte ProgrammingDependencies) => ProgrammingDependencies switch
    {
        0 => "Invalid",
        1 => "OK",
        2 => "minimum one SWE does not have the programming status CheckSignatureOK or the check on the programmingDependencies was not performed.",
        3 => "minimum one SWE does not match up with one HWE.",
        4 => "minimum one SWE does not match up with one SWE.",
        _ => "---",
    };

    public string GetProgrammingCounterStatusDescription(byte ProgrammingCounterStatus) => ProgrammingCounterStatus switch
    {
        0 => "several times",
        1 => "one or more times",
        2 => "not anymore",
        _ => "Invalid",
    };

    public string GetSweProgrammingStatusDescription(byte SweProgrammingStatus) => SweProgrammingStatus switch
    {
        2 => "EraseMemoryStarted",
        3 => "DataTransferStarted",
        4 => "CheckSignatureStarted",
        5 => "CheckSignatureOK",
        6 => "CheckSignatureNOK",
        7 => "MemorySuccessfullyErased",
        _ => "---",
    };
}