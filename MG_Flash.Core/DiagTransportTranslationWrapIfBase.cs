﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Globalization;
using System.Diagnostics;

using CableInterface;
using DiagTransportInterface;

using Microsoft.Extensions.Logging;

namespace DiagTransportTranslationWrapIf;

public abstract class DiagTransportTranslationWrapIfBase
{
    public DiagTransportInterfaceBase _DiagTransportInterface;

    public const int maxExecutionTimeDefault = 10000; // in ms, 10 seconds
    public const int maxWaitingTimeMSDefault = 20000; // in ms, 20 seconds

    public abstract UInt32 TransportVersion { get; }
    public abstract string TransportName { get; }
    public abstract int TransportMaxPayloadSize { get; }

    public abstract bool Initialize(string mode, int baudRate);

    public abstract void SetTimingParameters(ushort P2max, uint P2star);

    public abstract ushort GetP2max();

    public abstract ushort GetP2Starmax();

    public abstract TransportWrapperErrorCodes SendReceive(byte testerId, byte moduleId, byte[] snddata, ref uint responseTesterId, ref uint responseModuleId, out byte[] rcvdata, bool responseExpected = true, int maxExecutionTime = maxExecutionTimeDefault, int maxWaitingTimeMS = maxWaitingTimeMSDefault, bool enableLogging = true);

    // all frames should contain data and should not have dependance on eachother
    public abstract TransportWrapperErrorCodes SendReceivePipelined(byte testerId, List<PipelineSlot> cmdPipeline, out List<PipelineSlot> rspPipeline, bool responseExpected = true, int maxExecutionTime = maxExecutionTimeDefault, int maxWaitingTimeMS = maxWaitingTimeMSDefault, bool enableLogging = true);

    public struct PipelineSlot
    {
        public byte moduleId; // targetId (module) for this command
        public byte SQT; // sequence nr in this set
        public uint length; // payload length
        public byte[] data; // payload, should be prepared to send as is.
        public uint responseTesterId; // ??? does this belong here?
        public uint responseModuleId; // ??? does this belong here?

        public PipelineSlot(byte _moduleId, byte _SQT, uint _length, byte[] _data, uint _responseTesterId = 0, uint _responseModuleId = 0)
        {
            moduleId = _moduleId;
            SQT = _SQT;
            length = _length;
            data = _data;
            responseTesterId = _responseTesterId; // ??? does this belong here?
            responseModuleId = _responseModuleId; // ??? does this belong here?
        }
    }

    public enum TransportWrapperErrorCodes : uint
    {
        TRANSPORT_ERR_NONE = 0,
        TRANSPORT_ERR_SND_IF = 1,
        TRANSPORT_ERR_ECHO_NA = 2,
        TRANSPORT_ERR_ECHO_IV = 3,
        TRANSPORT_ERR_RSP_NOHEAD = 4,
        TRANSPORT_ERR_RSP_HEAD_IV = 5,
        TRANSPORT_ERR_RSP_NOTAIL = 6,
        TRANSPORT_ERR_RSP_CHKS = 7,
    }

    public string GetErrorDescription(TransportWrapperErrorCodes errorCode)
    {
        switch (errorCode)
        {
            case TransportWrapperErrorCodes.TRANSPORT_ERR_NONE: return "postiveResponse";
            case TransportWrapperErrorCodes.TRANSPORT_ERR_SND_IF: return "could not send data to interface";
            case TransportWrapperErrorCodes.TRANSPORT_ERR_ECHO_NA: return "no echo recieved from interface";
            case TransportWrapperErrorCodes.TRANSPORT_ERR_ECHO_IV: return "invalid echo recieved from interface";
            case TransportWrapperErrorCodes.TRANSPORT_ERR_RSP_NOHEAD: return "no header recieved from module";
            case TransportWrapperErrorCodes.TRANSPORT_ERR_RSP_HEAD_IV: return "invalid header recieved from module";
            case TransportWrapperErrorCodes.TRANSPORT_ERR_RSP_NOTAIL: return "no tail recieved from module";
            case TransportWrapperErrorCodes.TRANSPORT_ERR_RSP_CHKS: return "invalid checksum on data received from interface";
            default: return string.Format("unknown Error Code 0x{0:X04}", errorCode);
        }
    }

    public bool AttachTransportToTransportWrapper(string transportName, ILoggerFactory loggerFactory)
    {
        switch (transportName)
        {
            case "ENET_RAW": _DiagTransportInterface = new TransportENET(loggerFactory.CreateLogger<TransportENET>()); break;
            case "BMW-FAST": _DiagTransportInterface = new TransportBMW_Fast(loggerFactory.CreateLogger<TransportBMW_Fast>()); break;
            case "CANRAW_DIAG": _DiagTransportInterface = new TransportCanRaw("DIAG_FILTER", loggerFactory.CreateLogger<TransportCanRaw>()); break;
            case "CANRAW": _DiagTransportInterface = new TransportCanRaw("NONE", loggerFactory.CreateLogger<TransportCanRaw>()); break;
        }
        return true;
    }

    public bool AttachCableToTransport(CableInterfaceBase CableInterface)
    {
        _DiagTransportInterface.CableInterface = CableInterface;
        return true;
    }

    public CableInterfaceBase CableInterface
    { get { return _DiagTransportInterface.CableInterface; } }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            _DiagTransportInterface.Dispose();
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}