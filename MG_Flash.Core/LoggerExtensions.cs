﻿using System.Globalization;
using System.Text;
using Microsoft.Extensions.Logging;

namespace DiagTransportInterface;

public static class LoggerExtensions
{
    public static void LogInfo(this ILogger logger, string message, bool ignore, params object[] args)
    {
        if (!ignore)
        {
            logger.LogInformation(message, args);
        }
    }

    public static void LogData(this ILogger logger, byte[] data, int offset, int length, string info)
    {
        var stringBuilder = new StringBuilder(length);
        for (int i = 0; i < length; i++)
        {
            stringBuilder.Append(string.Format(CultureInfo.InvariantCulture, "{0:X02} ", data[offset + i]));
        }

        logger.LogTrace(string.Format(" (" + info + "): ({0}) {1}", length, stringBuilder));
    }

    public static void LogFormat(this ILogger logger, string format, params object[] args)
    {
        for (int i = 0; i < args.Length; i++)
        {
            if (args[i] == null)
            {
                continue;
            }
            if (args[i] is string)
            {
                args[i] = "'" + (string)args[i] + "'";
            }
            if (args[i] is byte[] argArray)
            {
                StringBuilder stringBuilder = new StringBuilder(argArray.Length);
                foreach (byte arg in argArray)
                {
                    stringBuilder.Append(string.Format(CultureInfo.InvariantCulture, "{0:X02} ", arg));
                }

                args[i] = "[" + stringBuilder + "]";
            }
        }
        logger.LogTrace(string.Format(format, args));
    }
}