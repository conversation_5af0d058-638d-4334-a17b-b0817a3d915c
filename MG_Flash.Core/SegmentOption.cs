﻿/// OPTIONS FOR U<PERSON> BRANCH:
#define UDS_WRITE_VIN
#define USE_UDS_PROTOCOL
#define BOSCH_MG1_UNLOCK_BYPASS
// #define BOSCH_MG1_UNLOCK_BYPASS
#define LMV_XCP_OVER_FLEX_UNLOCK

/// GENERIC OPTIONS (Both Branches)
// #define DOWNLOAD_BACKSTEP_AUTOFAIL

namespace UNI_Flash;

public enum SegmentOption : byte
{
    LAR = 0x00, // Linear address range
    ROM_INT = 0x01, // ROM / EPROM, internal
    ROM_EXT = 0x02, // ROM / EPROM, external
    NVRAM = 0x03, // NV-RAM
    RAM_INT = 0x04, // RAM, internal (short MOV)
    RAM_EXT = 0x05, // RAM, external (x data MOV)
    FLASH_INT = 0x06, // Flash EPROM, internal
    UIF = 0x07, // User Info Field Memory
    VOM = 0x08, // Vehicle Order Data Memory
    FLASH_EXT = 0x09, // Flash EPROM, external
    RAM_INTB = 0xB, // RAM, internal (long MOV / Register)-
}