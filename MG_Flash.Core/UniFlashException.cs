﻿using System;
using static DiagProtocolInterface.DiagProtocolBase;

namespace UNI_Flash.Utils;

public class UniFlashException : Exception
{
    public UniFlashException(string message, Exception e) : base(message, e)
    {
    }

    public UniFlashException(string message) : base(message)
    {
    }
}

public class UniFlashConnectionFailedException : UniFlashException
{
    public UniFlashConnectionFailedException(string host, int port) : base($"could not connect to {host}:{port}")
    {
    }
}

public class UniFlashModuleInitializationException : UniFlashException
{
    public UniFlashModuleInitializationException(ModuleIds moduleId) : base($"could not initialize module {moduleId}")
    {
        ModuleId = moduleId;
    }

    public ModuleIds ModuleId { get; }
}

public class UniFlashExecutionFailedException : UniFlashException
{
    public UniFlashExecutionFailedException(string cmdLine, string[] args) : base(cmdLine)
    {
        Args = args;
    }

    public string[] Args { get; }
}

public class UniFlashReadCodingException : UniFlashException
{
    public ModuleIds ModuleId { get; }

    public UniFlashReadCodingException(ModuleIds moduleId) : this(moduleId, null)
    {
    }

    public UniFlashReadCodingException(ModuleIds moduleId, Exception e) : base($"could not read coding from module {moduleId}", e)
    {
        ModuleId = moduleId;
    }
}

public class UniFlashModuleDoesNotHaveCodingException : UniFlashReadCodingException
{
    public UniFlashModuleDoesNotHaveCodingException(ModuleIds moduleId) : base(moduleId)
    {
    }
}