﻿using DiagProtocolUtils;
using EdiabasTest;
using MG_Flash.Core.RsaKeys;
using System;
using System.Security.Cryptography;

namespace UNI_Flash;

public class KeyExchange_UDS : RsaSignatureBigInteger
{
    public byte[] ComputeRSAKeyBigIntegerModPow(byte[] testerId, byte[] btldId, byte[] seed)
    {
        var btldIdMayorUint = ProtocolUtils.btldMayorIdOf(btldId);
        var (modulus, privateExponent) = RsaKeys.GetL3Key(btldIdMayorUint);
        if (string.IsNullOrEmpty(modulus) || string.IsNullOrEmpty(privateExponent))
        {
            Console.WriteLine("\n  ERROR Could not find private key for btld id: 0x{0:X08}, aborting", btldIdMayorUint);
            return new byte[0];
        }

        var md5Seed = new byte[16];
        testerId.CopyTo(md5Seed, 0);
        ProtocolUtils.btldMayorIdBytesOf(btldId).CopyTo(md5Seed, 4);
        seed.CopyTo(md5Seed, 8);

        return ImportKeyComputeMd5HashRsaSignature(modulus, privateExponent, md5Seed);
    }
}

public class CodingSignature_UDS : RsaSignatureBigInteger
{
    public byte[] ComputeCodingSignatureBigIntegerModPow(byte[] btldId, byte[] codingDataArray)
    {
        var btldIdMayorUint = ProtocolUtils.btldMayorIdOf(btldId);
        var (modulus, privateExponent) = RsaKeys.GetNcdKey(btldIdMayorUint);
        if (string.IsNullOrEmpty(modulus) || string.IsNullOrEmpty(privateExponent))
        {
            Console.WriteLine("\n  ERROR Could not find private key for btld id: 0x{0:X08}, signature NOT corrected", btldIdMayorUint);
            return new byte[0];
        }

        return ImportKeyComputeMd5HashRsaSignature(modulus, privateExponent, codingDataArray);
    }
}

public class RsaSignatureBigInteger
{
    public byte[] ImportKeyComputeMd5HashRsaSignature(string pkN, string pkE, byte[] hashInput)
    {
        BigInteger pkNbigInt = new BigInteger(pkN, 16);
        BigInteger pkEbigInt = new BigInteger(pkE, 16);
        return ComputeMd5HashRsaSignature(pkNbigInt, pkEbigInt, hashInput);
    }

    public byte[] ImportKeyComputeMd5HashRsaSignature(byte[] pkN, byte[] pkE, byte[] hashInput)
    {
        var pkNbigInt = new BigInteger(pkN);
        var pkEbigInt = new BigInteger(pkE);
        return ComputeMd5HashRsaSignature(pkNbigInt, pkEbigInt, hashInput);
    }

    public byte[] ImportKeyComputeSha1HashRsaSignature(string pkN, string pkE, byte[] hashInput)
    {
        BigInteger pkNbigInt = new BigInteger(pkN, 16);
        BigInteger pkEbigInt = new BigInteger(pkE, 16);
        return ComputeSha1HashRsaSignature(pkNbigInt, pkEbigInt, hashInput);
    }

    public byte[] ComputeMd5HashRsaSignature(BigInteger pkNbigInt, BigInteger pkEbigInt, byte[] hashInput)
    {
        // using biginteger modPow
        var md5Crypto = new MD5CryptoServiceProvider();
        var md5Hash = new BigInteger(StringFunctions.SwitchEndian(md5Crypto.ComputeHash(hashInput)));

        byte[] signature = md5Hash.modPow(pkEbigInt, pkNbigInt).getBytes();

        if ((signature.Length % 4) != 0) // padd signature with leading zero's if needed so we are fully word aligned after
        {
            byte[] sigtemp = signature;
            signature = new byte[(signature.Length / 4 + 1) * 4];
            sigtemp.CopyTo(signature, 4 - sigtemp.Length % 4);
        }

        signature = StringFunctions.ExtractKeySectionArrayWordseSwap(signature, 0, signature.Length);

        byte[] signaturePlusSize = new byte[4 + signature.Length];
        signaturePlusSize[3] = (byte)(signature.Length / 4);
        signature.CopyTo(signaturePlusSize, 4);

        return signaturePlusSize;
    }

    public byte[] ComputeSha1HashRsaSignature(BigInteger pkNbigInt, BigInteger pkEbigInt, byte[] hashInput)
    {
        // using biginteger modPow
        var sha1Crypto = new SHA1CryptoServiceProvider();
        var sha1Hash = new BigInteger(StringFunctions.SwitchEndian(sha1Crypto.ComputeHash(hashInput)));
        return sha1Hash.modPow(pkEbigInt, pkNbigInt).getBytes();
    }
}