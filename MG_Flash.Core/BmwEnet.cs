﻿using System;
using System.Threading;
using System.Diagnostics;
using System.Net.Sockets;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using System.Runtime.CompilerServices;

namespace CableInterface;

public class BmwEnet : CableInterfaceBase
{
    private readonly ILoggerFactory _loggerFactory;
    private readonly ILogger<BmwEnet> _logger;
    private const int EnetRecieveFcDataTimeout = 1000;
    private const int ResetTimeout = 0; // Not available/Not used for ENET
    private const int TransBufferSize = 0x10010; // transmit buffer size
    private const int TcpAckTimeout = 5000;
    private const int ConnectTimeout = 5000;
    private static readonly long TickResolMs = Stopwatch.Frequency / 1000;

    protected byte[] DataBuffer = new byte[TransBufferSize];
    protected byte[] AckBuffer = new byte[TransBufferSize];

    private TcpClient TcpDiagClient;
    private NetworkStream TcpDiagStream;
    private AutoResetEvent TcpDiagStreamRecEvent;
    private TcpClient TcpControlClient;
    private NetworkStream TcpControlStream;
    private object TcpDiagStreamSendLock;
    private object TcpDiagStreamRecLock;
    private byte[] TcpDiagBuffer;
    private int TcpDiagRecLen;
    private long LastTcpDiagRecTime;
    private Queue<byte[]> TcpDiagRecQueue;
    private bool _disposed;

    public string EnetHost { get; }
    public int EnetPort { get; }

    public BmwEnet(string host, int port, ILoggerFactory loggerFactory, bool ignoreLogs = false)
        : base(loggerFactory.CreateLogger<BmwEnet>())
    {
        if (string.IsNullOrEmpty(host) || port <= 0)
        {
            throw new ArgumentException($"Error parsing ENET IP {host}:{port}");
        }

        _loggerFactory = loggerFactory;

        _logger = loggerFactory.CreateLogger<BmwEnet>();

        EnetHost = host;
        EnetPort = port;

        TcpDiagStreamRecEvent = new AutoResetEvent(false);
        TcpDiagStreamSendLock = new object();
        TcpDiagStreamRecLock = new object();
        TcpDiagBuffer = new byte[TransBufferSize];
        TcpDiagRecLen = 0;
        LastTcpDiagRecTime = DateTime.MinValue.Ticks;
        TcpDiagRecQueue = new Queue<byte[]>();

        if (!ignoreLogs)
        {
            _logger.LogInformation("ENET IP: {EnetHost}:{EnetPort}", EnetHost, EnetPort);
        }
    }

    protected override void Dispose(bool disposing)
    {
        base.Dispose(disposing);
        if (disposing)
        {
            InterfaceDisconnect();
        }
    }

    ~BmwEnet()
    {
        Dispose(false);
    }

    public override UInt32 InterfaceVersion
    {
        get
        {
            return 1;
        }
    }

    public override string InterfaceName
    {
        get
        {
            return "ENET";
        }
    }

    public override string InterfaceMode
    {
        get
        {
            return "Ethernet";
        }
    }

    public override int InterfaceMaxFrameSize
    {
        get
        {
            // ISOTP limit (length = 3 nibbles wide so 4095 bytes)
            return 0xfff;
        }
    }

    public override int InterfaceReadTimeout
    {
        get
        {
            return EnetRecieveFcDataTimeout;
        }
    }

    public override int InterfaceResetTimeout
    {
        get
        {
            return ResetTimeout;
        }
    }

    // Checks if a usable cable is present
    public override bool Connect()
    {
        if (TcpDiagClient != null) // already connected, report don't retry
        {
            return true;
        }

        try
        {
            _disposed = false;

            TcpDiagClient = new TcpClientWithTimeout(EnetHost, EnetPort, ConnectTimeout,
                _loggerFactory.CreateLogger<TcpClientWithTimeout>()).Connect();
            TcpDiagStream = TcpDiagClient.GetStream();

            TcpDiagRecLen = 0;
            LastTcpDiagRecTime = DateTime.MinValue.Ticks;
            lock (TcpDiagStreamRecLock)
            {
                TcpDiagRecQueue.Clear();
            }
            StartReadTcpDiag(6);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, $"{nameof(Connect)} error");

            InterfaceDisconnect();

            return false;
        }

        return true;
    }

    private bool InterfaceDisconnect()
    {
        bool result = true;
        try
        {
            _disposed = true;
            // Diag disconnect
            if (TcpDiagStream != null)
            {
                TcpDiagStream.Close();
                TcpDiagStream = null;
            }

            if (TcpDiagClient != null)
            {
                TcpDiagClient.Close();
                TcpDiagClient = null;
            }

            // Control disconnect
            if (TcpControlStream != null)
            {
                TcpControlStream.Close();
                TcpControlStream = null;
            }

            if (TcpControlClient != null)
            {
                TcpControlClient.Close();
                TcpControlClient = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "InterfaceDisconnect error");
            result = false;
        }
        return result;
    }

    public override bool CableConnectedAndAlive()
    {
        if ((TcpDiagClient != null) && (TcpDiagStream != null)) return true;
        return false;
    }

    public override bool Initialize(string mode, int canBitRate)
    {
        if (CableConnectedAndAlive()) return true;
        else return false;
    }

    // Resets the Cable (if present)
    public override bool CableReset()
    {
        return true;
    }

    public override bool FlushBuffers()
    {
        // TODO
        return true;
    }

    protected bool StartReadTcpDiag(int telLength)
    {
        NetworkStream localStream = TcpDiagStream;
        if (localStream == null)
        {
            return false;
        }
        try
        {
            RuntimeHelpers.EnsureSufficientExecutionStack();
            localStream.BeginRead(TcpDiagBuffer, TcpDiagRecLen, telLength - TcpDiagRecLen, TcpDiagReceiver, TcpDiagStream);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "StartReadTcpDiag error");
            return false;
        }
        return true;
    }

    protected void TcpDiagReceiver(IAsyncResult ar)
    {
        if (_disposed)
        {
            return;
        }

        try
        {
            NetworkStream networkStream = TcpDiagStream;
            if (networkStream == null)
            {
                return;
            }
            if (TcpDiagRecLen > 0)
            {
                if ((Stopwatch.GetTimestamp() - LastTcpDiagRecTime) > 300 * TickResolMs)
                {   // pending telegram parts too late
                    TcpDiagRecLen = 0;
                }
            }
            int recLen = networkStream.EndRead(ar);
            if (recLen > 0)
            {
                LastTcpDiagRecTime = Stopwatch.GetTimestamp();
                TcpDiagRecLen += recLen;
            }
            int nextReadLength = 6;
            if (TcpDiagRecLen >= 6)
            {   // header received
                long telLen = (((long)TcpDiagBuffer[0] << 24) | ((long)TcpDiagBuffer[1] << 16) | ((long)TcpDiagBuffer[2] << 8) | TcpDiagBuffer[3]) + 6;
                if (TcpDiagRecLen == telLen)
                {   // telegram received
                    switch (TcpDiagBuffer[5])
                    {
                        case 0x01:  // diag data
                        case 0x02:  // ack
                        case 0xFF:  // nack
                            lock (TcpDiagStreamRecLock)
                            {
                                if (TcpDiagRecQueue.Count > 256)
                                {
                                    TcpDiagRecQueue.Dequeue();
                                }
                                byte[] recTelTemp = new byte[telLen];
                                Array.Copy(TcpDiagBuffer, recTelTemp, TcpDiagRecLen);
                                TcpDiagRecQueue.Enqueue(recTelTemp);
                                TcpDiagStreamRecEvent.Set();
                            }
                            break;

                        case 0x12:  // alive check
                            TcpDiagBuffer[0] = 0x00;
                            TcpDiagBuffer[1] = 0x00;
                            TcpDiagBuffer[2] = 0x00;
                            TcpDiagBuffer[3] = 0x02;
                            TcpDiagBuffer[4] = 0x00;
                            TcpDiagBuffer[5] = 0x13;    // alive check response
                            TcpDiagBuffer[6] = 0x00;
                            TcpDiagBuffer[7] = 0xF4; // (byte)TesterAddress;
                            lock (TcpDiagStreamSendLock)
                            {
                                networkStream.Write(TcpDiagBuffer, 0, 8);
                            }
                            break;
                    }
                    TcpDiagRecLen = 0;
                }
                else if (TcpDiagRecLen > telLen)
                {
                    TcpDiagRecLen = 0;
                }
                else if (telLen > TcpDiagBuffer.Length)
                {   // telegram too large -> remove all
                    while (TcpDiagStream.DataAvailable)
                    {
                        TcpDiagStream.ReadByte();
                    }
                    TcpDiagRecLen = 0;
                }
                else
                {
                    nextReadLength = (int)telLen;
                }
            }
            StartReadTcpDiag(nextReadLength);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "TcpDiagReceiver error");
            TcpDiagRecLen = 0;
            StartReadTcpDiag(6);
        }
    }

    protected bool SendData(byte[] sendData, int length, byte targetAddr, int timeout, bool enableLogging)
    {
        if (TcpDiagStream == null)
        {
            return false;
        }
        try
        {
            lock (TcpDiagStreamRecLock)
            {
                TcpDiagStreamRecEvent.Reset();
                TcpDiagRecQueue.Clear();
            }

            byte sourceAddr = 0xF4; // (byte)TesterAddress;
            int dataLength = length;

            int payloadLength = dataLength + 2;
            DataBuffer[0] = (byte)((payloadLength >> 24) & 0xFF);
            DataBuffer[1] = (byte)((payloadLength >> 16) & 0xFF);
            DataBuffer[2] = (byte)((payloadLength >> 8) & 0xFF);
            DataBuffer[3] = (byte)(payloadLength & 0xFF);
            DataBuffer[4] = 0x00;   // Payoad type: Diag message
            DataBuffer[5] = 0x01;
            DataBuffer[6] = sourceAddr;
            DataBuffer[7] = targetAddr;
            Array.Copy(sendData, 0, DataBuffer, 8, dataLength);
            int sendLength = dataLength + 8;
            lock (TcpDiagStreamSendLock)
            {
                TcpDiagStream.Write(DataBuffer, 0, sendLength);
            }

            var tcpAckTimeout = Math.Min(TcpAckTimeout, timeout);
            // wait for ack
            int recLen = ReceiveAck(AckBuffer, tcpAckTimeout, enableLogging);
            if (recLen < 0)
            {
                if (enableLogging) _logger.LogInformation("*** No ack received");
                return false;
            }
            if ((recLen == 6) && (AckBuffer[5] == 0xFF))
            {
                if (enableLogging) _logger.LogInformation("nack received: resending");
                lock (TcpDiagStreamSendLock)
                {
                    TcpDiagStream.Write(DataBuffer, 0, sendLength);
                }
                recLen = ReceiveAck(AckBuffer, tcpAckTimeout, enableLogging);
                if (recLen < 0)
                {
                    if (enableLogging) _logger.LogInformation("*** No ack received");
                    return false;
                }
            }
            if ((recLen < 6) || (recLen > sendLength) || (AckBuffer[5] != 0x02))
            {
                if (enableLogging) LogData(AckBuffer, 0, recLen, "*** Ack frame invalid");
                return false;
            }
            AckBuffer[4] = DataBuffer[4];
            AckBuffer[5] = DataBuffer[5];
            for (int i = 4; i < recLen; i++)
            {
                if (AckBuffer[i] != DataBuffer[i])
                {
                    if (enableLogging) LogData(AckBuffer, 0, recLen, "*** Ack data invalid");
                    return false;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "SendData error");
            return false;
        }
        return true;
    }

    protected bool ReceiveData(out byte[] receiveData, ref uint responseTesterId, ref uint responseModuleId, int timeout, bool enableLogging)
    {
        responseTesterId = 0;
        responseModuleId = 0;

        receiveData = Array.Empty<byte>();

        if (TcpDiagStream == null)
        {
            return false;
        }
        try
        {
            int recLen = ReceiveTelegram(DataBuffer, timeout);
            if (recLen < 4)
            {
                return false;
            }

            if (DataBuffer[5] != 0x01)
            {
                if (enableLogging) LogFormat("*** Invalid data telegram type: {0:X02}", DataBuffer[5]);
                return false;
            }

            // ReSharper disable RedundantCast
            int dataLen = (((int)DataBuffer[0] << 24) | ((int)DataBuffer[1] << 16) | ((int)DataBuffer[2] << 8) | DataBuffer[3]) - 2;

            // ReSharper restore RedundantCast
            if ((dataLen < 1) || ((dataLen + 8) > recLen) || (dataLen > 0xFFFF))
            {
                if (enableLogging) LogFormat("*** Invalid data length: {0}", dataLen);
                return false;
            }

            // source address (moduleId) is lost
            byte moduleId = DataBuffer[6];
            responseModuleId = (uint)moduleId;

            byte testerId = DataBuffer[7]; // ??? correct byte?
            responseTesterId = (uint)testerId;

            receiveData = new byte[dataLen];
            Array.Copy(DataBuffer, 8, receiveData, 0, dataLen);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "ReceiveData error");
            return false;
        }
        return true;
    }

    protected int ReceiveTelegram(byte[] receiveData, int timeout)
    {
        if (TcpDiagStream == null)
        {
            return -1;
        }
        int recLen;
        try
        {
            int recTels;
            lock (TcpDiagStreamRecLock)
            {
                recTels = TcpDiagRecQueue.Count;
                if (recTels == 0)
                {
                    TcpDiagStreamRecEvent.Reset();
                }
            }
            if (recTels == 0)
            {
                if (!TcpDiagStreamRecEvent.WaitOne(timeout, false))
                {
                    return -1;
                }
            }
            lock (TcpDiagStreamRecLock)
            {
                if (TcpDiagRecQueue.Count > 0)
                {
                    byte[] recTelFirst = TcpDiagRecQueue.Dequeue();
                    recLen = recTelFirst.Length;
                    Array.Copy(recTelFirst, receiveData, recLen);
                }
                else
                {
                    recLen = 0;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "ReceiveTelegram error");
            return -1;
        }
        return recLen;
    }

    protected int ReceiveAck(byte[] receiveData, int timeout, bool enableLogging)
    {
        long startTick = Stopwatch.GetTimestamp();
        for (; ; )
        {
            int recLen = ReceiveTelegram(receiveData, timeout);
            if (recLen < 0)
            {
                return recLen;
            }
            if ((recLen >= 6) &&
                ((receiveData[5] == 0x02) || (receiveData[5] == 0xFF)))
            {   // ACK or NACK received
                return recLen;
            }
            if (enableLogging) _logger.LogInformation("*** Ignore Non ack");
            if ((Stopwatch.GetTimestamp() - startTick) > timeout * TickResolMs)
            {
                if (enableLogging) _logger.LogInformation("*** Ack timeout");
                return -1;
            }
        }
    }

    public override bool InterfaceSendData(byte[] sendData, int length, uint sendId, uint responseId, int timeout)
    {
        return SendData(sendData, length, (byte)responseId, timeout, true);
    }

    public override bool InterfaceReceiveData(out byte[] receiveData, ref uint responseTesterId, ref uint responseModuleId, int timeout, int timeoutTelEnd)
    {
        return ReceiveData(out receiveData, ref responseTesterId, ref responseModuleId, timeout, true);
    }
}