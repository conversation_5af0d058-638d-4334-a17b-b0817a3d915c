﻿using EdiabasTest;
using System;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace UNI_Flash;

public class IntegrationStepInfo
{
    public IntegrationStepType Type { get; set; } = IntegrationStepType.Unknown;
    public string MainSeries { get; set; } = string.Empty;
    public byte Year { get; set; } = 0x00;
    public byte Month { get; set; } = 0x00;
    public ushort Build { get; set; } = 0x00;
    public Version Version => new Version(Year, Month, Build);
    public string Name => $"{MainSeries}-{Year:D2}-{Month:D2}-{Build:D3}";

    public bool IsValid => !string.IsNullOrWhiteSpace(MainSeries)
                           && Month != 0x00
                           && Build != 0x00;

    public IntegrationStepInfo()
    {
    }

    public IntegrationStepInfo(IntegrationStepType type, string mainSeries, byte year, byte month, ushort build)
    {
        Type = type;
        MainSeries = mainSeries;
        Year = year;
        Month = month;
        Build = build;
    }

    public IntegrationStepInfo(IntegrationStepType type, byte[] byteArray)
    {
        if (byteArray == null || byteArray.Length != 8)
        {
            throw new ArgumentException("Cannot parse IStep Version: Too short!");
        }

        // only check last 7 bytes for for empty
        var checkEmpty = StringFunctions.ExtractSectionArray(byteArray, 1, 7);
        if (checkEmpty.All(b => b == 0) || checkEmpty.All(b => b == 0xff))
        {
            throw new ArgumentException("Cannot parse IStep Version: Empty data!");
        }

        Type = type;
        MainSeries = Encoding.ASCII.GetString(byteArray, 0, 4);
        Year = byteArray[4];
        Month = byteArray[5];
        Build = (ushort)((byteArray[6] << 8) + byteArray[7]);
    }

    /// <summary>
    /// Can parse:
    /// #DME__8x1#D1#PST#EPST:R1C9J8B3B#F020-18-07-523_#CCv7.1_
    /// #S15A_S18A-19-07-490
    /// S18A-19-07-490
    /// F020-16-17-506
    /// </summary>
    public IntegrationStepInfo(IntegrationStepType type, string input, string prefix = "")
    {
        var iStepPattern = $@"{prefix}(?<MainSeries>(?:[A-Za-z][A-Za-z0-9]\d[A-Za-z0-9]_?)+)-(?<Year>\d\d)-(?<Month>\d\d)-(?<Build>\d\d\d)";
        var iStepMatches = Regex.Match(input, iStepPattern);
        if (!iStepMatches.Success)
        {
            throw new ArgumentException("Cannot parse IStep Version string: Input is invalid!");
        }

        Type = type;
        MainSeries = iStepMatches.Groups["MainSeries"].Value;
        Year = Convert.ToByte(iStepMatches.Groups["Year"].Value, 10);
        Month = Convert.ToByte(iStepMatches.Groups["Month"].Value, 10);
        Build = Convert.ToUInt16(iStepMatches.Groups["Build"].Value, 10);
    }

    public override string ToString()
    {
        // example I-Step (Current): F020-16-17-506
        return $"I-Step ({Type}): {Name}";
    }
}