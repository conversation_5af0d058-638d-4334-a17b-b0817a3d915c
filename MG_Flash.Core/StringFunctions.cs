﻿using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Xml;
using Microsoft.Win32;

namespace EdiabasTest;

using System.Reflection;

public class StringFunctions
{
    public static string ConvertBytesToHexStringPart(byte[] data, int start, int length)
    {
        var retData = new byte[length];
        if (data.Length >= start + length)
        {
            for (int i = start; i < start + length; i++)
            {
                retData[i - start] = data[i];
            }
            string hexString = "";
            for (int i = 0; i <= retData.GetUpperBound(0); i++)
            {
                hexString += ConvertByteToHexString(retData[i]);
            }
            return hexString;
        }

        return "";
    }

    public static string ConvertBytesPartToString(byte[] data, int start, int length)
    {
        var retData = new byte[length];
        if (data.Length >= start + length)
        {
            for (int i = start; i < start + length; i++)
            {
                retData[i - start] = data[i];
            }
            return ConvertBytesToHexString(retData).Replace(" ", "");
        }
        return "";
    }

    public static string ConvertBytesToHexString(byte[] data)
    {
        string hexString = "";
        foreach (byte t in data)
        {
            hexString += ConvertByteToHexString(t);
            hexString += " ";
        }
        return hexString.TrimEnd();
    }

    public static string ConvertByteToHexString(byte val)
    {
        return System.Convert.ToString(val, 16).ToUpper().PadLeft(2, '0');
    }

    public static byte[] ConvertHexStringToBytes(string strInput)
    {
        int i = 0; int x = 0;
        byte[] bytes = new byte[(strInput.Length) / 2];
        while (strInput.Length > i + 1)
        {
            long lngDecimal = Convert.ToInt32(strInput.Substring(i, 2), 16);
            bytes[x] = Convert.ToByte(lngDecimal); i = i + 2; ++x;
        }
        return bytes;
    }

    public static string RemoveLeadingZero(string input)
    {
        int startPos = 0;
        for (int i = 0; i < input.Length; i++)
        {
            if (input.Substring(i, 1) != "0")
            {
                startPos = i;
                break;
            }
        }
        return input.Substring(startPos, input.Length - startPos);
    }

    public static string ConvertBytesToAsciiString(byte[] data, int start, int length)
    {
        var retData = new byte[length];
        if (data.Length >= start + length)
        {
            for (int i = start; i < start + length; i++)
            {
                retData[i - start] = data[i];
            }

            return Encoding.UTF8.GetString(retData);
        }
        return "";
    }

    public static string ConvertUint32ToHexString(uint value)
    {
        byte[] data = SwitchEndian(BitConverter.GetBytes(value));
        return ConvertBytesToHexString2(data);
    }

    public static string ConvertBytesToHexString2(byte[] data)
    {
        string hexString = "";
        for (int i = 0; i <= data.GetUpperBound(0); i++)
        {
            hexString += ConvertByteToHexString(data[i]);
        }
        return hexString;
    }

    public static byte[] SwitchEndian(byte[] recBytes)
    {
        // Switch Endianes of the byte order so the bytes 01 02 03 04 Becomes 04 03 02 01
        Array.Reverse(recBytes);
        return recBytes;
    }

    public static int SwitchEndianInt(int input)
    {
        byte[] data = BitConverter.GetBytes(input);
        var output = new byte[data.Length];
        for (int i = 0; i < data.Length; i++)
        {
            output[(data.Length - 1) - i] = data[i];
        }
        return BitConverter.ToInt32(output, 0);
    }

    public static short SwitchEndianShort(short input)
    {
        byte[] data = BitConverter.GetBytes(input);
        var output = new byte[data.Length];
        for (int i = 0; i < data.Length; i++)
        {
            output[(data.Length - 1) - i] = data[i];
        }
        return (short)BitConverter.ToUInt16(output, 0);
    }

    public static UInt16 SwitchEndianUInt16(UInt16 input)
    {
        byte[] data = BitConverter.GetBytes(input);
        var output = new byte[data.Length];
        for (int i = 0; i < data.Length; i++)
        {
            output[(data.Length - 1) - i] = data[i];
        }
        return BitConverter.ToUInt16(output, 0);
    }

    public static Int16 SwitchEndianInt16(Int16 input)
    {
        byte[] data = BitConverter.GetBytes(input);
        var output = new byte[data.Length];
        for (int i = 0; i < data.Length; i++)
        {
            output[(data.Length - 1) - i] = data[i];
        }
        return BitConverter.ToInt16(output, 0);
    }

    public static uint SwitchEndianUInt32(uint input)
    {
        byte[] data = BitConverter.GetBytes(input);
        var output = new byte[data.Length];
        for (int i = 0; i < data.Length; i++)
        {
            output[(data.Length - 1) - i] = data[i];
        }
        return BitConverter.ToUInt32(output, 0);
    }

    public static byte[] ExtractSectionArray(byte[] data, int startPos, int len)
    {
        if (data.Length < (startPos + len))
            return data;

        byte[] results = new byte[len];
        int pos = 0;
        for (int i = startPos; i < startPos + len; i++)
            results[pos++] = data[i];
        return results;
    }

    // lenght must be word aligned (so dividable by 4)
    public static byte[] ExtractKeySectionArrayWordseSwap(byte[] data, int startPos, int len)
    {
        if (data.Length < (startPos + len))
            return data;

        byte[] results = new byte[len];

        int inpos = startPos + len - 1;
        int outpos = 0;

        for (int i = 0; i < len; i += 4)
        {
            results[outpos + i + 3] = data[inpos - i - 0];
            results[outpos + i + 2] = data[inpos - i - 1];
            results[outpos + i + 1] = data[inpos - i - 2];
            results[outpos + i + 0] = data[inpos - i - 3];
        }
        return results;
    }

    public static bool CopyArraySection(byte[] destination, int destinationAddress, byte[] source, int sourceAddress, int length)
    {
        try
        {
            for (int i = destinationAddress; i < destinationAddress + length; i++)
            {
                destination[i] = source[sourceAddress++];
            }
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return false;
        }
    }
}