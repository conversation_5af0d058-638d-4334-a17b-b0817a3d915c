﻿using MgFlasher.Views.Controls;
using CoreGraphics;
using Foundation;
using ViewRenderer = Microsoft.Maui.Controls.Handlers.Compatibility.ViewRenderer;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Platform;
using UIKit;

namespace MgFlasher.Platforms.iOS.Renderers;

[Preserve(AllMembers = true)]
public class ClassicProgressBarRenderer : ViewRenderer
{
    public override void Draw(CGRect rect)
    {
        base.Draw(rect);

        using (CGContext g = UIGraphics.GetCurrentContext())
        {
            var progressBar = (ClassicProgressBar)Element;

            var progress = (float)progressBar.Progress;
            var backColor = progressBar.BaseColor.ToPlatform();
            var frontColor = progressBar.ForegroundProgressColor.ToPlatform();
            var halfSizeMode = progressBar.HalfSizeMode;

            DrawProgressBar(g, progress, backColor, frontColor, halfSizeMode);
        }
    }

    private void DrawProgressBar(CGContext g, nfloat progress, UIColor backColor, UIColor frontColor, bool halfSizeMode)
    {
        DrawBackgroundLine(g, backColor);
        DrawProgressLine(g, progress, frontColor, halfSizeMode);
    }

    private void DrawBackgroundLine(CGContext g, UIColor backColor)
    {
        g.SetLineWidth(Bounds.Height);

        CGRect rect = new CGRect(0, 0, Bounds.Width, Bounds.Height);

        backColor.SetStroke();

        g.AddRect(rect);
        g.DrawPath(CGPathDrawingMode.FillStroke);
    }

    private void DrawProgressLine(CGContext g, nfloat progress, UIColor frontColor, bool halfSizeMode)
    {
        g.SetLineWidth(150f * Bounds.Height / 100f);

        var intervals = halfSizeMode ? new nfloat[] { 12, 4 } : new nfloat[] { 6, 2 };
        g.SetLineDash(3, intervals);

        var centerY = Bounds.GetMidY();
        var width = Bounds.Width;

        var path = new CGPath();
        path.MoveToPoint(0, centerY);
        path.AddQuadCurveToPoint(0, centerY, width * progress, centerY);

        frontColor.SetStroke();

        g.AddPath(path);
        g.DrawPath(CGPathDrawingMode.Stroke);
    }

    protected override void OnElementPropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        base.OnElementPropertyChanged(sender, e);

        if (e.PropertyName == ProgressBar.ProgressProperty.PropertyName ||
            e.PropertyName == ClassicProgressBar.BaseColorProperty.PropertyName ||
            e.PropertyName == ClassicProgressBar.ForegroundProgressColorProperty.PropertyName ||
            e.PropertyName == ClassicProgressBar.HalfSizeModeProperty.PropertyName)
        {
            SetNeedsDisplay();
        }

        if (e.PropertyName == VisualElement.WidthProperty.PropertyName ||
            e.PropertyName == VisualElement.HeightProperty.PropertyName)
        {
            SetNeedsDisplay();
        }
    }
}