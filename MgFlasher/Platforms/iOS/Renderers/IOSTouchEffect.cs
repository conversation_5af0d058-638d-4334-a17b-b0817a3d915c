﻿using Microsoft.Maui.Controls.Platform;
using System.Linq;
using UIKit;
using Forms = MgFlasher.Behaviors;

namespace MgFlasher.Platforms.iOS.Renderers;

public class IOSTouchEffect : PlatformEffect
{
    UIView view;
    IOSTouchRecognizer touchRecognizer;

    protected override void OnAttached()
    {
        // Get the iOS UIView corresponding to the Element that the effect is attached to
        view = Control == null ? Container : Control;

        // Uncomment this line if the UIView does not have touch enabled by default
        //view.UserInteractionEnabled = true;

        // Get access to the TouchEffect class in the .NET Standard library
        Forms.TouchEffect effect = (Forms.TouchEffect)Element.Effects.FirstOrDefault(e => e is Forms.TouchEffect);

        if (effect != null && view != null)
        {
            // Create a TouchRecognizer for this UIView
            touchRecognizer = new IOSTouchRecognizer(Element, view, effect);
            view.AddGestureRecognizer(touchRecognizer);
        }
    }

    protected override void OnDetached()
    {
        if (touchRecognizer != null)
        {
            // Clean up the TouchRecognizer object
            touchRecognizer.Detach();

            // Remove the TouchRecognizer from the UIView
            view.RemoveGestureRecognizer(touchRecognizer);
        }
    }
}