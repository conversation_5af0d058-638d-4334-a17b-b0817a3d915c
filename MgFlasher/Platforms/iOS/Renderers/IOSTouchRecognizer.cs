﻿using System.Linq;
using UIKit;
using Forms = MgFlasher.Behaviors;
using System.Collections.Generic;
using Foundation;
using CoreGraphics;
using System;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace MgFlasher.Platforms.iOS.Renderers;

public class IOSTouchRecognizer : UIGestureRecognizer
{
    Element element;        // Forms element for firing events
    UIView view;            // iOS UIView
    Forms.TouchEffect touchEffect;
    bool capture;

    static Dictionary<UIView, IOSTouchRecognizer> viewDictionary =
        new Dictionary<UIView, IOSTouchRecognizer>();

    static Dictionary<long, IOSTouchRecognizer> idToTouchDictionary =
        new Dictionary<long, IOSTouchRecognizer>();

    public IOSTouchRecognizer(Element element, UIView view, Forms.TouchEffect touchEffect)
    {
        this.element = element;
        this.view = view;
        this.touchEffect = touchEffect;

        viewDictionary.Add(view, this);
    }

    public void Detach()
    {
        viewDictionary.Remove(view);
    }

    // touches = touches of interest; evt = all touches of type UITouch
    public override void TouchesBegan(NSSet touches, UIEvent evt)
    {
        base.TouchesBegan(touches, evt);

        foreach (UITouch touch in touches.Cast<UITouch>())
        {
            long id = touch.Handle;
            FireEvent(this, id, Forms.TouchActionType.Pressed, touch, true);

            if (!idToTouchDictionary.ContainsKey(id))
            {
                idToTouchDictionary.Add(id, this);
            }
        }

        // Save the setting of the Capture property
        capture = touchEffect.Capture;
    }

    public override void TouchesMoved(NSSet touches, UIEvent evt)
    {
        base.TouchesMoved(touches, evt);

        foreach (UITouch touch in touches.Cast<UITouch>())
        {
            long id = touch.Handle;

            if (capture)
            {
                FireEvent(this, id, Forms.TouchActionType.Moved, touch, true);
            }
            else
            {
                CheckForBoundaryHop(touch);

                if (idToTouchDictionary[id] != null)
                {
                    FireEvent(idToTouchDictionary[id], id, Forms.TouchActionType.Moved, touch, true);
                }
            }
        }
    }

    public override void TouchesEnded(NSSet touches, UIEvent evt)
    {
        base.TouchesEnded(touches, evt);

        foreach (UITouch touch in touches.Cast<UITouch>())
        {
            long id = touch.Handle;

            if (capture)
            {
                FireEvent(this, id, Forms.TouchActionType.Released, touch, false);
            }
            else
            {
                CheckForBoundaryHop(touch);

                if (idToTouchDictionary[id] != null)
                {
                    FireEvent(idToTouchDictionary[id], id, Forms.TouchActionType.Released, touch, false);
                }
            }
            idToTouchDictionary.Remove(id);
        }
    }

    public override void TouchesCancelled(NSSet touches, UIEvent evt)
    {
        base.TouchesCancelled(touches, evt);

        foreach (UITouch touch in touches.Cast<UITouch>())
        {
            long id = touch.Handle;
            idToTouchDictionary.Remove(id);
        }
    }

    void CheckForBoundaryHop(UITouch touch)
    {
        long id = touch.Handle;

        // TODO: Might require converting to a List for multiple hits
        IOSTouchRecognizer recognizerHit = null;

        foreach (UIView view in viewDictionary.Keys)
        {
            CGPoint location = touch.LocationInView(view);

            if (new CGRect(new CGPoint(), view.Frame.Size).Contains(location))
            {
                recognizerHit = viewDictionary[view];
            }
        }
        if (recognizerHit != idToTouchDictionary[id])
        {
            idToTouchDictionary[id] = recognizerHit;
        }
    }

    void FireEvent(IOSTouchRecognizer recognizer, long id, Forms.TouchActionType actionType, UITouch touch, bool isInContact)
    {
        // Convert touch location to Xamarin.Forms Point value
        CGPoint cgPoint = touch.LocationInView(recognizer.View);
        Point xfPoint = new Point(cgPoint.X, cgPoint.Y);

        // Get the method to call for firing events
        Action<Element, Forms.TouchActionEventArgs> onTouchAction = recognizer.touchEffect.OnTouchAction;

        // Call that method
        onTouchAction(recognizer.element,
            new Forms.TouchActionEventArgs(id, actionType, xfPoint, isInContact));
    }
}