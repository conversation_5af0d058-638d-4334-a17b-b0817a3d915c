﻿using System;

using UIKit;

namespace MgFlasher.Platforms.iOS.Renderers;

public class UILongPressGestureRecognizerMultiGesturesSupport : UILongPressGestureRecognizer
{
    public UILongPressGestureRecognizerMultiGesturesSupport(Action<UILongPressGestureRecognizer> action) : base(action)
    {
    }

    public override bool ShouldBeRequiredToFailByGestureRecognizer(UIGestureRecognizer otherGestureRecognizer)
    {
        return true;
    }
}