﻿using CoreGraphics;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Platform.Compatibility;

namespace MgFlasher.Platforms.iOS.Renderers;

public class CustomTitleViewContainer : UIContainerView
{
    public CustomTitleViewContainer(View view) : base(view)
    {
        TranslatesAutoresizingMaskIntoConstraints = false;
    }

    public override CGSize IntrinsicContentSize => UILayoutFittingExpandedSize;
}