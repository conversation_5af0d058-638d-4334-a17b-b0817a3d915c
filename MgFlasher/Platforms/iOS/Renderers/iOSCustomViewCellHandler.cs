using Microsoft.Maui.Platform;
using MgFlasher.Views.Controls;
using Microsoft.Maui.Controls;
using UIKit;

namespace MgFlasher.Platforms.iOS.Renderers;

public class iOSCustomViewCellHandler : Microsoft.Maui.Controls.Handlers.Compatibility.ViewCellRenderer
{
    public override UITableViewCell GetCell(Cell item, UITableViewCell reusableCell, UITableView tv)
    {
        var cell = base.GetCell(item, reusableCell, tv);

        cell.SelectedBackgroundView = new UIView
        {
            BackgroundColor = ((CustomViewCell)item).SelectedBackgroundColor.ToPlatform()
        };

        return cell;
    }
}