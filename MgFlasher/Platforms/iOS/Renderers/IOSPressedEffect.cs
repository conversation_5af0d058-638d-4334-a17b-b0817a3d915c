﻿using MgFlasher.Behaviors;
using Microsoft.Maui.Controls.Platform;
using UIKit;

namespace MgFlasher.Platforms.iOS.Renderers;

public class IOSPressedEffect : PlatformEffect
{
    private bool _attached;
    private readonly UILongPressGestureRecognizer _longPressGestureRecognizer;
    private readonly UITapGestureRecognizer _pressGestureRecognizer;
    private readonly UITapGestureRecognizer _doublePressGestureRecognizer;

    public IOSPressedEffect()
    {
        _longPressGestureRecognizer = new UILongPressGestureRecognizerMultiGesturesSupport(OnLongClick);
        _pressGestureRecognizer = new UITapGestureRecognizer(OnClick);
        _doublePressGestureRecognizer = new UITapGestureRecognizer(OnDoubleClick) { NumberOfTapsRequired = 2 };
    }

    protected override void OnAttached()
    {
        if (!_attached)
        {
            Container.AddGestureRecognizer(_longPressGestureRecognizer);
            Container.AddGestureRecognizer(_pressGestureRecognizer);
            Container.AddGestureRecognizer(_doublePressGestureRecognizer);
            _attached = true;
        }
    }

    private void OnClick()
    {
        PressedEffect.GetPressedCommand(Element)?.Execute(null);
    }

    private void OnDoubleClick()
    {
        PressedEffect.GetDoublePressedCommand(Element)?.Execute(null);
    }

    private void OnLongClick(UILongPressGestureRecognizer gestureRecognizer)
    {
        if (gestureRecognizer.State == UIGestureRecognizerState.Ended)
        {
            PressedEffect.GetLongPressedCommand(Element)?.Execute(null);
        }
    }

    protected override void OnDetached()
    {
        if (_attached)
        {
            Container.RemoveGestureRecognizer(_longPressGestureRecognizer);
            Container.RemoveGestureRecognizer(_pressGestureRecognizer);
            Container.RemoveGestureRecognizer(_doublePressGestureRecognizer);
            _attached = false;
        }
    }
}