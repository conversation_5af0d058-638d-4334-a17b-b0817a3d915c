<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>UIDeviceFamily</key>
		<array>
			<integer>1</integer>
			<integer>2</integer>
		</array>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>arm64</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>XSAppIconAssets</key>
		<string>Assets.xcassets/appicon.appiconset</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLName</key>
				<string>#</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>mgflasher</string>
				</array>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
			</dict>
		</array>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
		</dict>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>AppCenterAppDelegateForwarderEnabled</key>
		<string>0</string>
		<key>UIStatusBarStyle</key>
		<string>UIStatusBarStyleLightContent</string>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>This app needs access to the photo gallery for saving maps.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Picture Picker uses photo library</string>
		<key>NSLocalNetworkUsageDescription</key>
		<string>This app needs to connect to the car by ENET Cable or ENET Wi-Fi Adapter</string>
		<key>NSBluetoothAlwaysUsageDescription</key>
		<string>This app needs to connect to the car by ENET Adapter</string>
		<key>NSCameraUsageDescription</key>
		<string>This app needs access to the camera to take photos.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>This app needs access to microphone for taking videos.</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>This app needs access to the photo gallery for picking photos and videos.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>This app needs access to photos gallery for picking photos and videos.</string>
		<key>UIBackgroundModes</key>
		<array>
			<string>remote-notification</string>
			<string>fetch</string>
			<string>processing</string>
		</array>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>com.shiny.job</string>
			<string>com.shiny.jobpower</string>
			<string>com.shiny.jobnet</string>
			<string>com.shiny.jobpowernet</string>
		</array>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Current location of car will be stored in log file produced during run with our logger.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Current location of car will be stored in log file produced during run with our logger.</string>
		<key>NSLocationTemporaryUsageDescriptionDictionary</key>
		<dict>
			<key>TemporaryFullAccuracyUsageDescription</key>
			<string>The accurate location of car is useful to identify some exterior parameters during run with our logger.</string>
		</dict>
	</dict>
</plist>
