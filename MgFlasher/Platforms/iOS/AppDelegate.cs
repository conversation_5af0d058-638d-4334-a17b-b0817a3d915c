﻿using Foundation;
using System;
using System.Threading.Tasks;
using MgFlasher.Cache;
using MgFlasher.Helpers;
using MgFlasher.Platforms.iOS.Services;
using MgFlasher.Services;
using UIKit;
using Microsoft.Maui.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Maui;
using ObjCRuntime;
using Platform = Microsoft.Maui.ApplicationModel.Platform;

namespace MgFlasher.Platforms.iOS;

[Register("AppDelegate")]
public class AppDelegate : MauiUIApplicationDelegate
{
    protected override MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();

    [Export("application:didRegisterForRemoteNotificationsWithDeviceToken:")]
    public void RegisteredForRemoteNotifications(UIApplication application, NSData deviceToken)
        => global::Shiny.Hosting.Host.Lifecycle.OnRegisteredForRemoteNotifications(deviceToken);

    [Export("application:didFailToRegisterForRemoteNotificationsWithError:")]
    public void FailedToRegisterForRemoteNotifications(UIApplication application, NSError error)
        => global::Shiny.Hosting.Host.Lifecycle.OnFailedToRegisterForRemoteNotifications(error);

    [Export("application:didReceiveRemoteNotification:fetchCompletionHandler:")]
    public void DidReceiveRemoteNotification(UIApplication application, NSDictionary userInfo, Action<UIBackgroundFetchResult> completionHandler)
        => global::Shiny.Hosting.Host.Lifecycle.OnDidReceiveRemoteNotification(userInfo, completionHandler);

    [Export("applicationDidReceiveMemoryWarning:")]
    public async void ReceiveMemoryWarning(UIApplication application)
    {
        var memoryBefore = GC.GetTotalMemory(false);
        await DependencyResolver.Resolve<ICacheService>().OnShutdownAsync();
        GC.Collect();
        var memoryAfter = GC.GetTotalMemory(false);
        DependencyResolver.Resolve<ILogger<AppDelegate>>().LogWarning(
            "Received memory warning. Memory cleared from " +
            "[{memoryBefore}] to [{memoryAfter}] ({memoryAfter - memoryBefore} diff)",
            memoryBefore, memoryAfter, memoryAfter - memoryBefore);
    }

    public override bool FinishedLaunching(UIApplication app, NSDictionary options)
    {
        AppDomain.CurrentDomain.UnhandledException += CurrentDomainOnUnhandledException;
        TaskScheduler.UnobservedTaskException += TaskSchedulerOnUnobservedTaskException;
        Runtime.MarshalManagedException += MarshalManagedException;

        UINavigationBar.Appearance.TintColor = UIColor.White;
        return base.FinishedLaunching(app, options);
    }

    public override bool OpenUrl(UIApplication app, NSUrl url, NSDictionary options)
    {
        if (Platform.OpenUrl(app, url, options))
            return true;

        return false;
    }

    private void MarshalManagedException(object sender, MarshalManagedExceptionEventArgs args)
    {
        args.ExceptionMode = MarshalManagedExceptionMode.UnwindNativeCode;
        PlatformUnhandledExceptionHandler.LogError<AppDelegate>(args.Exception, "MarshalManagedException error");
    }

    private static void TaskSchedulerOnUnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs unobservedTaskExceptionEventArgs)
    {
        unobservedTaskExceptionEventArgs.SetObserved();
        PlatformUnhandledExceptionHandler.LogError<AppDelegate>(unobservedTaskExceptionEventArgs.Exception, "TaskSchedulerOnUnobservedTaskException error");
    }

    private static void CurrentDomainOnUnhandledException(object sender, UnhandledExceptionEventArgs unhandledExceptionEventArgs)
    {
        PlatformUnhandledExceptionHandler.LogError<AppDelegate>(unhandledExceptionEventArgs.ExceptionObject as Exception, "CurrentDomainOnUnhandledException error");
    }
}