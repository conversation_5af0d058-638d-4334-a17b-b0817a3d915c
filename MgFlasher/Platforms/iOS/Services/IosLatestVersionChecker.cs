using System;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using MgFlasher.Flasher.Client.Common;
using MgFlasher.Services;
using Microsoft.Maui.ApplicationModel;

namespace MgFlasher.Platforms.iOS.Services;

public class IosLatestVersionChecker : ILatestVersionChecker
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly string _bundleIdentifier;

    public IosLatestVersionChecker(IHttpClientFactory httpClientFactory)
    {
        _bundleIdentifier = Foundation.NSBundle.MainBundle.ObjectForInfoDictionary("CFBundleIdentifier").ToString();
        _httpClientFactory = httpClientFactory;
    }

    public async Task<(bool UsingLatestVersion, string LatestVersion)> GetVersionInfoAsync()
    {
        using var http = _httpClientFactory.CreateBackendClient("https://itunes.apple.com", TimeSpan.FromSeconds(10));
        var requestUri = $"/lookup?bundleId={_bundleIdentifier}";
        var response = await http.GetAsync(requestUri);
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        var appLookup = JsonNode.Parse(content) ?? throw new InvalidOperationException("Could not parse response");

        var storeVersion = appLookup["results"][0]["version"].ToString();
        var usingLatest = int.Parse(AppInfo.VersionString) >= int.Parse(storeVersion);

        return (usingLatest, storeVersion);
    }
}