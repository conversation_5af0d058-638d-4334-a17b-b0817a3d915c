﻿using MgFlasher.Flasher.Services.Cars;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Extensions.Logging;
using UIKit;

namespace MgFlasher.Platforms.iOS.Services;

public class IosPowerManager(ILogger<IosPowerManager> logger) : IPowerManager
{
    public void AcquireWakeLock(bool disableBluetooth)
    {
        logger.LogInformation("acquiring wake lock");

        MainThread.BeginInvokeOnMainThread(() =>
        {
            UIApplication.SharedApplication.IdleTimerDisabled = true;
        });
    }

    public void ReleaseWakeLock()
    {
        logger.LogInformation("releasing wake lock");

        MainThread.BeginInvokeOnMainThread(() =>
        {
            UIApplication.SharedApplication.IdleTimerDisabled = false;
        });
    }
}