﻿using System;
using System.Net.Http;
using MgFlasher.Flasher.Client.Common;
using Microsoft.Extensions.Logging;
using Sentry;

namespace MgFlasher.Platforms.iOS.Services;

public class PlatformHttpClientFactory : IPlatformHttpClientFactory
{
    private readonly ILoggerFactory _loggerFactory;

    public PlatformHttpClientFactory(ILoggerFactory loggerFactory)
    {
        _loggerFactory = loggerFactory;
    }

    public HttpClient Create(TimeSpan timeout, string baseUri)
    {
        BrowserTrunk.Delete();

        var nativeHandler = new NSUrlSessionHandler()
        {
            AutomaticDecompression = System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate,
            BypassBackgroundSessionCheck = true
        };
        var loggingHandler = new WebClientLoggingHandler(nativeHandler, _loggerFactory.CreateLogger<WebClientLoggingHandler>());
        var sentryHttpHandler = new SentryHttpMessageHandler(loggingHandler);

        return new HttpClient(sentryHttpHandler)
        {
            Timeout = timeout,
            BaseAddress = new Uri(baseUri)
        };
    }
}