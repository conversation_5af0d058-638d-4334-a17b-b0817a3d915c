﻿using Foundation;
using Microsoft.Extensions.Logging;
using System;
using MgFlasher.Helpers;
using UIKit;

namespace MgFlasher.Platforms.iOS.Services;

public class BrowserTrunk
{
    public static void Delete()
    {
        try
        {
            DeleteCookies();
        }
        catch (Exception ex)
        {
            DependencyResolver.Resolve<ILogger<BrowserTrunk>>().LogError(ex, "Error deleting cached cookies");
        }
    }

    private static void DeleteCookies()
    {
        if (UIDevice.CurrentDevice.CheckSystemVersion(9, 0))
        {
            NSHttpCookieStorage.SharedStorage.RemoveCookiesSinceDate(NSDate.DistantPast);
        }
        else
        {
            var cookies = NSHttpCookieStorage.SharedStorage.Cookies;

            foreach (var c in cookies)
            {
                NSHttpCookieStorage.SharedStorage.DeleteCookie(c);
            }
        }
    }
}