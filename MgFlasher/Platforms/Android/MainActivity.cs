﻿using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Android;
using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using AndroidX.Core.App;
using AndroidX.Core.Content;
using MgFlasher.Platforms.Android.Renderers;
using MgFlasher.Services;
using Microsoft.Maui;

namespace MgFlasher.Platforms.Android;

[Activity(Label = "MG Flasher", Icon = "@drawable/Icon", Theme = "@style/Maui.SplashTheme", MainLauncher = true,
    LaunchMode = LaunchMode.SingleTop,
    ScreenOrientation = ScreenOrientation.Portrait,
    ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation,
    HardwareAccelerated = true, Exported = true)]
[IntentFilter([Shiny.ShinyPushIntents.NotificationClickAction], Categories = ["android.intent.category.DEFAULT"])]
public class MainActivity : MauiAppCompatActivity
{
    public static readonly int PickImageId = 1000;
    private const int RequestCodeWriteExternalStorage = 10;

    public TaskCompletionSource<Stream> PickImageTaskCompletionSource { set; get; }

    protected override void OnActivityResult(int requestCode, Result resultCode, Intent intent)
    {
        base.OnActivityResult(requestCode, resultCode, intent);

        if (requestCode == PickImageId)
        {
            if (resultCode == Result.Ok && intent != null)
            {
                var uri = intent.Data;
                var stream = ContentResolver.OpenInputStream(uri);

                PickImageTaskCompletionSource.SetResult(stream);
            }
            else
            {
                PickImageTaskCompletionSource.SetResult(null);
            }
        }
    }

    protected override void OnCreate(Bundle savedInstanceState)
    {
        base.OnCreate(savedInstanceState);
        AppDomain.CurrentDomain.UnhandledException += CurrentDomainOnUnhandledException;
        TaskScheduler.UnobservedTaskException += TaskSchedulerOnUnobservedTaskException;
        AndroidPressedEffect.Init();
        InitApp();
        Window.SetBackgroundDrawable(null);
    }

    private void InitApp(int? requestCode = null)
    {
        var requiredPermissions = new string[]
        {
            Manifest.Permission.WakeLock,
            Manifest.Permission.Bluetooth,
            Manifest.Permission.BluetoothAdmin,
            Manifest.Permission.AccessWifiState,
            Manifest.Permission.AccessCoarseLocation,
            Manifest.Permission.AccessFineLocation,
            Manifest.Permission.ChangeNetworkState,
            Manifest.Permission.NearbyWifiDevices,
            Manifest.Permission.ChangeWifiState,
            Manifest.Permission.Internet,
            Manifest.Permission.Vibrate,
            Manifest.Permission.ReadMediaAudio,
            Manifest.Permission.ReadMediaImages,
            Manifest.Permission.ReadMediaVideo,
            Manifest.Permission.PostNotifications,
            Manifest.Permission.BatteryStats,
            Manifest.Permission.ReceiveBootCompleted,
            Manifest.Permission.AccessNetworkState,
            "com.google.android.c2dm.permission.RECEIVE"
        };

        var notGrantedPermissions = requiredPermissions
            .Where(x => ContextCompat.CheckSelfPermission(this, x) != Permission.Granted)
            .ToList();

        if (requestCode is null && notGrantedPermissions.Count > 0)
        {
            ActivityCompat.RequestPermissions(this, notGrantedPermissions.ToArray(), RequestCodeWriteExternalStorage);
        }
    }

    private static void TaskSchedulerOnUnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs unobservedTaskExceptionEventArgs)
    {
        unobservedTaskExceptionEventArgs.SetObserved();
        PlatformUnhandledExceptionHandler.LogError<MainActivity>(unobservedTaskExceptionEventArgs.Exception, "TaskSchedulerOnUnobservedTaskException error");
    }

    private static void CurrentDomainOnUnhandledException(object sender, UnhandledExceptionEventArgs unhandledExceptionEventArgs)
    {
        PlatformUnhandledExceptionHandler.LogError<MainActivity>(unhandledExceptionEventArgs.ExceptionObject as Exception, "CurrentDomainOnUnhandledException error");
    }
}