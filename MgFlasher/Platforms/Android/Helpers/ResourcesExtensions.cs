﻿using Android.App;
using Microsoft.Maui.ApplicationModel;
using System;

namespace MgFlasher.Platforms.Android.Helpers;

public static class ResourcesExtensions
{
    public static int GetDrawableResourceId(this string name)
    {
        return Platform.CurrentActivity?.Resources.GetIdentifier(name, "drawable", Platform.CurrentActivity.PackageName) ??
               throw new InvalidOperationException("Could not resolve Drawable res id");
    }

    public static int GetRawResourceId(this string name)
    {
        return Platform.CurrentActivity?.Resources?.GetIdentifier(name, "raw", Platform.CurrentActivity.PackageName) ??
               throw new InvalidOperationException("Could not resolve Raw res id");
    }

    public static int GetIdResourceId(this string name)
    {
        return Platform.CurrentActivity?.Resources?.GetIdentifier(name, "id", Platform.CurrentActivity.PackageName) ??
               throw new InvalidOperationException("Could not resolve Id res id");
    }
}