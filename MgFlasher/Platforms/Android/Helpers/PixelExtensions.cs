﻿using Microsoft.Maui.ApplicationModel;

namespace MgFlasher.Platforms.Android.Helpers;

public static class PixelExtensions
{
    public static int ToPixels(this double dps)
    {
        return ToPixels((float)dps);
    }

    public static int ToPixels(this int dps)
    {
        return ToPixels((float)dps);
    }

    public static int ToPixels(this float dps)
    {
        var scale = Platform.CurrentActivity.Resources.DisplayMetrics.Density;
        var pixels = (int)(dps * scale + 0.5f);
        return pixels;
    }
}