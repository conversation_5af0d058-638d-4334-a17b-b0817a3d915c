﻿using Android.Bluetooth;
using Android.Content;
using Android.OS;
using MgFlasher.Flasher.Services.Cars;
using Microsoft.Extensions.Logging;
using System;
using Microsoft.Maui.ApplicationModel;

namespace MgFlasher.Platforms.Android.Services;

public class AndroidPowerManager : IPowerManager
{
    private readonly ILogger<AndroidPowerManager> _logger;
    private volatile PowerManager.WakeLock _wakeLock;
    private volatile bool _bluetoothEnabled;

    public AndroidPowerManager(ILogger<AndroidPowerManager> logger)
    {
        _logger = logger;
    }

    public void AcquireWakeLock(bool disableBluetooth)
    {
        try
        {
            _logger.LogInformation("acquiring wake lock...");
            if (GetPowerManager() is PowerManager.WakeLock wl)
            {
                wl.Acquire();
                _logger.LogInformation("wake lock acquired.");
            }
            else
            {
                _logger.LogInformation("wake lock could not be acquired.");
                return;
            }

            Platform.CurrentActivity?.RunOnUiThread(() =>
            {
                _logger.LogInformation("adding flag {WindowManagerFlags}...", global::Android.Views.WindowManagerFlags.KeepScreenOn);
                Platform.CurrentActivity?.Window.AddFlags(global::Android.Views.WindowManagerFlags.KeepScreenOn);
            });

            if (disableBluetooth)
            {
                TurnOffBluetooth();
            }
            else
            {
                _logger.LogInformation("Keeping Bluetooth state unchanged");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AcquireWakeLock error");
        }
    }

    private void TurnOffBluetooth()
    {
        try
        {
            _bluetoothEnabled = false;
            var bluetoothAdapter = BluetoothAdapter.DefaultAdapter;
            if (bluetoothAdapter != null && bluetoothAdapter.IsEnabled)
            {
                _bluetoothEnabled = true;
                bluetoothAdapter.Disable();
                _logger.LogInformation("Bluetooth was turned on but is turned off now");
            }
            else
            {
                _logger.LogInformation("Bluetooth was turned off");
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "TurnOffBluetooth error");
        }
    }

    public void ReleaseWakeLock()
    {
        _logger.LogInformation("releasing wake lock...");
        _wakeLock?.Release();
        _logger.LogInformation("wake lock released.");

        Platform.CurrentActivity?.RunOnUiThread(() =>
        {
            _logger.LogInformation("removing flag {WindowManagerFlags}...", global::Android.Views.WindowManagerFlags.KeepScreenOn);
            Platform.CurrentActivity?.Window.ClearFlags(global::Android.Views.WindowManagerFlags.KeepScreenOn);
        });
        ResetBluetooth();
    }

    private void ResetBluetooth()
    {
        try
        {
            var bluetoothAdapter = BluetoothAdapter.DefaultAdapter;
            if (bluetoothAdapter != null && _bluetoothEnabled)
            {
                bluetoothAdapter.Enable();
                _logger.LogInformation("Bluetooth re-enabled");
            }
            else
            {
                _logger.LogInformation("Bluetooth was turned off");
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "ResetBluetooth error");
        }
    }

    private PowerManager.WakeLock GetPowerManager()
    {
        if (_wakeLock != null)
        {
            return _wakeLock;
        }

        var pm = Platform.CurrentActivity?.GetSystemService(Context.PowerService) as PowerManager;
        var wakeLock = pm?.NewWakeLock(WakeLockFlags.Full, "MgFlasher::FullWakeLockTag");
        if (wakeLock != null)
        {
            _wakeLock = wakeLock;
        }
        return wakeLock;
    }
}