﻿using Android.OS;
using MgFlasher.Platforms.Android.Services;
using MgFlasher.Services;
using Microsoft.Maui.Controls;
using Microsoft.Maui.ApplicationModel;

namespace MgFlasher.Platforms.Android.Services;

public class CloseAppService : ICloseAppService
{
    private MainActivity MainActivity => (MainActivity)Platform.CurrentActivity;

    public void CloseApplication()
    {
        MainActivity.Finish();
        Process.KillProcess(Process.MyPid());
    }
}