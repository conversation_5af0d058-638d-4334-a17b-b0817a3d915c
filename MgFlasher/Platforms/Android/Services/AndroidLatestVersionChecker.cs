using System;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using MgFlasher.Flasher.Client.Common;
using MgFlasher.Services;
using Microsoft.Maui.ApplicationModel;
using Application = Android.App.Application;

namespace MgFlasher.Platforms.Android.Services;

public class AndroidLatestVersionChecker : ILatestVersionChecker
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly string _packageName;

    public AndroidLatestVersionChecker(
        IHttpClientFactory httpClientFactory)
    {
        _httpClientFactory = httpClientFactory;
        _packageName = Application.Context.PackageName;
    }

    public async Task<(bool UsingLatestVersion, string LatestVersion)> GetVersionInfoAsync()
    {
        using var http = _httpClientFactory.CreateBackendClient("https://play.google.com", TimeSpan.FromSeconds(10));
        var requestUri = $"/store/apps/details?id={_packageName}&hl=en";
        var response = await http.GetAsync(requestUri);
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();

        var match = Regex.Match(content, @"\[\[\[\""(?<Version>\d+)\""\]\]", RegexOptions.Compiled);
        if (!match.Success)
        {
            return (true, null);
        }

        var storeVersion = match.Groups["Version"].Value;
        var usingLatest = int.Parse(AppInfo.VersionString) >= int.Parse(storeVersion);
        return (usingLatest, storeVersion);
    }
}