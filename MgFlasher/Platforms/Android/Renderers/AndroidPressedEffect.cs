﻿using Android.Views;
using MgFlasher.Behaviors;
using System.Diagnostics;
using System.Windows.Input;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Devices;

namespace MgFlasher.Platforms.Android.Renderers;

public class AndroidPressedEffect : PlatformEffect
{
    private bool _attached;
    private TapGestureRecognizer _doublePressedGestureRecognizer;
    private TapGestureRecognizer _pressedGestureRecognizer;
    private Stopwatch _longPressedTimestamp;

    public static void Init() { }

    protected override void OnAttached()
    {
        if (!_attached)
        {
            var nativeView = Control ?? Container;
            nativeView.Touch += OnTouch;

            var view = Element as Microsoft.Maui.Controls.View;
            if (PressedEffect.GetDoublePressedCommand(Element) is ICommand doublePressed)
            {
                _doublePressedGestureRecognizer = new TapGestureRecognizer()
                {
                    Command = doublePressed,
                    NumberOfTapsRequired = 2,
                    BindingContext = Element.BindingContext
                };

                view.GestureRecognizers.Add(_doublePressedGestureRecognizer);
            }
            if (PressedEffect.GetPressedCommand(Element) is ICommand pressedCommand)
            {
                _pressedGestureRecognizer = new TapGestureRecognizer()
                {
                    Command = pressedCommand,
                    NumberOfTapsRequired = 1,
                    BindingContext = Element.BindingContext
                };

                view.GestureRecognizers.Add(_pressedGestureRecognizer);
            }

            _attached = true;
        }
    }

    private void OnTouch(object sender, global::Android.Views.View.TouchEventArgs e)
    {
        e.Handled = false;

        if (e.Event.Action == MotionEventActions.Down)
        {
            Vibration.Vibrate(100);
            _longPressedTimestamp = Stopwatch.StartNew();
            return;
        }

        if (e.Event.Action == MotionEventActions.Up && _longPressedTimestamp?.ElapsedMilliseconds > 350)
        {
            _longPressedTimestamp = null;
            PressedEffect.GetLongPressedCommand(Element)?.Execute(null);
        }
    }

    protected override void OnDetached()
    {
        if (_attached)
        {
            var view = Element as Microsoft.Maui.Controls.View;

            if (_doublePressedGestureRecognizer != null)
            {
                view.GestureRecognizers.Remove(_doublePressedGestureRecognizer);
            }
            if (_pressedGestureRecognizer != null)
            {
                view.GestureRecognizers.Remove(_pressedGestureRecognizer);
            }

            var nativeView = Control ?? Container;
            nativeView.Touch -= OnTouch;

            _attached = false;
        }
    }
}