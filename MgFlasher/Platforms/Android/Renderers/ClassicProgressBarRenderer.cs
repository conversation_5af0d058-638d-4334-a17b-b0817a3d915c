﻿using Android.Content;
using Android.Graphics;
using MgFlasher.Views.Controls;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Paint = Android.Graphics.Paint;
using Path = Android.Graphics.Path;
using RectF = Android.Graphics.RectF;
using ViewRenderer = Microsoft.Maui.Controls.Handlers.Compatibility.ViewRenderer;

namespace MgFlasher.Platforms.Android.Renderers;

public class ClassicProgressBarRenderer : ViewRenderer
{
    private Paint _paint;

    private ClassicProgressBar ClassicProgressBar => (ClassicProgressBar)Element;

    public ClassicProgressBarRenderer(Context context) : base(context)
    {
        SetWillNotDraw(false);
    }

    protected override void Dispose(bool disposing)
    {
        _paint?.Dispose();
        _paint = null;
        base.Dispose(disposing);
    }

    protected override void OnDraw(Canvas canvas)
    {
        if (_paint == null)
        {
            _paint = CreatePaint();
        }

        DrawBackgroundLine(canvas);
        DrawProgressLine(canvas);
    }

    private RectF CreateRectDrawArea(Canvas canvas)
    {
        var left = 0;
        var top = 0;
        var right = canvas.ClipBounds.Width();
        var bottom = canvas.ClipBounds.Height();

        return new RectF(left, top, right, bottom);
    }

    private Paint CreatePaint()
    {
        var paint = new Paint();
        paint.Flags = PaintFlags.AntiAlias;
        return paint;
    }

    private void DrawBackgroundLine(Canvas canvas)
    {
        using var drawArea = CreateRectDrawArea(canvas);

        _paint.SetStyle(Paint.Style.FillAndStroke);
        _paint.Color = ClassicProgressBar.BaseColor.ToAndroid();
        _paint.SetPathEffect(null);
        _paint.StrokeWidth = canvas.Height / 2f;

        canvas.DrawRect(drawArea, _paint);
    }

    private void DrawProgressLine(Canvas canvas)
    {
        var progress = (float)ClassicProgressBar.Progress;
        using var drawArea = CreatePathDrawArea(canvas, progress);
        _paint.SetStyle(Paint.Style.Stroke);
        _paint.Color = ClassicProgressBar.ForegroundProgressColor.ToAndroid();
        _paint.StrokeWidth = canvas.Height / 1.2f;

        var intervals = ClassicProgressBar.HalfSizeMode ? new float[] { 12, 4 } : new float[] { 6, 2 };
        _paint.SetPathEffect(new DashPathEffect(intervals, 3));

        canvas.DrawPath(drawArea, _paint);
    }

    private Path CreatePathDrawArea(Canvas canvas, float progress)
    {
        var centerY = canvas.ClipBounds.CenterY();
        var width = canvas.ClipBounds.Width();

        var path = new Path();
        path.MoveTo(0, centerY);
        path.QuadTo(0, centerY, width * progress, centerY);

        return path;
    }

    protected override void OnElementPropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        base.OnElementPropertyChanged(sender, e);

        if (e.PropertyName == ProgressBar.ProgressProperty.PropertyName ||
            e.PropertyName == ClassicProgressBar.ForegroundProgressColorProperty.PropertyName ||
            e.PropertyName == ClassicProgressBar.BaseColorProperty.PropertyName ||
            e.PropertyName == ClassicProgressBar.HalfSizeModeProperty.PropertyName)
        {
            Invalidate();
        }

        if (e.PropertyName == VisualElement.WidthProperty.PropertyName ||
            e.PropertyName == VisualElement.HeightProperty.PropertyName)
        {
            Invalidate();
        }
    }
}