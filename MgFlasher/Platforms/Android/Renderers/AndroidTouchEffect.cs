﻿using Android.Views;
using Forms = MgFlasher.Behaviors;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Platform;
using Microsoft.Maui.Graphics;
using System;
using Microsoft.Maui.Controls;
using System.Linq;
using System.Collections.Generic;

namespace MgFlasher.Platforms.Android.Renderers;

public class AndroidTouchEffect : PlatformEffect, IDisposable
{
    private static Dictionary<global::Android.Views.View, AndroidTouchEffect> ViewDictionary = new();
    private static Dictionary<int, AndroidTouchEffect> IdToEffectDictionary = new();

    private global::Android.Views.View view;
    private Element formsElement;
    private Forms.TouchEffect libTouchEffect;
    private bool capture;
    private Func<double, double> fromPixels;
    private int[] twoIntArray = new int[2];

    protected override void OnAttached()
    {
        // Get the Android View corresponding to the Element that the effect is attached to
        view = Control == null ? Container : Control;

        // Get access to the TouchEffect class in the .NET Standard library
        Forms.TouchEffect touchEffect =
            (Forms.TouchEffect)Element.Effects.
                FirstOrDefault(e => e is Forms.TouchEffect);

        if (touchEffect != null && view != null)
        {
            ViewDictionary.Add(view, this);

            formsElement = Element;

            libTouchEffect = touchEffect;

            // Save fromPixels function
            fromPixels = view.Context.FromPixels;

            // Set event handler on View
            view.Touch += OnTouch;
        }
    }

    protected override void OnDetached()
    {
        if (ViewDictionary.ContainsKey(view))
        {
            ViewDictionary.Remove(view);
            view.Touch -= OnTouch;
        }
    }

    void OnTouch(object sender, global::Android.Views.View.TouchEventArgs args)
    {
        // Two object common to all the events
        global::Android.Views.View senderView = sender as global::Android.Views.View;
        MotionEvent motionEvent = args.Event;

        // Get the pointer index
        int pointerIndex = motionEvent.ActionIndex;

        // Get the id that identifies a finger over the course of its progress
        int id = motionEvent.GetPointerId(pointerIndex);

        senderView.GetLocationOnScreen(twoIntArray);
        Point screenPointerCoords = new Point(twoIntArray[0] + motionEvent.GetX(pointerIndex),
            twoIntArray[1] + motionEvent.GetY(pointerIndex));

        // Use ActionMasked here rather than Action to reduce the number of possibilities
        switch (args.Event.ActionMasked)
        {
            case MotionEventActions.Down:
            case MotionEventActions.PointerDown:
                FireEvent(this, id, Forms.TouchActionType.Pressed, screenPointerCoords, true);

                IdToEffectDictionary[id] = this;

                capture = libTouchEffect.Capture;
                break;

            case MotionEventActions.Move:
                // Multiple Move events are bundled, so handle them in a loop
                for (pointerIndex = 0; pointerIndex < motionEvent.PointerCount; pointerIndex++)
                {
                    id = motionEvent.GetPointerId(pointerIndex);

                    if (capture)
                    {
                        senderView.GetLocationOnScreen(twoIntArray);

                        screenPointerCoords = new Point(twoIntArray[0] + motionEvent.GetX(pointerIndex),
                            twoIntArray[1] + motionEvent.GetY(pointerIndex));

                        FireEvent(this, id, Forms.TouchActionType.Moved, screenPointerCoords, true);
                    }
                    else
                    {
                        CheckForBoundaryHop(id, screenPointerCoords);

                        if (IdToEffectDictionary[id] != null)
                        {
                            FireEvent(IdToEffectDictionary[id], id, Forms.TouchActionType.Moved, screenPointerCoords, true);
                        }
                    }
                }
                break;

            case MotionEventActions.Up:
            case MotionEventActions.Pointer1Up:
                if (capture)
                {
                    FireEvent(this, id, Forms.TouchActionType.Released, screenPointerCoords, false);
                }
                else
                {
                    CheckForBoundaryHop(id, screenPointerCoords);

                    if (IdToEffectDictionary[id] != null)
                    {
                        FireEvent(IdToEffectDictionary[id], id, Forms.TouchActionType.Released, screenPointerCoords, false);
                    }
                }
                IdToEffectDictionary.Remove(id);
                break;

            case MotionEventActions.Cancel:
                IdToEffectDictionary.Remove(id);
                break;
        }
    }

    void CheckForBoundaryHop(int id, Point pointerLocation)
    {
        AndroidTouchEffect touchEffectHit = null;

        foreach (global::Android.Views.View view in ViewDictionary.Keys)
        {
            // Get the view rectangle
            try
            {
                view.GetLocationOnScreen(twoIntArray);
            }
            catch // System.ObjectDisposedException: Cannot access a disposed object.
            {
                continue;
            }
            Rect viewRect = new Rect(twoIntArray[0], twoIntArray[1], view.Width, view.Height);

            if (viewRect.Contains(pointerLocation))
            {
                touchEffectHit = ViewDictionary[view];
            }
        }

        if (touchEffectHit != IdToEffectDictionary[id])
        {
            IdToEffectDictionary[id] = touchEffectHit;
        }
    }

    void FireEvent(AndroidTouchEffect touchEffect, int id, Forms.TouchActionType actionType, Point pointerLocation, bool isInContact)
    {
        // Get the method to call for firing events
        Action<Element, Forms.TouchActionEventArgs> onTouchAction = touchEffect.libTouchEffect.OnTouchAction;

        // Get the location of the pointer within the view
        touchEffect.view.GetLocationOnScreen(twoIntArray);
        double x = pointerLocation.X - twoIntArray[0];
        double y = pointerLocation.Y - twoIntArray[1];
        Point point = new Point(fromPixels(x), fromPixels(y));

        // Call the method
        onTouchAction(touchEffect.formsElement,
            new Forms.TouchActionEventArgs(id, actionType, point, isInContact));
    }

    public void Dispose()
    {
        OnDetached();
    }
}