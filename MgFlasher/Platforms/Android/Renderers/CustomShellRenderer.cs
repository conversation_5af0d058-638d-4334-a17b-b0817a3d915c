﻿using Android.Views;
using Microsoft.Maui.Controls.Handlers.Compatibility;
using Microsoft.Maui.Controls.Platform.Compatibility;

namespace MgFlasher.Platforms.Android.Renderers;

public class CustomShellRenderer : ShellRenderer
{
    protected override IShellFlyoutRenderer CreateShellFlyoutRenderer()
    {
        var flyoutRenderer = base.CreateShellFlyoutRenderer();
        flyoutRenderer.AndroidView.Touch += OnFlyoutRendererTouch;
        return flyoutRenderer;
    }

    private void OnFlyoutRendererTouch(object sender, View.TouchEventArgs e)
    {
        if (e.Event.Action == MotionEventActions.Move)
            e.Handled = true;
        else
            e.Handled = false;
    }
}