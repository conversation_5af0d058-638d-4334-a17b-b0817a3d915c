using Android.Graphics.Drawables;
using MgFlasher.Views.Controls;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using AContext = Android.Content.Context;
using AView = Android.Views.View;
using AViewGroup = Android.Views.ViewGroup;

namespace MgFlasher.Platforms.Android.Renderers;

public class AndroidCustomViewCellHandler : Microsoft.Maui.Controls.Handlers.Compatibility.ViewCellRenderer
{
    private AView _pCellCore;
    private bool _pSelected;
    private Drawable _pUnselectedBackground;

    protected override AView GetCellCore(Cell item, AView convertView, AViewGroup parent, AContext context)
    {
        _pCellCore = base.GetCellCore(item, convertView, parent, context);
        _pSelected = false;
        _pUnselectedBackground = _pCellCore.Background;

        return _pCellCore;
    }

    protected override void OnCellPropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        base.OnCellPropertyChanged(sender, e);

        if (e.PropertyName == "IsSelected")
        {
            _pSelected = !(_pSelected);
            if (_pSelected)
            {
                _pCellCore.SetBackgroundColor(((CustomViewCell)sender).SelectedBackgroundColor.ToAndroid());
            }
            else
            {
                _pCellCore.SetBackground(_pUnselectedBackground);
            }
        }
    }
}