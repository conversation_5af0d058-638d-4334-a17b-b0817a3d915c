﻿using Android.Content;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Handlers.Compatibility;
using Microsoft.Maui.Controls.Platform;

namespace MgFlasher.Platforms.Android.Renderers;

public class CustomListViewRenderer : ListViewRenderer
{
    public CustomListViewRenderer(Context context) : base(context)
    {
    }

    protected override void OnElementChanged(ElementChangedEventArgs<ListView> e)
    {
        base.OnElementChanged(e);

        if (Control != null)
        {
            Control.VerticalScrollBarEnabled = false;
        }
    }
}