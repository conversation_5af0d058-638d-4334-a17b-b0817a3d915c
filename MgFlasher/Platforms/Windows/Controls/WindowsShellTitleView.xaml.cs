﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.InteropServices;
using MgFlasher.Helpers;
using MgFlasher.ViewModels;
using MgFlasher.Views.Controls;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Xaml;
using Microsoft.UI;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;

namespace MgFlasher.Platforms.Windows.Controls;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class WindowsShellTitleView : Microsoft.UI.Xaml.Controls.UserControl
{
    public static readonly DependencyProperty HasBarBackButtonProperty = DependencyProperty.Register(nameof(HasBarBackButton), typeof(bool), typeof(WindowsShellTitleView), new PropertyMetadata(false));

    public bool HasBarBackButton
    {
        get => (bool)GetValue(HasBarBackButtonProperty);
        set => SetValue(HasBarBackButtonProperty, value);
    }

    public static readonly DependencyProperty HasContextOptionsButtonProperty = DependencyProperty.Register(nameof(HasContextOptionsButton), typeof(bool), typeof(WindowsShellTitleView), new PropertyMetadata(false));

    public bool HasContextOptionsButton
    {
        get => (bool)GetValue(HasContextOptionsButtonProperty);
        set => SetValue(HasContextOptionsButtonProperty, value);
    }

    public static readonly DependencyProperty BackButtonShouldBeVisibleProperty = DependencyProperty.Register(nameof(BackButtonShouldBeVisible), typeof(bool), typeof(WindowsShellTitleView), new PropertyMetadata(false));

    public bool BackButtonShouldBeVisible
    {
        get => (bool)GetValue(BackButtonShouldBeVisibleProperty);
        set => SetValue(BackButtonShouldBeVisibleProperty, value);
    }

    public static readonly DependencyProperty ContextOptionsButtonShouldBeVisibleProperty = DependencyProperty.Register(nameof(ContextOptionsButtonShouldBeVisible), typeof(bool), typeof(WindowsShellTitleView), new PropertyMetadata(false));

    public bool ContextOptionsButtonShouldBeVisible
    {
        get => (bool)GetValue(ContextOptionsButtonShouldBeVisibleProperty);
        set => SetValue(ContextOptionsButtonShouldBeVisibleProperty, value);
    }

    private Page _currentPage;
    public ShellTitleViewModel ShellViewModel { get; }

    public WindowsShellTitleView()
    {
        DataContext = this;
        ShellViewModel = DependencyResolver.Resolve<ShellTitleViewModel>();
        InitializeComponent();

        Shell.Current.Navigated += OnNavigatedTo;
        Shell.Current.PropertyChanged += OnCurrentPagePropertyChanged;

        var titleBar = App.NativeAppWindow.TitleBar;
        titleBar.ButtonBackgroundColor = Colors.Transparent;
        titleBar.ExtendsContentIntoTitleBar = true;
        AppTitleBar.Loaded += (_, _) => SetDragRegionForCustomTitleBar();
        AppTitleBar.SizeChanged += (_, _) => SetDragRegionForCustomTitleBar();
    }

    private void OnNavigatedTo(object sender, ShellNavigatedEventArgs e)
    {
        if (_currentPage is not null)
        {
            _currentPage.PropertyChanged -= OnCurrentPagePropertyChanged;
        }

        _currentPage = Shell.Current.CurrentPage;
        _currentPage.PropertyChanged += OnCurrentPagePropertyChanged;
        OnCurrentPagePropertyChanged(null, null);
    }

    private void OnCurrentPagePropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e is not null &&
           e.PropertyName != "NavBarIsVisible" &&
           e.PropertyName != nameof(ShellTitleView.IsBusy) &&
           e.PropertyName != nameof(ShellTitleView.HasBarBackButton) &&
           e.PropertyName != nameof(ShellTitleView.HasContextOptionsButton) &&
           e.PropertyName != nameof(ShellTitleView.BarBackButtonVisible))
        {
            return;
        }

        IsEnabled = Shell.GetNavBarIsVisible(_currentPage) || ShellTitleView.GetIsBusy(_currentPage);
        HasBarBackButton = ShellTitleView.GetHasBarBackButton(_currentPage);
        HasContextOptionsButton = ShellTitleView.GetHasContextOptionsButton(_currentPage);
        BackButtonShouldBeVisible = ShellTitleView.GetHasBarBackButton(_currentPage) && ShellTitleView.GetBarBackButtonVisible(_currentPage);
        ContextOptionsButtonShouldBeVisible = ShellTitleView.GetHasContextOptionsButton(_currentPage);
        SetDragRegionForCustomTitleBar();
    }

    internal enum Monitor_DPI_Type : int
    {
        MDT_Effective_DPI = 0,
        MDT_Angular_DPI = 1,
        MDT_Raw_DPI = 2,
        MDT_Default = MDT_Effective_DPI
    }

    [DllImport("Shcore.dll", SetLastError = true)]
    internal static extern int GetDpiForMonitor(IntPtr hmonitor, Monitor_DPI_Type dpiType, out uint dpiX, out uint dpiY);

    private double GetScaleAdjustment()
    {
        var displayArea = DisplayArea.GetFromWindowId(App.WindowId, DisplayAreaFallback.Primary);
        var hMonitor = Win32Interop.GetMonitorFromDisplayId(displayArea.DisplayId);
        var result = GetDpiForMonitor(hMonitor, Monitor_DPI_Type.MDT_Default, out uint dpiX, out uint _);
        if (result != 0)
        {
            throw new Exception("Could not get DPI for monitor.");
        }

        var scaleFactorPercent = (uint)(((long)dpiX * 100 + (96 >> 1)) / 96);
        return scaleFactorPercent / 100.0;
    }

    private void SetDragRegionForCustomTitleBar()
    {
        var scaleAdjustment = GetScaleAdjustment();
        RightPaddingColumn.Width = new GridLength(App.NativeAppWindow.TitleBar.RightInset / scaleAdjustment);
        LeftPaddingColumn.Width = new GridLength(App.NativeAppWindow.TitleBar.LeftInset / scaleAdjustment);
        List<global::Windows.Graphics.RectInt32> dragRectsList = new();
        global::Windows.Graphics.RectInt32 dragRectL;
        dragRectL.X = (int)((LeftPaddingColumn.ActualWidth) * scaleAdjustment);
        dragRectL.Y = 0;
        dragRectL.Height = (int)(AppTitleBar.ActualHeight * scaleAdjustment);
        dragRectL.Width = (int)
            (
                (
                    IconColumn.ActualWidth +
                    TitleColumn.ActualWidth +
                    LeftDragColumn.ActualWidth
                ) * scaleAdjustment
            );
        dragRectsList.Add(dragRectL);
        global::Windows.Graphics.RectInt32 dragRectR;
        dragRectR.X = (int)
            (
                (
                    LeftPaddingColumn.ActualWidth +
                    IconColumn.ActualWidth +
                    TitleTextBlock.ActualWidth +
                    LeftDragColumn.ActualWidth +
                    (MenuButton.Visibility == Visibility.Visible ? (MenuButton.ActualWidth + MenuButton.Margin.Left) : 0) +
                    (BackButton.Visibility == Visibility.Visible ? (BackButton.ActualWidth + BackButton.Margin.Left) : 0) +
                    (ConnectivityButton.Visibility == Visibility.Visible ? (Math.Max(MenuButton.ActualWidth, ConnectivityButton.ActualWidth) + ConnectivityButton.Margin.Left) : 0) +
                    (ContextOptionsButton.Visibility == Visibility.Visible ? (Math.Max(MenuButton.ActualWidth, ContextOptionsButton.ActualWidth) + ContextOptionsButton.Margin.Left) : 0)
                 ) * scaleAdjustment
            );
        dragRectR.Y = 0;
        dragRectR.Height = (int)(AppTitleBar.ActualHeight * scaleAdjustment);
        dragRectR.Width = (int)(RightDragColumn.ActualWidth * scaleAdjustment);
        dragRectsList.Add(dragRectR);

        App.NativeAppWindow.TitleBar.SetDragRectangles(dragRectsList.ToArray());
    }
}