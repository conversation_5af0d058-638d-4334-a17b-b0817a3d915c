﻿<UserControl x:Class="MgFlasher.Platforms.Windows.Controls.WindowsShellTitleView" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" DataContext="{Binding RelativeSource={RelativeSource Mode=Self}}">
    <Grid x:Name="AppTitleBar" HorizontalAlignment="Stretch">
        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="LeftPaddingColumn" Width="0" />
            <ColumnDefinition x:Name="IconColumn" Width="Auto" />
            <ColumnDefinition x:Name="TitleColumn" Width="Auto" />
            <ColumnDefinition x:Name="LeftDragColumn" Width="0" />
            <ColumnDefinition x:Name="MenuOrBackColumn" Width="Auto" />
            <ColumnDefinition x:Name="ConnectivityColumn" Width="Auto" />
            <ColumnDefinition x:Name="ContextOptionsColumn" Width="Auto" />
            <ColumnDefinition x:Name="RightDragColumn" Width="*" />
            <ColumnDefinition x:Name="RightPaddingColumn" Width="0" />
        </Grid.ColumnDefinitions>
        <Image
            Grid.Column="1" Margin="8,0,0,0"
            VerticalAlignment="Center" Width="20" Height="20"
            Source="ms-appx:///logo_2.png" />
        <TextBlock
            Grid.Column="2" Margin="4,0,0,0"
            x:Name="TitleTextBlock" FontWeight="Bold"
            Style="{StaticResource CaptionTextBlockStyle}"
            VerticalAlignment="Center" Text="MG Flasher" />
        <Button
            x:Name="MenuButton" Grid.Column="4" Margin="4,0,0,0"
            Command="{Binding ShellViewModel.MenuCommand}"
            Content="&#x2630;"
            Visibility="{Binding HasBarBackButton, Converter={StaticResource InvertedBoolConverter}}" />
        <Button
            x:Name="BackButton" Grid.Column="4" Margin="4,0,0,0"
            Command="{Binding ShellViewModel.BackCommand}"
            Content="&#x2190;"
            Visibility="{Binding BackButtonShouldBeVisible}" />
        <Button x:Name="ConnectivityButton" Grid.Column="5" Margin="4,0,0,0" Command="{Binding ShellViewModel.ConnectivityCommand}">
            <Image Source="{Binding ShellViewModel.CarConnectivity, Converter={StaticResource CarConnectivityIconConverter}}" Width="16" Height="16" />
        </Button>
        <Button
            x:Name="ContextOptionsButton" Grid.Column="6"
            Margin="4,0,0,0"
            Command="{Binding ShellViewModel.ContextOptionsCommand}"
            Content="&#x2699;"
            Visibility="{Binding ContextOptionsButtonShouldBeVisible}" />
    </Grid>
</UserControl>