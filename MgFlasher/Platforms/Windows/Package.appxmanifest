﻿<?xml version="1.0" encoding="utf-8"?>
<Package
  xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
  xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
  xmlns:mp="http://schemas.microsoft.com/appx/2014/phone/manifest"
  xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
  IgnorableNamespaces="uap rescap">

	<Identity Name="19007DamianPozimski.MGFlasher" Publisher="CN=E5DC4DDA-EA0D-41C0-97ED-8F64FA0BB0CE" Version="0.0.0.0" />

	<mp:PhoneIdentity PhoneProductId="AC8AC91F-6CCE-45CA-9FAF-3D1E748F9747" PhonePublisherId="00000000-0000-0000-0000-000000000000"/>

	<Properties>
		<DisplayName>MG Flasher</DisplayName>
		<PublisherDisplayName><PERSON></PublisherDisplayName>
		<Logo>$placeholder$.png</Logo>
	</Properties>

	<Dependencies>
		<TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
	</Dependencies>

	<Resources>
		<Resource Language="x-generate" />
	</Resources>

	<Applications>
		<Application Id="App" Executable="$targetnametoken$.exe" EntryPoint="$targetentrypoint$">
			<uap:VisualElements
			  DisplayName="$placeholder$"
			  Description="$placeholder$"
			  Square150x150Logo="$placeholder$.png"
			  Square44x44Logo="$placeholder$.png"
			  BackgroundColor="transparent">
				<uap:DefaultTile Square71x71Logo="$placeholder$.png" Wide310x150Logo="$placeholder$.png" Square310x310Logo="$placeholder$.png" />
				<uap:SplashScreen Image="$placeholder$.png" />
			</uap:VisualElements>
			<Extensions>
				<uap:Extension Category="windows.protocol">
					<uap:Protocol Name="mgflasher"/>
				</uap:Extension>
			</Extensions>
		</Application>
	</Applications>

	<Capabilities>
		<rescap:Capability Name="runFullTrust" />
	</Capabilities>

</Package>
