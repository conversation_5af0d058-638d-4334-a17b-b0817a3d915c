﻿using Shiny.Push;
using System.Threading;
using System.Threading.Tasks;

namespace MgFlasher.Platforms.Windows.Services;

public class WindowsPushManager : IPushManager
{
    public IPushTagSupport Tags => null;
    public string RegistrationToken => null;
    public string NativeRegistrationToken => null;
    public Task<PushAccessState> RequestAccess(CancellationToken cancelToken = default) => Task.FromResult(PushAccessState.Denied);
    public Task UnRegister() => Task.CompletedTask;
}