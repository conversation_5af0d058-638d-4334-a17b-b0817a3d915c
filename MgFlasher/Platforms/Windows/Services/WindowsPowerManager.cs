﻿using MgFlasher.Flasher.Services.Cars;
using Windows.System.Display;

namespace MgFlasher.Platforms.Windows.Services;

public class WindowsPowerManager : IPowerManager
{
    private readonly DisplayRequest _displayRequest;
    private int _requestCounter;

    public WindowsPowerManager()
    {
        _displayRequest = new DisplayRequest();
    }

    public void AcquireWakeLock(bool disableBluetooth = true)
    {
        _requestCounter++;
        _displayRequest.RequestActive();
    }

    public void ReleaseWakeLock()
    {
        for (int i = 0; i < _requestCounter; i++)
        {
            _displayRequest.RequestRelease();
        }

        _requestCounter = 0;
    }
}