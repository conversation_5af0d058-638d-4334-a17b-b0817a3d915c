﻿using System;
using System.Net.Http;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using MgFlasher.Flasher.Client.Common;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Backend;
using MgFlasher.Flasher.Services.DevMode;
using Microsoft.Extensions.Logging;
using Sentry;

namespace MgFlasher.Platforms.Windows.Services;

public class PlatformHttpClientFactory : IPlatformHttpClientFactory
{
    private readonly ILoggerFactory _loggerFactory;
    private readonly IAppContext _appContext;
    private readonly IDevModeAccessor _devModeAccessor;

    public PlatformHttpClientFactory(ILoggerFactory loggerFactory, IAppContext appContext, IDevModeAccessor devModeAccessor)
    {
        _loggerFactory = loggerFactory;
        _appContext = appContext;
        _devModeAccessor = devModeAccessor;
    }

    public HttpClient Create(TimeSpan timeout, string baseUri)
    {
        var nativeHandler = new HttpClientHandler()
        {
            AllowAutoRedirect = true,
            AutomaticDecompression = System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate,
            MaxAutomaticRedirections = 5,
            ServerCertificateCustomValidationCallback = OnServerCertificateCustomValidationCallback
        };
        var loggingHandler = new WebClientLoggingHandler(nativeHandler, _loggerFactory.CreateLogger<WebClientLoggingHandler>());
        var sentryHttpHandler = new SentryHttpMessageHandler(loggingHandler);

        return new HttpClient(sentryHttpHandler)
        {
            Timeout = timeout,
            BaseAddress = new Uri(baseUri)
        };
    }

    private bool OnServerCertificateCustomValidationCallback(HttpRequestMessage message, X509Certificate2 certificate, X509Chain chain, SslPolicyErrors errors)
    {
        if (message.RequestUri is not null && !message.RequestUri.ToString().StartsWith(_appContext.GetBackendUrl(_devModeAccessor)))
        {
            return true;
        }

        var expectedIssuerName = "CN=GeoTrust Global";
        var expectedSubject = "CN=backend.mgflasher.com";

        if (!certificate.Issuer.StartsWith(expectedIssuerName))
        {
            return false;
        }

        if (certificate.Subject != expectedSubject)
        {
            return false;
        }

        return true;
    }
}