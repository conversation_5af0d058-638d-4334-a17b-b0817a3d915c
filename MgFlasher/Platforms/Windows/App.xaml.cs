﻿using Microsoft.Maui;
using Microsoft.Maui.Hosting;
using Microsoft.UI.Windowing;
using Microsoft.UI;
using Windows.Graphics;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Controls;
using MgFlasher.Flasher.Services.Cars.Files;
using Microsoft.Maui.Devices;

// To learn more about WinUI, the WinUI project structure, and more about our project templates, see: http://aka.ms/winui-project-info.

namespace MgFlasher.Platforms.Windows;

/// <summary>
/// Provides application-specific behavior to supplement the default Application class.
/// </summary>
public partial class App : MauiWinUIApplication
{
    public static Microsoft.UI.Xaml.Window NativeWindow { get; private set; }
    public static nint WindowHandle { get; private set; }
    public static WindowId WindowId { get; private set; }
    public static AppWindow NativeAppWindow { get; private set; }

    private const int MinimumWidth = 896;
    private const int MinimumHeight = 720;

    /// <summary>
    /// Initializes the singleton application object. This is the first line of authored code executed, and as
    /// such is the logical equivalent of main() or WinMain().
    /// </summary>
    public App()
    {
        if (WinUIEx.WebAuthenticator.CheckOAuthRedirectionActivation())
        {
            return;
        }

        this.InitializeComponent();

        WindowHandler.Mapper.AppendToMapping(nameof(IWindow), (handler, view) => ConfigureWindow(handler));
    }

    protected override MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();

    private static void ConfigureWindow(IWindowHandler handler)
    {
        NativeWindow = handler.PlatformView;
        NativeWindow.ExtendsContentIntoTitleBar = true;
        WindowHandle = WinRT.Interop.WindowNative.GetWindowHandle(NativeWindow);
        WindowId = Win32Interop.GetWindowIdFromWindow(WindowHandle);
        NativeAppWindow = AppWindow.GetFromWindowId(WindowId);
        NativeAppWindow.Title = "MG Flasher";
        CenterAndResize();
        NativeWindow.SizeChanged += NativeWindow_SizeChanged;
        Shell.Current.FlyoutBehavior = FlyoutBehavior.Disabled;
        PlatformShareService.SetWindowHandle(WindowHandle);
    }

    private static void NativeWindow_SizeChanged(object sender, Microsoft.UI.Xaml.WindowSizeChangedEventArgs args)
    {
        // 1.05 is a magic number, because for some reason Size is reported smaller than it is and causes an infinite loop of
        // making the window smaller and smaller.
        if (args.Size.Width * DeviceDisplay.MainDisplayInfo.Density * 1.05 < MinimumWidth)
        {
            int height = (int)(args.Size.Height * DeviceDisplay.MainDisplayInfo.Density);
            NativeAppWindow.Resize(new(MinimumWidth, height));
        }

        if (args.Size.Height * DeviceDisplay.MainDisplayInfo.Density * 1.05 < MinimumHeight)
        {
            int width = (int)(args.Size.Width * DeviceDisplay.MainDisplayInfo.Density);
            NativeAppWindow.Resize(new(width, MinimumHeight));
        }
    }

    private static void CenterAndResize()
    {
        var mainDisplayInfo = DeviceDisplay.MainDisplayInfo;

        int width = (int)(MinimumWidth * mainDisplayInfo.Density);
        int height = (int)(MinimumHeight * mainDisplayInfo.Density);
        int x = (int)((mainDisplayInfo.Width - width) / 2);
        int y = (int)((mainDisplayInfo.Height - height) / 2);

        NativeAppWindow.Resize(new(width, height));
        NativeAppWindow.Move(new(x, y));
    }
}