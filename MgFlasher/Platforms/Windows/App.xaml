﻿<maui:MauiWinUIApplication
    x:Class="MgFlasher.Platforms.Windows.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:maui="using:Microsoft.Maui"
    xmlns:nativeControls="using:MgFlasher.Platforms.Windows.Controls"
    xmlns:converters="using:MgFlasher.Converters">
    <maui:MauiWinUIApplication.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />
            </ResourceDictionary.MergedDictionaries>
            <DataTemplate x:Key="MauiAppTitleBarContainerTemplate">
                <Grid Canvas.ZIndex="1" HorizontalAlignment="Stretch">
                    <nativeControls:WindowsShellTitleView />
                </Grid>
            </DataTemplate>
            <converters:InvertedBoolConverter x:Key="InvertedBoolConverter" />
            <converters:CarConnectivityIconConverter x:Key="CarConnectivityIconConverter" />
        </ResourceDictionary>
    </maui:MauiWinUIApplication.Resources>
</maui:MauiWinUIApplication>