﻿using Microsoft.UI.Xaml.Data;
using System;
using System.Globalization;

namespace MgFlasher.Converters;

public partial class InvertedBoolConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, string language)
    {
        return Convert(value, targetType, parameter, CultureInfo.InvariantCulture);
    }

    public object ConvertBack(object value, Type targetType, object parameter, string language)
    {
        return ConvertBack(value, targetType, parameter, CultureInfo.InvariantCulture);
    }
}