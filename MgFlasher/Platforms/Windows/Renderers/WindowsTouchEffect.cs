﻿using MgFlasher.Behaviors;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Graphics;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Input;
using System.Linq;

namespace MgFlasher.Platforms.Windows.Renderers;

public class WindowsTouchEffect : PlatformEffect
{
    private UIElement view;
    private TouchEffect _touchEffect;
    private bool _capture;

    protected override void OnAttached()
    {
        view = Control ?? Container;

        _touchEffect = (TouchEffect)Element.Effects.FirstOrDefault(e => e is TouchEffect);

        if (_touchEffect != null && view != null)
        {
            view.AddHandler(UIElement.PointerPressedEvent, new PointerEventHandler(OnPointerPressed), true);
            view.AddHandler(UIElement.PointerMovedEvent, new PointerEventHandler(OnPointerMoved), true);
            view.AddHandler(UIElement.PointerReleasedEvent, new PointerEventHandler(OnPointerReleased), true);
        }
    }

    private void OnPointerPressed(object sender, PointerRoutedEventArgs args)
    {
        var ptrPt = args.GetCurrentPoint(view);
        Point pointerCoords = new Point(ptrPt.Position.X, ptrPt.Position.Y);

        FireEvent(this, ptrPt.PointerId, TouchActionType.Pressed, pointerCoords, true);

        _capture = _touchEffect.Capture;
        if (_capture)
        {
            view.CapturePointer(args.Pointer);
        }
    }

    private void OnPointerMoved(object sender, PointerRoutedEventArgs args)
    {
        var ptrPt = args.GetCurrentPoint(view);
        Point pointerCoords = new Point(ptrPt.Position.X, ptrPt.Position.Y);

        if (_capture)
        {
            FireEvent(this, ptrPt.PointerId, TouchActionType.Moved, pointerCoords, ptrPt.IsInContact);
        }
    }

    private void OnPointerReleased(object sender, PointerRoutedEventArgs args)
    {
        var ptrPt = args.GetCurrentPoint(view);
        Point pointerCoords = new Point(ptrPt.Position.X, ptrPt.Position.Y);

        if (_capture)
        {
            view.ReleasePointerCapture(args.Pointer);
            FireEvent(this, ptrPt.PointerId, TouchActionType.Released, pointerCoords, false);
        }
    }

    private void FireEvent(WindowsTouchEffect touchEffect, uint id, TouchActionType actionType, Point pointerLocation, bool isInContact)
    {
        var elementLocation = view.TransformToVisual(touchEffect.view).TransformPoint(new global::Windows.Foundation.Point(pointerLocation.X, pointerLocation.Y));
        Point elementPoint = new Point(elementLocation.X, elementLocation.Y);
        touchEffect._touchEffect.OnTouchAction(touchEffect.Element, new TouchActionEventArgs((int)id, actionType, elementPoint, isInContact));
    }

    protected override void OnDetached()
    {
        view.RemoveHandler(UIElement.PointerPressedEvent, new PointerEventHandler(OnPointerPressed));
        view.RemoveHandler(UIElement.PointerMovedEvent, new PointerEventHandler(OnPointerMoved));
        view.RemoveHandler(UIElement.PointerReleasedEvent, new PointerEventHandler(OnPointerReleased));
    }
}