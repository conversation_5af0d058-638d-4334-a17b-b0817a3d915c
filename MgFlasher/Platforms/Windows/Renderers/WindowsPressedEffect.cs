﻿using MgFlasher.Behaviors;
using Microsoft.Maui.Controls.Platform;
using Microsoft.UI.Dispatching;
using Microsoft.UI.Xaml.Input;
using System;

namespace MgFlasher.Platforms.Windows.Renderers;

public class WindowsPressedEffect : PlatformEffect
{
    private bool _attached;
    private PointerEventHandler _pointerPressedHandler;
    private PointerEventHandler _pointerReleasedHandler;
    private DispatcherQueueTimer _doubleClickTimer;
    private int _clickCount = 0;
    private DateTime _pressStartTime;
    private const int LongPressThresholdMilliseconds = 1000;

    public WindowsPressedEffect()
    {
        _doubleClickTimer = DispatcherQueue.GetForCurrentThread().CreateTimer();
        _doubleClickTimer.Interval = TimeSpan.FromMilliseconds(500);
        _doubleClickTimer.IsRepeating = false;
        _doubleClickTimer.Tick += (sender, e) =>
        {
            if (_clickCount == 1)
            {
                OnClick();
            }
            _clickCount = 0;
        };
    }

    protected override void OnAttached()
    {
        if (!_attached)
        {
            _pointerPressedHandler = new PointerEventHandler(OnPointerPressed);
            _pointerReleasedHandler = new PointerEventHandler(OnPointerReleased);

            Container.PointerPressed += _pointerPressedHandler;
            Container.PointerReleased += _pointerReleasedHandler;

            _attached = true;
        }
    }

    private void OnPointerPressed(object sender, PointerRoutedEventArgs e)
    {
        _clickCount++;
        _pressStartTime = DateTime.UtcNow;
        if (_clickCount == 1)
        {
            _doubleClickTimer.Start();
        }
        else if (_clickCount == 2)
        {
            _doubleClickTimer.Stop();
            _clickCount = 0;
            OnDoubleClick();
        }
    }

    private void OnPointerReleased(object sender, PointerRoutedEventArgs e)
    {
        _doubleClickTimer.Stop();
        var pressDuration = DateTime.UtcNow - _pressStartTime;
        if (pressDuration.TotalMilliseconds >= LongPressThresholdMilliseconds)
        {
            _clickCount = 0;
            OnLongClick();
        }
        else if (_clickCount == 1)
        {
            OnClick();
        }
    }

    private void OnClick()
    {
        PressedEffect.GetPressedCommand(Element)?.Execute(null);
    }

    private void OnDoubleClick()
    {
        PressedEffect.GetDoublePressedCommand(Element)?.Execute(null);
    }

    private void OnLongClick()
    {
        PressedEffect.GetLongPressedCommand(Element)?.Execute(null);
    }

    protected override void OnDetached()
    {
        if (_attached)
        {
            Container.PointerPressed -= _pointerPressedHandler;
            Container.PointerReleased -= _pointerReleasedHandler;
            _attached = false;
        }
    }
}