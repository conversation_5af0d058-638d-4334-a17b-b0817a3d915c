﻿using CommunityToolkit.Mvvm.Messaging;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.AppFiles;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.ViewModels;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;
using System;
using System.Diagnostics;
using System.Threading.Tasks;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Files;
using System.Collections.Generic;

namespace MgFlasher.Bootstrap;

public class ApplicationBootstrapService : IApplicationBootstrapService
{
    private readonly PendingPageViewModel _viewModel;
    private readonly IAppContext _appContext;
    private readonly IUserService _userService;
    private readonly ICarRepository _carsService;
    private readonly ILogger<ApplicationBootstrapService> _logger;
    private readonly IAppFilesExistenceValidator _appFilesExistenceValidator;
    private readonly ILoggerFactory _loggerFactory;
    private readonly IAppUpdateService _appUpdateService;
    private readonly IDirectoryPathFactory _directoryPathFactory;
    private readonly ICustomCodeChangesetsService _customCodeChangesetsService;
    private readonly IAppVersionBlockService _appVersionBlockService;
    private readonly IEnumerable<IAppFileProvider> _appFileProviders;

    public ApplicationBootstrapService(
        PendingPageViewModel viewModel,
        IAppContext appContext,
        IUserService userService,
        ICarRepository carsService,
        ILogger<ApplicationBootstrapService> logger,
        IAppFilesExistenceValidator appFilesExistenceValidator,
        ILoggerFactory loggerFactory,
        IAppUpdateService appUpdateService,
        ICustomCodeChangesetsService customCodeChangesetsService,
        IDirectoryPathFactory directoryPathFactory,
        IAppVersionBlockService appVersionBlockService,
        IEnumerable<IAppFileProvider> appFileProviders)
    {
        _viewModel = viewModel;
        _appContext = appContext;
        _userService = userService;
        _carsService = carsService;
        _logger = logger;
        _appFilesExistenceValidator = appFilesExistenceValidator;
        _loggerFactory = loggerFactory;
        _appUpdateService = appUpdateService;
        _directoryPathFactory = directoryPathFactory;
        _customCodeChangesetsService = customCodeChangesetsService;
        _appVersionBlockService = appVersionBlockService;
        _appFileProviders = appFileProviders;
    }

    public Page GetStartupPage()
    {
        try
        {
            return _appContext.RetailMode == RetailModeEnum.China ? new AppShellChina() : new AppShellGlobal();
        }
        finally
        {
            _ = Task.Run(async () => await Bootup());
        }
    }

    private async Task Bootup()
    {
        _logger.LogInformation("App booting started");
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var nextPageArgs = PendingPageNavigationArgs.InProgress(PageType.MyCars, AppResources.Loading);
            //nextPageArgs.NextPage = PageType.FlashingCar;
            //nextPageArgs.NextPageArguments = new FlashingCarPageNavigationArgs() { ContinousCommand = _fakeCommand, Options = new() };
            await _viewModel.OnNavigatedToAsync(PageType.Pending, nextPageArgs);

            UniFlasherHelper.Init(_loggerFactory);
            _directoryPathFactory.Init();

            SendMessage(MessagingCenterIdentifiers.PendingPageUpdateText, new PendingPageUpdateArgs(AppResources.Car_Loading, 10));
            var cars = await _carsService.GetAll();

            SendMessage(MessagingCenterIdentifiers.PendingPageUpdateText, new PendingPageUpdateArgs(AppResources.AppFiles_Initializing, 20));
            foreach (var appFileProvider in _appFileProviders)
                await appFileProvider.InitAsync();
            await _appFilesExistenceValidator.ValidateAsync();

            SendMessage(MessagingCenterIdentifiers.PendingPageUpdateText, new PendingPageUpdateArgs(AppResources.User_Sync, 75));
            await _userService.Initialize();

            SendMessage(MessagingCenterIdentifiers.PendingPageUpdateText, new PendingPageUpdateArgs(AppResources.CheckingAppVersionBlock, 90));
            await _appVersionBlockService.CheckAndUpdateAppVersionBlockStatus();

            SendMessage(MessagingCenterIdentifiers.PendingPageUpdateText, new PendingPageUpdateArgs(AppResources.CCC_Polling, 95));
            await _customCodeChangesetsService.FetchCustomCodeChangesetsStatusesFromBackend();
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "App booting crash: \"{Message}\"", ex.Message);
        }
        finally
        {
            _logger.LogInformation("App booting finished and it took [{InitializationTime}ms]", stopwatch.ElapsedMilliseconds);
            SendMessage(MessagingCenterIdentifiers.PendingPageCloseNavigation);

            _ = Task.Run(async () => await _appUpdateService.NotifyNewVersionAvailableAsync());
        }
    }

    private void SendMessage(string messageId)
    {
        Application.Current.Dispatcher.Dispatch(() =>
        {
            WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(messageId));
        });
    }

    private void SendMessage<T>(string messageId, T args)
    {
        Application.Current.Dispatcher.Dispatch(() =>
        {
            WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(messageId, args));
        });
    }
}