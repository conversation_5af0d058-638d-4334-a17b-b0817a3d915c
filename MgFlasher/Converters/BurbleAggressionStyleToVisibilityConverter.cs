using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace MgFlasher.Converters;

public class BurbleAggressionStyleToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var selectedIndex = (int)value;
        var expectedIndex = int.Parse((string)parameter);
        // value == BurbleAggressionStyle
        // OFF == 0
        // Style 1 == 1
        // Style 2 == 2
        // Style 3 == 3
        return selectedIndex == expectedIndex;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}