﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class BoolToColumnHeightOnDesktopConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isWizardMode)
        {
            bool shouldShow = false;
            if (parameter is string strParam && bool.TryParse(strParam, out var parsed))
            {
                shouldShow = parsed;
            }

            return isWizardMode && shouldShow ? new GridLength(0.2, GridUnitType.Star) : new GridLength(0);
        }

        return new GridLength(0);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}