﻿using MgFlasher.Localization.Resources;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class AntilagBoostConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var val_hPa = ((int)value * 50) - 1000;
        var val_psi = val_hPa * 0.0145037738;
        return $"{val_hPa} hPa\n{val_psi.ToString("0.0")} psi\n({AppResources.BoostPressure_Relative})";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}