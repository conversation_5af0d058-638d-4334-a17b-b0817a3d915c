﻿using MgFlasher.Flasher.Services.Models;
using Microsoft.Maui.Controls;
using System;
using System.Globalization;

namespace MgFlasher.Converters;

public partial class CarConnectivityIconConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return (CarConnectivity)value switch
        {
            CarConnectivity.CarConnected => "connectivity_icon_connected.png",
            CarConnectivity.EnetConnected => "connectivity_icon_ecuconnected.png",
            CarConnectivity.NotConnected => "connectivity_icon.png",
            _ => throw new InvalidOperationException($"Not supported connectivity state {value}")
        };
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}