﻿using MgFlasher.Flasher.Services.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class ConnectedCarStageToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var stage = (StageType?)value;

        switch (stage)
        {
            case StageType.Custom:
                return Application.Current.Resources["AccentSuccessTextColor"];

            case StageType.Activated:
            case StageType.Stage1:
            case StageType.Stage2:
            case StageType.Stage2_5:
                return Application.Current.Resources["PrimarySuccessTextColor"];

            default:
                return Application.Current.Resources["PrimaryWarningTextColor"];
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}