﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class TorqueByGearNoLimitVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        // KL_MDG_MAX_GANG_AT
        // Adjustable from 200 Nm to 1000 Nm, to No Limit
        int val = (int)value;
        if (val >= 1001)
        {
            return true; // max slider position
        }
        else
        {
            return false; // all other slider positions
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}