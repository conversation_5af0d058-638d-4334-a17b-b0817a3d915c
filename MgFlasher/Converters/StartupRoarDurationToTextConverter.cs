﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class StartupRoarDurationToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        int val = (int)value;
        switch (val)
        {
            // AXX_KF_MD_RES_NST_KEY
            // Minimum Resolution of 0.01 s
            // User Resolution of 0.1 s
            // Adjustable from 0.1 s to 1.0 s

            case 11:
                return "OEM"; // max slider position
            default:
                // 0.1 s -> 1.0 s by 0.1 s increments
                // Required positions = (1.0 - 0.1) / 0.1 = 9
                // min value == 1, max value == 10 + 1
                // val == 1 => 0.1 s
                // val == 10 => 1.0 s
                return $"{Math.Round(val * 0.1, 2):0.0} s"; // all other slider positions
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}