﻿using System;
using System.Collections;
using System.Globalization;
using System.Linq;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class CollectionToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType,
        object parameter, CultureInfo culture)
    {
        if (value == null)
        {
            return false;
        }
        if (value is IEnumerable)
        {
            return ((IEnumerable)value).Cast<object>().ToList().Any();
        }
        var enumerable = (ICollection)value;
        return enumerable.Count > 0;
    }

    public object ConvertBack(object value, Type targetType,
        object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}