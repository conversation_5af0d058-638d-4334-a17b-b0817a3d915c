﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class ConnectedCarUpdateAvailableToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var status = (bool)value;

        if (!status)
            return Application.Current.Resources["PrimarySuccessTextColor"];
        else
            return Application.Current.Resources["PrimaryImportantTextColor"];
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}