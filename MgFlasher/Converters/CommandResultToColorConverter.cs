﻿using MgFlasher.Flasher.Commands.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class CommandResultToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var commandResult = (CommandResultType)value;

        switch (commandResult)
        {
            case CommandResultType.Completed:
                return Application.Current.Resources["PrimarySuccessTextColor"];

            case CommandResultType.Warning:
                return Application.Current.Resources["PrimaryWarningTextColor"];

            case CommandResultType.Error:
                return Application.Current.Resources["PrimaryImportantTextColor"];

            default:
                throw new NotImplementedException($"{commandResult} doesnt have assigned color");
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}