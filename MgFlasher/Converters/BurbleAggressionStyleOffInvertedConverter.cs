﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class BurbleAggressionStyleOffInvertedConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        // value == BurbleAggressionStyle
        // OFF == 0
        // Style 1 == 1
        // Style 2 == 2
        // Style 3 == 3
        if ((int)value == 0)
        {
            return false;
        }
        return true;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}