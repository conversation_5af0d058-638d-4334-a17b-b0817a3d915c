﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class StartupRoarAggressionToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        int val = (int)value;
        switch (val)
        {
            // KF_MD_RES_NST_KEY
            // Minimum Resolution of 0.1 Nm
            case 1:
                return "OEM"; // min slider position
            default:
                return $"{val - 2} %"; // all slider positions
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}