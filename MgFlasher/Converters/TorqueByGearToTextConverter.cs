﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class TorqueByGearToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        int val = (int)value;
        switch (val)
        {
            // KL_MDG_MAX_GANG_AT
            // Minimum Resolution of 0.1 Nm
            // User Resolution of 1 Nm
            // Adjustable from 200 Nm to 1000 Nm, to No Limit
            case 1001:
                return "No Limit"; // max slider position
            default:
                // 200 Nm -> 1000 Nm by 1 Nm increments
                // Required positions = (1000 - 200) / 1 = 800
                // min value == 200, max value == 1000 + 1
                // val == 200 => 200 Nm
                // val == 1000 => 1000 Nm
                return $"{val} Nm"; // all other slider positions
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}