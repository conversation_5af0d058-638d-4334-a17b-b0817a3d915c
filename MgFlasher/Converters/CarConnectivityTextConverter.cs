﻿using MgFlasher.Flasher.Services.Models;
using MgFlasher.Localization.Resources;
using Microsoft.Maui.Controls;
using System;
using System.Globalization;

namespace MgFlasher.Converters;

public class CarConnectivityTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return (CarConnectivity)value switch
        {
            CarConnectivity.CarConnected => AppResources.Car_Connectivity_Connected,
            CarConnectivity.EnetConnected => AppResources.Car_Connectivity_EnetConnected,
            CarConnectivity.NotConnected => AppResources.Car_Connectivity_NotConnected,
            _ => throw new InvalidOperationException($"Not supported connectivity state {value}")
        };
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}