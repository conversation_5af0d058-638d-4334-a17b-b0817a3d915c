﻿using MgFlasher.Localization.Resources;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class BurbleSpeedMinToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        int val = (int)value;
        switch (val)
        {
            // K_V_SOUND_MN
            // Minimum Resolution of 0.02 km/h
            // User Resolution of 5.0 km/h
            // Adjustable from OEM, to 0 km/h to 300 km/h

            case 0:
                return "OEM"; // min slider position
            default:
                // 0 km/h -> 300 km/h by 5.0 km/h increments
                // Required positions = (300 - 0) / 5 = 60
                // min value == 1, max value == 60 + 1
                // val == 1 => 0 km/h
                // val == 61 => 300 km/h
                return $"{(val * 5) - 5} km/h"; // all other slider positions
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}