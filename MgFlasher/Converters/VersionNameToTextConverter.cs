﻿using MgFlasher.Localization.Resources;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class VersionNameToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return Convert(value);
    }

    public static string Convert(object value) => value switch
    {
        "89RON" => AppResources.Map_Version_89, // For china app: World 89 == China 91
        "89OCT" => AppResources.Map_Version_89, // For china app: World 89 == China 91

        "91RON" => AppResources.Map_Version_91, // For china app: World 91 == China 95
        "91OCT" => AppResources.Map_Version_91, // For china app: World 91 == China 95

        "95RON" => AppResources.Map_Version_95, // For china app: World 95 == China 98
        "95OCT" => AppResources.Map_Version_95, // For china app: World 95 == China 98

        "98RON" => AppResources.Map_Version_98, // For china app: World 98 == China 100
        "98OCT" => AppResources.Map_Version_98, // For china app: World 98 == China 100

        "100RON" => AppResources.Map_Version_100, // For china app: World 100 == China 102
        "100OCT" => AppResources.Map_Version_100, // For china app: World 100 == China 102

        "102RON" => AppResources.Map_Version_102, // For china app: World 102 == China 105
        "102OCT" => AppResources.Map_Version_102, // For china app: World 102 == China 105

        "E30" => AppResources.Map_Version_E30,
        "E50" => AppResources.Map_Version_E50,
        "E85" => AppResources.Map_Version_E85,
        _ => (string)value,
    };

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}