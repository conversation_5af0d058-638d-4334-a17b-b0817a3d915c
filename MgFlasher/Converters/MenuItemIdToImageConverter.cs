﻿using MgFlasher.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace MgFlasher.Converters;

public class MenuItemIdToImageConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var @enum = (Enum)value;

        if (@enum is PageType pageId)
        {
            return ImageSource.FromFile(GetImageByPageId(pageId));
        }
        else if (@enum is ActionType actionId)
        {
            return ImageSource.FromFile(GetImageByActionId(actionId));
        }
        else
        {
            throw new InvalidOperationException($"Value must be of type {nameof(PageType)}");
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }

    private string GetImageByActionId(ActionType actionId)
    {
        switch (actionId)
        {
            case ActionType.Exit:
                return "iconexit.png";

            case ActionType.Support:
                return "icon_manual.png";

            default:
                throw new NotImplementedException($"There is no image assigned to {actionId} action");
        }
    }

    private string GetImageByPageId(PageType pageId)
    {
        switch (pageId)
        {
            case PageType.MyCars:
                return "menumycarsmall.png";

            case PageType.About:
                return "menudemosmall.png";

            case PageType.Feedback:
                return "icon_faq.png";

            case PageType.User:
                return "menuusersmall.png";

            case PageType.Settings:
                return "menuusersmall.png";

            default:
                throw new NotImplementedException($"There is no image assigned to {pageId} page");
        }
    }
}