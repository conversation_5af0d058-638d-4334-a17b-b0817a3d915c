﻿using System;
using System.Globalization;
using Microsoft.Maui;
using Microsoft.Maui.Controls;

namespace MgFlasher.Converters;

public class ProportionalPaddingConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is double width)
        {
            if (width > 896)
            {
                double padding = (width - 896) / 2 /* * 0.5*/;
                return new Thickness(padding, 0, padding, 0);
            }
        }

        return new Thickness(0);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotSupportedException("ProportionalPaddingConverter does not support ConvertBack.");
    }
}