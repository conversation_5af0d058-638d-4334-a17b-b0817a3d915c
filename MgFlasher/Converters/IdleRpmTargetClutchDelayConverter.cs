﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class IdleRpmTargetClutchDelayConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var val = (int)value;
        switch (val)
        {
            case 24:
                return "12.0 s\n(OEM)";

            default:
                {
                    var seconds = val / 2.0;
                    return $"{seconds.ToString("0.0")} s";
                }
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}