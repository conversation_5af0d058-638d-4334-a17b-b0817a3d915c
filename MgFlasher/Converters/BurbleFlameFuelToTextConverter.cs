﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace MgFlasher.Converters;

public class BurbleFlameFuelToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        int val = (int)value;
        switch (val)
        {
            // KL_LA_SBL
            // Minimum Resolution of 0.001 λ
            // User Resolution of 0.01 λ
            // Adjustable from 0.7 to 1.0, to OEM

            case 32:
                return "OEM"; // max slider position
            default:
                // 0.7 -> 1.0 by 0.01 increments
                // Required positions = (1.0 - 0.7) / 0.01 = 30
                // min value == 1, max value == 31 + 1
                // val == 1 => 0.7 λ
                // val == 31 => 1.0 λ
                return $"{(0.69 + (val * 0.01)):0.00} λ"; // all other slider positions
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}