﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class BurbleFlameTimingToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        int val = (int)value;
        // KF_ZWMIN_WSOT and KF_ZWMIN_KSOT
        return $"{val - 1} %"; // all slider positions
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}