﻿using MgFlasher.Flasher.Commands.Models;
using MgFlasher.Localization.Resources;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

class CommandResultToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var commandResult = (CommandResultType)value;

        switch (commandResult)
        {
            case CommandResultType.Completed:
                return AppResources.Success;

            case CommandResultType.Warning:
                return AppResources.Warning;

            case CommandResultType.Error:
                return AppResources.Error;

            default:
                throw new NotImplementedException($"{commandResult} doesnt have assigned text");
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}