﻿using MgFlasher.Flasher.Services.Models;
using MgFlasher.Localization.Resources;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class StageTypeToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return GetStageText((StageType)value);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }

    private string GetStageText(StageType stageType) => stageType switch
    {
        StageType.None => "-",
        StageType.Activated => AppResources.Stage_Super.ToUpper(),
        StageType.Custom => AppResources.Stage_CustomMap.ToUpper(),
        StageType.Stage1 => AppResources.Stage_Stage1.ToUpper(),
        StageType.Stage2 => AppResources.Stage_Stage2.ToUpper(),
        StageType.Stage2_5 => AppResources.Stage_Stage2_5.ToUpper(),
        StageType.Stock => AppResources.Stage_Stock.ToUpper(),
        _ => throw new InvalidOperationException($"Cannot convert {stageType} stage to text"),
    };
}