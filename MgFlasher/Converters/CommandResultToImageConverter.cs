﻿using MgFlasher.Flasher.Commands.Models;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class CommandResultToImageConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var commandResult = (CommandResultType)value;

        var imageName = GetImageName(commandResult);

        return ImageSource.FromFile(imageName);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }

    private string GetImageName(CommandResultType commandResult)
    {
        switch (commandResult)
        {
            case CommandResultType.Completed:
                return "communicate60.png";

            case CommandResultType.Warning:
                return "communicate260.png";

            case CommandResultType.Error:
                return "communicate360.png";

            default:
                throw new NotImplementedException($"{commandResult} doesnt have assigned icon");
        }
    }
}