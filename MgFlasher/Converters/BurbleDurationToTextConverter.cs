﻿using MgFlasher.Localization.Resources;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class BurbleDurationToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        int val = (int)value;
        switch (val)
        {
            case 0:
                return "OEM";

            case 41:
                return $"{(val - 1) / 10.0} s (MPPSK)"; // max slider position
            default:
                return $"{(val - 1) / 10.0} s";
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}