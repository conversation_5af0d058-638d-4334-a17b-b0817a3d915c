using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace MgFlasher.Converters;

public class BackgroundColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value == null || parameter == null)
        {
            return Colors.Transparent;
        }

        if (!int.TryParse(value.ToString(), out int currentStep) || !int.TryParse(parameter.ToString(), out int thisStep))
        {
            return Colors.Transparent;
        }

        string resourceKey = thisStep == currentStep ? "PrimaryTextColor" :
                             thisStep <= currentStep ? "PrimarySuccessColor" : "AccentTextColor";

        if (Application.Current.Resources.TryGetValue(resourceKey, out var color))
        {
            return color as Color ?? Colors.Transparent;
        }

        return thisStep == currentStep ? Colors.Blue :
               thisStep <= currentStep ? Colors.Green : Colors.Grey;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}