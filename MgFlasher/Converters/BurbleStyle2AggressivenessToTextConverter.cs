﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class BurbleStyle2AggressivenessToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var val = (int)value;
        var val_double = val * 3.333333333333333333;
        val_double = Math.Round(val_double * 3, MidpointRounding.AwayFromZero) / 3;

        switch (val)
        {
            case 0:
                return "0%";

            default:
                return $"{Math.Round(val_double, MidpointRounding.AwayFromZero)}%";
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}