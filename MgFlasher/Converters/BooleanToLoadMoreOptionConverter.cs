﻿using Microsoft.Maui.Controls;
using Syncfusion.Maui.ListView;
using System;
using System.Globalization;

namespace MgFlasher.Converters;

public class BooleanToLoadMoreOptionConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return (bool)value switch
        {
            true => LoadMoreOption.Manual,
            _ => LoadMoreOption.None
        };
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}