﻿using MgFlasher.ViewModels.MyCar;
using Microsoft.Maui.Controls;
using System;
using System.Globalization;
using UNI_Flash;

namespace MgFlasher.Converters;

public class ConnectedCarSecurityWaveToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var waveType = (SecurityColourType)value;

        return waveType switch
        {
            SecurityColourType.Green => Application.Current.Resources["PrimarySuccessTextColor"],
            SecurityColourType.Yellow => Application.Current.Resources["PrimaryWarningTextColor"],
            SecurityColourType.Red => Application.Current.Resources["PrimaryImportantTextColor"],
            _ => Application.Current.Resources["PrimaryTextColor"],
        };
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}