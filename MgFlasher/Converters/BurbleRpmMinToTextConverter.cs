﻿using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Converters;

public class BurbleRpmMinToTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        int val = (int)value;
        switch (val)
        {
            // KL_NKW_SOUND_MN
            // Minimum Resolution of 1 RPM
            // User Resolution of 100 RPM
            // Adjustable from OEM, to 900 RPM to 7000 RPM

            case 0:
                return "OEM"; // min slider position
            default:
                // 900 RPM -> 7000 RPM by 100 RPM increments
                // Required positions = (7000 - 900) / 100 = 70 - 9 = 61
                // min value == 1, max value == 61 + 1
                // val == 1 => 900 RPM
                // val == 62 => 7000 RPM
                return $"{800 + (val * 100)} RPM"; // all other slider positions
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}