﻿using MgFlasher.Helpers;
using MgFlasher.Files.Initializers;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls.Xaml;
using MgFlasher.Bootstrap;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Cars.Connectivity;
using MgFlasher.Flasher.Services.DevMode;
using Microsoft.Maui.Controls;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Devices;
using MgFlasher.Flasher.Client.Common;
using MgFlasher.Cache;
using System;
using MgFlasher.Flasher.Services.User.Notifications;
using MgFlasher.Services;
using Sentry;

[assembly: XamlCompilation(XamlCompilationOptions.Compile)]

namespace MgFlasher;

public partial class App : Application
{
    private readonly ILogger<App> _logger;

    private static bool Resume { get; set; }

    public App()
    {
        Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzY0NjY4MkAzMjM4MmUzMDJlMzBOTEZtK01MYW9oSTJYcUQ0V2lwTWtzTUlaS055c3VrQk4xdFpodVUrQ1p3PQ==");
        _logger = DependencyResolver.Resolve<ILogger<App>>();
        InitializeAppServices();
        InitializeComponent();
        SetAndLogAppInformation();
        MainPage = GetMainPage();
    }

    protected override async void OnSleep()
    {
        try
        {
            _logger.LogInformation("OnSleep - starting");
            base.OnSleep();

            //do not try to resolve icacheservice in ctor - dll not found exception
            await DependencyResolver.Resolve<ICacheService>().OnShutdownAsync();

            //potential approach for stackoverflow exception
            await DependencyResolver.Resolve<ILoggerSleeper>().TryStopLoggerServiceAsync();

            //stop ecu permission ping
            DependencyResolver.Resolve<IEcuPermissionHandler>().OnSleep();

            //publish pending events to sentry
            await SentrySdk.FlushAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "OnSleep - error");
        }
        finally
        {
            _logger.LogInformation("OnSleep - finished");
        }
    }

    protected override void OnResume()
    {
        try
        {
            _logger.LogInformation("OnResume - starting");
            base.OnResume();

            //start ecu permission ping
            DependencyResolver.Resolve<IEcuPermissionHandler>().OnResume();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "OnResume - error");
        }
        finally
        {
            _logger.LogInformation("OnResume - finished");
        }
    }

    protected override void OnStart()
    {
        base.OnStart();
        DependencyResolver.Resolve<ITagsListener>().InitAsync();
    }

    private Page GetMainPage()
    {
        var bootstrapService = DependencyResolver.Resolve<IApplicationBootstrapService>();

        return bootstrapService.GetStartupPage();
    }

    private async void InitializeAppServices()
    {
        DependencyResolver.Resolve<IDevModeService>().Init();
        DependencyResolver.Resolve<IDirectoryTreeInitializer>().Initialize();

        await DependencyResolver.Resolve<IAppContextService>().Initialize();
        await DependencyResolver.Resolve<ICarConnectivityStatusChecker>().Init();
    }

    private void SetAndLogAppInformation()
    {
        HttpClientFactory.AppVersionString = AppInfo.VersionString;
        HttpClientFactory.DeviceVersionString = DeviceInfo.VersionString;
        HttpClientFactory.DeviceModel = DeviceInfo.Model;
        HttpClientFactory.DevicePlatform = DeviceInfo.Platform.ToString();

        _logger.LogInformation(
            "APP VERSION: {AppInfoVersionString}, " +
            "BUILD VERSION: {BuildInfoVersionString}, " +
            "OS VERSION: {DeviceInfoVersionString}, " +
            "DEVICE: {DeviceInfoModel}, " +
            "DEVICE TYPE: {DeviceType}, " +
            "DEVICE MANUFACTURER: {Manufacturer}, " +
            "DEVICE NAME: {Name}, " +
            "DEVICE PLATFORM: {Platform}, " +
            "RESUMING: {Resume}",
            AppInfo.VersionString,
            AppInfo.BuildString,
            DeviceInfo.VersionString,
            DeviceInfo.Model,
            DeviceInfo.DeviceType,
            DeviceInfo.Manufacturer,
            DeviceInfo.Name,
            DeviceInfo.Platform,
            Resume);

        Resume = true;
    }
}