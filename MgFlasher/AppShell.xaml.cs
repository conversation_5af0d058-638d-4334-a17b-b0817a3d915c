﻿using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels;
using MgFlasher.Views;
using Microsoft.Maui.Controls;

namespace MgFlasher;

public class AppShell : Shell, INavigationRoot
{
    private readonly INavigationService _navigationService;

    public Shell CurrentPageContainer { get => this; }
    public new BasePage CurrentPage => base.CurrentPage as BasePage;

    public AppShell() : base()
    {
        _navigationService = DependencyResolver.Resolve<INavigationService>();

        BindingContext = DependencyResolver.Resolve<SideMenuViewModel>();

        _navigationService.Init(this);

#if !WINDOWS
        SetTitleView(this, new Views.Controls.ShellTitleView());
#endif
    }

    protected override void OnNavigating(ShellNavigatingEventArgs args)
    {
        base.OnNavigating(args);
        if (args.Source == ShellNavigationSource.Unknown)
        {
            _navigationService.NavigateAsync(PageType.About);
            args.Cancel();
        }
    }
}