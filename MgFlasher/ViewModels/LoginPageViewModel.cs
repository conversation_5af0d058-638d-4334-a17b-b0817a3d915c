﻿using MgFlasher.Flasher.Client.Backend.Client.Contract;
using MgFlasher.Flasher.Client.Common;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services;
using System;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.Cars.Connectivity;
using MgFlasher.Flasher.Services.DevMode;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;
using MgFlasher.Services.Navigation;
using Microsoft.Maui.Controls;
using MgFlasher.Bootstrap;
using Microsoft.Maui;
using System.Collections.Generic;

namespace MgFlasher.ViewModels;

public class LoginPageViewModel : PageViewModel, IContextOptionsViewModel
{
    private readonly ShellTitleViewModel _shellTitleViewModel;
    private readonly IUserService _userService;
    private readonly INavigationService _navigationService;
    private readonly IConnectivityStatusAccessor _connectivityStatusAccessor;
    private readonly IAppContext _appContext;
    private readonly IAppContextService _appContextService;
    private readonly IUserDialogs _userDialogs;
    private readonly ILogger<LoginPageViewModel> _logger;
    private readonly IDevModeService _devModeService;
    private readonly IApplicationBootstrapService _applicationBootstrapService;
    private string _email;
    private string _password;
    private string _errorMessage;
    private bool _loginErrorOccured;
    private bool _isEmailValid;
    private LoginPageNavigationArgs _args;
    private Dictionary<string, Action> _contextOptions;

    public bool IsEmailValid
    {
        get => _isEmailValid;
        set
        {
            SetProperty(ref _isEmailValid, value);
            OnPropertyChanged(nameof(CanLogin));
        }
    }

    public string Email
    {
        get => _email;
        set
        {
            SetProperty(ref _email, value);
            OnPropertyChanged(nameof(CanLogin));
        }
    }

    public string Password
    {
        get => _password;
        set
        {
            SetProperty(ref _password, value);
            OnPropertyChanged(nameof(CanLogin));
        }
    }

    public string Message
    {
        get => _errorMessage;
        private set
        {
            SetProperty(ref _errorMessage, value);
        }
    }

    public bool HasMessage
    {
        get => _loginErrorOccured;
        private set
        {
            SetProperty(ref _loginErrorOccured, value);
        }
    }

    public bool CanLogin =>
        !string.IsNullOrEmpty(_email) && _isEmailValid &&
        !string.IsNullOrEmpty(_password) &&
        !IsBusy;

    public Dictionary<string, Action> ContextOptions
    {
        get => _contextOptions;
        private set => SetProperty(ref _contextOptions, value);
    }

    public ICommand LoginCommand { get; }
    public ICommand MiscCommand { get; }
    public ICommand RegisterCommand { get; }
    public ICommand ForgotPasswordCommand { get; }

    public LoginPageViewModel(
        ShellTitleViewModel shellTitleViewModel,
        IUserService userService,
        INavigationService navigationService,
        IConnectivityStatusAccessor connectivityStatusAccessor,
        IAppContext appContext,
        IUserDialogs userDialogs,
        ILogger<LoginPageViewModel> logger,
        IDevModeService devModeService,
        IApplicationBootstrapService applicationBootstrapService,
        IAppContextService appContextService)
    {
        _shellTitleViewModel = shellTitleViewModel;
        _userService = userService;
        _navigationService = navigationService;
        _connectivityStatusAccessor = connectivityStatusAccessor;
        _appContext = appContext;
        _userDialogs = userDialogs;
        _logger = logger;
        _devModeService = devModeService;
        _applicationBootstrapService = applicationBootstrapService;
        _appContextService = appContextService;
        LoginCommand = new TraceableCommand(Login, nameof(Login));
        RegisterCommand = new TraceableCommand(Register, nameof(Register));
        ForgotPasswordCommand = new TraceableCommand(ForgotPassword, nameof(ForgotPassword));
        MiscCommand = new TraceableCommand(Misc, nameof(Misc));

        InitContextOptions();
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        SetArgs(arg);

        IsBusy = false;
        SetViewModel();
        SetMessage(null);

        return base.OnNavigatedToAsync(previousPage, arg);
    }

    private void SetArgs(object arg)
    {
        _args = (arg as LoginPageNavigationArgs) ?? _args ?? new LoginPageNavigationArgs()
        {
            NextPage = PageType.MyCars,
            Email = _appContext.CurrentUser?.Context?.Credentials?.Email
        };
    }

    private void SetViewModel()
    {
        if (!string.IsNullOrEmpty(_args.Email))
        {
            Email = _args.Email;
            Password = _args.Password;
        }
    }

    private void Misc(object obj)
    {
        _shellTitleViewModel.ContextOptionsCommand.Execute(null);
    }

    private void InitContextOptions()
    {
        var options = new Dictionary<string, Action>
        {
            [AppResources.Login_SwitchToSms] = SwitchToSmsLoginPage
        };

        ContextOptions = options;
    }

    private async void Register()
    {
        await _navigationService.NavigateAsync(PageType.Register, _args);
    }

    private async void ForgotPassword()
    {
        _args.Email = Email;
        await _navigationService.NavigateAsync(PageType.ForgotPassword, _args);
    }

    private void SwitchToSmsLoginPage()
    {
        _appContextService.SetRetailMode(RetailModeEnum.China);
        Application.Current.MainPage = _applicationBootstrapService.GetStartupPage();
        Shell.Current.FlyoutBehavior = FlyoutBehavior.Disabled;
    }

    private async void Login(object arg)
    {
        var scheme = arg as string;

        try
        {
            SetMessage(AppResources.Login_InProgress);
            SetIsBusyWithCommandExecutionNotification(true);

            if (!await _connectivityStatusAccessor.IsConnected())
            {
                await _userDialogs.AlertAsync(message: AppResources.InternetConnectionRequired, okText: AppResources.Ok);
                SetMessage(null);

                return;
            }

            await SignIn(scheme);
            HandleDevMode();

            await _navigationService.NavigateAsync(_args.NextPage, _args.NextPageArguments);

            SetMessage(null);
        }
        catch (UserAuthenticationException ex)
        {
            SetMessage(ex.Message);
        }
        catch (Exception)
        {
            SetMessage(AppResources.Error_Occured);
        }
        finally
        {
            SetIsBusyWithCommandExecutionNotification(false);
        }
    }

    private void SetMessage(string error)
    {
        HasMessage = !string.IsNullOrEmpty(error);
        Message = error;
    }

    private async Task SignIn(string scheme)
    {
        if (scheme is null)
        {
            var credentials = CreateCredentials();
            await _userService.SignIn(credentials);
        }
        else
        {
            await _userService.SignInByExternalProvider(scheme);
        }
    }

    private void SetIsBusyWithCommandExecutionNotification(bool value)
    {
        IsBusy = value;
        OnPropertyChanged(nameof(CanLogin));
    }

    private UserCredentialsDto CreateCredentials() => new UserCredentialsDto()
    {
        Email = Email,
        Password = Password
    };

    private void HandleDevMode()
    {
        if (!_appContext.IsForcedToBenchEcuConnection())
        {
            return;
        }

        if (_appContext.ShouldEnablePpcTestEcu())
        {
            _devModeService.SetDevMode(true);
            _devModeService.SetEnetIp(TestEcus.PpcHost);
            _logger.LogInformation("Enabled PPC test ecu({ip}) because of user role", TestEcus.PpcHost);
        }
        else if (_appContext.ShouldEnableAurixTestEcu())
        {
            _devModeService.SetDevMode(true);
            _devModeService.SetEnetIp(TestEcus.AurixHost);
            _logger.LogInformation("Enabled Aurix test ecu({ip}) because of user role", TestEcus.PpcHost);
        }
    }
}