﻿using MgFlasher.Flasher.Commands.Models;
using MgFlasher.Models;
using MgFlasher.Localization.Resources;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Commands.Diagnostics.Generic;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Helpers;
using Syncfusion.Maui.DataSource.Extensions;

namespace MgFlasher.ViewModels;

public class ReadDtcPageViewModel : PageViewModel
{
    private readonly IReadDtcCommand _readDtcCommand;
    private readonly IUserDialogs _userDialogs;
    private readonly IClearDtcCommand _clearDtcCommand;

    private ObservableCollection<DtcCode> _codes;

    public ObservableCollection<DtcCode> Codes
    {
        get => _codes;
        set => SetProperty(ref _codes, value);
    }

    public ICommand ClearDtcCommand { get; }

    public ReadDtcPageViewModel(IClearDtcCommand clearDtcCommand,
        IReadDtcCommand readDtcCommand,
        IUserDialogs userDialogs)
    {
        _clearDtcCommand = clearDtcCommand;
        _readDtcCommand = readDtcCommand;
        _userDialogs = userDialogs;
        ClearDtcCommand = new TraceableCommand(ClearDtc, nameof(ClearDtc));
        Codes = new ObservableCollection<DtcCode>();
    }

    private async void ClearDtc()
    {
        using (var loading = _userDialogs.Loading(AppResources.ClearDtc_InProgress))
        {
            await _clearDtcCommand.ExecuteAsync();
        }
        var result = _clearDtcCommand.GetResult();

        await _userDialogs.AlertAsync(result);

        using (var loading = _userDialogs.Loading(AppResources.ReadDtc_InProgress))
        {
            await _readDtcCommand.ExecuteAsync();
        }
        var dtcReadResult = _readDtcCommand.GetResult();
        await InformUserAsync(dtcReadResult);
    }

    public async override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _codes.Clear();
        await InformUserAsync(arg as ReadDtcCommandResult);
    }

    private async Task InformUserAsync(ReadDtcCommandResult result)
    {
        if (result?.Result == CommandResultType.Completed)
        {
            Codes.Clear();
            if (!result.Codes.Any())
            {
                await _userDialogs.AlertAsync(AppResources.Flash_Msg_NoDtcFound, okText: AppResources.Ok);
            }
            else
            {
                result.Codes.ForEach(x => Codes.Add(x));
                await _userDialogs.AlertAsync(result.Message, okText: AppResources.Ok);
            }
        }
        else if (result?.Result == CommandResultType.Error)
        {
            await _userDialogs.AlertAsync(result.Message, title: AppResources.Error, okText: AppResources.Ok);
        }
        else if (result?.Result == CommandResultType.Warning)
        {
            await _userDialogs.AlertAsync(result.Message, title: AppResources.Warning, okText: AppResources.Ok);
        }
        else
        {
            Codes.Clear();
        }
    }
}