﻿using MgFlasher.Flasher.Commands;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Commands.Flash;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels;

public class StageVersionsPageViewModel : PageViewModel
{
    private readonly IDCANService _dCANService;
    private readonly IFilesService _filesService;
    private readonly INavigationService _navigationService;
    private readonly IUserDialogs _userDialogs;
    private readonly IChangesetVersionNameViewModelsFactory _changesetVersionNameViewModelsFactory;
    private ObservableCollection<ChangesetVersionNameViewModel> _versionNameViewModels;
    private Car _connectedCar;
    private StageVersionsPageNavigationArgs _args;

    public ObservableCollection<ChangesetVersionNameViewModel> VersionNameViewModels
    {
        get => _versionNameViewModels;
        private set => SetProperty(ref _versionNameViewModels, value);
    }

    public ICommand SubmitCommand { get; }

    public bool CanFlash => !IsBusy && _versionNameViewModels.Any(vn => vn.AnyChecked);

    public StageVersionsPageViewModel(
        IDCANService dCANService,
        IFilesService filesService,
        INavigationService navigationService,
        IUserDialogs userDialogs,
        IChangesetVersionNameViewModelsFactory changesetVersionNameViewModelsFactory)
    {
        _dCANService = dCANService;
        _filesService = filesService;
        _navigationService = navigationService;
        _userDialogs = userDialogs;
        _changesetVersionNameViewModelsFactory = changesetVersionNameViewModelsFactory;
        VersionNameViewModels = new ObservableCollection<ChangesetVersionNameViewModel>();
        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _connectedCar = await _dCANService.GetConnectedCar();
        _args = (arg as StageVersionsPageNavigationArgs) ?? _args;

        var vms = await _changesetVersionNameViewModelsFactory.CreateAsync(_args.StageType, _args.CustomCodeVersion, _connectedCar, OnItemChecked);
        VersionNameViewModels = new ObservableCollection<ChangesetVersionNameViewModel>(vms);

        if (VersionNameViewModels.Count < 1)
        {
            IsBusy = true;
            await _userDialogs.AlertAsync(AppResources.StageFile_DoesntExists, okText: AppResources.Ok);
        }
        else
        {
            IsBusy = false;
        }
        OnPropertyChanged(nameof(CanFlash));
    }

    private void OnItemChecked(IRadioButtonViewModel vm)
    {
        if (!vm.IsChecked)
        {
            return;
        }

        foreach (var versionNameVm in VersionNameViewModels)
        {
            versionNameVm.PropagateChecked(vm);
        }

        OnPropertyChanged(nameof(CanFlash));
    }

    private async void Submit()
    {
        var selectedId = VersionNameViewModels.First(vm => vm.AnyChecked).GetCheckedChangesetId();
        var changeset = _connectedCar.Changesets.GetChangesetsByType(_args.StageType).First(v => v.Id == selectedId);
        if (!await _filesService.Exists(_connectedCar.VIN, _connectedCar.Fingerprint, changeset))
        {
            await _userDialogs.AlertAsync(AppResources.StageFile_DoesntExists, okText: AppResources.Ok);
            return;
        }

        if (_args.FlasherCommand is UniFlashStageFlashCommand uniFlashStageFlashCommand)
        {
            uniFlashStageFlashCommand.MapId = changeset.Id;
            uniFlashStageFlashCommand.TargetCustomCodeVersionString = _args.CustomCodeVersion.ToString();
        }

        if (!_args.SkipFlashingCarOptions && ((_args.FlasherCommand is UniFlashStageFlashCommand command && command.Stage != StageType.Stock) || _args.FlasherCommand is UniFlashCustomMapFlashCommand))
        {
            await _navigationService.NavigateAsync(PageType.FlashingCarOptions, _args.FlasherCommand);
        }
        else
        {
            var flashingCarPageNavigationArguments = new FlashingCarPageNavigationArgs()
            {
                ContinousCommand = _args.FlasherCommand,
                WarningInfos = FlashingCarWarningInfo.GetWarningInfos(changeset.Type)
            };
            await _navigationService.NavigateAsync(PageType.FlashingCarWarningInfo, flashingCarPageNavigationArguments);
        }
    }
}