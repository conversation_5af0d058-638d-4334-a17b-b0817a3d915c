﻿using MgFlasher.Flasher.Commands;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using Microsoft.Maui.Controls;
using CommunityToolkit.Mvvm.Messaging;
using Microsoft.Extensions.Logging;
using static MgFlasher.Helpers.MessagingCenterIdentifiers;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Commands.Models;
using Newtonsoft.Json;
using MgFlasher.Flasher.Services.Tracing;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels;

public class FlashingCarPageViewModel : PageViewModel
{
    private readonly INavigationService _navigationService;
    private readonly IAppTransactionFactory _appTransactionFactory;
    private readonly ILogger<FlashingCarPageViewModel> _logger;

    private int _progress;
    private ObservableCollection<EventMessageModel> _messages;
    private FlashingCarPageNavigationArgs _flashingCarPageArguments;
    private IContinousFlasherCommand _command;
    private IAppTransactionTracer _transaction;
    private IAppTransactionSpan _transactionSpan;

    public FlashingCarPageViewModel(INavigationService navigationService, IAppTransactionFactory appTransactionFactory, ILogger<FlashingCarPageViewModel> logger)
    {
        _navigationService = navigationService;
        _appTransactionFactory = appTransactionFactory;
        _logger = logger;
    }

    public int Progress
    {
        get => _progress;
        private set => SetProperty(ref _progress, value);
    }

    public ObservableCollection<EventMessageModel> Messages
    {
        get => _messages;
        private set => SetProperty(ref _messages, value);
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _flashingCarPageArguments = (FlashingCarPageNavigationArgs)arg;
        _command = _flashingCarPageArguments.ContinousCommand;

        Progress = 0;
        Messages = new ObservableCollection<EventMessageModel>();

        InitCommand();
        ExecuteCommand();

        return base.OnNavigatedToAsync(previousPage, arg);
    }

    public override Task OnNavigatedFromAsync(PageType nextPage)
    {
        CarMessageBroadcaster.Instance.LogArrived -= OnLogMessageArrived;
        _command.ProgressChanged -= OnProgressChanged;
        _command.Completed -= OnCompleted;

        return base.OnNavigatedFromAsync(nextPage);
    }

    private async void ExecuteCommand()
    {
        try
        {
            IsBusy = true;

            _transaction = _appTransactionFactory.StartTransaction(_command.GetType().Name);
            _transactionSpan = _transaction.StartChild("Execute", JsonConvert.SerializeObject(_flashingCarPageArguments.Options));

            await _command.ExecuteAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Flashing execution failed");
        }
        finally
        {
            IsBusy = false;
        }
    }

    private void InitCommand()
    {
        CarMessageBroadcaster.Instance.LogArrived += OnLogMessageArrived;
        _command.ProgressChanged += OnProgressChanged;
        _command.Completed += OnCompleted;
        _command.Init(_flashingCarPageArguments.Options);
    }

    private void OnCompleted(object sender, bool success)
    {
        Application.Current.Dispatcher.Dispatch(async () =>
        {
            var result = _command.GetResult();
            _logger.LogInformation("result => {Message}, {Result}", result.Message, result.Result);
            _transactionSpan?.Finish(result.Result == CommandResultType.Completed);
            _transaction?.Finish(result.Result == CommandResultType.Completed);
            await _navigationService.NavigateAsync(PageType.ProcessCompleted, result);
        });
    }

    private void OnProgressChanged(object sender, int e)
    {
        Application.Current.Dispatcher.Dispatch(() =>
        {
            Progress = e;
            _transaction?.SetMeasurement(nameof(Progress), e);
            _transactionSpan?.SetMeasurement(nameof(Progress), e);
        });
    }

    private void OnLogMessageArrived(object sender, string e)
    {
        Application.Current.Dispatcher.Dispatch(() =>
        {
            var model = new EventMessageModel(e);
            Messages.Add(model);
            _transactionSpan?.Finish();
            _transactionSpan = _transaction.StartChild(e);
            WeakReferenceMessenger.Default.Send(new FlashingCarLogMessage(model));
        });
    }
}