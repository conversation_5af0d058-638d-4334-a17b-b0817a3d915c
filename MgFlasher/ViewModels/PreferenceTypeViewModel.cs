﻿using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User.Storage;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Localization.Resources;
using System;

namespace MgFlasher.ViewModels;

public class PreferenceTypeViewModel : BaseViewModel
{
    private Enum _preferenceType;

    public Enum PreferenceType
    {
        get => _preferenceType;
        private set => SetProperty(ref _preferenceType, value);
    }

    public string Translation => PreferenceType switch
    {
        ModuleLoggingPreferenceType.Both => AppResources.LoggerPreference_BothEcus,
        ModuleLoggingPreferenceType.MasterOnly => AppResources.LoggerPreference_MasterOnly,
        ModuleLoggingPreferenceType.SlaveOnly => AppResources.LoggerPreference_SlaveOnly,
        GaugePresentationType.Circular => AppResources.LoggerPreference_CircularGauge,
        GaugePresentationType.Rectangle => AppResources.LoggerPreference_RectangleGauge,
        LoggerAlertType.Above => AppResources.LoggerAlertType_Above,
        LoggerAlertType.Below => AppResources.LoggerAlertType_Below,
        LoggerAlertType.Equals => AppResources.LoggerAlertType_Equals,
        LoggerAlertType.OutsideRange => AppResources.LoggerAlertType_OutsideRange,
        LoggerAlertType.InsideRange => AppResources.LoggerAlertType_InsideRange,
        _ => "NA"
    };

    public PreferenceTypeViewModel(Enum preferenceType)
    {
        _preferenceType = preferenceType;
    }
}