﻿using MgFlasher.Ecu.Common.Extensions;
using MgFlasher.Files;
using MgFlasher.Flasher.Client.Common;
using MgFlasher.Flasher.Commands.Diagnostics.Generic;
using MgFlasher.Flasher.Commands.Models;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Backend;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.Connectivity;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.DevMode;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Devices;
using Shiny;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels;

public class AboutViewModel : PageViewModel
{
    private volatile int _devModeToggleCnt = 0;
    private readonly IShareService _shareService;
    private readonly IDirectoryPathFactory _directoryPathFactory;
    private readonly IFilesZipper _filesZipper;
    private readonly IDevModeService _devModeService;
    private readonly IDCANService _dCANService;
    private readonly INavigationService _navigationService;
    private readonly IReadPartialEcuCommand _readPartialEcuCommand;
    private readonly IAppContext _appContext;
    private readonly IUserDialogs _userDialogs;
    private readonly IConnectivityStatusAccessor _connectivityStatusAccessor;
    private readonly ILogger<AboutViewModel> _logger;
    private readonly object _syncObject;

    private bool _isDevMode;
    private bool _isAbleToDeleteBackups;

    public bool IsDevMode
    {
        get => _isDevMode;
        set => SetProperty(ref _isDevMode, value);
    }

    public bool IsAbleToDeleteBackups
    {
        get => _isAbleToDeleteBackups;
        set => SetProperty(ref _isAbleToDeleteBackups, value);
    }

    public string AppVersion { get; }
    public string OsVersion { get; }
    public string DeviceName { get; }
    public string ENETIP { get; }
    public string BackendURL { get; private set; }

    public ICommand ToggleDevModeCommand { get; }
    public ICommand ShareAppLogsCommand { get; }
    public ICommand DeleteDebugBackupCommand { get; }
    public ICommand ReadPartialEcuCommand { get; }
    public ICommand OverrideEnetIpCommand { get; }
    public ICommand OverrideBackendUrlCommand { get; }

    public AboutViewModel(IShareService shareService,
        IDirectoryPathFactory directoryPathFactory,
        IFilesZipper filesZipper,
        IDevModeService devModeService,
        IDCANService dCANService,
        INavigationService navigationService,
        IReadPartialEcuCommand readPartialEcuCommand,
        IAppContext appContext,
        IUserDialogs userDialogs,
        ILogger<AboutViewModel> logger,
        IConnectivityStatusAccessor connectivityStatusAccessor)
    {
        _shareService = shareService;
        _directoryPathFactory = directoryPathFactory;
        _filesZipper = filesZipper;
        _devModeService = devModeService;
        _dCANService = dCANService;
        _navigationService = navigationService;
        _readPartialEcuCommand = readPartialEcuCommand;
        _appContext = appContext;
        _userDialogs = userDialogs;
        _connectivityStatusAccessor = connectivityStatusAccessor;
        _logger = logger;
        _isDevMode = _devModeService.IsDevMode();
        _syncObject = new object();

        AppVersion = $"Version {AppInfo.VersionString} / Build {AppInfo.BuildString}";
        OsVersion = $"OS {DeviceInfo.VersionString}";
        DeviceName = $"Device {DeviceInfo.Model}";
        ENETIP = $"ECU: {_devModeService.GetEnetIp()}";
        BackendURL = $"Backend: {_appContext.GetBackendUrl(_devModeService)}";

        ToggleDevModeCommand = new TraceableCommand(ToggleDevMode, nameof(ToggleDevMode));
        ShareAppLogsCommand = new TraceableCommand(ShareAppLogs, nameof(ShareAppLogs));
        DeleteDebugBackupCommand = new TraceableCommand(DeleteDebugBackup, nameof(DeleteDebugBackup));
        ReadPartialEcuCommand = new TraceableCommand(ReadPartialEcu, nameof(ReadPartialEcu));
        OverrideEnetIpCommand = new TraceableCommand(async () => await PromptEnetDevAddress(), nameof(PromptEnetDevAddress));
        OverrideBackendUrlCommand = new TraceableCommand(async () => await PromptBackendUrl(), nameof(PromptBackendUrl));
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (!_appContext.IsDeveloper() && !_appContext.HasBenchEcuAccess())
        {
            await ResetDevMode();
        }

        IsAbleToDeleteBackups = _appContext.IsSavingOutputFileTester() || _appContext.IsDeveloper();
        await base.OnNavigatedToAsync(previousPage, arg);
    }

    private async void DeleteDebugBackup()
    {
        IsBusy = true;
        _logger.LogInformation("Confirming if user wants to delete temporary flashing file backups created in debug mode...");

        try
        {
            if (!await _userDialogs.ConfirmAsync("Delete debug flashing backup files?", okText: AppResources.Ok, cancelText: AppResources.Cancel))
            {
                _logger.LogInformation("User cancelled");
                return;
            }

            _logger.LogInformation("Deleting debug flashing backup files...");

            var filesToDelete = new List<string>
            {
                Path.Combine(_directoryPathFactory.GetPublicStoragePath(), "shared-app-content.zip"),
                Path.Combine(_directoryPathFactory.GetSharePath(), "shared-backups.zip"),
            };
            var myDocumentsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "Data");
            if (Directory.Exists(myDocumentsPath))
            {
                filesToDelete.AddRange(Directory.GetFileSystemEntries(myDocumentsPath, "temp*.bin", new EnumerationOptions() { RecurseSubdirectories = true }));
            }

            if (Directory.Exists(_directoryPathFactory.GetPublicStoragePath()))
            {
                filesToDelete.AddRange(Directory.GetFileSystemEntries(_directoryPathFactory.GetPublicStoragePath(), "temp*.bin", new EnumerationOptions() { RecurseSubdirectories = true }));
            }

            filesToDelete.AddRange(Directory.GetFileSystemEntries(_directoryPathFactory.GetBackupsPath(), "temp*.bin"));

            var deletedDataSize = (long)0;
            var deletedFiles = new List<string> { };
            foreach (var file in filesToDelete)
            {
                if (File.Exists(file))
                {
                    var fileInfo = new FileInfo(file);
                    deletedDataSize += fileInfo.Length;
                    _logger.LogInformation("Deleting file: {file}", file);
                    File.Delete(file);
                    deletedFiles.Add(file);
                }
            }

            var message = $"Done deleting [{deletedFiles.Count}] files. Cleared {SizeSuffix(deletedDataSize)}!";
            await _userDialogs.AlertAsync(message, okText: AppResources.Ok);
            _logger.LogInformation(message);
        }
        catch (Exception e)
        {
            await _userDialogs.AlertAsync(string.Concat("Deleting debug backup failed", e), okText: AppResources.Ok);
            _logger.LogInformation("Deleting debug backup failed!!!");
        }
        finally
        {
            IsBusy = false;
        }
    }

    private static readonly string[] SizeSuffixes = { "bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB" };

    private static string SizeSuffix(Int64 value, int decimalPlaces = 1)
    {
        if (decimalPlaces < 0)
        {
            throw new ArgumentOutOfRangeException("decimalPlaces");
        }

        if (value < 0)
        {
            return "-" + SizeSuffix(-value, decimalPlaces);
        }

        if (value == 0)
        {
            return string.Format("{0:n" + decimalPlaces + "} bytes", 0);
        }

        // mag is 0 for bytes, 1 for KB, 2, for MB, etc.
        int mag = (int)Math.Log(value, 1024);

        // 1L << (mag * 10) == 2 ^ (10 * mag) [i.e. the number of bytes in the unit corresponding to mag]
        decimal adjustedSize = (decimal)value / (1L << (mag * 10));

        // make adjustment when the value is large enough that it would round up to 1000 or more
        if (Math.Round(adjustedSize, decimalPlaces) >= 1000)
        {
            mag += 1;
            adjustedSize /= 1024;
        }

        return string.Format("{0:n" + decimalPlaces + "} {1}", adjustedSize, SizeSuffixes[mag]);
    }

    private async void ShareAppLogs()
    {
        IsBusy = true;

        try
        {
            var includeInternalFiles = _appContext.IsDeveloper() || _appContext.IsTroubleshooter() || _appContext.HasBenchEcuAccess();
            if (includeInternalFiles)
            {
                if (!await _userDialogs.ConfirmAsync(AppResources.About_ExportAllFiles_Message, okText: AppResources.Yes, cancelText: AppResources.No))
                {
                    _logger.LogInformation("Exporting only app logs, not all app files!");
                    includeInternalFiles = false;
                }
            }

            string archiveAppPath = null;

            using (var loading = _userDialogs.Loading(AppResources.Loading))
            {
                await Task.Factory.StartNew(() => archiveAppPath = _filesZipper.MakeBackupArchive(includeInternalFiles));
            }

            _logger.LogInformation("sharing...");
            await _shareService.Share("Logs", archiveAppPath);

            _logger.LogInformation("done sharing");
        }
        catch (Exception e)
        {
            await _userDialogs.AlertAsync(message: e.ToString(), title: AppResources.Menu_About_Share_Log_Failure, okText: AppResources.Ok);
            _logger.LogError(e, "Sharing app logs failed!!!");
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task PromptEnetDevAddress()
    {
        var ppcString = $"PPC [{TestEcus.PpcHost}]";
        var aurixString = $"AURIX [{TestEcus.AurixHost}]";
        var customHostString = $"Custom host";
        var noOverrideString = $"Disable host overwrite";
        var result = await _userDialogs.ActionSheetAsync(
            title: AppResources.DevMode_ChooseBenchECU,
            cancel: AppResources.Cancel,
            destructive: null, cancelToken: null,
            buttons: [ppcString, aurixString, customHostString, noOverrideString]
        );

        var message = string.Empty;
        if (result.Contains(ppcString))
        {
            await SetEnetDevAddress(TestEcus.PpcHost);
            message = $"Setting override host to PPC: {TestEcus.PpcHost}";
        }
        else if (result.Contains(aurixString))
        {
            await SetEnetDevAddress(TestEcus.AurixHost);
            message = $"Setting override host to AURIX: {TestEcus.AurixHost}";
        }
        else if (result.Contains(customHostString))
        {
            var promptResult = await _userDialogs.PromptAsync(new PromptConfig
            {
                OkText = AppResources.About_OverrideEnetIp_Ok,
                Title = AppResources.About_OverrideEnetIp_Title,
                Message = AppResources.About_OverrideEnetIp_Message,
                Text = _devModeService.GetEnetIp(),
            });

            if (promptResult.Ok)
            {
                await SetEnetDevAddress(promptResult.Text);
                message = $"Custom host set to [{promptResult.Text}]";
            }
        }
        else if (result.Contains(noOverrideString))
        {
            await SetEnetDevAddress(null);
            message = "Not overriding connection host";
        }

        if (!string.IsNullOrEmpty(message))
        {
            await _userDialogs.AlertAsync(message);
        }
    }

    private async Task SetEnetDevAddress(string target)
    {
        if (string.IsNullOrEmpty(target))
        {
            _logger.LogInformation("Changing Enet Dev address from {Old} to {New}", _devModeService.GetEnetIp(), "empty");
            _devModeService.SetEnetIp(null);
            _dCANService.ChangeConnectedCar(null);
            return;
        }

        var old = _devModeService.GetEnetIp();
        var splitAddress = target?.Split(":", StringSplitOptions.RemoveEmptyEntries) ?? new string[2];

        var ipOrDomain = splitAddress.ElementAtOrDefault(0);
        var port = splitAddress.ElementAtOrDefault(1);

        if (!Uri.CheckHostName(ipOrDomain).In(UriHostNameType.IPv4, UriHostNameType.Dns) || !ushort.TryParse(port, out ushort _))
        {
            await _userDialogs.AlertAsync(AppResources.About_OverrideEnetIp_InvalidIp, okText: AppResources.Ok);
        }
        else
        {
            if (old != target)
            {
                _logger.LogInformation("Changing Enet Dev address from {Old} to {New}", old, target);
                _devModeService.SetEnetIp(target);
                _dCANService.ChangeConnectedCar(null);
            }
        }
    }

    private async Task PromptBackendUrl()
    {
        var localHostString = $"localhost:8080";
        var customHostString = $"Custom host";
        var noOverrideString = $"Disable host overwrite";
        var result = await _userDialogs.ActionSheetAsync(
            title: AppResources.DevMode_ChooseBenchECU,
            cancel: AppResources.Cancel,
            destructive: null, cancelToken: null,
            buttons: [localHostString, customHostString, noOverrideString]
        );
        var message = string.Empty;

        if (result.Contains(customHostString))
        {
            var promptResult = await _userDialogs.PromptAsync(new PromptConfig
            {
                OkText = AppResources.About_OverrideEnetIp_Ok,
                Title = AppResources.About_OverrideEnetIp_Title,
                Message = AppResources.About_OverrideEnetIp_Message,
                Text = _devModeService.GetBackendUrl(),
            });

            if (promptResult.Ok)
            {
                var isUrlSet = await SetBackendUrl(promptResult.Text);
                if (isUrlSet)
                {
                    message = $"Custom backend url set to [{promptResult.Text}]";
                }
            }
        }
        else if (result.Contains(noOverrideString))
        {
            var isUrlSet = await SetBackendUrl(null);
            if (isUrlSet)
            {
                message = "Not overriding backend url";
            }
        }
        else if (result.Contains(localHostString))
        {
            var isUrlSet = await SetBackendUrl("localhost:8080");
            if (isUrlSet)
            {
                message = "Custom backend set to localhost:8080";
            }
        }

        using (var loading = _userDialogs.Loading(AppResources.Loading))
        {
            var isConnected = await _connectivityStatusAccessor.IsConnected();
            if (!isConnected)
            {
                await _userDialogs.AlertAsync(AppResources.BadBackendOverrideIp, okText: AppResources.Ok);
            }
            else
            {
                await _userDialogs.AlertAsync(AppResources.GoodBackendOverrideIp, okText: AppResources.Ok);
            }
        }

        if (!string.IsNullOrEmpty(message))
        {
            await _userDialogs.AlertAsync(message);
        }
    }

    private async Task<bool> SetBackendUrl(string target)
    {
        var oldBackendUrl = _devModeService.GetBackendUrl();
        if (string.IsNullOrEmpty(target))
        {
            _logger.LogInformation("Changing backend url from {Old} to {New}", oldBackendUrl, "empty");
            _devModeService.SetBackendUrl(null);
            await UpdateBackendUrlDisplay();
            return true;
        }

        var splitUrl = target.Split(":", StringSplitOptions.RemoveEmptyEntries);

        var ipOrDomain = splitUrl.ElementAtOrDefault(0);
        var port = splitUrl.ElementAtOrDefault(1);

        if (!Uri.CheckHostName(ipOrDomain).In(UriHostNameType.IPv4, UriHostNameType.Dns) || !ushort.TryParse(port, out ushort _))
        {
            await _userDialogs.AlertAsync(AppResources.About_OverrideEnetIp_InvalidIp, okText: AppResources.Ok);
            return false;
        }
        else
        {
            if (oldBackendUrl != target)
            {
                _logger.LogInformation("Changing backend url from {Old} to {New}", oldBackendUrl, target);
                _devModeService.SetBackendUrl(target);
                await UpdateBackendUrlDisplay();
                return true;
            }

            return false;
        }
    }

    private async void ToggleDevMode(object obj)
    {
        lock (_syncObject)
        {
            if (++_devModeToggleCnt >= 5)
            {
                _devModeToggleCnt = 0;
#if !DEBUG
                    if (_appContext.IsDeveloper() || _appContext.HasBenchEcuAccess())
                    {
#endif
                var devModeValue = !_devModeService.IsDevMode();
                _devModeService.SetDevMode(devModeValue);
                IsDevMode = devModeValue;
#if !DEBUG
                    }
                    else
                    {
                        ResetDevMode();
                    }
#endif
            }
        }

        if (_devModeToggleCnt == 0)
        {
            if (!_devModeService.IsDevMode())
            {
                await SetEnetDevAddress(null);
                _logger.LogInformation("Removing Override IP address...");
                await SetBackendUrl(null);
                await UpdateBackendUrlDisplay();
                _logger.LogInformation("Removing Override Backend address...");
            }

            if (_appContext.IsDeveloper() || _appContext.HasBenchEcuAccess())
            {
                if (_devModeService.IsDevMode())
                {
                    await _userDialogs.AlertAsync("Dev-Mode Activated!");
                    await PromptEnetDevAddress();
                }
                else
                {
                    await _userDialogs.AlertAsync("Dev-Mode deactivated");
                }
            }
            else
            {
                await _userDialogs.AlertAsync("You are not a developer!");
            }
        }
    }

    private async void ReadPartialEcu()
    {
        var car = await _dCANService.GetConnectedCar();

        if (car is null)
        {
            await _userDialogs.AlertAsync(AppResources.Flash_SelectCar, okText: AppResources.Ok);
            return;
        }

        if (car.AnyEcuActivated)
        {
            if (_appContext.IsDeveloper())
            {
                if (!await _userDialogs.ConfirmAsync("Reading ECU will fail if Readout Protection is enabled. Continue?", okText: AppResources.Ok, cancelText: AppResources.Cancel))
                {
                    return;
                }
            }
            else
            {
                await _userDialogs.AlertAsync(AppResources.ReadEcu_Error_Activated, okText: AppResources.Ok);
                return;
            }
        }

        if (!_appContext.IsDeveloper())
        {
            if (!await _userDialogs.ConfirmAsync(AppResources.ReadEcu_Warning, okText: AppResources.Ok, cancelText: AppResources.Cancel))
            {
                return;
            }
        }

        await _navigationService.NavigateAsync(PageType.Pending, new PendingPageNavigationArgs()
        {
            Text = AppResources.ReadEcu_InProgress,
            NextPage = PageType.MyCar,
            BackPage = PageType.MyCar
        });

        await Task.Run(async () =>
        {
            try
            {
                await _readPartialEcuCommand.ExecuteAsync();
                var result = _readPartialEcuCommand.GetResult();
                if (result.Result == CommandResultType.Completed)
                {
                    Application.Current.Dispatcher.Dispatch(async () =>
                    {
                        await _userDialogs.AlertAsync(AppResources.ReadEcu_Success, null, AppResources.Ok);
                    });
                }
                else
                {
                    Application.Current.Dispatcher.Dispatch(async () =>
                    {
                        await _userDialogs.AlertAsync(result.Message, title: AppResources.Error, AppResources.Ok);
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error!");
                var result = new CommandResult()
                {
                    Message = $"{ex.GetType().Name}({ex.Message})",
                    Result = CommandResultType.Error
                };
                Application.Current.Dispatcher.Dispatch(async () =>
                {
                    await _userDialogs.AlertAsync(result.Message, title: AppResources.Error, AppResources.Ok);
                });
            }
        });
    }

    /// <summary>
    /// Reset the developer mode preferences to null.
    /// </summary>
    /// <returns></returns>
    private async Task ResetDevMode()
    {
        var devModeValue = false;
        _devModeService.SetDevMode(devModeValue);
        _devModeService.SetEnetIp(null);
        _devModeService.SetBackendUrl(null);
        await UpdateBackendUrlDisplay();
        IsDevMode = devModeValue;
    }

    private async Task UpdateBackendUrlDisplay()
    {
        BackendURL = $"Backend: {_appContext.GetBackendUrl(_devModeService)}";
        OnPropertyChanged(nameof(BackendURL));
    }
}