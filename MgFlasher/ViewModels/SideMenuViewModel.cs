﻿using MgFlasher.Models;
using MgFlasher.Services;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User;
using System;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels;

public class SideMenuViewModel : BaseViewModel
{
    private readonly INavigationService _navigationService;
    private readonly ICloseAppService _closeAppService;
    private readonly IUserSupportOpenner _userSupportOpenner;

    public ICommand NavigateCommand { get; }

    public SideMenuViewModel(INavigationService navigationService,
        ICloseAppService closeAppService,
        IUserSupportOpenner userSupportOpenner)
    {
        _navigationService = navigationService;
        _closeAppService = closeAppService;
        _userSupportOpenner = userSupportOpenner;
        NavigateCommand = new TraceableCommand(Navigate, nameof(Navigate));
    }

    private async void Navigate(object obj)
    {
        if (obj is not Enum @enum)
        {
            return;
        }

        if (@enum is PageType pageId)
        {
            await _navigationService.NavigateAsync(pageId);
        }
        else if (@enum is ActionType actionType)
        {
            if (actionType == ActionType.Exit)
            {
                _closeAppService.CloseApplication();
            }
            else if (actionType == ActionType.Support)
            {
                await _userSupportOpenner.Open();
            }
        }
    }
}