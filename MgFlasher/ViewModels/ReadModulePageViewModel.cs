﻿using DiagProtocolInterface;
using MgFlasher.Flasher.Commands.Diagnostics.Generic;
using MgFlasher.Flasher.Commands.Models;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User.Dialogs;
using UNI_Flash;
using Microsoft.Maui.Controls;
using Microsoft.Maui;
using MgFlasher.Helpers;
using Microsoft.Extensions.Logging;
using MgFlasher.Services.Navigation;
using MgFlasher.Flasher.Services.User.Feedback;

namespace MgFlasher.ViewModels;

public class ReadModulePageViewModel : PageViewModel
{
    private readonly IReadModuleCommand _readModuleCommand;
    private readonly INavigationService _navigationService;
    private readonly IUserDialogs _userDialogs;
    private readonly IAppContext _appContext;
    private readonly ILogger<ReadModulePageViewModel> _logger;
    private readonly IFilesService _filesService;

    private ObservableCollection<EcuInformationValue> _ecuInformation;
    private bool _carNotSupported, _canSendFeedback;
    private string _ecuInfoHeadingString;
    private string _sweListNameString;

    private ReadModuleCommandResult _result;

    public ObservableCollection<EcuInformationValue> EcuInformation
    {
        get => _ecuInformation;
        set => SetProperty(ref _ecuInformation, value);
    }

    public bool CanSendFeedback
    {
        get => _canSendFeedback;
        private set => SetProperty(ref _canSendFeedback, value);
    }

    public bool CarNotSupported
    {
        get => _carNotSupported;
        private set => SetProperty(ref _carNotSupported, value);
    }

    public string EcuInfoHeadingString
    {
        get => _ecuInfoHeadingString;
        private set => SetProperty(ref _ecuInfoHeadingString, value);
    }

    public string SweListNameString
    {
        get => _sweListNameString;
        private set => SetProperty(ref _sweListNameString, value);
    }

    public ICommand SendFeedbackComand { get; set; }
    public ICommand SelectSweListCommand { get; }

    public ReadModulePageViewModel(
        IReadModuleCommand readModuleCommand,
        INavigationService navigationService,
        IFilesService filesService,
        IUserDialogs userDialogs,
        IAppContext appContext,
        ILogger<ReadModulePageViewModel> logger)
    {
        _readModuleCommand = readModuleCommand;
        _navigationService = navigationService;
        _userDialogs = userDialogs;
        _appContext = appContext;
        _logger = logger;
        _filesService = filesService;
        SendFeedbackComand = new TraceableCommand(SendFeedback, nameof(SendFeedback));
        SelectSweListCommand = new TraceableCommand(SelectSweList, nameof(SelectSweList));
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        EcuInformation = null;

        _result = arg as ReadModuleCommandResult ?? _readModuleCommand.GetResult() ?? _result;

        CanSendFeedback = _appContext.RetailMode != RetailModeEnum.China;
        if (_result != null && _result.ConnectedCar != null)
        {
            CarNotSupported = (bool)_result.ConnectedCar.Supported;
        }
        else
        {
            CarNotSupported = false;
        }

        SweListNameString = UDS_BMW.UDS_RecordIdentifier.SVK_AKTUELL.ToString();
        SetObservableCollection(_result);

        if (_result != null && _result.ConnectedCar != null && !string.IsNullOrEmpty(_result.ConnectedCar.EngineCode))
        {
            EcuInfoHeadingString = string.Concat(_result.ConnectedCar.EngineCode, " ", AppResources.ReadModule_Headline);
        }
        else
        {
            EcuInfoHeadingString = AppResources.ReadModule_Headline;
        }

        if (_result.Result != CommandResultType.Completed)
        {
            await _userDialogs.AlertAsync(_result);
        }
    }

    private void SetObservableCollection(ReadModuleCommandResult result)
    {
        if (result == null || result.ConnectedCar == null || result.ConnectedCar.EcuMaster == null)
        {
            EcuInformation = new ObservableCollection<EcuInformationValue>();
            return;
        }

        var ecuMaster = result.ConnectedCar.EcuMaster;
        EcuInformation = new ObservableCollection<EcuInformationValue>(
            ecuMaster.SweVersions?.Select(x => new EcuInformationValue(x.GetXweString())) ?? Enumerable.Empty<EcuInformationValue>());

        if (!string.IsNullOrEmpty(ecuMaster.CALID) && !string.IsNullOrEmpty(ecuMaster.CVN))
        {
            EcuInformation.Add(new(string.Concat(AppResources.ReadModule_CALID, ecuMaster.CALID, AppResources.ReadModule_CVN, ecuMaster.CVN)));
        }

        if (!string.IsNullOrEmpty(ecuMaster.CustomCodeVersionInstalled))
        {
            if (ecuMaster.CustomCodeVersionInstalled != "NotFound")
            {
                EcuInformation.Add(new(string.Concat(AppResources.ReadModule_CustomCodeVersion, ecuMaster.CustomCodeVersionInstalled)));
            }
        }

        if (!string.IsNullOrEmpty(ecuMaster.ManufacturingDate))
        {
            EcuInformation.Add(new(string.Concat(AppResources.ReadModule_ManufacturingDate, ecuMaster.ManufacturingDate)));
        }

        if (!string.IsNullOrWhiteSpace(ecuMaster.BootctrlVersion))
        {
            EcuInformation.Add(new(string.Concat(AppResources.ReadModule_BootctrlVersion, ecuMaster.BootctrlVersion)));
        }

        if (ecuMaster.IsBootMode)
        {
            EcuInformation.Add(new(string.Concat(AppResources.ReadModule_Status, AppResources.ReadModule_Status_Bootmode)));
        }
        else
        {
            if (result.ConnectedCar.Activated)
            {
                EcuInformation.Add(new(string.Concat(AppResources.ReadModule_Status, AppResources.ReadModule_Status_Unlocked)));
            }
            else
            {
                EcuInformation.Add(new(string.Concat(AppResources.ReadModule_Status, AppResources.ReadModule_Status_Locked)));
            }
        }

        EcuInformation.Add(new(string.Concat(AppResources.ReadModule_RockerComboSupport, result.ConnectedCar.BdcSupportsAllCanbusCruiseControlData ? AppResources.Yes : AppResources.No)));
        EcuInformation.Add(new(string.Concat(AppResources.ReadModule_SecurityWave, result.ConnectedCar.SecurityWave)));

        if (result.ConnectedCar.EcuSlave != null)
        {
            var ecuSlave = result.ConnectedCar.EcuMaster;

            EcuInformation.Add(new(AppResources.ReadModule_FoundSlaveEcu));

            foreach (var svkItem in ecuSlave.SweVersions?.Select(x => x.GetXweString()).ToList())
            {
                EcuInformation.Add(new(string.Concat(AppResources.ReadModule_SlaveSwe, svkItem)));
            }

            if (!string.IsNullOrEmpty(ecuSlave.CALID) && !string.IsNullOrEmpty(ecuSlave.CVN))
            {
                EcuInformation.Add(new(string.Concat(AppResources.ReadModule_CALID_Slave, ecuSlave.CALID, AppResources.ReadModule_CVN_Slave, ecuSlave.CVN)));
            }

            if (!string.IsNullOrEmpty(ecuSlave.CustomCodeVersionInstalled))
            {
                if (ecuSlave.CustomCodeVersionInstalled != "NotFound")
                {
                    EcuInformation.Add(new(string.Concat(AppResources.ReadModule_CustomCodeVersion_Slave, ecuSlave.CustomCodeVersionInstalled)));
                }
            }

            if (!string.IsNullOrEmpty(ecuSlave.ManufacturingDate))
            {
                EcuInformation.Add(new(string.Concat(AppResources.ReadModule_ManufacturingDate, ecuSlave.ManufacturingDate)));
            }

            if (!string.IsNullOrWhiteSpace(ecuSlave.BootctrlVersion))
            {
                EcuInformation.Add(new(string.Concat(AppResources.ReadModule_BootctrlVersion, ecuSlave.BootctrlVersion)));
            }

            if (ecuSlave.IsBootMode)
            {
                EcuInformation.Add(new(string.Concat(AppResources.ReadModule_SlaveStatus, AppResources.ReadModule_Status_Bootmode)));
            }
            else
            {
                if (result.ConnectedCar.Activated)
                {
                    EcuInformation.Add(new(string.Concat(AppResources.ReadModule_SlaveStatus, AppResources.ReadModule_Status_Unlocked)));
                }
                else
                {
                    EcuInformation.Add(new(string.Concat(AppResources.ReadModule_SlaveStatus, AppResources.ReadModule_Status_Locked)));
                }
            }
        }

        _logger.LogInformation($"ECU Info page shows:\n\t{string.Join("\n\t", EcuInformation.Select(x => x.Value))}");
    }

    private async void SendFeedback()
    {
        var args = await CreateFeedbackNavigationArgsAsync();
        await _navigationService.NavigateAsync(PageType.Feedback, args);
    }

    private async void SelectSweList()
    {
        var availableSweHistoryItemCommandIds = _result.ConnectedCar.EcuMaster.SweBackupList.Select(i => i.CommandId).ToArray();
        if (availableSweHistoryItemCommandIds.Count() > 0)
        {
            var sweListToDisplay = await _userDialogs.ActionSheetAsync(AppResources.ReadModule_SelectSweList, AppResources.Cancel, null, null, availableSweHistoryItemCommandIds);
            if (sweListToDisplay != AppResources.Cancel && !string.IsNullOrWhiteSpace(sweListToDisplay))
            {
                var sweList = _result.ConnectedCar.EcuMaster.SweBackupList.Where(l => l.CommandId == sweListToDisplay).FirstOrDefault().SweVersions;
                if (sweList != null)
                {
                    SweListNameString = sweListToDisplay;
                    _logger.LogInformation("Setting SWE List to [{sweListToDisplay}]...", sweListToDisplay);
                    _result.ConnectedCar.EcuMaster.SweVersions = sweList;
                    if (_result.ConnectedCar.EcuSlave != null)
                    {
                        var sweListSlave = _result.ConnectedCar.EcuSlave.SweBackupList.Where(l => l.CommandId == sweListToDisplay).FirstOrDefault().SweVersions;
                        _result.ConnectedCar.EcuSlave.SweVersions = sweListSlave ?? new List<SweVersion>();
                    }

                    _logger.LogInformation("Displaying update...");
                    SetObservableCollection(_result);
                }
                else
                {
                    var msg = sweListToDisplay + " " + AppResources.ReadModule_NoItems;
                    await _userDialogs.AlertAsync(msg, AppResources.Error);
                }
            }
        }
        else
        {
            await _userDialogs.AlertAsync(AppResources.ReadModule_NoOtherSweLists, AppResources.Error);
        }
    }

    private async Task<FeedbackPageNavigationArgs> CreateFeedbackNavigationArgsAsync()
    {
        var args = new FeedbackPageNavigationArgs()
        {
            SystemSubject = SystemFeedbackSubject.CarSupportRequest,
            Subject = AppResources.Feedback_MyCarNotSupported_Subject,
            Message = string.Format(AppResources.Feedback_MyCarNotSupported_Message, _result.ConnectedCar.VIN)
        };

        args.AdditionalProperties = new Dictionary<string, string>
        {
            { "Car-Module-VIN", _result.ConnectedCar.VIN },
            { "Car-Module-Fingerprint", _result.ConnectedCar.Fingerprint }
        };

        if (_result.ConnectedCar.StockMap != null && await _filesService.Exists(_result.ConnectedCar.VIN, _result.ConnectedCar.Fingerprint, _result.ConnectedCar.StockMap))
        {
            args.AdditionalProperties.Add("Stock-File", "Present");
        }
        else
        {
            args.AdditionalProperties.Add("Stock-File", "Missing");
        }

        args.AdditionalProperties.Add("Changeset-Files", $"Count: [{_result.ConnectedCar.Changesets.Count}]");
        args.AdditionalProperties.Add("CustomCodeChangesets-Files", $"Count: [{_result.ConnectedCar.CustomCodeChangesets?.Count ?? 0}]");

        args.AdditionalProperties.Add("Car-Module-Master-Patched-Btld-Present", $"{_result.ConnectedCar.EcuMaster.IsActive}");
        args.AdditionalProperties.Add("Car-Module-Master-Ids", $"SGBDIndex [{_result.ConnectedCar.SgbdIndex}] / " +
                                                               $"Engine Code [{_result.ConnectedCar.EngineCode}] / " +
                                                               $"SerialNo [{_result.ConnectedCar.EcuMaster.SerialNo}] / " +
                                                               $"A2L [{_result.ConnectedCar.EcuMaster.A2Lstring}] / " +
                                                               $"Logistikbereich [{_result.ConnectedCar.EcuMaster.Logistikbereich}] / " +
                                                               $"CustomCodeVersionInstalled [{_result.ConnectedCar.EcuMaster.CustomCodeVersionInstalled}] / " +
                                                               $"I-Step [{_result.ConnectedCar.EcuMaster.IStep}] / " +
                                                               $"I-Step (BDC) [{_result.ConnectedCar.EcuMaster.BdcIStepList?.FirstOrDefault(x => x.Type.Equals(IntegrationStepType.Current))}] / " +
                                                               $"Region [{_result.ConnectedCar.EcuMaster.Region}]" +
                                                               $"CALID [{_result.ConnectedCar.EcuMaster.CALID}]" +
                                                               $"CVN [{_result.ConnectedCar.EcuMaster.CVN}]" +
                                                               $"Manufacturing Date [{_result.ConnectedCar.EcuMaster.ManufacturingDate}]" +
                                                               $"Boot Control Version [{_result.ConnectedCar.EcuMaster.BootctrlVersion}]" +
                                                               $"Security [{_result.ConnectedCar.EcuMaster.SecurityWave}]" +
                                                               $"UnlockCompany [{_result.ConnectedCar.EcuMaster.UnlockCompany}]"
        );
        args.AdditionalProperties.Add("Car-Module-Master-Current-Swe-List", string.Join("\r\n", _result.ConnectedCar.EcuMaster.SweVersions?.Select(x => x.GetXweString()).ToList() ?? Enumerable.Empty<string>()));

        var difVarianteListMasterSB = new StringBuilder();
        foreach (var difVariante in _result.ConnectedCar.EcuMaster.DifVariantes)
        {
            var line = Encoding.ASCII.GetString(difVariante.Data);
            difVarianteListMasterSB.AppendLine(line);
        }

        args.AdditionalProperties.Add("Dif-Variante-Master-List", string.Join("\r\n", difVarianteListMasterSB.ToString()));

        if (_result.ConnectedCar.EcuSlave != null)
        {
            args.AdditionalProperties.Add("Car-Has-Slave-ECU", "Yes");

            args.AdditionalProperties.Add("Car-Module-Slave-Patched-Btld-Present", $"{_result.ConnectedCar.EcuSlave.IsActive}");
            args.AdditionalProperties.Add("Car-Module-Slave-Ids", $"SerialNo [{_result.ConnectedCar.EcuSlave.SerialNo}] / " +
                                                                  $"A2L [{_result.ConnectedCar.EcuSlave.A2Lstring}] / " +
                                                                  $"Logistikbereich [{_result.ConnectedCar.EcuSlave.Logistikbereich}] / " +
                                                                  $"CustomCodeVersionInstalled [{_result.ConnectedCar.EcuSlave.CustomCodeVersionInstalled}] / " +
                                                                  $"IStep [{_result.ConnectedCar.EcuSlave.IStep}] / " +
                                                                  $"Region [{_result.ConnectedCar.EcuSlave.Region}]" +
                                                                  $"CALID [{_result.ConnectedCar.EcuSlave.CALID}]" +
                                                                  $"CVN [{_result.ConnectedCar.EcuSlave.CVN}]" +
                                                                  $"Manufacturing Date [{_result.ConnectedCar.EcuSlave.ManufacturingDate}]" +
                                                                  $"Boot Control Version [{_result.ConnectedCar.EcuMaster.BootctrlVersion}]" +
                                                                  $"Security [{_result.ConnectedCar.EcuSlave.SecurityWave}]" +
                                                                  $"UnlockCompany [{_result.ConnectedCar.EcuSlave.UnlockCompany}]"
            );
            args.AdditionalProperties.Add("Car-Module-Slave-Current-Swe-List", string.Join("\r\n", _result.ConnectedCar.EcuSlave.SweVersions?.Select(x => x.GetXweString()).ToList() ?? Enumerable.Empty<string>()));

            var difVarianteListSlaveSB = new StringBuilder();
            foreach (var difVariante in _result.ConnectedCar.EcuSlave.DifVariantes)
            {
                var line = Encoding.ASCII.GetString(difVariante.Data);
                difVarianteListSlaveSB.AppendLine(line);
            }

            args.AdditionalProperties.Add("Dif-Variante-Slave-List", string.Join("\r\n", difVarianteListSlaveSB.ToString()));
        }

        return args;
    }
}

public class EcuInformationValue
{
    public string Value { get; }

    public EcuInformationValue(string value)
    {
        Value = value;
    }
}