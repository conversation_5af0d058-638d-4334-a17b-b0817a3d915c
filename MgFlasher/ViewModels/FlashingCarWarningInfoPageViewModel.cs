﻿using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels;

public class FlashingCarWarningInfoPageViewModel : PageViewModel
{
    private readonly INavigationService _navigationService;

    private FlashingCarPageNavigationArgs _flashingCarPageNavigationArguments;

    public ObservableCollection<FlashingCarWarningInfoItem> FlashingCarWarningInfos { get; }
    public ICommand StartFlashingCommand { get; }

    public FlashingCarWarningInfoPageViewModel(INavigationService navigationService)
    {
        _navigationService = navigationService;
        StartFlashingCommand = new TraceableCommand(StartFlashing, nameof(StartFlashing));
        FlashingCarWarningInfos = new ObservableCollection<FlashingCarWarningInfoItem>();
    }

    private async void StartFlashing()
    {
        await _navigationService.NavigateAsync(PageType.FlashingCar, _flashingCarPageNavigationArguments);
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (arg is null)
        {
            return base.OnNavigatedToAsync(previousPage, arg);
        }

        _flashingCarPageNavigationArguments = (FlashingCarPageNavigationArgs)arg;
        FlashingCarWarningInfos.Clear();
        var i = 1;
        foreach (var warningInfoText in _flashingCarPageNavigationArguments.WarningInfos)
        {
            FlashingCarWarningInfos.Add(new FlashingCarWarningInfoItem
            {
                Index = i++,
                Text = warningInfoText
            });
        }

        return base.OnNavigatedToAsync(previousPage, arg);
    }
}

public class FlashingCarWarningInfoItem
{
    public int Index { get; set; }
    public string Text { get; set; }
}