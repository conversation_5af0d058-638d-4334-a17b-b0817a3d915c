﻿using MgFlasher.Flasher.Client.Common;
using MgFlasher.Flasher.Client.Shop.Contract;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Purchases;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Localization;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Services.Navigation;
using MgFlasher.Flasher.Client.Shop;
using MgFlasher.Flasher.Services.Cars.Sync.Links;
using System.Threading;
using MgFlasher.Flasher.Services.Cars.Sync.Chains;
using MgFlasher.Flasher.Services.Cars.FlashHistory;
using Plugin.Maui.Audio;
using MgFlasher.Services.Tutorials;

namespace MgFlasher.ViewModels;

public class UserPageViewModel : PageViewModel, IContextOptionsViewModel
{
    private ObservableCollection<string> _roles;
    private readonly List<string> _rolesToHide = ["Registered", "AutomaticBetaTester"];
    private readonly IDCANService _dCANService;
    private readonly IUserService _userService;
    private readonly INavigationService _navigationService;
    private readonly IPurchaseService _purchaseService;
    private readonly ICarRepository _carsService;
    private readonly IAppContext _appContext;
    private readonly IConnectivityStatusAccessor _connectivityStatusAccessor;
    private readonly IUserDialogs _userDialogs;
    private readonly IShopService _shopService;
    private readonly ILogger<UserPageViewModel> _logger;
    private string _email;
    private Dictionary<string, Action> _contextOptions;
    private bool _canGoToMyCarsPage;

    private ObservableCollection<RedeemableProductItemViewModel> _redeemableProductItems;

    public ObservableCollection<string> Roles
    {
        get => _roles;
        private set
        {
            SetProperty(ref _roles, value);
            OnPropertyChanged(nameof(CanJoinBetaTesting));
        }
    }

    public string Email
    {
        get => _email;
        private set => SetProperty(ref _email, value);
    }

    public ObservableCollection<RedeemableProductItemViewModel> RedeemableProductItems
    {
        get => _redeemableProductItems;
        set
        {
            SetProperty(ref _redeemableProductItems, value);
            OnPropertyChanged(nameof(HasRedeemableProductItems));
        }
    }

    public bool HasRedeemableProductItems => RedeemableProductItems?.Count > 0;

    public bool CanJoinBetaTesting => !Roles.Contains("CustomCodeBeta") && !_appContext.IsEcuTestUser();

    public Dictionary<string, Action> ContextOptions
    {
        get => _contextOptions;
        private set => SetProperty(ref _contextOptions, value);
    }

    public bool CanGoToMyCarsPage
    {
        get => _canGoToMyCarsPage;
        private set => SetProperty(ref _canGoToMyCarsPage, value);
    }

    public ICommand GoToSyncPageCommand { get; }
    public ICommand GoToMyCarsPageCommand { get; }
    public ICommand LogoutCommand { get; }
    public ICommand DeleteCommand { get; }
    public ICommand RegisterToBetaTestingCommand { get; }
    public ICommand ResetPopupTutorialsCommand { get; }

    public UserPageViewModel(
        IDCANService dCANService,
        IUserService userService,
        INavigationService navigationService,
        IPurchaseService purchaseService,
        ICarRepository carsService,
        IAppContext appContext,
        IConnectivityStatusAccessor connectivityStatusAccessor,
        IUserDialogs userDialogs,
        ILogger<UserPageViewModel> logger,
        IShopService shopService)
    {
        _dCANService = dCANService;
        _userService = userService;
        _navigationService = navigationService;
        _purchaseService = purchaseService;
        _carsService = carsService;
        _appContext = appContext;
        _connectivityStatusAccessor = connectivityStatusAccessor;
        _userDialogs = userDialogs;
        _logger = logger;
        _shopService = shopService;

        Email = "-";
        RedeemableProductItems = new ObservableCollection<RedeemableProductItemViewModel>();
        Roles = new ObservableCollection<string>();

        GoToMyCarsPageCommand = new TraceableCommand(MyCars, _ => CanGoToMyCarsPage, nameof(GoToMyCarsPageCommand));
        GoToSyncPageCommand = new TraceableCommand(SyncPage, nameof(GoToSyncPageCommand));
        LogoutCommand = new TraceableCommand(Logout, nameof(LogoutCommand));
        DeleteCommand = new TraceableCommand(Delete, nameof(DeleteCommand));
        RegisterToBetaTestingCommand = new TraceableCommand(RegisterToBetaTesting, nameof(RegisterToBetaTestingCommand));
        ResetPopupTutorialsCommand = new TraceableCommand(ResetPopupTutorials, nameof(ResetPopupTutorialsCommand));

        InitContextOptions();
    }

    private void InitContextOptions()
    {
        var options = new Dictionary<string, Action>();

        if (!string.IsNullOrEmpty(_dCANService.ConnectedCarFingerprint))
        {
            options[AppResources.UserPage_Misc_SyncPageButtonText] = SyncPage;
        }

        options[AppResources.UserPage_Misc_Logout] = Logout;
        options[AppResources.UserPage_Misc_Delete] = Delete;

        ContextOptions = options;
    }

    private async void SyncPage()
    {
        await _navigationService.NavigateAsync(PageType.SyncPage);
    }

    private async void Logout()
    {
        if (!await _connectivityStatusAccessor.IsConnected())
        {
            await _userDialogs.AlertAsync(message: AppResources.InternetConnectionRequired, okText: AppResources.Ok);

            return;
        }

        await _userService.SignOut();

        await _navigationService.NavigateAsync(PageType.Login, new LoginPageNavigationArgs()
        {
            NextPage = PageType.User
        });
    }

    private async void Delete()
    {
        _logger.LogInformation("User account delete click");

        if (!await _userDialogs.ConfirmAsync(
            AppResources.User_Delete_Confirmation,
            okText: AppResources.Ok,
            cancelText: AppResources.Cancel))
        {
            return;
        }

        if (!await _connectivityStatusAccessor.IsConnected())
        {
            await _userDialogs.AlertAsync(message: AppResources.InternetConnectionRequired, okText: AppResources.Ok);
            return;
        }

        try
        {
            IsBusy = true;

            await _userService.Delete();

            await _navigationService.NavigateAsync(PageType.Login, new LoginPageNavigationArgs()
            {
                NextPage = PageType.User
            });
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async void RegisterToBetaTesting()
    {
        if (!await _userDialogs.ConfirmAsync(
           AppResources.BetaTesting_PromptQuestion,
           AppResources.BetaTesting_PromptTitle,
           AppResources.Yes,
           AppResources.No))
        {
            return;
        }

        bool result;
        string message;
        using (var loading = _userDialogs.Loading(AppResources.Loading))
        {
            (result, message) = await _userService.ApplyForBetaTesting();
        }

        if (!result)
        {
            await _userDialogs.AlertAsync(message);
        }
        else
        {
            await _userDialogs.AlertAsync(message);
        }
    }

    private async void MyCars(object obj)
    {
        await _navigationService.NavigateAsync(PageType.MyCars);
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        var user = await _userService.GetCurrentUser();
        await SetUserData(user);

        CanGoToMyCarsPage = !string.IsNullOrEmpty(_dCANService.ConnectedCarFingerprint);
        InitContextOptions();
        await base.OnNavigatedToAsync(previousPage, arg);
    }

    private async Task SetUserData(User user)
    {
        var vms = await CreateViewModels(user.AvailablePurchases);
        Email = _userService.GetEmail(user);
        RedeemableProductItems = new ObservableCollection<RedeemableProductItemViewModel>(vms);
        Roles = new ObservableCollection<string>(user.Roles?.Where(r => !_rolesToHide.Contains(r)) ?? new List<string>());
    }

    private async Task<IEnumerable<RedeemableProductItemViewModel>> CreateViewModels(List<RedeemableOrderItemsDto> redeemablePurchases)
    {
        var cars = await _carsService.GetAll();
        var carPurchases = new Dictionary<string, IEnumerable<InAppPurchase>>();

        foreach (var car in cars)
        {
            var purchases = await _purchaseService.GetPurchases(car.Fingerprint);
            carPurchases[car.VIN] = purchases;
        }

        redeemablePurchases ??= new List<RedeemableOrderItemsDto>();

        return redeemablePurchases
            .Where(rp => string.IsNullOrEmpty(rp.VIN) || !carPurchases[rp.VIN].Any(cp => cp.ProductId == rp.ProductSKU))
            .Select(rp =>
            (
                ProductTypeText: GetProductTypeText(rp.ProductName, rp.ProductSKU, rp.VIN),
                Quantity: string.IsNullOrEmpty(rp.VIN) ? rp.OrderItemReferences.Select(oir => oir.Quantity).Sum() : 1
            ))
            .Select(pt => new RedeemableProductItemViewModel(pt.ProductTypeText, pt.Quantity))
            .Where(vm => vm.Quantity > 0);
    }

    private string GetProductTypeText(string productName, string productId, string vin)
    {
        var sb = new StringBuilder();

        if (productName != null)
        {
            sb.Append(TranslateHelper.TryGetString(productName));
        }
        else
        {
            var text = productId switch
            {
                _ when productId.Contains("all") => AppResources.license_all_001,
                _ when productId.Contains("flash") => AppResources.license_flash_001,
                _ when productId.Contains("stage1") => AppResources.license_stage1_001,
                _ when productId.Contains("stage2") => AppResources.license_stage2_001,
                _ when productId.Contains("logger") => AppResources.license_logger_001,
                _ => "Product"
            };

            sb.Append(text + $" ({productId})");
        }

        if (!string.IsNullOrEmpty(vin))
        {
            sb.Append($" ({vin})");
        }

        return sb.ToString();
    }

    private string GetEmail(User user)
    {
        var email = user.Context.Credentials.Email;

        if (_appContext.RetailMode == RetailModeEnum.China)
        {
            return email.Split("@").FirstOrDefault() ?? email;
        }

        return email;
    }

    private async void ResetPopupTutorials()
    {
        PopupTutorialsService.ResetAllPopupTutorials();
        await _userDialogs.AlertAsync(AppResources.UserPage_ResetPopupTutorials_Success);
    }
}