﻿using CommunityToolkit.Mvvm.Messaging;
using MgFlasher.CustomCode;
using MgFlasher.CustomCode.Services;
using MgFlasher.CustomCode.Storage.Changesets;
using MgFlasher.Ecu.Common.Extensions;
using MgFlasher.Files;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.Cars.Sync;
using MgFlasher.Flasher.Services.Cars.Sync.Chains;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Flasher.Services.User.Feedback;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services.Navigation;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MessagingCenterIdentifiers = MgFlasher.Helpers.MessagingCenterIdentifiers;

namespace MgFlasher.ViewModels;

public class CustomMapFilesPageViewModel : BaseOptionsPageViewModel
{
    private readonly INavigationService _navigationService;
    private readonly ICarDirectoryPathFactory _directoryPathFactory;
    private readonly IReferenceFileVersionExtractor _referenceFileVersionExtractor;
    private readonly IDCANService _dCANService;
    private readonly ICarMapPicker _carMapPicker;
    private readonly IShareService _shareService;
    private readonly IFileDescriptorRepository _fileDescriptorRepository;
    private readonly IFileDecryptor _fileDecryptor;
    private readonly IUserDialogs _userDialogs;
    private readonly ICarCompatabilityService _carCompatabilityService;
    private readonly ISyncChainExecutor _syncChain;
    private readonly CustomBaseMapSyncChainFactory _customBaseMapSyncChainFactory;
    private readonly OnlyMapsSyncChainFactory _onlyMapsSyncChainFactory;
    private readonly FullSyncChainFactory _fullSyncChainFactory;
    private readonly ILogger<CustomMapFilesPageViewModel> _logger;
    private readonly ILogger<MapFileItemViewModel> _itemLogger;
    private CustomMapFilesPageNavigationArgs _args;
    private Car _connectedCar;

    public ICommand MiscCommand { get; }
    public ICommand UploadCommand { get; }

    public CustomMapFilesPageViewModel(INavigationService navigationService,
        ICarDirectoryPathFactory directoryPathFactory,
        IReferenceFileVersionExtractor referenceFileVersionExtractor,
        IDCANService dCANService,
        ICarMapPicker carMapPicker,
        IShareService shareService,
        IFileDescriptorRepository fileDescriptorRepository,
        IFileDecryptor fileDecryptor,
        IUserDialogs userDialogs,
        ICarCompatabilityService carCompatabilityService,
        ILoggerFactory loggerFactory,
        ISyncChainExecutor syncChain,
        CustomBaseMapSyncChainFactory customBaseMapSyncChainFactory,
        OnlyMapsSyncChainFactory onlyMapsSyncChainFactory,
        FullSyncChainFactory fullSyncChainFactory) : base(navigationService)
    {
        MiscCommand = new TraceableCommand(MiscMenu, nameof(MiscMenu));
        UploadCommand = new TraceableCommand(UploadMap, nameof(UploadMap));
        _navigationService = navigationService;
        _directoryPathFactory = directoryPathFactory;
        _referenceFileVersionExtractor = referenceFileVersionExtractor;
        _dCANService = dCANService;
        _carMapPicker = carMapPicker;
        _shareService = shareService;
        _fileDescriptorRepository = fileDescriptorRepository;
        _fileDecryptor = fileDecryptor;
        _userDialogs = userDialogs;
        _carCompatabilityService = carCompatabilityService;
        _syncChain = syncChain;
        _customBaseMapSyncChainFactory = customBaseMapSyncChainFactory;
        _onlyMapsSyncChainFactory = onlyMapsSyncChainFactory;
        _fullSyncChainFactory = fullSyncChainFactory;
        _logger = loggerFactory.CreateLogger<CustomMapFilesPageViewModel>();
        _itemLogger = loggerFactory.CreateLogger<MapFileItemViewModel>();
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _args = (arg as CustomMapFilesPageNavigationArgs) ?? _args;

        _connectedCar = await _dCANService.GetConnectedCar();

        await SetOptionsAsync();
        await RemoveOldFilesAsync();
    }

    protected override async void Submit(IEnumerable<OptionalItemViewModel> selectedMapFiles, INavigationService navigationService)
    {
        if (selectedMapFiles is null || !selectedMapFiles.Any())
        {
            await _userDialogs.AlertAsync(AppResources.CustomMap_PleaseChooseMap, okText: AppResources.Ok);
            return;
        }

        if (selectedMapFiles.Count() > 1)
        {
            await _userDialogs.AlertAsync(AppResources.CustomMap_PleaseSelectSingleMap, okText: AppResources.Ok);
            return;
        }

        var selectedMapFileVm = selectedMapFiles.OfType<MapFileItemViewModel>().FirstOrDefault();
        _logger.LogInformation("user has chosen custom map file: {filename}", selectedMapFileVm.FileDescriptor.Filename);
        var customMapFilePath = selectedMapFileVm.FileDescriptor.GetFilePath(_connectedCar.VIN, _directoryPathFactory);
        if (!File.Exists(customMapFilePath))
        {
            _logger.LogInformation("Custom Map file not found: {filename}", selectedMapFileVm.FileDescriptor.Filename);
            await _userDialogs.AlertAsync(AppResources.CustomMapFilesPage_FileNotFound, okText: AppResources.Ok);
            return;
        }

        var customCodeVersions = await _carCompatabilityService.GetCustomCodeOverviewByMapContent(_connectedCar, selectedMapFileVm.FileDescriptor);

        if (_connectedCar.AnyEcuReqiresWave3Unlock && HasMissingCustomCode(customCodeVersions))
        {
            await NavigateToFeedbackDueToMissingCustomCodeVersion(AppResources.Flash_Discarded_DueToMissingCustomCode, _connectedCar.EcuMaster.CustomCodeVersionInstalled);
            return;
        }

        _args.FlasherCommand.FileDescriptor = selectedMapFileVm.FileDescriptor;

        if (customCodeVersions.Count == 0)
        {
            await _userDialogs.AlertAsync(string.Format(AppResources.Flash_Warning_MissingCustomCodeVersions, FlashAllowanceProductType.CustomMap.GetLocalizedString()), okText: AppResources.Ok);
            _args.FlasherCommand.TargetCustomCodeVersionString = null;
            await navigationService.NavigateAsync(PageType.FlashingCarWarningInfo, new FlashingCarPageNavigationArgs()
            {
                ContinousCommand = _args.FlasherCommand,
                WarningInfos = FlashingCarWarningInfo.GetWarningInfos(StageType.Custom)
            });
            return;
        }

        if (selectedMapFileVm.FileDescriptor.TryExtractCustomBaseMapInfo(out var ccVersion))
        {
            _args.FlasherCommand.TargetCustomCodeVersionString = ccVersion;
            await _navigationService.NavigateAsync(PageType.FlashingCarOptions, _args.FlasherCommand);
            return;
        }

        var args = new CustomCodeVersionsPageNavigationArgs(customCodeVersions, StageType.Custom, _args.FlasherCommand);
        await navigationService.NavigateAsync(PageType.CustomCodeVersions, args);
    }

    private async void UploadMap()
    {
        try
        {
            FileDescriptor descriptor;
            bool shouldSync;
            using (var loading = _userDialogs.Loading(AppResources.Loading))
            {
                (descriptor, shouldSync) = await _carMapPicker.Pick(_connectedCar).ConfigureAwait(false);
            }

            if (descriptor?.Id is null)
            {
                return;
            }

            if (shouldSync)
            {
                Application.Current.Dispatcher.Dispatch(async () =>
                {
                    await _userDialogs.AlertAsync(message: AppResources.CustomMapFilesPage_SyncAfterUpload, okText: AppResources.Ok);
                    await _navigationService.NavigateAsync(PageType.SyncPage);
                });
            }
            else
            {
                var vm = CreateViewModel(descriptor);
                vm.IsChecked = true;
                OptionalItemViewModels.Insert(0, vm);
            }
        }
        catch (MgFlasherException ex)
        {
            _logger.LogInformation(ex, "File is invalid");
            await _userDialogs.AlertAsync(message: ex.Message, okText: AppResources.Ok);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error on map file pick");
            await _userDialogs.AlertAsync(message: AppResources.CustomMapFilesPage_UploadError, okText: AppResources.Ok);
        }
    }

    private async void Delete()
    {
        var vms = OptionalItemViewModels
            .OfType<MapFileItemViewModel>()
            .Where(vm => vm.IsChecked)
            .ToList();

        if (vms.Count == 0)
        {
            await _userDialogs.AlertAsync(AppResources.SelectAtLeastOneFile, okText: AppResources.Ok);
            return;
        }

        var message = string.Format(AppResources.ConfirmFileDelete, vms.Count);
        if (!await _userDialogs.ConfirmAsync(message, okText: AppResources.Yes, cancelText: AppResources.No))
        {
            await _userDialogs.AlertAsync(AppResources.DeletionCancelled, okText: AppResources.Ok);
            return;
        }

        foreach (var vm in vms)
        {
            OptionalItemViewModels.Remove(vm);
            await _fileDescriptorRepository.DeleteDescriptorAsync(_connectedCar.Fingerprint, vm.FileDescriptor.Id,
                vm.FileDescriptor.Type, _connectedCar.VIN);
        }

        await _userDialogs.AlertAsync(AppResources.DeletionFileCompleted, okText: AppResources.Ok);
    }

    private async void MiscMenu(object obj)
    {
        var options = new Dictionary<string, Action>
        {
            { AppResources.CustomMapFilesPage_Misc_SyncMapsOnly, () => _navigationService.NavigateAsync(PageType.SyncPage, new SyncPageNavigationArgs(_onlyMapsSyncChainFactory)) },
            { AppResources.CustomMapFilesPage_Misc_SyncAllData, () => _navigationService.NavigateAsync(PageType.SyncPage, new SyncPageNavigationArgs(_fullSyncChainFactory)) },
            { AppResources.CustomMapFilesPage_Misc_GenerateCustomBaseMap, GenerateCustomBaseMap },
            { AppResources.Misc_Share, Share },
            { AppResources.Misc_MarkAll, MarkAll },
            { AppResources.Misc_Delete, Delete }
        };

        var result = await _userDialogs.ActionSheetAsync(AppResources.Misc_Title, AppResources.Cancel, null, null, options.Keys.ToArray());
        if (options.ContainsKey(result))
        {
            options[result]();
        }
    }

    private async void GenerateCustomBaseMap()
    {
        var overview = await _carCompatabilityService.GetCustomCodeOverviewByStockMap(_connectedCar);
        var buttons = overview
            .Where(x => x.CanSelect)
            .Where(x => x.Version.ToVersion() >= new Version(7, 0, 5)) // api limitations
            .Select(x => x.Version.Version)
            .Distinct().ToArray();
        if (buttons.Length == 0)
        {
            await _userDialogs.AlertAsync(AppResources.CustomMap_GenerateCustomBaseMap_NoCustomCodeVersionAvailable);
            return;
        }

        var customCodeVersion = await _userDialogs.ActionSheetAsync(
            AppResources.CustomMap_GenerateCustomBaseMap_PleaseSelectCustomCodeVersion, AppResources.Cancel, null,
            null, buttons);

        if (string.IsNullOrEmpty(customCodeVersion))
        {
            return;
        }

        await _navigationService.NavigateAsync(PageType.Pending, PendingPageNavigationArgs.InProgress(PageType.CustomMapFiles));

        try
        {
            IsBusy = true;
            _syncChain.ProgressChanged += OnSyncChainProgressChanged;
            var result = await _syncChain.SyncAsync(_customBaseMapSyncChainFactory, new() { RequestedCustomCodeBaseMapVersion = customCodeVersion });
            if (!result.Success)
            {
                _logger.LogWarning("Custom base map generation failed Failed with msg: {msg}", result.Error);
                await _userDialogs.AlertAsync(result.Error);
                return;
            }

            await SetOptionsAsync();
            var share = await _userDialogs.ConfirmAsync(string.Format(AppResources.CustomMap_GenerateCustomBaseMap_Success, customCodeVersion));
            if (share)
            {
                await ShareCustomBaseMapAsync(customCodeVersion);
            }
        }
        finally
        {
            IsBusy = false;
            _syncChain.ProgressChanged -= OnSyncChainProgressChanged;

            WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageCloseNavigation));
        }

        void OnSyncChainProgressChanged(object sender, SyncChainProgressEventArgs args)
        {
            WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageUpdateText, new PendingPageUpdateArgs(args.CurrentLinkTitle, args.Progress)));
        }
    }

    private void MarkAll()
    {
        MarkAllCommand.Execute(null);
    }

    private async void Share()
    {
        var vms = OptionalItemViewModels
            .OfType<MapFileItemViewModel>()
            .Where(vm => vm.IsChecked)
            .ToList();

        if (vms.Count == 0)
        {
            await _userDialogs.AlertAsync(AppResources.SelectAtLeastOneFile, okText: AppResources.Ok);
            return;
        }

        await _shareService.Share(_connectedCar, vms.Select(x => x.FileDescriptor).ToArray());
    }

    private async Task SetOptionsAsync()
    {
        var visibleFiles = await GetVisibleFileDescriptorListAsync();
        var vms = visibleFiles.Select(CreateViewModel).ToList();
        OptionalItemViewModels = new ObservableCollection<OptionalItemViewModel>(vms);
    }

    private async Task<List<FileDescriptor>> GetVisibleFileDescriptorListAsync()
    {
        var fileDescriptors = await _fileDescriptorRepository.GetDescriptors(_connectedCar.Fingerprint);

        return fileDescriptors
            .Where(x => x.Type.In(FileType.STOCK, FileType.USER_CUSTOM))
            .Where(x => !x.Type.In(FileType.STOCK) || _connectedCar.StockMap?.Id is null || x.Id == _connectedCar.StockMap?.Id)
            .OrderByDescending(x => x.Type == FileType.STOCK ? int.MinValue : x.Id)
            .ToList();
    }

    private async Task RemoveOldFilesAsync()
    {
        try
        {
            var mapDirectory = _directoryPathFactory.GetCarMapsPath(_connectedCar.VIN);
            var mapsStoredOnDevice = Directory.EnumerateFiles(mapDirectory, "*", SearchOption.AllDirectories).ToList();

            var visibleFiles = await GetVisibleFileDescriptorListAsync();
            var stockmapList = visibleFiles
                .Where(x => x.Type == FileType.STOCK)
                .ToList();

            var filesToDelete = new List<FileInfo>();
            foreach (var map in mapsStoredOnDevice)
            {
                var fileInfo = new FileInfo(map);
                if (!OptionalItemViewModels.Any(x => fileInfo.Name.Equals(x.Option) || stockmapList.Select(sm => sm.ToDisplayName(_connectedCar)).Any(smf => smf.Equals(x.Option))))
                {
                    filesToDelete.Add(fileInfo);
                }
            }

            _logger.LogWarning($"Files marked for deletion:\n\t{string.Join("\n\t", filesToDelete.Select(x => x.Name))}");
            foreach (var fileToDelete in filesToDelete)
            {
                if (File.Exists(fileToDelete.FullName))
                {
                    File.Delete(fileToDelete.FullName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
        }
    }

    private bool HasMissingCustomCode(List<CustomCodeOverview> sortedList)
    {
        var installedCc = CustomCodeId.FromString(_connectedCar.EcuMaster.CustomCodeVersionInstalled);
        return !sortedList.Any(x => x.Version.ToVersion().Equals(installedCc.ToVersion()));
    }

    private MapFileItemViewModel CreateViewModel(FileDescriptor mapFile)
    {
        var child = new MapFileItemViewModel(mapFile, _shareService, _directoryPathFactory, _referenceFileVersionExtractor, _userDialogs, _fileDecryptor, _connectedCar, _itemLogger, _fileDescriptorRepository);
        child.DeleteActionOccurred += Child_DeleteActionOccurred;
        return child;
    }

    private void Child_DeleteActionOccurred(object vm, EventArgs e)
    {
        _logger.LogInformation("Callback received in ParentClass.");
        if (vm is MapFileItemViewModel)
        {
            OptionalItemViewModels.Remove(vm as MapFileItemViewModel);
        }
    }

    private async Task<bool> NavigateToFeedbackDueToMissingCustomCodeVersion(string msgResource, string missingCustomCodeVersion)
    {
        var message = string.Format(msgResource, missingCustomCodeVersion);
        var feedbackConfirmation = await _userDialogs.ConfirmAsync(
            message: message,
            okText: AppResources.CustomFlash_WithMissingCustomCodeVersions_NotifyUs);

        if (feedbackConfirmation)
        {
            var feedbackMsg = CreateCustomCodeMissingFeedbackNavigationArgs(missingCustomCodeVersion);
            await _navigationService.NavigateAsync(PageType.Feedback, feedbackMsg);
            return true;
        }

        return false;
    }

    private async Task ShareCustomBaseMapAsync(string customCodeVersion)
    {
        var item = OptionalItemViewModels.OfType<MapFileItemViewModel>()
            .FirstOrDefault(x => x.FileDescriptor.TryExtractCustomBaseMapInfo(out var ccVer) && ccVer == customCodeVersion);
        if (item is null)
        {
            return;
        }

        await _shareService.Share(_connectedCar, item.FileDescriptor);
    }

    private FeedbackPageNavigationArgs CreateCustomCodeMissingFeedbackNavigationArgs(string missingCustomCodeVersion) => new FeedbackPageNavigationArgs
    {
        SystemSubject = SystemFeedbackSubject.CustomCodeSupportRequest,
        Subject = "Custom Code is missing",
        AdditionalProperties = new Dictionary<string, string>()
        {
            ["MissingCustomCode"] = missingCustomCodeVersion,
        },
        Message = $"My Wave3 unlocked ECU cannot be flashed due to a missing Custom Code version. Please prepare {missingCustomCodeVersion} for me.",
    };
}