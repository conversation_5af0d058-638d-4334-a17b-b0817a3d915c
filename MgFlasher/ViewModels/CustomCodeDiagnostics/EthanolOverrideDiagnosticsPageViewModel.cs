﻿using MgFlasher.CustomCode;
using MgFlasher.Flasher.Commands;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System;
using MgFlasher.Flasher.Commands.Diagnostics.Custom_Code.Interfaces;
using MgFlasher.Flasher.Services.User.Dialogs;

namespace MgFlasher.ViewModels.CustomCodeDiagnostics;

public class EthanolOverrideDiagnosticsPageViewModel : PageViewModel
{
    private readonly ICustomCodeDiagnosticServices _customCodeDiagnosticServices;
    private readonly ILogger<EthanolOverrideDiagnosticsPageViewModel> _logger;
    private readonly IDCANService _dCANService;
    private readonly IUserDialogs _userDialogs;
    private bool _hasBarBackButton;
    private ObservableCollection<DiagnosticCommandItemModel> _diagnosticCommands;

    public ObservableCollection<DiagnosticCommandItemModel> DiagnosticCommands
    {
        get => _diagnosticCommands;
        private set => SetProperty(ref _diagnosticCommands, value);
    }

    public bool HasBarBackButton
    {
        get => _hasBarBackButton;
        private set => SetProperty(ref _hasBarBackButton, value);
    }

    public EthanolOverrideDiagnosticsPageViewModel(
        IDCANService dCANService,
        IUserDialogs userDialogs,
        ICustomCodeDiagnosticServices customCodeDiagnosticServices,
        ILogger<EthanolOverrideDiagnosticsPageViewModel> logger)
    {
        _dCANService = dCANService;
        _userDialogs = userDialogs;
        _customCodeDiagnosticServices = customCodeDiagnosticServices;
        _logger = logger;
        DiagnosticCommands = new ObservableCollection<DiagnosticCommandItemModel>();
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        var connectedCar = await _dCANService.GetConnectedCar();
        HasBarBackButton = !string.IsNullOrEmpty(connectedCar?.Fingerprint) && (connectedCar.Supported ?? false);
        await _customCodeDiagnosticServices.UpdateAllLocatorsAsync(connectedCar, showWarnings: true);
        InitCommandItems(connectedCar);
    }

    private void InitCommandItems(Car connectedCar)
    {
        DiagnosticCommands = new ObservableCollection<DiagnosticCommandItemModel>();

        if (CustomCodeVersions.IsAtLeastCustomCodev71(connectedCar.EcuMaster.CustomCodeVersionInstalled))
        {
            foreach (var command in GetCustomCodev71Commands())
                DiagnosticCommands.Add(command);
        }
        _logger.LogInformation($"Available options: {JsonConvert.SerializeObject(DiagnosticCommands.Select(x => x.CommandName))}");
    }

    private async void ReadStatusCommandAsync()
    {
        await this.ExecuteExclusivelyAsync(async () =>
        {
            var result = await _customCodeDiagnosticServices.ReadFlexFuelEthanolContentOverrideFlagAsync();
            await _userDialogs.AlertAsync(result);
        });
    }

    private async void ReadValueCommandAsync()
    {
        await this.ExecuteExclusivelyAsync(async () =>
        {
            var result = await _customCodeDiagnosticServices.ReadFlexFuelEthanolContentOverrideValueAsync();
            await _userDialogs.AlertAsync(result);
        });
    }

    private async Task SetStatusCommandAsync(byte value)
    {
        await this.ExecuteExclusivelyAsync(async () =>
        {
            var result = await _customCodeDiagnosticServices.SetFlexFuelEthanolContentOverrideFlagAsync(value);
            await _userDialogs.AlertAsync(result); await _userDialogs.AlertAsync(result);
        });
    }

    private async Task SetValueCommandAsync(byte value)
    {
        await this.ExecuteExclusivelyAsync(async () =>
        {
            var result = await _customCodeDiagnosticServices.SetFlexFuelEthanolContentOverrideValueAsync(value);
            await _userDialogs.AlertAsync(result);
        });
    }

    private IEnumerable<DiagnosticCommandItemModel> GetCustomCodev71Commands()
    {
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Read_Action,
            Command = new TraceableCommand(ReadStatusCommandAsync, nameof(ReadStatusCommandAsync)),
            CommandName = AppResources.Diagnostics_CustomCodeReadActiveStatus_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Read_Action,
            Command = new TraceableCommand(ReadValueCommandAsync, nameof(ReadValueCommandAsync)),
            CommandName = AppResources.Diagnostics_CustomCodeReadActiveValue_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Select_Action,
            Command = new TraceableCommand(EthanolOverrideEnable, nameof(EthanolOverrideEnable)),
            CommandName = AppResources.Diagnostics_CustomCodeEnable_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Select_Action,
            Command = new TraceableCommand(EthanolOverrideDisable, nameof(EthanolOverrideDisable)),
            CommandName = AppResources.Diagnostics_CustomCodeDisable_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Select_Action,
            Command = new TraceableCommand(EthanolOverrideValueConfigure, nameof(EthanolOverrideValueConfigure)),
            CommandName = AppResources.Diagnostics_CustomCodeConfigure_Label,
            Icon = "menudiagnosticssmall.png"
        };
    }

    private async void EthanolOverrideEnable()
    {
        await SetStatusCommandAsync(0x01);
    }

    private async void EthanolOverrideDisable()
    {
        await SetStatusCommandAsync(0x00);
    }

    private async void EthanolOverrideValueConfigure()
    {
        if (IsBusy)
            return;
        var min = 0;
        var max = 100;
        var step = 5;
        var options = Enumerable.Range(min, max - min + 1).Where(i => (i - min) % step == 0).Select(x => $"{x}%").ToArray();

        var userSelection = await _userDialogs.ActionSheetAsync(AppResources.CustomCodeDiagnostics_SelectEthanolContentPercentage, AppResources.Cancel, null, null, options);

        if (!byte.TryParse(userSelection.Replace("%", ""), out byte ethanolContent))
        {
            await _userDialogs.AlertAsync(AppResources.CustomCodeDiagnostics_FailedToParseUserInput, AppResources.Error, okText: AppResources.Ok);
            return;
        }

        await SetValueCommandAsync(ethanolContent);
    }
}