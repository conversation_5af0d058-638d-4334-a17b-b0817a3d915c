﻿using System;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels.CustomCodeDiagnostics;

public static class PageViewModelExtensions
{
    public static async Task ExecuteExclusivelyAsync(this PageViewModel vm, Func<Task> func)
    {
        if (vm.IsBusy)
            return;

        try
        {
            vm.IsBusy = true;
            await func();
        }
        finally
        {
            vm.IsBusy = false;
        }
    }
}