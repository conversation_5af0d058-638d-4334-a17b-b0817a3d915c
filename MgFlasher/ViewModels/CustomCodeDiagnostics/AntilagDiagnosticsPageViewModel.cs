﻿using MgFlasher.CustomCode;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.Flasher.Commands.Diagnostics.Custom_Code.Interfaces;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;

namespace MgFlasher.ViewModels.CustomCodeDiagnostics;

public class AntilagDiagnosticsPageViewModel : PageViewModel
{
    private readonly ICustomCodeDiagnosticServices _customCodeDiagnosticServices;
    private readonly ILogger<AntilagDiagnosticsPageViewModel> _logger;
    private readonly IDCANService _dCANService;
    private readonly IUserDialogs _userDialogs;
    private bool _hasBarBackButton;
    private ObservableCollection<DiagnosticCommandItemModel> _diagnosticCommands;

    public ObservableCollection<DiagnosticCommandItemModel> DiagnosticCommands
    {
        get => _diagnosticCommands;
        private set => SetProperty(ref _diagnosticCommands, value);
    }

    public bool HasBarBackButton
    {
        get => _hasBarBackButton;
        private set => SetProperty(ref _hasBarBackButton, value);
    }

    public AntilagDiagnosticsPageViewModel(
        IDCANService dCANService,
        IUserDialogs userDialogs,
        ICustomCodeDiagnosticServices customCodeDiagnosticServices,
        ILogger<AntilagDiagnosticsPageViewModel> logger)
    {
        _dCANService = dCANService;
        _userDialogs = userDialogs;
        _customCodeDiagnosticServices = customCodeDiagnosticServices;
        _logger = logger;
        DiagnosticCommands = new ObservableCollection<DiagnosticCommandItemModel>();
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        var connectedCar = await _dCANService.GetConnectedCar();
        HasBarBackButton = !string.IsNullOrEmpty(connectedCar?.Fingerprint) && (connectedCar.Supported ?? false);
        await _customCodeDiagnosticServices.UpdateAllLocatorsAsync(connectedCar, showWarnings: true);
        InitCommandItems(connectedCar);
    }

    private void InitCommandItems(Car connectedCar)
    {
        DiagnosticCommands = new ObservableCollection<DiagnosticCommandItemModel>();

        if (CustomCodeVersions.IsAtLeastCustomCodev6x(connectedCar.EcuMaster.CustomCodeVersionInstalled))
        {
            foreach (var command in GetCustomCodev6xCommands())
            {
                DiagnosticCommands.Add(command);
            }
        }

        _logger.LogInformation($"Available options: {JsonConvert.SerializeObject(DiagnosticCommands.Select(x => x.CommandName))}");
    }

    private async void ReadCommandAsync()
    {
        await this.ExecuteExclusivelyAsync(async () =>
        {
            var result = await _customCodeDiagnosticServices.ReadAntilagStandingRpmSetpointAsync();
            await _userDialogs.AlertAsync(result);
        });
    }

    private async Task SetCommandAsync(byte value)
    {
        await this.ExecuteExclusivelyAsync(async () =>
        {
            var result = await _customCodeDiagnosticServices.SetAntilagStandingRpmSetpointAsync(value);
            await _userDialogs.AlertAsync(result);
        });
    }

    private async void AntilagConfigureStandingRpmSetpoint()
    {
        if (IsBusy)
        {
            return;
        }

        var min = 1500;
        var max = 6000;
        var step = 100;
        var options = Enumerable.Range(min, max - min + 1).Where(i => (i - min) % step == 0).Select(x => $"{x} RPM").ToArray();

        var userSelection = await _userDialogs.ActionSheetAsync(AppResources.CustomCodeDiagnostics_SelectAntilagStandingRpmSetpoint, AppResources.Cancel, null, null, options);

        if (string.IsNullOrEmpty(userSelection))
        {
            return;
        }

        if (!uint.TryParse(userSelection.Replace(" RPM", ""), out uint setpoint))
        {
            await _userDialogs.AlertAsync(AppResources.CustomCodeDiagnostics_FailedToParseUserInput, AppResources.Error, okText: AppResources.Ok);
            return;
        }

        await SetCommandAsync((byte)(setpoint / 100));
    }

    private IEnumerable<DiagnosticCommandItemModel> GetCustomCodev6xCommands()
    {
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Read_Action,
            Command = new TraceableCommand(ReadCommandAsync, nameof(ReadCommandAsync)),
            CommandName = AppResources.Diagnostics_CustomCodeReadActiveValue_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Select_Action,
            Command = new TraceableCommand(AntilagConfigureStandingRpmSetpoint, nameof(AntilagConfigureStandingRpmSetpoint)),
            CommandName = AppResources.Diagnostics_CustomCodeConfigure_Label,
            Icon = "menudiagnosticssmall.png"
        };
    }
}