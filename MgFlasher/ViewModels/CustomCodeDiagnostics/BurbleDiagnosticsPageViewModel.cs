﻿using MgFlasher.CustomCode;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.Flasher.Commands.Diagnostics.Custom_Code.Interfaces;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;

namespace MgFlasher.ViewModels.CustomCodeDiagnostics;

public class BurbleDiagnosticsPageViewModel : PageViewModel
{
    private readonly ICustomCodeDiagnosticServices _customCodeDiagnosticServices;
    private readonly ILogger<BurbleDiagnosticsPageViewModel> _logger;
    private readonly IDCANService _dCANService;
    private readonly IUserDialogs _userDialogs;
    private bool _hasBarBackButton;
    private ObservableCollection<DiagnosticCommandItemModel> _diagnosticCommands;

    public ObservableCollection<DiagnosticCommandItemModel> DiagnosticCommands
    {
        get => _diagnosticCommands;
        private set => SetProperty(ref _diagnosticCommands, value);
    }

    public bool HasBarBackButton
    {
        get => _hasBarBackButton;
        private set => SetProperty(ref _hasBarBackButton, value);
    }

    public BurbleDiagnosticsPageViewModel(
        IDCANService dCANService,
        IUserDialogs userDialogs,
        ICustomCodeDiagnosticServices customCodeDiagnosticServices,
        ILogger<BurbleDiagnosticsPageViewModel> logger)
    {
        _dCANService = dCANService;
        _userDialogs = userDialogs;
        _customCodeDiagnosticServices = customCodeDiagnosticServices;
        _logger = logger;
        DiagnosticCommands = new ObservableCollection<DiagnosticCommandItemModel>();
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        var connectedCar = await _dCANService.GetConnectedCar();
        HasBarBackButton = !string.IsNullOrEmpty(connectedCar?.Fingerprint) && (connectedCar.Supported ?? false);
        await _customCodeDiagnosticServices.UpdateAllLocatorsAsync(connectedCar, showWarnings: true);
        InitCommandItems(connectedCar);
    }

    private void InitCommandItems(Car connectedCar)
    {
        DiagnosticCommands = new ObservableCollection<DiagnosticCommandItemModel>();

        if (CustomCodeVersions.IsAtLeastCustomCodev6x(connectedCar.EcuMaster.CustomCodeVersionInstalled))
        {
            foreach (var command in GetCustomCodev6xCommands())
                DiagnosticCommands.Add(command);
        }
        if (CustomCodeVersions.IsAtLeastCustomCodev71(connectedCar.EcuMaster.CustomCodeVersionInstalled))
        {
            foreach (var command in GetCustomCodev71Commands())
                DiagnosticCommands.Add(command);
        }
        _logger.LogInformation($"Available options: {JsonConvert.SerializeObject(DiagnosticCommands.Select(x => x.CommandName))}");
    }

    private async void ReadCommandAsync()
    {
        await this.ExecuteExclusivelyAsync(async () =>
        {
            var result = await _customCodeDiagnosticServices.ReadBurbleStatusAsync();
            await _userDialogs.AlertAsync(result);
        });
    }

    private async Task SetCommandAsync(byte value)
    {
        await this.ExecuteExclusivelyAsync(async () =>
        {
            var result = await _customCodeDiagnosticServices.SetBurbleStatusAsync(value);
            await _userDialogs.AlertAsync(result);
        });
    }

    private IEnumerable<DiagnosticCommandItemModel> GetCustomCodev6xCommands()
    {
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Read_Action,
            Command = new TraceableCommand(ReadCommandAsync, nameof(ReadCommandAsync)),
            CommandName = AppResources.Diagnostics_CustomCodeReadActiveValue_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Select_Action,
            Command = new TraceableCommand(BurbleSelectSlot0, nameof(BurbleSelectSlot0)),
            CommandName = AppResources.Diagnostics_CustomCodeSlot0_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Select_Action,
            Command = new TraceableCommand(BurbleSelectSlot1, nameof(BurbleSelectSlot1)),
            CommandName = AppResources.Diagnostics_CustomCodeSlot1_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Select_Action,
            Command = new TraceableCommand(BurbleSelectSlot2, nameof(BurbleSelectSlot2)),
            CommandName = AppResources.Diagnostics_CustomCodeSlot2_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Select_Action,
            Command = new TraceableCommand(BurbleSelectSlot3, nameof(BurbleSelectSlot3)),
            CommandName = AppResources.Diagnostics_CustomCodeSlot3_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Select_Action,
            Command = new TraceableCommand(BurbleSelectSlot4, nameof(BurbleSelectSlot4)),
            CommandName = AppResources.Diagnostics_CustomCodeSlot4_Label,
            Icon = "menudiagnosticssmall.png"
        };
    }

    private IEnumerable<DiagnosticCommandItemModel> GetCustomCodev71Commands()
    {
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Select_Action,
            Command = new TraceableCommand(BurbleSelectSlot5, nameof(BurbleSelectSlot5)),
            CommandName = AppResources.Diagnostics_CustomCodeSlot5_Label,
            Icon = "menudiagnosticssmall.png"
        };
    }

    private async void BurbleSelectSlot0()
    {
        await SetCommandAsync(0x00);
    }

    private async void BurbleSelectSlot1()
    {
        await SetCommandAsync(0x01);
    }

    private async void BurbleSelectSlot2()
    {
        await SetCommandAsync(0x02);
    }

    private async void BurbleSelectSlot3()
    {
        await SetCommandAsync(0x03);
    }

    private async void BurbleSelectSlot4()
    {
        await SetCommandAsync(0x04);
    }

    private async void BurbleSelectSlot5()
    {
        await SetCommandAsync(0x05);
    }
}