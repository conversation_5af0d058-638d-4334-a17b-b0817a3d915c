﻿using MgFlasher.Models;
using System.Threading.Tasks;
using MgFlasher.Helpers;
using System.Windows.Input;
using System.Collections.ObjectModel;
using Syncfusion.Maui.Popup;
using MgFlasher.Services.Tutorials;

namespace MgFlasher.ViewModels;

public class PageViewModel : BaseViewModel
{
    private bool _isBusy = false;
    private bool _isPopupOpen;
    private ObservableCollection<PopupTutorial> _tutorialSteps;
    private string _tutorialPageTitle;

    public bool IsPopupOpen
    {
        get => _isPopupOpen;
    }

    public SfPopup TutorialPopup { get; set; }

    public PageLoaderViewModel PageLoader { get; }

    public ObservableCollection<PopupTutorial> TutorialSteps
    {
        get => _tutorialSteps;
        set
        {
            SetProperty(ref _tutorialSteps, value, nameof(TutorialSteps));
        }
    }

    public string TutorialPageTitle
    {
        get => _tutorialPageTitle;
        set
        {
            SetProperty(ref _tutorialPageTitle, value, nameof(TutorialPageTitle));
        }
    }

    public bool IsBusy
    {
        get { return _isBusy; }
        set { SetProperty(ref _isBusy, value); }
    }

    public ICommand DontShowPopupTutorialAgainCommand { get; }

    public PageType CurrentPageType { get; set; }

    public PageViewModel()
    {
        PageLoader = DependencyResolver.Resolve<PageLoaderViewModel>();
        DontShowPopupTutorialAgainCommand = new TraceableCommand(DontShowPopupTutorialAgain, nameof(DontShowPopupTutorialAgain));
    }

    public virtual Task OnNavigatedFromAsync(PageType nextPage)
    {
        return Task.CompletedTask;
    }

    public virtual Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (CurrentPageType != PageType.Pending)
        {
            ShowTutorial();
        }

        return Task.CompletedTask;
    }

    public virtual void ShowTutorial()
    {
        if (!PopupTutorialsService.IsPopupShown(CurrentPageType))
        {
            var tutorial = PopupTutorialsService.GetTutorial(CurrentPageType);
            if (tutorial is not null)
            {
                TutorialPageTitle = PopupTutorialsService.GetPageTitle(CurrentPageType);
                TutorialSteps = tutorial;
                SetProperty(ref _isPopupOpen, true, nameof(IsPopupOpen));
            }
        }
    }

    public virtual void DontShowPopupTutorialAgain()
    {
        PopupTutorialsService.MarkPopupAsShown(CurrentPageType);
        SetProperty(ref _isPopupOpen, false, nameof(IsPopupOpen));
    }
}