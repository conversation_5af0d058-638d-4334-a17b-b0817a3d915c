﻿using Microsoft.Extensions.Logging;
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using MgFlasher.Helpers;

namespace MgFlasher.ViewModels;

public class BaseViewModel : INotifyPropertyChanged
{
    private readonly ILogger _logger;

    public BaseViewModel()
    {
        _logger = DependencyResolver.Resolve<ILoggerFactory>().CreateLogger(GetType().FullName);
    }

    #region INotifyPropertyChanged

    protected void SetProperty<T>(ref T backingStore, T value,
        [CallerMemberName] string propertyName = "",
        Action onChanged = null)
    {
        if (Equals(backingStore, value))
        {
            return;
        }

        backingStore = value;
        onChanged?.Invoke();
        OnPropertyChanged(propertyName);
    }

    public event PropertyChangedEventHandler PropertyChanged;

    protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
    {
        try
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"{nameof(OnPropertyChanged)} - propertyName error");
        }
    }

    #endregion INotifyPropertyChanged
}