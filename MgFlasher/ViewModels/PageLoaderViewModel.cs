﻿using MgFlasher.Localization.Resources;

namespace MgFlasher.ViewModels;

public class PageLoaderViewModel : BaseViewModel
{
    private bool _isLoading;
    private string _loadingTitle = AppResources.Loading;

    public bool IsLoading
    {
        get { return _isLoading; }
        set { SetProperty(ref _isLoading, value); }
    }

    public string LoadingTitle
    {
        get { return _loadingTitle; }
        set { SetProperty(ref _loadingTitle, value); }
    }
}