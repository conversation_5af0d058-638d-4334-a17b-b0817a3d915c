﻿using MgFlasher.Flasher.Commands;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.FlashCarAllowance;
using MgFlasher.Flasher.Services.Purchases;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Localization.Resources;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.AppFiles;
using MgFlasher.Flasher.Services.FlasherState;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.Logger;
using MgFlasher.CustomCode.Storage.Changesets;
using System.Globalization;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarFlashViewModel : BaseViewModel, ITabComponent
{
    private readonly INavigationService _navigationService;
    private readonly IDCANService _dCANService;
    private readonly IPurchaseService _inAppPurchasesPurchasesAccessor;
    private readonly IFlashCarAllowanceService _flashCarAllowanceService;
    private readonly IFilesService _filesService;
    private readonly IAppContext _appContext;
    private readonly IDataService _dataService;
    private readonly IUserDialogs _userDialogs;
    private readonly IAppFilesExistenceValidator _appFilesExistenceValidator;
    private readonly ICarCompatabilityService _carCompatabilityService;
    private readonly IAppVersionBlockService _appVersionBlockService;
    private readonly ILogger<MyCarFlashViewModel> _logger;
    private ObservableCollection<MyCarFlashAllowanceItemViewModel> _flashCarAllowanceItemViewModels;
    private FlashAllowanceItem _loggerFlashAllowanceItem;
    private bool _canActivate;
    private Car _car;
    private IReadOnlyList<InAppPurchase> _purchases;

    public ObservableCollection<MyCarFlashAllowanceItemViewModel> FlashCarAllowanceItemViewModels
    {
        get => _flashCarAllowanceItemViewModels;
        set => SetProperty(ref _flashCarAllowanceItemViewModels, value);
    }

    public ICommand ActivateCommand { get; }
    public ICommand LoggerCommand { get; }

    public bool CanActivate { get => _canActivate; private set => SetProperty(ref _canActivate, value); }

    public MyCarFlashViewModel(INavigationService navigationService,
        IDCANService dCANService,
        IPurchaseService inAppPurchasesPurchasesAccessor,
        IFlashCarAllowanceService flashCarAllowanceService,
        IFilesService filesService,
        IAppContext appContext,
        IDataService dataService,
        IUserDialogs userDialogs,
        IAppFilesExistenceValidator appFilesExistenceValidator,
        ILogger<MyCarFlashViewModel> logger,
        ICarCompatabilityService carCompatabilityService,
        IAppVersionBlockService appVersionBlockService)
    {
        _navigationService = navigationService;
        _dCANService = dCANService;
        _inAppPurchasesPurchasesAccessor = inAppPurchasesPurchasesAccessor;
        _flashCarAllowanceService = flashCarAllowanceService;
        _filesService = filesService;
        _appContext = appContext;
        _dataService = dataService;
        _userDialogs = userDialogs;
        _appFilesExistenceValidator = appFilesExistenceValidator;
        _logger = logger;
        ActivateCommand = new TraceableCommand(Activate, nameof(Activate));
        LoggerCommand = new TraceableCommand(Logger, nameof(Logger));
        _carCompatabilityService = carCompatabilityService;
        _appVersionBlockService = appVersionBlockService;
    }

    private async void Logger()
    {
        await _navigationService.NavigateToTabbedComponent<MyCarLoggerViewModel>(PageType.MyCar);
    }

    private async void Activate()
    {
        if (!CanExecuteGoToFlash())
        {
            await _userDialogs.AlertAsync(AppResources.Message_Activate_BuyLicenseFirst, okText: AppResources.Ok);
            return;
        }

        var car = await _dCANService.GetConnectedCar();

        if (car.Activated && !CheckPreviousActivationAttemptFailed())
        {
            await _userDialogs.AlertAsync(AppResources.Messag_Already_Activated, okText: AppResources.Ok);
            return;
        }

        var flashCommand = DependencyResolver.Resolve<IActivateCarFlashCommand>();

        if (!await _filesService.Exists(car.VIN, car.Fingerprint, car.StockMap))
        {
            await _userDialogs.AlertAsync(AppResources.StageFile_DoesntExists, okText: AppResources.Ok);
            return;
        }

        var result = await _appFilesExistenceValidator.ValidateAsync();
        if (!string.IsNullOrEmpty(result))
        {
            await _userDialogs.AlertAsync(message: result, okText: AppResources.Ok);
            return;
        }

        if (car.AnyEcuReqiresWave3Unlock)
        {
            await _userDialogs.AlertAsync(AppResources.MyCar_PromptWave3UnlockStatus, okText: AppResources.Ok);
            return;
        }

        var ccVersions = await _carCompatabilityService.GetCustomCodeOverviewByStockMap(car);
        if (!ccVersions.Any())
        {
            var flashingCarPageNavigationArguments = new FlashingCarPageNavigationArgs()
            {
                ContinousCommand = flashCommand,
                WarningInfos = FlashingCarWarningInfo.GetWarningInfos(StageType.Activated)
            };
            await _navigationService.NavigateAsync(PageType.FlashingCarWarningInfo, flashingCarPageNavigationArguments);
            return;
        }

        var args = new CustomCodeVersionsPageNavigationArgs(ccVersions, StageType.Activated, flashCommand);
        await _navigationService.NavigateAsync(PageType.CustomCodeVersions, args);
    }

    private bool CanExecuteGoToFlash()
    {
        return _purchases != null && _purchases.HasFlasherLicense();
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        try
        {
            await _appVersionBlockService.CheckAppVersionBlockStatus();

            await InitializeConnectedCar();

            _purchases = await _inAppPurchasesPurchasesAccessor.GetPurchases(_car.Fingerprint);

            var items = await _flashCarAllowanceService.GetFlashAllowanceItems(_car);
            var vms = CreateFlashAllowanceViewModels(items.Where(x => x.ProductType != FlashAllowanceProductType.Logger).ToList());
            _loggerFlashAllowanceItem = items.FirstOrDefault(x => x.ProductType == FlashAllowanceProductType.Logger);

            FlashCarAllowanceItemViewModels = new ObservableCollection<MyCarFlashAllowanceItemViewModel>(vms);

            var previousActivationFailed = CheckPreviousActivationAttemptFailed();
            bool useSavepoint = true;
            if (previousActivationFailed)
            {
                useSavepoint = await _userDialogs.ConfirmAsync(AppResources.Previous_Activation_Failed,
                    okText: AppResources.Ok,
                    cancelText: AppResources.RemoveSavePoint);
                if (!useSavepoint)
                {
                    _logger.LogInformation("User selected to clear Unlock Savepoint!");
                    _dataService.ClearUnlockSavepoint(_car.VIN, _car.EcuMaster.SerialNo);
                }
            }

            CanActivate = ((previousActivationFailed && useSavepoint)
                           || (!_car.AnyEcuActivated && !_car.AnyEcuInBootMode))
                          && !_car.AnyEcuHasNewBtldSecurity
                          && !_car.AnyEcuReqiresWave3Unlock;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error on OnNavigatedToAsync");
            await _userDialogs.AlertAsync(ex.Message, AppResources.Error_Occured, AppResources.Ok);
            await _navigationService.NavigateToPreviousAsync();
        }
    }

    private async Task InitializeConnectedCar()
    {
        var car = await _dCANService.GetConnectedCar();

        if (car is null && !string.IsNullOrEmpty(_car?.Fingerprint))
        {
            _dCANService.ChangeConnectedCar(_car.Fingerprint);
            car = await _dCANService.GetConnectedCar();
            _logger.LogInformation("Restoring connected car {fingerprint}", _car.Fingerprint);
        }

        _car = car;
    }

    private bool CheckPreviousActivationAttemptFailed()
    {
        var savepointMaster = _dataService.GetUnlockSavepoint(_car.VIN, _car.EcuMaster.SerialNo);
        var failedStatus = false;
        if (savepointMaster.HasValue)
        {
            failedStatus = savepointMaster.Value.ToString().EndsWith("Failed");
        }

        if (_car.EcuSlave != null && !failedStatus)
        {
            var savepointSlave = _dataService.GetUnlockSavepoint(_car.VIN, _car.EcuSlave.SerialNo);
            if (savepointSlave.HasValue)
            {
                failedStatus = savepointSlave.Value.ToString().EndsWith("Failed");
            }
        }

        return failedStatus;
    }

    private IEnumerable<MyCarFlashAllowanceItemViewModel> CreateFlashAllowanceViewModels(IReadOnlyList<FlashAllowanceItem> items)
    {
        foreach (var item in items)
        {
            yield return new MyCarFlashAllowanceItemViewModel(item, _car);
        }
    }
}