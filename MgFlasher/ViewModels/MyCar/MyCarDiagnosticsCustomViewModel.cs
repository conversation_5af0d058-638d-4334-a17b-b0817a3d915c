﻿using MgFlasher.CustomCode;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MgFlasher.Flasher.Commands.Diagnostics.Custom_Code.Interfaces;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Services.Navigation;
using System;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarDiagnosticsCustomViewModel : BaseViewModel, ITabComponent
{
    private readonly ICustomCodeDiagnosticServices _customCodeDiagnosticServices;
    private readonly ILogger<MyCarDiagnosticsCustomViewModel> _logger;
    private readonly INavigationService _navigationService;
    private readonly IDCANService _dCANService;
    private readonly IAppContext _appContext;
    private readonly IUserDialogs _userDialogs;
    private bool _hasOptionAvailable, _executing;
    private string _customCodeVersionString;
    private ObservableCollection<DiagnosticCommandItemModel> _diagnosticCommands;

    public ObservableCollection<DiagnosticCommandItemModel> DiagnosticCommands
    {
        get => _diagnosticCommands;
        private set => SetProperty(ref _diagnosticCommands, value);
    }

    public bool HasOptionAvailable
    {
        get => _hasOptionAvailable;
        private set => SetProperty(ref _hasOptionAvailable, value);
    }

    public string CustomCodeVersionString
    {
        get => _customCodeVersionString;
        private set => SetProperty(ref _customCodeVersionString, value);
    }

    public bool Executing
    {
        get => _executing;
        private set => SetProperty(ref _executing, value);
    }

    public MyCarDiagnosticsCustomViewModel(INavigationService navigationService,
        IDCANService dCANService,
        IAppContext appContext,
        IUserDialogs userDialogs,
        ICustomCodeDiagnosticServices customCodeDiagnosticServices,
        ILogger<MyCarDiagnosticsCustomViewModel> logger)
    {
        _navigationService = navigationService;
        _dCANService = dCANService;
        _appContext = appContext;
        _userDialogs = userDialogs;
        DiagnosticCommands = new ObservableCollection<DiagnosticCommandItemModel>();
        _customCodeDiagnosticServices = customCodeDiagnosticServices;
        _logger = logger;
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        var connectedCar = await _dCANService.GetConnectedCar();
        InitCommandItems(connectedCar);
        await _customCodeDiagnosticServices.UpdateAllLocatorsAsync(connectedCar, showWarnings: HasOptionAvailable);
    }

    private void InitCommandItems(Car connectedCar)
    {
        DiagnosticCommands = new ObservableCollection<DiagnosticCommandItemModel>();
        CustomCodeVersionString = $"Custom Code {connectedCar.EcuMaster.CustomCodeVersionInstalled}";

        if (CustomCodeVersions.IsAtLeastCustomCodev6x(connectedCar.EcuMaster.CustomCodeVersionInstalled))
        {
            foreach (var command in GetCustomCodev6xCommands())
                DiagnosticCommands.Add(command);
        }
        if (CustomCodeVersions.IsAtLeastCustomCodev7x(connectedCar.EcuMaster.CustomCodeVersionInstalled))
        {
            foreach (var command in GetCustomCodev70Commands())
                DiagnosticCommands.Add(command);
        }
        if (CustomCodeVersions.IsAtLeastCustomCodev71(connectedCar.EcuMaster.CustomCodeVersionInstalled))
        {
            foreach (var command in GetCustomCodev71Commands(connectedCar.IsAurix))
                DiagnosticCommands.Add(command);
        }

        _logger.LogInformation($"Available options: {JsonConvert.SerializeObject(DiagnosticCommands.Select(x => x.CommandName))}");
        HasOptionAvailable = DiagnosticCommands.Any();

        if (HasOptionAvailable)
        {
            foreach (var command in GetEepromCommands())
                DiagnosticCommands.Add(command);
        }
    }

    private IEnumerable<DiagnosticCommandItemModel> GetCustomCodev6xCommands()
    {
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Open_Action,
            Command = new TraceableCommand(SwitchableMapDiag, nameof(SwitchableMapDiag)),
            CommandName = AppResources.Diagnostics_SwitchableMapDiag_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Open_Action,
            Command = new TraceableCommand(BurbleDiag, nameof(BurbleDiag)),
            CommandName = AppResources.Diagnostics_BurbleDiag_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Open_Action,
            Command = new TraceableCommand(MaxCoolingDiag, nameof(MaxCoolingDiag)),
            CommandName = AppResources.Diagnostics_MaxCoolingDiag_Label,
            Icon = "menudiagnosticssmall.png"
        };
    }

    private IEnumerable<DiagnosticCommandItemModel> GetCustomCodev70Commands()
    {
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Open_Action,
            Command = new TraceableCommand(AntilagDiag, nameof(AntilagDiag)),
            CommandName = AppResources.Diagnostics_AntilagDiag_Label,
            Icon = "menudiagnosticssmall.png"
        };
    }

    private IEnumerable<DiagnosticCommandItemModel> GetCustomCodev71Commands(bool isAurixCar = false)
    {
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Open_Action,
            Command = new TraceableCommand(EthanolOverrideDiag, nameof(EthanolOverrideDiag)),
            CommandName = AppResources.Diagnostics_EthanolOverrideDiag_Label,
            Icon = "menudiagnosticssmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Open_Action,
            Command = new TraceableCommand(ExhaustFlapDiag, nameof(ExhaustFlapDiag)),
            CommandName = AppResources.Diagnostics_ExhaustFlapDiag_Label,
            Icon = "menudiagnosticssmall.png"
        };
        if (isAurixCar)
        {
            yield return new DiagnosticCommandItemModel()
            {
                ActionName = AppResources.Diagnostics_Open_Action,
                Command = new TraceableCommand(RadiatorFlapDiag, nameof(RadiatorFlapDiag)),
                CommandName = AppResources.Diagnostics_RadiatorFlapDiag_Label,
                Icon = "menudiagnosticssmall.png"
            };
        }
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Open_Action,
            Command = new TraceableCommand(ValetModeDiag, nameof(ValetModeDiag)),
            CommandName = AppResources.Diagnostics_ValetModeDiag_Label,
            Icon = "menudiagnosticssmall.png"
        };
    }

    private IEnumerable<DiagnosticCommandItemModel> GetEepromCommands()
    {
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Read_Action,
            Command = new TraceableCommand(ReadEepromCommandAsync, nameof(ReadEepromCommandAsync)),
            CommandName = AppResources.Diagnostics_ReadEeprom_Label,
            Icon = "menudiagnosticssmall.png"
        };
        if (_appContext.IsDeveloper())
        {
            yield return new DiagnosticCommandItemModel()
            {
                ActionName = AppResources.Diagnostics_Set_Action,
                Command = new TraceableCommand(SetAllEepromCommandAsync, nameof(SetAllEepromCommandAsync)),
                CommandName = AppResources.Diagnostics_SetAllEeprom_Label,
                Icon = "menudiagnosticssmall.png"
            };
        }
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_Save_Action,
            Command = new TraceableCommand(SaveEepromCommandAsync, nameof(SaveEepromCommandAsync)),
            CommandName = AppResources.Diagnostics_SaveEeprom_Label,
            Icon = "menudiagnosticssmall.png"
        };
    }

    private async void ReadEepromCommandAsync()
    {
        await this.ExecuteExclusivelyAsync(async () =>
        {
            var result = await _customCodeDiagnosticServices.ReadAllEepromStringResultsAsync(useProgressDialog: true);
            await _userDialogs.AlertAsync(result);
        });
    }

    private async void SetAllEepromCommandAsync()
    {
        await this.ExecuteExclusivelyAsync(async () =>
        {
            var result = await _customCodeDiagnosticServices.SetAllEepromAsync(useProgressDialog: true);
            await _userDialogs.AlertAsync(result);
        });
    }

    private async void SaveEepromCommandAsync()
    {
        await ExecuteExclusivelyAsync(async () =>
        {
            var result = await _customCodeDiagnosticServices.SaveEepromAsync(useProgressDialog: true);
            await _userDialogs.AlertAsync(result);
        });
    }

    private async void SwitchableMapDiag(object obj)
    {
        await ExecuteExclusivelyAsync(() => _navigationService.NavigateAsync(PageType.SwitchableMapDiagnostics));
    }

    private async void BurbleDiag(object obj)
    {
        await ExecuteExclusivelyAsync(() => _navigationService.NavigateAsync(PageType.BurbleDiagnostics));
    }

    private async void MaxCoolingDiag(object obj)
    {
        await ExecuteExclusivelyAsync(() => _navigationService.NavigateAsync(PageType.MaxCoolinDiagnostics));
    }

    private async void AntilagDiag(object obj)
    {
        await ExecuteExclusivelyAsync(() => _navigationService.NavigateAsync(PageType.AntilagDiagnostics));
    }

    private async void EthanolOverrideDiag(object obj)
    {
        await ExecuteExclusivelyAsync(() => _navigationService.NavigateAsync(PageType.EthanolOverrideDiagnostics));
    }

    private async void ExhaustFlapDiag(object obj)
    {
        await ExecuteExclusivelyAsync(() => _navigationService.NavigateAsync(PageType.ExhaustFlapDiagnostics));
    }

    private async void RadiatorFlapDiag(object obj)
    {
        await ExecuteExclusivelyAsync(() => _navigationService.NavigateAsync(PageType.RadiatorFlapDiagnostics));
    }

    private async void ValetModeDiag(object obj)
    {
        await ExecuteExclusivelyAsync(() => _navigationService.NavigateAsync(PageType.ValetModeDiagnostics));
    }

    private async Task ExecuteExclusivelyAsync(Func<Task> func)
    {
        if (Executing)
            return;

        try
        {
            Executing = true;
            await func();
        }
        finally
        {
            Executing = false;
        }
    }
}