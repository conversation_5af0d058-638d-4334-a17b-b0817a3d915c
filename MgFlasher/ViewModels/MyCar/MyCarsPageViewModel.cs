﻿using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;
using MgFlasher.Models.NavigationArgs;
using CommunityToolkit.Mvvm.Messaging;
using MgFlasher.Helpers;
using System;
using MgFlasher.Flasher.Commands.Models;
using MgFlasher.Flasher.Services.Cars.Connectivity;
using MgFlasher.Services.Navigation;
using System.Linq;
using MgFlasher.Services;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarsPageViewModel : PageViewModel
{
    private readonly INavigationService _navigationService;
    private readonly ICarRepository _carsService;
    private readonly IDCANService _dCANService;
    private readonly IUserDialogs _userDialogs;
    private readonly IEcuPermissionHandler _ecuPermissionHandler;
    private readonly IAppVersionBlockService _appVersionBlockService;
    private readonly ILogger<MyCarsPageViewModel> _logger;
    private bool _connecting;

    private ObservableCollection<MyCarsOverviewViewModel> _cars;

    public ICommand AddCommand { get; }

    public ObservableCollection<MyCarsOverviewViewModel> Cars
    {
        get => _cars;
        set => SetProperty(ref _cars, value);
    }

    public bool HasAnyCars => Cars?.Count > 0;

    public MyCarsPageViewModel(
        INavigationService navigationService,
        ICarRepository carsService,
        IDCANService dCANService,
        IUserDialogs userDialogs,
        IEcuPermissionHandler ecuPermissionHandler,
        IAppVersionBlockService appVersionBlockService,
        ILogger<MyCarsPageViewModel> logger)
    {
        _navigationService = navigationService;
        _carsService = carsService;
        _dCANService = dCANService;
        _userDialogs = userDialogs;
        _ecuPermissionHandler = ecuPermissionHandler;
        _appVersionBlockService = appVersionBlockService;
        _logger = logger;
        AddCommand = new TraceableCommand(AddCar, nameof(AddCar));
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        await _appVersionBlockService.CheckAppVersionBlockStatus();
        var vms = await CreateViewModels();
        Cars = new ObservableCollection<MyCarsOverviewViewModel>(vms);
        OnPropertyChanged(nameof(HasAnyCars));
        await base.OnNavigatedToAsync(previousPage, arg);
    }

    private async void AddCar(object arg)
    {
        if (_connecting)
        {
            return;
        }

        _connecting = true;

        _dCANService.ChangeConnectedCar(null);
        var pendingPageArgs = new PendingPageNavigationArgs()
        {
            Text = AppResources.Connect_Connecting,
            NextPage = PageType.MyCar,
            NextPageTabbedComponent = typeof(MyCarHomeOverviewViewModel),
            BackPage = PageType.MyCars,
            EnableProgressBar = true
        };
        await _navigationService.NavigateAsync(PageType.Pending, pendingPageArgs);

        var msgNo = 0;
        void OnLogArrived(object obj, string msg)
        {
            var progress = Math.Min(99, ++msgNo * 100 / 20);
            WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageUpdateText, new PendingPageUpdateArgs(msg, progress)));
        }

        try
        {
            CarMessageBroadcaster.Instance.LogArrived += OnLogArrived;
            await Task.Run(async () =>
            {
                await _ecuPermissionHandler.AcquireAsync();
                await _dCANService.Connect();
            });
            WeakReferenceMessenger.Default.Send(
                new MessagingCenterIdentifiers.PendingPageMessage(
                    MessagingCenterIdentifiers.PendingPageCloseNavigation));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Connect error");
            await HandleConnectException(ex);
            WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageBackNavigation));
        }
        finally
        {
            CarMessageBroadcaster.Instance.LogArrived -= OnLogArrived;
            _connecting = false;
        }
    }

    private async Task HandleConnectException(Exception e)
    {
        _logger.LogError(e, "HandleConnectException error");

        async Task ShowErrorUserMessage(string message) =>
            await _userDialogs.AlertAsync(new CommandResult(message, CommandResultType.Error));

        if (e is not VehicleNotSupportedException vex)
        {
            await ShowErrorUserMessage(e.Message);
        }
        else if (vex.Reason == VehicleNotSupportedException.ReasonType.UnsupportedModule)
        {
            await ShowErrorUserMessage(AppResources.UnsupportedModule);
        }
    }

    private async Task<IEnumerable<MyCarsOverviewViewModel>> CreateViewModels()
    {
        var allCars = await _carsService.GetAll();
        var tasks = allCars
            .Select(async x => new MyCarsOverviewViewModel(x, _dCANService,
                _userDialogs, _carsService, _navigationService,
                _logger, OnRemovedCar,
                await _carsService.GetCarImageAsync(x.Fingerprint)))
            .ToList();
        var result = await Task.WhenAll(tasks);
        return result;
    }

    private void OnRemovedCar(Car x)
    {
        Cars.Remove(Cars.First(c => c.Fingerprint == x.Fingerprint));
        OnPropertyChanged(nameof(HasAnyCars));
    }
}