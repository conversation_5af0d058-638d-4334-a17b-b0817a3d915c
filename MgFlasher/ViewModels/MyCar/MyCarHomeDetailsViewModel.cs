﻿using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using System;
using System.Linq;
using System.Threading.Tasks;
using UNI_Flash;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarHomeDetailsViewModel : BaseViewModel, ITabComponent
{
    private readonly IDCANService _dCANService;
    private Car _connectedCar = new();

    public string VIN => _connectedCar.VIN;
    public string SerialNo => _connectedCar.EcuMaster?.SerialNo;
    public string UserCarName => _connectedCar.UserCarName;
    public string UnlockCompany => _connectedCar.EcuMaster?.UnlockCompany.ToString().Replace("_", " ").TrimStart();
    public string SecurityLevel => _connectedCar.EcuMaster?.SecurityWave.ToString();
    public string ManufacturingDate => _connectedCar.EcuMaster?.ManufacturingDate;
    public string ProcessorType => _connectedCar.EcuMaster?.ProcessorArchitectureType.ToString();

    public string Engine => string.Join(" / ", new[]
    {
        !string.IsNullOrEmpty(_connectedCar.FullEngineCode) ? _connectedCar.FullEngineCode : _connectedCar.EngineCode,
        _connectedCar.EngineConfiguration,
        _connectedCar.EngineProductionYears,
        _connectedCar.EngineDisplacment
    }.Where(x => !string.IsNullOrEmpty(x)));

    public string Calid => _connectedCar.EcuMaster?.CALID;
    public string CVN => _connectedCar.EcuMaster?.CVN;
    public string Region => _connectedCar.EcuMaster?.Region.ToString();
    public string SgbdIndex => _connectedCar.SgbdIndex?.ToString();

    public string A2L => _connectedCar.EcuMaster?.A2Lstring;
    public string Logistikbereich => _connectedCar.EcuMaster?.Logistikbereich;
    public string BootctrlVersion => _connectedCar.EcuMaster?.BootctrlVersion;
    public string CustomCode => _connectedCar.EcuMaster?.CustomCodeVersionInstalled ?? _connectedCar.EcuMaster?.CustomCodeApplied;
    public string HWEL => _connectedCar.EcuMaster?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.HWEL)?.GetXweString();
    public string HWAP => _connectedCar.EcuMaster?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.HWAP)?.GetXweString();
    public string BTLD => _connectedCar.EcuMaster?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.BTLD)?.GetXweString();
    public string SWFL => _connectedCar.EcuMaster?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.SWFL)?.GetXweString();
    public string SWFK => _connectedCar.EcuMaster?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.SWFK)?.GetXweString();
    public string CAFD => _connectedCar.EcuMaster?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.CAFD)?.GetXweString();

    public string ConnectionHost => _connectedCar.ConnectionIpAddress;
    public string ConnectionPort => _connectedCar.ConnectionPort > 0 ? _connectedCar.ConnectionPort.ToString() : null;
    public string LastConnectedOn => _connectedCar.LastLocalUpdateTimestamp > DateTime.MinValue ? _connectedCar.LastLocalUpdateTimestamp.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss") : null;
    public string LastSyncedOn => _connectedCar.LastServerSyncTimestamp > DateTime.MinValue ? _connectedCar.LastServerSyncTimestamp.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss") : null;

    public MyCarHomeDetailsViewModel(IDCANService dCANService)
    {
        _dCANService = dCANService;
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        _connectedCar = await _dCANService.GetConnectedCar();
        OnPropertyChanged(null);
    }
}