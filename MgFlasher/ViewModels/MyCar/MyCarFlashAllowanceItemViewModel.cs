﻿using MgFlasher.Flasher.Services.Models;
using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Services;
using System.Windows.Input;
using MgFlasher.Localization.Resources;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarFlashAllowanceItemViewModel
{
    private readonly INavigationService _navigationService;
    private readonly FlashAllowanceItem _flashAllowanceItem;

    public string AutomationId => $"FlashCarStepsManagerPage_{ProductType}_Button";
    public FlashAllowanceProductType ProductType => _flashAllowanceItem.ProductType;
    public string ProductTypeText => GetProductTypeText();
    public Car Car { get; }
    public bool IsBought => _flashAllowanceItem.IsBought;
    public string CommandLabel => _flashAllowanceItem.IsBought ? AppResources.FlashCarAllowanceItem_Flash : AppResources.FlashCarAllowanceItem_Redeem;
    public ICommand ViewCommand { get; }

    public MyCarFlashAllowanceItemViewModel(FlashAllowanceItem flashAllowanceItem, Car connectedCar)
    {
        _navigationService = DependencyResolver.Resolve<INavigationService>();
        _flashAllowanceItem = flashAllowanceItem;

        Car = connectedCar;
        ViewCommand = new TraceableCommand(View, nameof(View));
    }

    private async void View()
    {
        await _navigationService.NavigateAsync(PageType.BuyFlashStage, _flashAllowanceItem);
    }

    private string GetProductTypeText()
    {
        var productType = _flashAllowanceItem.ProductType;

        return productType.GetLocalizedString();
    }
}