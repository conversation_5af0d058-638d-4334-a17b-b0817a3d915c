﻿using MgFlasher.Flasher.Commands;
using MgFlasher.Flasher.Commands.Models;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Localization.Resources;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Flasher.Services.Purchases;
using System.Collections.Generic;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Flasher.Commands.Diagnostics.Generic;
using MgFlasher.Flasher.Commands.Diagnostics.Wastegate.Interfaces;
using MgFlasher.Flasher.Commands.Diagnostics.Ignition.Interfaces;
using Microsoft.Maui.Controls;
using CommunityToolkit.Mvvm.Messaging;
using MgFlasher.Flasher.Commands.Diagnostics.Exhaust_Flap.Interfaces;
using MgFlasher.Flasher.Commands.Diagnostics.Radiator_Flap.Interfaces;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;
using MgFlasher.Services.Navigation;
using MgFlasher.Flasher.Services.User.Feedback;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarDiagnosticsGeneralViewModel : BaseViewModel, ITabComponent
{
    private readonly INavigationService _navigationService;
    private readonly IReadDtcCommand _readDtcCommand;
    private readonly IClearDtcCommand _clearDtcCommand;
    private readonly IResetEcuCommand _resetEcuCommand;
    private readonly IResetFemCommand _resetFemCommand;
    private readonly IOpenExhaustFlapCommand _openExhaustFlapCommand;
    private readonly ICloseExhaustFlapCommand _closeExhaustFlapCommand;
    private readonly IAutoExhaustFlapCommand _autoExhaustFlapCommand;
    private readonly IOpenRadiatorFlapCommand _openRadiatorFlapCommand;
    private readonly ICloseRadiatorFlapCommand _closeRadiatorFlapCommand;
    private readonly IAutoRadiatorFlapCommand _autoRadiatorFlapCommand;
    private readonly IOpenWastegateCommand _openWastegateCommand;
    private readonly ICloseWastegateCommand _closeWastegateCommand;
    private readonly IAutoWastegateCommand _autoWastegateCommand;
    private readonly ITestWastegateCommand _testWastegateCommand;
    private readonly IIgnitionOnCommand _ignitionOnCommand;
    private readonly IIgnitionOffCommand _ignitionOffCommand;
    private readonly IResetAdaptationCommand _resetAdaptationCommand;
    private readonly IRestoreBackupCommand _restoreBackupCommand;
    private readonly IRestoreCodingCommand _restoreCodingCommand;
    private readonly IReadModuleCommand _readModuleCommand;
    private readonly IReadPartialEcuCommand _readPartialEcuCommand;
    private readonly IReadEcuCalibrationCommand _readEcuCalibrationCommand;
    private readonly IDCANService _dCANService;
    private readonly IPurchaseService _carPurchasesAccessor;
    private readonly IFilesService _filesService;
    private readonly IAppContext _appContext;
    private readonly IUserDialogs _userDialogs;
    private readonly ShareStockMapCommand _shareStockMapCommand;
    private readonly ILogger<MyCarDiagnosticsViewModel> _logger;
    private ObservableCollection<DiagnosticCommandItemModel> _diagnosticCommands;
    private bool _executing;

    public ObservableCollection<DiagnosticCommandItemModel> DiagnosticCommands
    {
        get => _diagnosticCommands;
        private set => SetProperty(ref _diagnosticCommands, value);
    }

    public bool Executing
    {
        get => _executing;
        private set => SetProperty(ref _executing, value);
    }

    public MyCarDiagnosticsGeneralViewModel(INavigationService navigationService,
        IReadDtcCommand readDtcCommand,
        IClearDtcCommand clearDtcCommand,
        IResetEcuCommand resetEcuCommand,
        IResetFemCommand resetFemCommand,
        IOpenExhaustFlapCommand openExhaustFlapCommand,
        ICloseExhaustFlapCommand closeExhaustFlapCommand,
        IAutoExhaustFlapCommand autoExhaustFlapCommand,
        IOpenRadiatorFlapCommand openRadiatorFlapCommand,
        ICloseRadiatorFlapCommand closeRadiatorFlapCommand,
        IAutoRadiatorFlapCommand autoRadiatorFlapCommand,
        IOpenWastegateCommand openWastegateCommand,
        ICloseWastegateCommand closeWastegateCommand,
        IAutoWastegateCommand autoWastegateCommand,
        ITestWastegateCommand testWastegateCommand,
        IIgnitionOnCommand ignitionOnCommand,
        IIgnitionOffCommand ignitionOffCommand,
        IResetAdaptationCommand resetAdaptationCommand,
        IRestoreBackupCommand restoreBackupCommand,
        IRestoreCodingCommand restoreCodingCommand,
        IReadModuleCommand readModuleCommand,
        IReadPartialEcuCommand readPartialEcuCommand,
        IReadEcuCalibrationCommand readEcuCalibrationCommand,
        IDCANService dCANService,
        IPurchaseService carPurchasesAccessor,
        IFilesService filesService,
        IAppContext appContext,
        IUserDialogs userDialogs,
        ShareStockMapCommand shareStockMapCommand,
        ILogger<MyCarDiagnosticsViewModel> logger)
    {
        _navigationService = navigationService;
        _readDtcCommand = readDtcCommand;
        _clearDtcCommand = clearDtcCommand;
        _resetEcuCommand = resetEcuCommand;
        _resetFemCommand = resetFemCommand;
        _openExhaustFlapCommand = openExhaustFlapCommand;
        _closeExhaustFlapCommand = closeExhaustFlapCommand;
        _autoExhaustFlapCommand = autoExhaustFlapCommand;
        _openRadiatorFlapCommand = openRadiatorFlapCommand;
        _closeRadiatorFlapCommand = closeRadiatorFlapCommand;
        _autoRadiatorFlapCommand = autoRadiatorFlapCommand;
        _openWastegateCommand = openWastegateCommand;
        _closeWastegateCommand = closeWastegateCommand;
        _autoWastegateCommand = autoWastegateCommand;
        _testWastegateCommand = testWastegateCommand;
        _ignitionOnCommand = ignitionOnCommand;
        _ignitionOffCommand = ignitionOffCommand;
        _resetAdaptationCommand = resetAdaptationCommand;
        _restoreBackupCommand = restoreBackupCommand;
        _restoreCodingCommand = restoreCodingCommand;
        _readModuleCommand = readModuleCommand;
        _readPartialEcuCommand = readPartialEcuCommand;
        _readEcuCalibrationCommand = readEcuCalibrationCommand;
        _dCANService = dCANService;
        _carPurchasesAccessor = carPurchasesAccessor;
        _filesService = filesService;
        _appContext = appContext;
        _userDialogs = userDialogs;
        _shareStockMapCommand = shareStockMapCommand;
        _logger = logger;
        DiagnosticCommands = new ObservableCollection<DiagnosticCommandItemModel>();
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        var connectedCar = await _dCANService.GetConnectedCar();

        var purchases = !string.IsNullOrEmpty(connectedCar?.Fingerprint) ?
            await _carPurchasesAccessor.GetPurchases(connectedCar.Fingerprint) :
            Enumerable.Empty<InAppPurchase>();

        InitCommandItems(purchases, connectedCar.IsAurix);
    }

    private void InitCommandItems(IEnumerable<InAppPurchase> purchases, bool isAurixCar = false)
    {
        DiagnosticCommands = new ObservableCollection<DiagnosticCommandItemModel>(GetFreeCommands(isAurixCar));

        if (purchases.HasFlasherLicense())
        {
            foreach (var command in GetFlasherLicenseCommands())
                DiagnosticCommands.Add(command);
        }
    }

    private IEnumerable<DiagnosticCommandItemModel> GetFreeCommands(bool isAurixCar = false)
    {
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_ReadModule_Action,
            Command = new TraceableCommand(ReadModule, nameof(ReadModule)),
            CommandName = AppResources.Diagnostics_ReadModule_Label,
            Icon = "readdtcsmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_ReadDtc_Action,
            Command = new TraceableCommand(ReadDtc, nameof(ReadDtc)),
            CommandName = AppResources.Diagnostics_ReadDtc_Label,
            Icon = "readdtcsmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_DownloadStockMap_Action,
            Command = _shareStockMapCommand,
            CommandName = AppResources.Diagnostics_DownloadStockMap_Label,
            Icon = "readdtcsmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_ClearDtc_Action,
            Command = new TraceableCommand(ClearDtc, nameof(ClearDtc)),
            CommandName = AppResources.Diagnostics_ClearDtc_Label,
            Icon = "cleardtcsmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_OpenExhaustFlap_Action,
            Command = new TraceableCommand(OpenExhaustFlap, nameof(OpenExhaustFlap)),
            CommandName = AppResources.Diagnostics_OpenExhaustFlap_Label,
            Icon = "exhaustsmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_CloseExhaustFlap_Action,
            Command = new TraceableCommand(CloseExhaustFlap, nameof(CloseExhaustFlap)),
            CommandName = AppResources.Diagnostics_CloseExhaustFlap_Label,
            Icon = "exhaustsmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_AutoExhaustFlap_Action,
            Command = new TraceableCommand(AutoExhaustFlap, nameof(AutoExhaustFlap)),
            CommandName = AppResources.Diagnostics_AutoExhaustFlap_Label,
            Icon = "exhaustsmall.png"
        };
        if (isAurixCar)
        {
            yield return new DiagnosticCommandItemModel()
            {
                ActionName = AppResources.Diagnostics_OpenRadiatorFlap_Action,
                Command = new TraceableCommand(OpenRadiatorFlap, nameof(OpenRadiatorFlap)),
                CommandName = AppResources.Diagnostics_OpenRadiatorFlap_Label,
                Icon = "grillsmall.png"
            };
            yield return new DiagnosticCommandItemModel()
            {
                ActionName = AppResources.Diagnostics_CloseRadiatorFlap_Action,
                Command = new TraceableCommand(CloseRadiatorFlap, nameof(CloseRadiatorFlap)),
                CommandName = AppResources.Diagnostics_CloseRadiatorFlap_Label,
                Icon = "grillsmall.png"
            };
            yield return new DiagnosticCommandItemModel()
            {
                ActionName = AppResources.Diagnostics_AutoRadiatorFlap_Action,
                Command = new TraceableCommand(AutoRadiatorFlap, nameof(AutoRadiatorFlap)),
                CommandName = AppResources.Diagnostics_AutoRadiatorFlap_Label,
                Icon = "grillsmall.png"
            };
        }
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_ResetAdaption_Action,
            Command = new TraceableCommand(ResetAdaptation, nameof(ResetAdaptation)),
            CommandName = AppResources.Diagnostics_ResetAdaption_Label,
            Icon = "resetadaptationsmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_ResetEcu_Action,
            Command = new TraceableCommand(ResetECU, nameof(ResetECU)),
            CommandName = AppResources.Diagnostics_ResetEcu_Label,
            Icon = "resetecusmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_ResetFem_Action,
            Command = new TraceableCommand(ResetFEM, nameof(ResetFEM)),
            CommandName = AppResources.Diagnostics_ResetFem_Label,
            Icon = "resetecusmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_IgnitionOn_Action,
            Command = new TraceableCommand(IgnitionOn, nameof(IgnitionOn)),
            CommandName = AppResources.Diagnostics_IgnitionOn_Label,
            Icon = "ignitionsmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_IgnitionOff_Action,
            Command = new TraceableCommand(IgnitionOff, nameof(IgnitionOff)),
            CommandName = AppResources.Diagnostics_IgnitionOff_Label,
            Icon = "ignitionsmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_OpenWastegate_Action,
            Command = new TraceableCommand(OpenWastegate, nameof(OpenWastegate)),
            CommandName = AppResources.Diagnostics_OpenWastegate_Label,
            Icon = "turbosmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_CloseWastegate_Action,
            Command = new TraceableCommand(CloseWastegate, nameof(CloseWastegate)),
            CommandName = AppResources.Diagnostics_CloseWastegate_Label,
            Icon = "turbosmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_AutoWastegate_Action,
            Command = new TraceableCommand(AutoWastegate, nameof(AutoWastegate)),
            CommandName = AppResources.Diagnostics_AutoWastegate_Label,
            Icon = "turbosmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_TestWastegate_Action,
            Command = new TraceableCommand(TestWastegate, nameof(TestWastegate)),
            CommandName = AppResources.Diagnostics_TestWastegate_Label,
            Icon = "turbosmall.png"
        };
        if (_appContext.CanDoCalibrationBackup())
        {
            yield return new DiagnosticCommandItemModel()
            {
                ActionName = AppResources.Diagnostics_ReadEcuCalibration_Action,
                Command = new TraceableCommand(ReadEcuCalibration, nameof(ReadEcuCalibration)),
                CommandName = AppResources.Diagnostics_ReadEcuCalibration_Label,
                Icon = "restorebackupsmall.png"
            };
        }
    }

    private IEnumerable<DiagnosticCommandItemModel> GetFlasherLicenseCommands()
    {
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_RestoreCoding_Action,
            Command = new TraceableCommand(RestoreCoding, nameof(RestoreCoding)),
            CommandName = AppResources.Diagnostics_RestoreCoding_Label,
            Icon = "restorebackupsmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_RestoreBackup_Action,
            Command = new TraceableCommand(RestoreBackup, nameof(RestoreBackup)),
            CommandName = AppResources.Diagnostics_RestoreBackup_Label,
            Icon = "restorebackupsmall.png"
        };
        yield return new DiagnosticCommandItemModel()
        {
            ActionName = AppResources.Diagnostics_ReadPartialEcu_Action,
            Command = new TraceableCommand(ReadPartialEcu, nameof(ReadPartialEcu)),
            CommandName = AppResources.Diagnostics_ReadPartialEcu_Label,
            Icon = "restorebackupsmall.png"
        };
    }

    private async void ReadDtc()
    {
        if (Executing)
            return;
        await Loading(AppResources.ReadDtc_InProgress, _readDtcCommand.ExecuteAsync());
        var dtcReadResult = _readDtcCommand.GetResult();
        if (dtcReadResult?.Codes?.Count > 0)
        {
            await _navigationService.NavigateAsync(PageType.ReadDtc, dtcReadResult);
        }
        else
        {
            await _userDialogs.AlertAsync(dtcReadResult);
        }
    }

    private async void ClearDtc()
    {
        if (Executing)
            return;
        await Loading(AppResources.ClearDtc_InProgress, _clearDtcCommand.ExecuteAsync());
        var result = _clearDtcCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void ResetECU()
    {
        if (Executing)
            return;
        await Loading(AppResources.ResetEcu_InProgress, _resetEcuCommand.ExecuteAsync());
        var result = _resetEcuCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void ResetFEM()
    {
        if (Executing)
            return;
        await Loading(AppResources.ResetFem_InProgress, _resetFemCommand.ExecuteAsync());
        var result = _resetFemCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void OpenExhaustFlap()
    {
        if (Executing)
            return;
        await Loading(AppResources.OpenExhaustFlap_InProgress, _openExhaustFlapCommand.ExecuteAsync());
        var result = _openExhaustFlapCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void CloseExhaustFlap()
    {
        if (Executing)
            return;
        await Loading(AppResources.CloseExhaustFlap_InProgress, _closeExhaustFlapCommand.ExecuteAsync());
        var result = _closeExhaustFlapCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void AutoExhaustFlap()
    {
        if (Executing)
            return;
        await Loading(AppResources.AutoExhaustFlap_InProgress, _autoExhaustFlapCommand.ExecuteAsync());
        var result = _autoExhaustFlapCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void OpenRadiatorFlap()
    {
        if (Executing)
            return;
        await Loading(AppResources.OpenRadiatorFlap_InProgress, _openRadiatorFlapCommand.ExecuteAsync());
        var result = _openRadiatorFlapCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void CloseRadiatorFlap()
    {
        if (Executing)
            return;
        await Loading(AppResources.CloseRadiatorFlap_InProgress, _closeRadiatorFlapCommand.ExecuteAsync());
        var result = _closeRadiatorFlapCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void AutoRadiatorFlap()
    {
        if (Executing)
            return;
        await Loading(AppResources.AutoRadiatorFlap_InProgress, _autoRadiatorFlapCommand.ExecuteAsync());
        var result = _autoRadiatorFlapCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void OpenWastegate()
    {
        if (Executing)
            return;
        await Loading(AppResources.OpenWastegate_InProgress, _openWastegateCommand.ExecuteAsync());
        var result = _openWastegateCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void CloseWastegate()
    {
        if (Executing)
            return;
        await Loading(AppResources.CloseWastegate_InProgress, _closeWastegateCommand.ExecuteAsync());
        var result = _closeWastegateCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void AutoWastegate()
    {
        if (Executing)
            return;
        await Loading(AppResources.AutoWastegate_InProgress, _autoWastegateCommand.ExecuteAsync());
        var result = _autoWastegateCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void TestWastegate()
    {
        if (Executing)
            return;
        await Loading(AppResources.TestWastegate_InProgress, _testWastegateCommand.ExecuteAsync());
        var result = _testWastegateCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void IgnitionOn()
    {
        if (Executing)
            return;
        await Loading(AppResources.IgnitionOn_InProgress, _ignitionOnCommand.ExecuteAsync());
        var result = _ignitionOnCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void IgnitionOff()
    {
        if (Executing)
            return;
        await Loading(AppResources.IgnitionOff_InProgress, _ignitionOffCommand.ExecuteAsync());
        var result = _ignitionOffCommand.GetResult();
        await _userDialogs.AlertAsync(result);
    }

    private async void ResetAdaptation()
    {
        if (Executing)
            return;
        await _navigationService.NavigateAsync(PageType.ResetAdaptation, _resetAdaptationCommand);
    }

    private async void RestoreBackup()
    {
        if (Executing)
            return;

        var car = await _dCANService.GetConnectedCar();

        if (!await _filesService.Exists(car.VIN, car.Fingerprint, car.StockMap))
        {
            await _userDialogs.AlertAsync(AppResources.MissingStockFiles, okText: AppResources.Ok);
            var args = CreateFeedbackNavigationArgs();
            await _navigationService.NavigateAsync(PageType.Feedback, args);
            return;
        }

        var flashingCarPageArguments = new FlashingCarPageNavigationArgs()
        {
            ContinousCommand = _restoreBackupCommand,
            WarningInfos = FlashingCarWarningInfo.GetWarningInfosForRestore()
        };
        await _navigationService.NavigateAsync(PageType.FlashingCarWarningInfo, flashingCarPageArguments);
    }

    private async void RestoreCoding(object obj)
    {
        if (Executing)
            return;

        var flashingCarPageArguments = new FlashingCarPageNavigationArgs()
        {
            ContinousCommand = _restoreCodingCommand
        };
        await _navigationService.NavigateAsync(PageType.FlashingCar, flashingCarPageArguments);
    }

    private async void ReadModule(object obj)
    {
        if (Executing)
            return;

        await _navigationService.NavigateAsync(PageType.Pending, new PendingPageNavigationArgs()
        {
            Text = AppResources.ReadModule_InProgress,
            NextPage = PageType.ReadModule,
            BackPage = PageType.MyCar
        });

        await Task.Run(async () =>
        {
            try
            {
                await _readModuleCommand.ExecuteAsync();
                var result = _readModuleCommand.GetResult();
                Application.Current.Dispatcher.Dispatch(async () =>
                {
                    if (result.Result != CommandResultType.Completed)
                    {
                        await _userDialogs.AlertAsync(result);
                    }
                    WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageCloseNavigation));
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error!");
                var result = new CommandResult()
                {
                    Message = $"{ex.GetType().Name}({ex.Message})",
                    Result = CommandResultType.Error
                };
                Application.Current.Dispatcher.Dispatch(async () =>
                {
                    await _userDialogs.AlertAsync(result);
                    WeakReferenceMessenger.Default.Send(
                        new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers
                            .PendingPageCloseNavigation));
                });
            }
        });
    }

    private async void ReadPartialEcu(object obj)
    {
        if (Executing)
            return;

        var car = await _dCANService.GetConnectedCar();

        if (car is null)
        {
            await _userDialogs.AlertAsync(AppResources.Flash_SelectCar, okText: AppResources.Ok);
            await _userDialogs.AlertAsync(AppResources.ReadEcu_Error_Account_Permissions, okText: AppResources.Ok);
            return;
        }

        if (car.AnyEcuActivated && !_appContext.IsDeveloper())
        {
            await _userDialogs.AlertAsync(AppResources.ReadEcu_Error_Activated, okText: AppResources.Ok);
            return;
        }
        if (!await _userDialogs.ConfirmAsync(AppResources.ReadEcu_Warning, okText: AppResources.Ok, cancelText: AppResources.Cancel))
        {
            return;
        }
        await _navigationService.NavigateAsync(PageType.Pending, new PendingPageNavigationArgs()
        {
            Text = AppResources.ReadEcu_InProgress,
            NextPage = PageType.MyCar,
            BackPage = PageType.MyCar
        });

        await Task.Run(async () =>
        {
            try
            {
                await _readPartialEcuCommand.ExecuteAsync();
                var result = _readPartialEcuCommand.GetResult();
                Application.Current.Dispatcher.Dispatch(async () =>
                {
                    if (result.Result == CommandResultType.Completed)
                    {
                        result.Message = AppResources.ReadEcu_Success;
                        await _userDialogs.AlertAsync(result);
                    }
                    else
                    {
                        result = new CommandResult()
                        {
                            Message = $"Error reading ECU binary",
                            Result = CommandResultType.Error
                        };
                        await _userDialogs.AlertAsync(result);
                    }
                    WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageCloseNavigation));
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error!");
                var result = new CommandResult()
                {
                    Message = $"{ex.GetType().Name}({ex.Message})",
                    Result = CommandResultType.Error
                };
                Application.Current.Dispatcher.Dispatch(async () =>
                {
                    await _userDialogs.AlertAsync(result);
                    WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageCloseNavigation));
                });
            }
        });
    }

    private async void ReadEcuCalibration(object obj)
    {
        if (Executing)
            return;

        var car = await _dCANService.GetConnectedCar();

        if (car is null)
        {
            await _userDialogs.AlertAsync(AppResources.Flash_SelectCar, okText: AppResources.Ok);
            await _userDialogs.AlertAsync(AppResources.ReadEcu_Error_Account_Permissions, okText: AppResources.Ok);
            return;
        }

        if (!_appContext.IsDeveloper())
        {
            if (car.AnyEcuActivated)
            {
                await _userDialogs.AlertAsync(AppResources.ReadEcu_Error_Activated, okText: AppResources.Ok);
                return;
            }
            if (!await _userDialogs.ConfirmAsync(AppResources.ReadEcu_Warning, okText: AppResources.Ok, cancelText: AppResources.Cancel))
            {
                return;
            }
        }

        await _navigationService.NavigateAsync(PageType.Pending, new PendingPageNavigationArgs()
        {
            Text = AppResources.ReadEcu_InProgress,
            NextPage = PageType.MyCar,
            BackPage = PageType.MyCar
        });

        await Task.Run(async () =>
        {
            try
            {
                await _readEcuCalibrationCommand.ExecuteAsync();
                var result = _readEcuCalibrationCommand.GetResult();
                Application.Current.Dispatcher.Dispatch(async () =>
                {
                    if (result.Result == CommandResultType.Completed)
                    {
                        result.Message = AppResources.ReadEcu_Success;
                        await _userDialogs.AlertAsync(result);
                    }
                    else
                    {
                        result = new CommandResult()
                        {
                            Message = $"Error reading ECU binary",
                            Result = CommandResultType.Error
                        };
                        await _userDialogs.AlertAsync(result);
                    }
                    WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageCloseNavigation));
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error!");
                var result = new CommandResult()
                {
                    Message = $"{ex.GetType().Name}({ex.Message})",
                    Result = CommandResultType.Error
                };
                Application.Current.Dispatcher.Dispatch(async () =>
                {
                    await _userDialogs.AlertAsync(result);
                    WeakReferenceMessenger.Default.Send(
                        new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers
                            .PendingPageCloseNavigation));
                });
            }
        });
    }

    private FeedbackPageNavigationArgs CreateFeedbackNavigationArgs()
    {
        var args = new FeedbackPageNavigationArgs();

        args.SystemSubject = SystemFeedbackSubject.StockMapSupportRequest;
        args.Subject = "Stock files are missing";
        args.Message = "I would like to flash my ECU to full stock, but files are missing.";

        return args;
    }

    private async Task Loading(string message, Task action)
    {
        try
        {
            Executing = true;

            using (_userDialogs.Loading(message))
            {
                await action;
            }
        }
        finally
        {
            Executing = false;
        }
    }
}