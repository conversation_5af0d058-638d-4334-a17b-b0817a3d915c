﻿using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.FlashCarAllowance;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.Purchases;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.Logger;
using MgFlasher.Views.Logger;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Devices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarPageViewModel : PageViewModel, IContextOptionsViewModel, ITabsComponent
{
    private readonly ICarRepository _carsService;
    private readonly IUserDialogs _userDialogs;
    private readonly ILogger<MyCarPageViewModel> _logger;
    private readonly INavigationService _navigationService;
    private readonly IFlashCarAllowanceService _flashCarAllowanceService;
    private readonly IAppContext _appContext;
    private readonly IPurchaseService _purchaseService;
    private readonly IDCANService _dCANService;
    private int _selectedTabIndex;
    private PageType _previousPage, _nextPage;
    private object _pageArg;
    private Car _connectedCar;
    private IReadOnlyList<InAppPurchase> _purchases;
    private ITabComponent _currentTab;

    public int SelectedTabIndex
    {
        get => _selectedTabIndex;
        set => SetProperty(ref _selectedTabIndex, value);
    }

    public double TabBarHeight => GetTabBarHeight();
    public bool NavBarIsVisible => _currentTab is not MyCarLoggerViewModel { LoggerStarted: true };

    public IList<ITabComponent> Tabs => new List<ITabComponent> { Home, Logger, Flash, History, Diagnostics };
    public MyCarHomeViewModel Home { get; }
    public MyCarLoggerViewModel Logger { get; }
    public MyCarFlashViewModel Flash { get; }
    public MyCarHistoryViewModel History { get; }
    public MyCarDiagnosticsViewModel Diagnostics { get; }
    public Dictionary<string, Action> ContextOptions => _currentTab is IContextOptionsViewModel vm ? vm.ContextOptions : new Dictionary<string, Action>();

    public MyCarPageViewModel(
        INavigationService navigationService,
        IFlashCarAllowanceService flashCarAllowanceService,
        IAppContext appContext,
        IPurchaseService purchaseService,
        IDCANService dCANService,
        ICarRepository carsService,
        IUserDialogs userDialogs,
        ILogger<MyCarPageViewModel> logger,
        MyCarHomeViewModel home,
        MyCarHistoryViewModel history,
        MyCarDiagnosticsViewModel diagnostics,
        MyCarFlashViewModel flash,
        MyCarLoggerViewModel loggerViewModel)
    {
        _navigationService = navigationService;
        _flashCarAllowanceService = flashCarAllowanceService;
        _appContext = appContext;
        _purchaseService = purchaseService;
        _dCANService = dCANService;
        _carsService = carsService;
        _userDialogs = userDialogs;
        _logger = logger;
        _currentTab = home;
        Home = home;
        History = history;
        Diagnostics = diagnostics;
        Flash = flash;
        Logger = loggerViewModel;
        InitTabs();
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _previousPage = previousPage;
        _pageArg = arg;
        _connectedCar = await _dCANService.GetConnectedCar();
        _purchases = await _purchaseService.GetPurchases(_connectedCar.Fingerprint);

        await _currentTab.OnNavigatedToAsync(_previousPage, null, _pageArg);
        OnPropertyChanged(nameof(ContextOptions));
    }

    public override async Task OnNavigatedFromAsync(PageType nextPage)
    {
        _nextPage = nextPage;
        if (_currentTab is not null)
        {
            await _currentTab.OnNavigatedFromAsync(nextPage, null);
        }
    }

    public async Task OnNavigatedToTabAsync(ITabComponent next)
    {
        if (_currentTab == next)
        {
            return;
        }

        var old = _currentTab;
        if (old is not null)
        {
            await _currentTab.OnNavigatedFromAsync(null, next);
        }

        _currentTab = next;
        await next.OnNavigatedToAsync(null, old, _pageArg);
        OnPropertyChanged(nameof(ContextOptions));
        OnPropertyChanged(nameof(NavBarIsVisible));
    }

    public (bool CanSelect, Func<Task> canSelectUserAction) CanSelect(ITabComponent tab) => tab switch
    {
        _ when !_connectedCar.Supported.HasValue => (false, PleaseCheckSupport),
        MyCarLoggerViewModel when !_purchases.HasLoggerLicense(_appContext) => (false, BuyLoggerItem),
        _ when Logger.LoggingToFile => (false, null),
        _ => (true, null)
    };

    private async Task PleaseCheckSupport()
    {
        await _userDialogs.AlertAsync(AppResources.MyCar_Please_CheckSupport);
        await _navigationService.NavigateToTabbedComponent<MyCarHomeOverviewViewModel>(PageType.MyCar);
    }

    private async Task BuyLoggerItem()
    {
        try
        {
            IsBusy = true;

            var items = await _flashCarAllowanceService.GetFlashAllowanceItems(_connectedCar);
            var loggerFlashAllowanceItem = items.FirstOrDefault(x => x.ProductType == FlashAllowanceProductType.Logger);
            if (loggerFlashAllowanceItem is null)
            {
                await _userDialogs.AlertAsync(AppResources.Logger_NotAvailable_ForYourCar, okText: AppResources.Ok);
                return;
            }

            var vm = DependencyResolver.Resolve<BuyFlashStagePageViewModel>();
            await vm.OnNavigatedToAsync(PageType.MyCar, loggerFlashAllowanceItem);
            await vm.Submit();

            _purchases = await _purchaseService.GetPurchases(_connectedCar.Fingerprint);
        }
        finally
        {
            IsBusy = false;
        }
    }

    private double GetTabBarHeight()
    {
        if (DeviceInfo.Platform == DevicePlatform.WinUI)
        {
            return 48d;
        }

        return 55d;
    }

    private void InitTabs()
    {
        Diagnostics.PropertyChanged += (o, e) =>
        {
            if (e.PropertyName == nameof(Diagnostics.General.Executing))
            {
                IsBusy = Diagnostics.General.Executing;
            }

            if (e.PropertyName == nameof(Diagnostics.Custom.Executing))
            {
                IsBusy = Diagnostics.Custom.Executing;
            }
        };
        History.PropertyChanged += (o, e) =>
        {
            if (e.PropertyName == nameof(History.ContextOptions))
            {
                OnPropertyChanged(nameof(ContextOptions));
            }
        };
        Home.PropertyChanged += (o, e) =>
        {
            if (e.PropertyName == nameof(History.ContextOptions))
            {
                OnPropertyChanged(nameof(ContextOptions));
            }
        };
        Logger.PropertyChanged += (o, e) =>
        {
            if (e.PropertyName == nameof(Logger.LoggerStarted))
            {
                OnPropertyChanged(nameof(NavBarIsVisible));
            }
        };
    }
}