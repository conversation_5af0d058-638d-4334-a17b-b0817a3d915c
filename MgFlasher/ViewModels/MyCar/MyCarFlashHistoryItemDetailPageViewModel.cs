﻿using MgFlasher.Converters;
using MgFlasher.Flasher.Services.Cars.FlashHistory;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Models;
using System;
using System.Linq;
using System.Threading.Tasks;
using UNI_Flash;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarFlashHistoryItemDetailPageViewModel : PageViewModel
{
    private readonly StageTypeToTextConverter _stageTypeToTextConverter = new();
    private FlashHistoryItem _model = new() { StageType = StageType.None };

    public string Stage => (string)_stageTypeToTextConverter.Convert(_model.StageType, null, null, null);

    public string VersionName => _model.VersionName;

    public string VersionNumber => _model.VersionNumber;

    public string CustomCodeVersion => _model.CustomCodeVersion;

    public string A2lString => _model.A2Lstring;

    public string HWEL => _model?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.HWEL)?.GetXweString();
    public string HWAP => _model?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.HWAP)?.GetXweString();
    public string BTLD => _model?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.BTLD)?.GetXweString();
    public string SWFL => _model?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.SWFL)?.GetXweString();
    public string SWFK => _model?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.SWFK)?.GetXweString();
    public string CAFD => _model?.SweVersions?.FirstOrDefault(x => x.ProcessClass == ProcessClassType.CAFD)?.GetXweString();

    public string StartedAt => _model.StartedAtUtc.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss");

    public string FinishedAt =>
        _model.FinishedAtUtc > DateTime.MinValue ? _model.FinishedAtUtc.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss") : null;

    public string Id => _model.Id;
    public bool Synced => _model.Synced;
    public bool Success => _model.Success;
    public string Details => _model.Details?[..Math.Min(150, _model.Details.Length)];
    public string UserEmail => _model.UserEmail;

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _model = (FlashHistoryItem)arg;

        OnPropertyChanged(nameof(Stage));
        OnPropertyChanged(nameof(VersionName));
        OnPropertyChanged(nameof(VersionNumber));
        OnPropertyChanged(nameof(CustomCodeVersion));
        OnPropertyChanged(nameof(StartedAt));
        OnPropertyChanged(nameof(FinishedAt));
        OnPropertyChanged(nameof(Id));
        OnPropertyChanged(nameof(Synced));
        OnPropertyChanged(nameof(Details));
        OnPropertyChanged(nameof(UserEmail));

        return base.OnNavigatedToAsync(previousPage, arg);
    }
}