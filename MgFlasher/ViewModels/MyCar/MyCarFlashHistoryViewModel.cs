﻿using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.FlashHistory;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarFlashHistoryViewModel : BaseViewModel, ITabComponent
{
    private ObservableCollection<MyCarFlashHistoryItemViewModel> _items = new();
    private readonly IDCANService _dCANService;
    private readonly IFlashHistoryRepository _flashHistoryRepository;
    private readonly INavigationService _navigationService;

    public ObservableCollection<MyCarFlashHistoryItemViewModel> Items
    {
        get => _items;
        private set => SetProperty(ref _items, value);
    }

    public MyCarFlashHistoryViewModel(
        IDCANService dCANService,
        IFlashHistoryRepository flashHistoryRepository,
        INavigationService navigationService)
    {
        _dCANService = dCANService;
        _flashHistoryRepository = flashHistoryRepository;
        _navigationService = navigationService;
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        var car = await _dCANService.GetConnectedCar();
        var history = await _flashHistoryRepository.GetFlashHistory(car.Fingerprint);
        Items = new(history
            .OrderByDescending(x => x.StartedAtUtc)
            .ThenByDescending(x => x.FinishedAtUtc)
            .Select((x, i) => new MyCarFlashHistoryItemViewModel(i + 1, x, _navigationService)));
    }
}