﻿using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.Logger;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarHistoryViewModel : BaseViewModel, ITabComponent, IContextOptionsViewModel, ITabsComponent
{
    private ITabComponent _currentTab;
    private PageType? _previousPage;
    private object _pageArg;
    private int _selectedTabIndex;

    public IList<ITabComponent> Tabs => new List<ITabComponent> { FlashHistory, LoggerFiles };
    public MyCarFlashHistoryViewModel FlashHistory { get; }
    public MyCarLoggerFilesViewModel LoggerFiles { get; }
    public Dictionary<string, Action> ContextOptions => _currentTab is IContextOptionsViewModel vm ? vm.ContextOptions : new Dictionary<string, Action>();

    public int SelectedTabIndex
    {
        get => _selectedTabIndex;
        set => SetProperty(ref _selectedTabIndex, value);
    }

    public bool IsLoggerButtonVisible => _currentTab is MyCarLoggerFilesViewModel;

    public MyCarHistoryViewModel(MyCarFlashHistoryViewModel flashHistory, MyCarLoggerFilesViewModel loggerFiles)
    {
        FlashHistory = flashHistory;
        LoggerFiles = loggerFiles;
        _currentTab = FlashHistory;
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        _previousPage = previousPage;
        _pageArg = arg;

        await _currentTab.OnNavigatedToAsync(_previousPage, previousPart, _pageArg);
        OnPropertyChanged(nameof(ContextOptions));
    }

    public async Task OnNavigatedToTabAsync(ITabComponent next)
    {
        if (_currentTab == next)
        {
            return;
        }

        var old = _currentTab;
        if (old is not null)
        {
            await _currentTab.OnNavigatedFromAsync(null, next);
        }

        _currentTab = next;
        await next.OnNavigatedToAsync(null, old, _pageArg);
        OnPropertyChanged(nameof(ContextOptions));
        OnPropertyChanged(nameof(IsLoggerButtonVisible));
    }
}