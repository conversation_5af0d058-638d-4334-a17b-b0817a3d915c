﻿using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarsOverviewViewModel : BaseViewModel
{
    private static readonly ImageSource DefaultCarPicture = ImageSource.FromFile("mycar60.png");
    private readonly Car _car;
    private readonly IDCANService _dCANService;
    private readonly IUserDialogs _userDialogs;
    private readonly ICarRepository _carRepository;
    private readonly INavigationService _navigationService;
    private readonly ILogger _logger;
    private readonly Action<Car> _onDeleteCallback;

    public string UserCarName => $"{(!string.IsNullOrEmpty(_car.UserCarName) ? _car.UserCarName : _car.GetUserCarName())}";
    public string Fingerprint => _car.Fingerprint;
    public string VIN => _car.VIN;
    public ImageSource Image { get; }

    public ICommand ViewCommand { get; }
    public ICommand MiscCommand { get; }

    public MyCarsOverviewViewModel(Car car, IDCANService dCANService,
        IUserDialogs userDialogs, ICarRepository carRepository,
        INavigationService navigationService,
        ILogger logger,
        Action<Car> onDeleteCallback,
        byte[] image)
    {
        _car = car;
        _dCANService = dCANService;
        _userDialogs = userDialogs;
        _carRepository = carRepository;
        _navigationService = navigationService;
        _logger = logger;
        _onDeleteCallback = onDeleteCallback;
        ViewCommand = new TraceableCommand(View, nameof(View));
        MiscCommand = new TraceableCommand(Misc, nameof(Misc));
        Image = image?.Length > 0 ? ImageSource.FromStream(() => new MemoryStream(image)) : DefaultCarPicture;
    }

    private async void Misc(object obj)
    {
        var result = await _userDialogs.ActionSheetAsync(AppResources.ChooseCarAction, AppResources.Cancel, null, null, [
            AppResources.MyCars_SyncCar,
            AppResources.MyCars_RenameCar,
            AppResources.MyCars_DeleteCar,
        ]);

        if (result.Contains(AppResources.MyCars_RenameCar))
        {
            var car = await _carRepository.GetCarByIdAsync(_car.Fingerprint);
            await UpdateUserCarName(car);
        }
        else if (result.Contains(AppResources.MyCars_DeleteCar))
        {
            var confirmation = await _userDialogs.ConfirmAsync(message: AppResources.MyCars_ConfirmDelete, cancelText: AppResources.Cancel);
            if (!confirmation)
                return;
            if (_car.Fingerprint == _dCANService.ConnectedCarFingerprint)
                _dCANService.ChangeConnectedCar(null);
            await _carRepository.DeleteCarAsync(_car.Fingerprint, _car.VIN);
            _onDeleteCallback?.Invoke(_car);
        }
        else if (result.Contains(AppResources.MyCars_SyncCar))
        {
            _dCANService.ChangeConnectedCar(_car.Fingerprint);
            await _navigationService.NavigateAsync(PageType.SyncPage);
        }
    }

    private async void View(object obj)
    {
        _dCANService.ChangeConnectedCar(_car.Fingerprint);
        await _navigationService.NavigateToTabbedComponent<MyCarHomeOverviewViewModel>(PageType.MyCar);
    }

    private async Task<string> UpdateUserCarName(Car car)
    {
        var promptResult = await _userDialogs.PromptAsync(new PromptConfig
        {
            OkText = AppResources.Ok,
            Title = AppResources.MyCar_PromptUserCarName,
            Text = car.UserCarName,
        });

        if (promptResult.Ok && !string.IsNullOrEmpty(promptResult.Text))
        {
            _logger.LogInformation("User entered a new car name: '{car.UserCarName}' -> '{promptResult.Value}'", car.UserCarName, promptResult.Value);
            car.UserCarName = promptResult.Value;
            await _carRepository.SaveUserCarNameAsync(car.Fingerprint, car.UserCarName);
            OnPropertyChanged(nameof(UserCarName));
        }
        return promptResult.Value;
    }
}