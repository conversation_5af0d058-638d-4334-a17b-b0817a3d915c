﻿using MgFlasher.Converters;
using MgFlasher.Ecu.Common.Extensions;
using MgFlasher.Flasher.Services.Cars.FlashHistory;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using System.Linq;
using System.Text;
using System.Windows.Input;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarFlashHistoryItemViewModel : IFilterableViewModel
{
    private readonly FlashHistoryItem _model;

    public string Name => GetTitle();
    public bool Success => _model.Success;
    public bool Synced => _model.Synced;
    public string CreatedAt => _model.StartedAtUtc.ToLocalTime().ToString("yyyy-MM-dd HH:mm");
    public ICommand GoToDetailsCommand { get; }
    public ICommand GoToSyncCommand { get; }
    public string DisplayName => Name;
    public string AutomationId { get; }

    public MyCarFlashHistoryItemViewModel(int order, FlashHistoryItem model, INavigationService navigationService)
    {
        _model = model;
        AutomationId = $"FlashingHistoryPage_GoToDetails_Button_{order}";
        GoToDetailsCommand = new TraceableCommand(async () => await navigationService.NavigateAsync(PageType.MyCarFlashHistoryItemDetail, _model), "GoToDetails");
        GoToSyncCommand = new TraceableCommand(async () => await navigationService.NavigateAsync(PageType.SyncPage), "GoToSync");
    }

    private string GetTitle()
    {
        var stageTypeTextConverter = new StageTypeToTextConverter();
        var sb = new StringBuilder();
        sb.Append(stageTypeTextConverter.Convert(_model.StageType, null, null, null));
        if (_model.StageType.In(StageType.Stage1, StageType.Stage2, StageType.Stage2_5, StageType.Stage3))
        {
            if (!string.IsNullOrEmpty(_model.VersionName))
            {
                sb.Append(" " + _model.VersionName);
            }

            if (!string.IsNullOrEmpty(_model.VersionNumber))
            {
                sb.Append(" v" + _model.VersionNumber);
            }
        }

        if (_model.ChangesetPatchIds?.Any() ?? false)
        {
            sb.Append(" P" + string.Join("|", _model.ChangesetPatchIds));
        }

        if (_model.StageType == StageType.Custom && _model.Success && !string.IsNullOrEmpty(_model.Details))
        {
            sb.Append(" " + _model.Details.Trim());
        }

        if (!string.IsNullOrEmpty(_model.CustomCodeVersion))
        {
            sb.Append(" CC" + _model.CustomCodeVersion);
        }

        return sb.ToString();
    }
}