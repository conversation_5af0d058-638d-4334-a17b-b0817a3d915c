﻿using MgFlasher.Models;
using System.Threading.Tasks;
using System.Collections.Generic;
using MgFlasher.Services.Navigation;
using MgFlasher.Flasher.Services.Purchases;
using MgFlasher.Flasher.Services.Cars;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarDiagnosticsViewModel : BaseViewModel, ITabsComponent, ITabComponent
{
    private readonly IPurchaseService _purchaseService;
    private readonly IDCANService _dCANService;
    private ITabComponent _currentTab;
    private PageType? _previousPage;
    private object _pageArg;
    private int _selectedTabIndex;
    private bool _hasFlasherLicense;

    public IList<ITabComponent> Tabs => new List<ITabComponent>() { General, Custom };
    public MyCarDiagnosticsGeneralViewModel General { get; }
    public MyCarDiagnosticsCustomViewModel Custom { get; }

    public int SelectedTabIndex
    {
        get => _selectedTabIndex;
        set => SetProperty(ref _selectedTabIndex, value);
    }

    public bool HasFlasherLicense
    {
        get => _hasFlasherLicense;
        private set => SetProperty(ref _hasFlasherLicense, value);
    }

    public MyCarDiagnosticsViewModel(
        MyCarDiagnosticsGeneralViewModel general,
        MyCarDiagnosticsCustomViewModel custom,
        IPurchaseService purchaseService,
        IDCANService dCANService)
    {
        _currentTab = general;
        General = general;
        Custom = custom;
        _purchaseService = purchaseService;
        _dCANService = dCANService;
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        _previousPage = previousPage;
        _pageArg = arg;

        var car = await _dCANService.GetConnectedCar();
        var purchases = await _purchaseService.GetPurchases(car.Fingerprint);
        HasFlasherLicense = purchases.HasFlasherLicense();

        await _currentTab.OnNavigatedToAsync(_previousPage, previousPart, _pageArg);
    }

    public async Task OnNavigatedToTabAsync(ITabComponent next)
    {
        if (_currentTab == next)
            return;
        var old = _currentTab;
        if (old is not null)
            await _currentTab.OnNavigatedFromAsync(null, next);
        _currentTab = next;
        await next.OnNavigatedToAsync(null, old, _pageArg);
    }
}