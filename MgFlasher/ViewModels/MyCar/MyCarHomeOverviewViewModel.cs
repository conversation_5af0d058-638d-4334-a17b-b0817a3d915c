﻿using CommunityToolkit.Mvvm.Messaging;
using MgFlasher.Changesets.SupportedEcu.Models;
using MgFlasher.Flasher.Commands;
using MgFlasher.Flasher.Commands.Models;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.Cars.FlashHistory;
using MgFlasher.Flasher.Services.Cars.Sync;
using MgFlasher.Flasher.Services.Cars.Sync.Chains;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.Purchases;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Flasher.Services.User.Feedback;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Devices;
using Microsoft.Maui.Dispatching;
using Microsoft.Maui.Storage;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using UNI_Flash;

namespace MgFlasher.ViewModels.MyCar;

public class MyCarHomeOverviewViewModel : BaseViewModel, ITabComponent, IContextOptionsViewModel
{
    private static readonly ImageSource DefaultCarPicture = ImageSource.FromFile("mycar_1artwork.png");
    private readonly IPicturePicker _picturePicker;
    private readonly ICarRepository _carsService;
    private readonly ICarCompatabilityService _carCompatabilityService;
    private readonly IPurchaseService _inAppPurchasesPurchasesAccessor;
    private readonly IFilesService _filesService;
    private readonly IFlashHistoryRepository _flashHistoryService;
    private readonly IUserDialogs _userDialogs;
    private readonly ILogger<MyCarPageViewModel> _logger;
    private readonly INavigationService _navigationService;
    private readonly ISyncChainExecutor _syncChain;
    private readonly IAppContext _appContext;
    private readonly FullSyncChainFactory _syncChainFactory;
    private readonly ShellTitleViewModel _shellTitleViewModel;
    private readonly IDCANService _dCANService;

    private Car _connectedCar;
    private string _customCodeText;
    private string _engineString;
    private string _customBinaryText;
    private string _stageTypeText;
    private string _otsMapText;
    private StageType? _currentStage;
    private bool _customCodeUpdateAvailable;
    private bool _changesetUpdateAvailable;
    private bool _showLastFlashedOtsMap;
    private bool _showSwitchableMapSlotInfo;
    private bool _isActive;
    private bool _contextButtonVisible;
    private bool _emergencyMode;
    private bool _customBinaryVisible;
    private SecurityColourType _securityColour;
    private string _securityWaveLevel;
    private bool _showEngineStrings;
    private bool _showEngineDetailsStrings;
    private bool _showUnlockStatus;
    private ImageSource _carPicture = DefaultCarPicture;
    private string _switchableMapsSlotDescriptionList;
    private string _latestCustomCodeVersion;
    private string _userCarName;
    private bool _shouldCheckSupport;

    public ICommand EnterUserCarNameCommand { get; }
    public ICommand OpenGalleryCommand { get; }
    public ICommand DisplaySwitchableMapsSlotDescriptionListCommand { get; }
    public ICommand CheckSupportCommand { get; }
    public ICommand ShowUnlockWaveTooltipCommand { get; }

    public ImageSource CarPicture { get => _carPicture; set => SetProperty(ref _carPicture, value); }

    public Car ConnectedCar
    {
        get => _connectedCar;
        private set => SetProperty(ref _connectedCar, value);
    }

    public string CustomCodeText { get => _customCodeText; private set => SetProperty(ref _customCodeText, value); }
    public string EngineString { get => _engineString; private set => SetProperty(ref _engineString, value); }
    public string CustomBinaryText { get => _customBinaryText; private set => SetProperty(ref _customBinaryText, value); }
    public string StageTypeText { get => _stageTypeText; private set => SetProperty(ref _stageTypeText, value); }
    public string OtsMapText { get => _otsMapText; private set => SetProperty(ref _otsMapText, value); }
    public StageType? CurrentStage { get => _currentStage; private set => SetProperty(ref _currentStage, value); }
    public bool CustomCodeUpdateAvailable { get => _customCodeUpdateAvailable; private set => SetProperty(ref _customCodeUpdateAvailable, value); }
    public bool ChangesetUpdateAvailable { get => _changesetUpdateAvailable; private set => SetProperty(ref _changesetUpdateAvailable, value); }
    public bool ShowLastFlashedOtsMap { get => _showLastFlashedOtsMap; private set => SetProperty(ref _showLastFlashedOtsMap, value); }
    public bool ShowSwitchableMapSlotInfo { get => _showSwitchableMapSlotInfo; private set => SetProperty(ref _showSwitchableMapSlotInfo, value); }
    public string SwitchableMapsSlotDescriptionList { get => _switchableMapsSlotDescriptionList; private set => SetProperty(ref _switchableMapsSlotDescriptionList, value); }
    public bool IsActive { get => _isActive; private set => SetProperty(ref _isActive, value); }
    public bool EmergencyMode { get => _emergencyMode; private set => SetProperty(ref _emergencyMode, value); }
    public bool CustomBinaryVisible { get => _customBinaryVisible; private set => SetProperty(ref _customBinaryVisible, value); }
    public SecurityColourType SecurityColour { get => _securityColour; private set => SetProperty(ref _securityColour, value); }
    public string SecurityWaveLevel { get => _securityWaveLevel; private set => SetProperty(ref _securityWaveLevel, value); }
    public bool ShowEngineStrings { get => _showEngineStrings; private set => SetProperty(ref _showEngineStrings, value); }
    public bool ShowEngineDetailsStrings { get => _showEngineDetailsStrings; private set => SetProperty(ref _showEngineDetailsStrings, value); }
    public bool ShowUnlockStatus { get => _showUnlockStatus; private set => SetProperty(ref _showUnlockStatus, value); }
    public bool ContextButtonVisible { get => _contextButtonVisible; private set => SetProperty(ref _contextButtonVisible, value); }

    public string UserCarName
    {
        get => _userCarName;
        set => SetProperty(ref _userCarName, value);
    }

    public bool ShouldCheckSupport
    {
        get => _shouldCheckSupport;
        private set => SetProperty(ref _shouldCheckSupport, value);
    }

    public Dictionary<string, Action> ContextOptions { get; }

    public MyCarHomeOverviewViewModel(
        IDCANService dCANService,
        IPicturePicker picturePicker,
        ICarRepository carsService,
        ICarCompatabilityService carCompatabilityService,
        IPurchaseService inAppPurchasesPurchasesAccessor,
        IFilesService filesService,
        IFlashHistoryRepository flashHistoryService,
        IUserDialogs userDialogs,
        ILogger<MyCarPageViewModel> logger,
        INavigationService navigationService,
        ISyncChainExecutor syncChainExecutor,
        IAppContext appContext,
        FullSyncChainFactory syncChainFactory,
        ShellTitleViewModel shellTitleViewModel)
    {
        _picturePicker = picturePicker;
        _dCANService = dCANService;
        _carsService = carsService;
        _carCompatabilityService = carCompatabilityService;
        _inAppPurchasesPurchasesAccessor = inAppPurchasesPurchasesAccessor;
        _filesService = filesService;
        _flashHistoryService = flashHistoryService;
        _userDialogs = userDialogs;
        _logger = logger;
        _navigationService = navigationService;
        _syncChain = syncChainExecutor;
        _appContext = appContext;
        _syncChainFactory = syncChainFactory;
        _shellTitleViewModel = shellTitleViewModel;
        OpenGalleryCommand = new TraceableCommand(PickAndSetCarPictureAsync, nameof(PickAndSetCarPictureAsync));
        DisplaySwitchableMapsSlotDescriptionListCommand = new TraceableCommand(DisplaySwitchableMapsSlotDescriptionList, nameof(DisplaySwitchableMapsSlotDescriptionList));
        EnterUserCarNameCommand = new TraceableCommand(EnterUserCarName, nameof(EnterUserCarName));
        CheckSupportCommand = new TraceableCommand(CheckSupport, nameof(CheckSupport));
        ShowUnlockWaveTooltipCommand = new TraceableCommand(ShowUnlockWaveTooltip, nameof(ShowUnlockWaveTooltip));
        ContextOptions = new Dictionary<string, Action>
        {
            [AppResources.MyCar_Misc_GoToSyncPage] = async () => await _navigationService.NavigateAsync(PageType.SyncPage)
        };
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        ConnectedCar = await _dCANService.GetConnectedCar();
        await CheckThirdPartyUnlockStatus();
        ShouldCheckSupport = !ConnectedCar.Supported.HasValue;
        UpdateContextButtonVisibility();
        UpdateBtldPatchOptions();
        await UpdateCurrentCustomCode();
        UpdateEngineString();
        UpdateShowUnlockStatus();
        UpdateSecurityColour();
        await UpdateCurrentStage();
        UserCarName = ConnectedCar.GetUserCarName();
        CarPicture = await _carsService.GetCarImageAsync(ConnectedCar.Fingerprint) is byte[] array ? ImageSource.FromStream(() => new MemoryStream(array)) : DefaultCarPicture;

        if (previousPage == PageType.Pending)
        {
            await ShowEngineCodeWarning();
            await ShowChangesetUpdateAvailable();
            await ShowMissingFlashHistoryItemWarning();
        }
    }

    private async void ShowUnlockWaveTooltip()
    {
        try
        {
            var uri = new Uri("https://mgflasher.atlassian.net/wiki/spaces/MGFLASHER/pages/24313943/Unlocking+Activating+the+DME+ECU");
            await Launcher.OpenAsync(uri);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open the web page with Wave Unlock in User Manual");
            await _userDialogs.AlertAsync("If you want to know what this means, check the 'Unlocking/Activating the DME (ECU)' article in our User Manual at MgFlasher.com", okText: "OK");
        }
    }

    private async void CheckSupport()
    {
        var pendingPageArgs = PendingPageNavigationArgs.InProgress(PageType.MyCar);
        await _navigationService.NavigateAsync(PageType.Pending, pendingPageArgs);

        try
        {
            _syncChain.ProgressChanged += OnSyncChainProgressChanged;
            var result = await _syncChain.SyncAsync(_syncChainFactory, new SyncChainLinkContext());
            if (result.Success)
            {
                ConnectedCar = await _dCANService.GetConnectedCar();
                if (ConnectedCar.Supported == false)
                {
                    pendingPageArgs.NextPage = PageType.ReadModule;
                    pendingPageArgs.NextPageArguments = ConnectedCar.ToReadModuleResult(true);
                }
                else if (ConnectedCar.Supported == true)
                {
                    await HandleUpdateCarInfoOnlineResult();
                }

                WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageCloseNavigation));
                return;
            }

            await Application.Current.Dispatcher.DispatchAsync(async () =>
            {
                await _userDialogs.AlertAsync(new CommandResult { Message = result.Error, Result = CommandResultType.Error });
                if (result is FeedbackRequestedSyncResult feedbackRequested)
                {
                    pendingPageArgs.NextPage = PageType.Feedback;
                    pendingPageArgs.NextPageArguments = new FeedbackPageNavigationArgs()
                    {
                        Subject = feedbackRequested.Subject,
                        Message = feedbackRequested.Description,
                        SystemSubject = SystemFeedbackSubject.StockMapSupportRequest
                    };
                    WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageCloseNavigation));
                    return;
                }

                WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageBackNavigation));
            });
        }
        finally
        {
            _syncChain.ProgressChanged -= OnSyncChainProgressChanged;
        }

        void OnSyncChainProgressChanged(object sender, SyncChainProgressEventArgs args)
        {
            WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageUpdateText, new PendingPageUpdateArgs(args.CurrentLinkTitle, args.Progress)));
        }
    }

    private async Task HandleUpdateCarInfoOnlineResult()
    {
        if (ConnectedCar.AnyEcuHasNewBtldSecurity && !ConnectedCar.Activated)
        {
            await HandleInactiveConnectedCarNewBtldSecurity();
        }
        else
        {
            if (ConnectedCar.AnyEcuHasThirdPartyUnlock && !ConnectedCar.AllEcuWave3Unlocked)
            {
                await _userDialogs.AlertAsync(AppResources.MyCar_CheckSupport_ThirdPartySupported, AppResources.MyCar_CheckSupport_Supported_Title);
            }

            if (await _filesService.Exists(ConnectedCar.VIN, ConnectedCar.Fingerprint, ConnectedCar.StockMap)
                && ConnectedCar.Changesets.Count <= 0)
            {
                await _userDialogs.AlertAsync(AppResources.MyCar_OTSMapsNotFoundMsg, AppResources.MyCar_OTSMapsNotFoundTitle, okText: AppResources.Ok);
            }
            else
            {
                await _userDialogs.AlertAsync(AppResources.MyCar_CheckSupport_Supported, AppResources.MyCar_CheckSupport_Supported_Title);
            }
        }
    }

    private async Task HandleInactiveConnectedCarNewBtldSecurity()
    {
        if (!UniFlasherHelper.SupportedBootctrlVersions(ConnectedCar.EcuMaster.BootctrlVersion, ConnectedCar.ProcessorArchitectureType, checkWave3Support: false))
        {
            var message = string.Format(AppResources.UnsupportedEcuBootctrlVersion, ConnectedCar.EcuMaster.BootctrlVersion);
            _logger.LogInformation("Boot Control:[{message}]", message);

            var viewWave3UnlockOptions = await _userDialogs.ConfirmAsync(message, okText: AppResources.ViewWave3UnlockServiceWebsites, cancelText: AppResources.Continue);
            if (viewWave3UnlockOptions)
            {
                var open1769Website = await _userDialogs.ConfirmAsync(message, okText: AppResources.View1769Website, cancelText: AppResources.Continue);
                if (open1769Website)
                {
                    await Launcher.OpenAsync(new Uri("https://www.instagram.com/17_69"));
                }

                var openFemtoWebsite = await _userDialogs.ConfirmAsync(message, okText: AppResources.ViewFemtoWebsite, cancelText: AppResources.Continue);
                if (openFemtoWebsite)
                {
                    await Launcher.OpenAsync(new Uri("https://www.instagram.com/femtoevo"));
                }
            }
        }
        else if (_appContext.RetailMode == RetailModeEnum.China)
        {
            await _userDialogs.AlertAsync(AppResources.ActivateEcu_Error_NewBtldSecurity);
        }
        else
        {
            var openDealerMap = await _userDialogs.ConfirmAsync(AppResources.ActivateEcu_Error_NewBtldSecurity, okText: AppResources.ViewDealerMap, cancelText: AppResources.Continue);
            if (openDealerMap)
            {
                await Launcher.OpenAsync(new Uri("https://mgflasher.com/dealer-locator"));
            }
        }
    }

    private async void EnterUserCarName()
    {
        var promptResult = await _userDialogs.PromptAsync(new PromptConfig
        {
            OkText = AppResources.Ok,
            Title = AppResources.MyCar_PromptUserCarName,
            Text = UserCarName,
        });

        if (!promptResult.Ok || string.IsNullOrEmpty(promptResult.Text))
        {
            return;
        }

        UserCarName = promptResult.Text;
        if (promptResult.Text != ConnectedCar.GetUserCarName())
        {
            _logger.LogInformation("User entered a new car name: '{car.UserCarName}' -> '{promptResult.Value}'", _connectedCar.UserCarName, promptResult.Value);
            _connectedCar = await _carsService.SaveUserCarNameAsync(_connectedCar.Fingerprint, promptResult.Text);
        }
    }

    private async Task CheckThirdPartyUnlockStatus()
    {
        if (ConnectedCar.AnyEcuHasNewBtldSecurity && ConnectedCar.AnyEcuHasBtldPatchUnknown)
        {
            _logger.LogInformation("Btld patch unknown and ECU has higher security! Informing user...");
            var promptUnlockStatus = AppResources.MyCar_Prompt3rdPartyUnlockStatus;
            if (ConnectedCar.AnyEcuReqiresWave3Unlock)
            {
                promptUnlockStatus = AppResources.MyCar_PromptWave3UnlockStatus;
            }

            var userConfirmation = await _userDialogs.ConfirmAsync(promptUnlockStatus,
                okText: AppResources.Yes,
                cancelText: AppResources.No);
            if (userConfirmation)
            {
                _logger.LogInformation("User accepted risky business! Message: {promptUnlockStatus}", promptUnlockStatus);

                ConnectedCar.EcuMaster.IsBtldPatchUnknown = false;
                ConnectedCar.EcuMaster.IsActive = true;
                ConnectedCar.EcuMaster.UnlockCompany = UnlockCompanyName.Unknown;

                if (UniFlasherHelper.SupportedBootctrlVersions(ConnectedCar.EcuMaster.BootctrlVersion, ConnectedCar.ProcessorArchitectureType, checkWave3Support: true))
                {
                    var unlockName = await _userDialogs.ActionSheetAsync(AppResources.WhichUnlock, AppResources.Cancel, null, null, new string[] {
                        AppResources.Wave3Unlock_Femto,
                        AppResources.Wave3Unlock_1769,
                        AppResources.Wave3Unlock_TAS,
                    });
                    if (!string.IsNullOrWhiteSpace(unlockName))
                    {
                        ConnectedCar.EcuMaster.UnlockCompany = GetUnlockCompanyNameFromString(unlockName);
                    }
                }

                if (ConnectedCar.EcuSlave != null)
                {
                    ConnectedCar.EcuSlave.IsBtldPatchUnknown = false;
                    ConnectedCar.EcuSlave.IsActive = true;
                    if (UniFlasherHelper.SupportedBootctrlVersions(ConnectedCar.EcuSlave.BootctrlVersion, ConnectedCar.ProcessorArchitectureType, checkWave3Support: true))
                    {
                        ConnectedCar.EcuSlave.UnlockCompany = ConnectedCar.EcuMaster.UnlockCompany;
                    }
                }

                await _userDialogs.AlertAsync(AppResources.MyCar_Prompt3rdPartyUnlockStatus_Confirmed, AppResources.Flash_Precheck_UnknownBTLD_Title, okText: AppResources.Ok);
            }
            else
            {
                _logger.LogInformation("User not certain of unlock! Message: {promptUnlockStatus}", promptUnlockStatus);
                ConnectedCar.EcuMaster.IsActive = false;
                if (ConnectedCar.EcuSlave != null)
                {
                    ConnectedCar.EcuSlave.IsActive = false;
                }

                await _userDialogs.AlertAsync(AppResources.MyCar_Prompt3rdPartyUnlockStatus_Denied, AppResources.Flash_Precheck_UnknownBTLD_Title, okText: AppResources.Ok);
            }

            await _carsService.SaveCarAsync(ConnectedCar);
        }

        IsActive = ConnectedCar.Activated;
    }

    private UnlockCompanyName GetUnlockCompanyNameFromString(string unlockName)
    {
        if (unlockName == AppResources.Wave3Unlock_Femto)
        {
            return UnlockCompanyName.FEMTO;
        }

        if (unlockName == AppResources.Wave3Unlock_1769)
        {
            return UnlockCompanyName._1769;
        }

        if (unlockName == AppResources.Wave3Unlock_TAS)
        {
            return UnlockCompanyName.TAS;
        }

        return UnlockCompanyName.Undefined;
    }

    private async Task ShowEngineCodeWarning()
    {
        if (string.IsNullOrEmpty(ConnectedCar.EngineCode) && !ConnectedCar.AnyEcuInBootMode)
        {
            await _userDialogs.AlertAsync(AppResources.FlashCarStepsManagerPage_MissingEngineCodeWarning, okText: AppResources.Ok);
        }
    }

    private async Task ShowMissingFlashHistoryItemWarning()
    {
        if (string.IsNullOrEmpty(ConnectedCar.LastSucceddedFlashHistoryItemId) || !ConnectedCar.Supported.HasValue)
        {
            return;
        }

        var historyItems = await _flashHistoryService.GetFlashHistory(ConnectedCar.Fingerprint);
        if (historyItems.Count > 0 &&
            !historyItems.Any(x => x.Id == ConnectedCar.LastSucceddedFlashHistoryItemId) &&
            await _userDialogs.ConfirmAsync(AppResources.FlashCarStepsManagerPage_MissingFlashHistoryItemWarning, okText: AppResources.FlashCarStepsManagerPage_Sync, cancelText: AppResources.Ask_Later))
        {
            _shellTitleViewModel.ContextOptionsCommand.Execute(null);
        }
    }

    private async Task ShowCustomCodeUpdateAvailable()
    {
        var key = $"{nameof(ShowCustomCodeUpdateAvailable)}-{_latestCustomCodeVersion}-shown";
        if (CustomCodeUpdateAvailable && !Preferences.Get(key, false))
        {
            Preferences.Set(key, true);
            await _userDialogs.AlertAsync(AppResources.MyCarPage_CustomCodeUpdateAvailable, okText: AppResources.Ok);
        }
    }

    private async Task ShowChangesetUpdateAvailable()
    {
        if (ChangesetUpdateAvailable)
        {
            await _userDialogs.AlertAsync(AppResources.MyCarPage_OtsMapUpdateAvailable, okText: AppResources.Ok);
        }
    }

    private async void PickAndSetCarPictureAsync()
    {
        try
        {
            if (DeviceInfo.Platform == DevicePlatform.WinUI)
            {
                return;
            }

            var image = await _picturePicker.GetImageStreamAsync();
            if (image == null)
            {
                return;
            }

            var scaledMs = new MemoryStream(image);
            await _carsService.SetCarImageAsync(ConnectedCar.Fingerprint, scaledMs);
            await Application.Current.Dispatcher.DispatchAsync(() =>
            {
                CarPicture = ImageSource.FromStream(() => scaledMs);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load car picture!");
            await _userDialogs.AlertAsync(AppResources.Car_Picture_FailedToLoad, okText: AppResources.Ok);
        }
    }

    private void UpdateContextButtonVisibility()
    {
        ContextButtonVisible = ConnectedCar.Supported.HasValue;
    }

    private void UpdateShowUnlockStatus()
    {
        if (!ConnectedCar.AnyEcuInBootMode)
        {
            ShowUnlockStatus = true;
        }
        else
        {
            ShowUnlockStatus = false;
        }

        if (ShowUnlockStatus)
        {
            SecurityWaveLevel = $"{GetSecurityWaveText(ConnectedCar.SecurityWave)}{(ConnectedCar.Activated ? StyleUnlockName() : string.Empty)}";
        }
    }

    private string StyleUnlockName()
    {
        if (ConnectedCar.EcuMaster.UnlockCompany == UnlockCompanyName.Undefined
            || ConnectedCar.EcuMaster.UnlockCompany == UnlockCompanyName.Unknown)
        {
            return string.Empty;
        }

        var name = ConnectedCar.EcuMaster.UnlockCompany.ToString().Replace("_", " ").TrimStart();
        if (string.IsNullOrWhiteSpace(name))
        {
            return string.Empty;
        }

        return $" - {name}";
    }

    private string GetSecurityWaveText(UnlockWaveType securityWave)
    {
        return securityWave switch
        {
            UnlockWaveType.Wave1 => AppResources.SecurityWaveLevel_Wave1,
            UnlockWaveType.Wave2 => AppResources.SecurityWaveLevel_Wave2,
            UnlockWaveType.Wave3 => AppResources.SecurityWaveLevel_Wave3,
            _ => AppResources.SecurityWaveLevel_Undefined,
        };
    }

    private void UpdateSecurityColour()
    {
        SecurityColour = SecurityColourType.White;
        if (ConnectedCar.SecurityWave == UnlockWaveType.Wave3)
        {
            SecurityColour = SecurityColourType.Red;
        }
        else if (ConnectedCar.SecurityWave == UnlockWaveType.Wave2)
        {
            SecurityColour = SecurityColourType.Yellow;
        }
        else if (ConnectedCar.SecurityWave == UnlockWaveType.Wave1)
        {
            SecurityColour = SecurityColourType.Green;
        }
        if (ConnectedCar.Activated)
        {
            SecurityColour = SecurityColourType.Green;
        }
    }

    private void UpdateBtldPatchOptions()
    {
        EmergencyMode = ConnectedCar.AnyEcuHasBtldPatchUnknown && !ConnectedCar.AnyEcuInBootMode;
    }

    private void UpdateEngineString()
    {
        if (ConnectedCar.AnyEcuInBootMode)
        {
            ShowEngineStrings = false;
        }
        else
        {
            ShowEngineStrings = true;
            if (!string.IsNullOrEmpty(ConnectedCar.EngineCode)
                && !string.IsNullOrEmpty(ConnectedCar.EngineConfiguration)
                && !string.IsNullOrEmpty(ConnectedCar.EngineDisplacment))
            {
                var generationSuffix = !ConnectedCar.IsBX() ? string.Empty : (ConnectedCar.ProcessorArchitectureType.Equals(ProcessorArchitectureType.PPC) ? " (Gen 1)" : (ConnectedCar.ProcessorArchitectureType.Equals(ProcessorArchitectureType.AURIX) ? " (Gen 2)" : string.Empty)); // B48C 2.0L (Gen 2)
                EngineString = ConnectedCar.EngineCode + " " + ConnectedCar.EngineDisplacment + generationSuffix;
                ShowEngineDetailsStrings = false;
            }
            else
            {
                EngineString = "-";
                ShowEngineDetailsStrings = false;
            }
        }
    }

    private async Task UpdateCurrentCustomCode()
    {
        CustomCodeText = "-";
        if (ConnectedCar != null || ConnectedCar.EcuMaster != null)
        {
            if (ConnectedCar.EcuMaster.CustomCodeVersionInstalled != null && !string.IsNullOrWhiteSpace(ConnectedCar.EcuMaster.CustomCodeVersionInstalled))
            {
                CustomCodeText = ConnectedCar.EcuMaster.CustomCodeVersionInstalled;
            }
        }

        var availableCustomCodes = await _carCompatabilityService.GetCustomCodeOverviewByStockMap(ConnectedCar);
        var nondeprecatedVersions = availableCustomCodes.Where(x => !x.Version.Deprecated).ToList();
        if (!nondeprecatedVersions.Any())
        {
            CustomCodeUpdateAvailable = false;
            return;
        }

        _latestCustomCodeVersion = nondeprecatedVersions.FirstOrDefault()?.Version?.Version;
        if (!string.IsNullOrWhiteSpace(CustomCodeText) && CustomCodeText != "-" && ConnectedCar?.StockMap != null)
        {
            if (!Version.TryParse(_latestCustomCodeVersion.Replace("v", ""), out var latestAvailableVersion) || latestAvailableVersion == null)
            {
                CustomCodeUpdateAvailable = false;
                return;
            }

            if (!Version.TryParse(CustomCodeText.Replace("v", ""), out var currentVersion) || currentVersion == null)
            {
                CustomCodeUpdateAvailable = false;
                return;
            }

            CustomCodeUpdateAvailable = latestAvailableVersion > currentVersion;
        }
        else
        {
            CustomCodeUpdateAvailable = true;
        }
    }

    private async Task UpdateCurrentStage()
    {
        try
        {
            var purchases = await _inAppPurchasesPurchasesAccessor.GetPurchases(ConnectedCar.Fingerprint);
            var lastFlashed = await _flashHistoryService.GetLastSucceddedFlashHistoryItem(ConnectedCar.Fingerprint);

            CurrentStage = lastFlashed?.StageType;
            StageTypeText = GetStageText(purchases, lastFlashed);
            if (ConnectedCar.AnyEcuInBootMode)
            {
                CustomBinaryVisible = false;
                ShowLastFlashedOtsMap = false;
            }

            await CheckForChangesetUpdates(lastFlashed);

            if (ConnectedCar.StockMap != null)
            {
                var slotDescriptions = await _carCompatabilityService.GetSwitchableMapsSlotDescriptionListAsync(ConnectedCar, useLastFlashedChangeset: true);
                SwitchableMapsSlotDescriptionList = string.Join("\n\n", slotDescriptions.ToArray());
            }

            ShowSwitchableMapSlotInfo = ShowLastFlashedOtsMap && (!string.IsNullOrWhiteSpace(SwitchableMapsSlotDescriptionList));
        }
        catch (Exception e)
        {
            _logger.LogError(e, "UpdateCurrentStage error");
        }
    }

    private async void DisplaySwitchableMapsSlotDescriptionList()
    {
        if (ShowSwitchableMapSlotInfo)
        {
            await _userDialogs.AlertAsync(SwitchableMapsSlotDescriptionList, title: AppResources.MyCar_SwitchableMapsSlotTitle, okText: AppResources.Ok);
        }
    }

    private string GetStageText(IEnumerable<InAppPurchase> purchases, FlashHistoryItem _lastFlashed)
    {
        string stageText;
        CustomBinaryVisible = false;
        CustomBinaryText = string.Empty;
        switch (_lastFlashed?.StageType ?? StageType.None)
        {
            case StageType.None:
                stageText = "-";
                ShowLastFlashedOtsMap = false;
                break;

            case StageType.Custom:
                if (!string.IsNullOrEmpty(_lastFlashed?.Details))
                {
                    int firstRowLength = 44;
                    if (_lastFlashed?.Details.Length > firstRowLength)
                    {
                        CustomBinaryText = $"{_lastFlashed?.Details.Substring(0, firstRowLength)}...\r\n{_lastFlashed?.Details[firstRowLength..(int)(_lastFlashed?.Details.Length)]}";
                    }
                    else
                    {
                        CustomBinaryText = $"{_lastFlashed?.Details}";
                    }
                    CustomBinaryVisible = true;
                }
                else
                {
                    CustomBinaryText = string.Empty;
                }

                stageText = AppResources.Stage_CustomMap.ToUpper();
                ShowLastFlashedOtsMap = false;
                break;

            case StageType.Stage1:
                stageText = AppResources.Stage_Stage1.ToUpper();
                ShowLastFlashedOtsMap = true;
                break;

            case StageType.Stage2:
                stageText = AppResources.Stage_Stage2.ToUpper();
                ShowLastFlashedOtsMap = true;
                break;

            case StageType.Stage2_5:
                stageText = AppResources.Stage_Stage2_5.ToUpper();
                ShowLastFlashedOtsMap = true;
                break;

            case StageType.Activated:
            case StageType.Stock:
                stageText = AppResources.Stage_Stock.ToUpper();
                ShowLastFlashedOtsMap = false;
                break;

            default:
                ShowLastFlashedOtsMap = false;
                throw new InvalidOperationException($"Cannot convert [{_lastFlashed?.StageType}] stage to text");
        }

        return stageText;
    }

    private async Task CheckForChangesetUpdates(FlashHistoryItem _lastFlashed)
    {
        if (ShowLastFlashedOtsMap && !string.IsNullOrEmpty(_lastFlashed?.VersionNumber))
        {
            OtsMapText = $"v{_lastFlashed?.VersionNumber}";
            var _currentVersionName = _lastFlashed?.VersionName;
            _logger.LogInformation("OTS was last flashed checking if updates are available...");
            var lastRelease = ConnectedCar.Changesets.GetChangesetsByType((StageType)CurrentStage)
                .Where(v => v.VersionName == _currentVersionName)
                .OrderByDescending(c => Version.Parse(c.VersionNumber))
                .ThenByDescending(c => c.ReleasedAtUtc)
                .FirstOrDefault();

            if (lastRelease != null)
            {
                if (await _flashHistoryService.IsNewerChangeset(ConnectedCar.Fingerprint, lastRelease))
                {
                    OtsMapText += AppResources.MyCar_UpdateAvailable;
                    ChangesetUpdateAvailable = true;
                }
                else
                {
                    OtsMapText += AppResources.MyCar_NoUpdate;
                    ChangesetUpdateAvailable = false;
                }
            }
            else
            {
                ChangesetUpdateAvailable = false;
            }
        }
        else
        {
            _logger.LogInformation("Last flash map is not OTS, not checking if OTS map updates are available!");
            ChangesetUpdateAvailable = false;
            OtsMapText = string.Empty;
        }
    }
}