﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Localization.Resources;
using MgFlasher.Flasher.Services.User;
using MgFlasher.CustomCode.Options.Configuration.User;
using System;

namespace MgFlasher.ViewModels.FlashingCarOptions;

public partial class FlashingCarOptionsPageViewModel : PageViewModel
{
    private async void SetCustomizableDefaults_FlashingSegmentOptions()
    {
        PreventPSTFlash.IsChecked = true; // forces the user to enable for each flash
        ForceFlashPST.IsChecked = false; // forces the user to enable for each flash
        ForceFlashPST_WithCustomCode.IsChecked = false; // forces the user to enable for each flash
        ForceFlashPST_WithoutCustomCode.IsChecked = false; // forces the user to enable for each flash
    }

    private async void SetCustomizableDefaults_MotivReflex()
    {
        MotivReflexDeepIntegration.IsChecked = _customOptions.MotivReflexDeepIntegration;
        MotivReflexCanBusIdInput = _customOptions.MotivReflexCanBusIdInput;
        MotivReflexCanBusIdOutput = _customOptions.MotivReflexCanBusIdOutput;
    }

    private async void SetCustomizableDefaults_HpfpInstalled()
    {
        if (!HpfpInstalled.IsVisible)
        {
            HpfpLambdaEnrichment.IsChecked = false;

            B58TU_Installed.IsChecked = true;

            SpoolPerformance_Fx150_Installed.IsChecked = false;
            SpoolPerformance_Fx170_Installed.IsChecked = false;
            SpoolPerformance_Fx180_Installed.IsChecked = false;
            SpoolPerformance_Fx200_Installed.IsChecked = false;
            SpoolPerformance_Fx350_Installed.IsChecked = false;
            SpoolPerformance_Fx400_Installed.IsChecked = false;
            SpoolPerformance_Fx400X_Installed.IsChecked = false;
            DorchEngineering_Stage1_DS1_Installed.IsChecked = false;
            DorchEngineering_Stage15_DS15_Installed.IsChecked = false;
            DorchEngineering_Stage2_DS2_Installed.IsChecked = false;
            DorchEngineering_Stage25_DS25_250Bar_Installed.IsChecked = false;
            DorchEngineering_Stage25_DS25_350Bar_Installed.IsChecked = false;
            DorchEngineering_Stage3_DS3_Installed.IsChecked = false;
            XtremeDI_35_Installed.IsChecked = false;
            XtremeDI_60_Installed.IsChecked = false;
            XtremeDI_EVO_Installed.IsChecked = false;
            PrecisionRaceworks_HPFP_Installed.IsChecked = false;
        }
        else
        {
            HpfpLambdaEnrichment.IsChecked = _customOptions.HpfpLambdaEnrichment;

            B58TU_Installed.IsChecked = _customOptions.B58TU_Installed;

            SpoolPerformance_Fx150_Installed.IsChecked = _customOptions.SpoolPerformance_Fx150_Installed;
            SpoolPerformance_Fx170_Installed.IsChecked = _customOptions.SpoolPerformance_Fx170_Installed;
            SpoolPerformance_Fx180_Installed.IsChecked = _customOptions.SpoolPerformance_Fx180_Installed;
            SpoolPerformance_Fx200_Installed.IsChecked = _customOptions.SpoolPerformance_Fx200_Installed;
            SpoolPerformance_Fx350_Installed.IsChecked = _customOptions.SpoolPerformance_Fx350_Installed;
            SpoolPerformance_Fx400_Installed.IsChecked = _customOptions.SpoolPerformance_Fx400_Installed;
            SpoolPerformance_Fx400X_Installed.IsChecked = _customOptions.SpoolPerformance_Fx400X_Installed;
            DorchEngineering_Stage1_DS1_Installed.IsChecked = _customOptions.DorchEngineering_Stage1_DS1_Installed;
            DorchEngineering_Stage15_DS15_Installed.IsChecked = _customOptions.DorchEngineering_Stage15_DS15_Installed;
            DorchEngineering_Stage2_DS2_Installed.IsChecked = _customOptions.DorchEngineering_Stage2_DS2_Installed;
            DorchEngineering_Stage25_DS25_250Bar_Installed.IsChecked = _customOptions.DorchEngineering_Stage25_DS25_250Bar_Installed;
            DorchEngineering_Stage25_DS25_350Bar_Installed.IsChecked = _customOptions.DorchEngineering_Stage25_DS25_350Bar_Installed;
            DorchEngineering_Stage3_DS3_Installed.IsChecked = _customOptions.DorchEngineering_Stage3_DS3_Installed;
            XtremeDI_35_Installed.IsChecked = _customOptions.XtremeDI_35_Installed;
            XtremeDI_60_Installed.IsChecked = _customOptions.XtremeDI_60_Installed;
            XtremeDI_EVO_Installed.IsChecked = _customOptions.XtremeDI_EVO_Installed;
            PrecisionRaceworks_HPFP_Installed.IsChecked = _customOptions.PrecisionRaceworks_HPFP_Installed;
        }
    }

    private async void SetCustomizableDefaults_InjectorsInstalled()
    {
        SpoolPerformance_Ifx150_Installed.IsChecked = InjectorsInstalled.IsVisible && _customOptions.SpoolPerformance_Ifx150_Installed;
        SpoolPerformance_Ifx350_Installed.IsChecked = InjectorsInstalled.IsVisible && _customOptions.SpoolPerformance_Ifx350_Installed;
        SpoolPerformance_Ifx350X_Installed.IsChecked = InjectorsInstalled.IsVisible && _customOptions.SpoolPerformance_Ifx350X_Installed;
        XtremeDI_40_Installed.IsChecked = InjectorsInstalled.IsVisible && _customOptions.XtremeDI_40_Installed;
        XtremeDI_75_Installed.IsChecked = InjectorsInstalled.IsVisible && _customOptions.XtremeDI_75_Installed;
        Nostrum_Stage1_Installed.IsChecked = InjectorsInstalled.IsVisible && _customOptions.Nostrum_Stage1_Installed;
        Nostrum_Stage2_Installed.IsChecked = InjectorsInstalled.IsVisible && _customOptions.Nostrum_Stage2_Installed;
    }

    private async void SetCustomizableDefaults_LsdInstalled()
    {
        LsdInstalledFinalDrive = _customOptions.LsdInstalledFinalDrive;
    }

    private async void SetCustomizableDefaults_EthanolSupport()
    {
        FlexFuelSensorInstalled.IsChecked = _customOptions.FlexFuelSensorInstalled;
        FlexFuelSensorModel = _customOptions.FlexFuelSensorModel;
        FlexFuelDelayToShowOnDash = _customOptions.FlexFuelDelayToShowOnDash;
        FlexFuelBlending.IsChecked = _customOptions.FlexFuelBlending;
        FlexFuelEthanolContent.IsChecked = _customOptions.FlexFuelEthanolContent;
        FlexFuelEthanolContentSlot0 = _customOptions.FlexFuelEthanolContentSlot0;
        FlexFuelEthanolContentSlot1 = _customOptions.FlexFuelEthanolContentSlot1;
        FlexFuelEthanolContentSlot2 = _customOptions.FlexFuelEthanolContentSlot2;
        FlexFuelEthanolContentSlot3 = _customOptions.FlexFuelEthanolContentSlot3;
        FlexFuelEthanolContentSlot4 = _customOptions.FlexFuelEthanolContentSlot4;
        FlexFuelSensorCanBusId = _customOptions.FlexFuelSensorCanBusId;
    }

    private async void SetCustomizableDefaults_CustomCodeMenu()
    {
        CustomCodeMenuDelayToEnter = _customOptions.CustomCodeMenuDelayToEnter;
        CustomCodeMenuTimeout = _customOptions.CustomCodeMenuTimeout;
        CustomCodeMenuForceAllowAllButtonConfigurations.IsChecked = _appContext.CanTestRockerCombo() && _customOptions.CustomCodeMenuForceAllowAllButtonConfigurations;
        UseRockerCombo.IsChecked = UseRockerCombo.IsVisible && _customOptions.UseRockerCombo;
        UniRockerEnterDetectionDelay = _customOptions.UniRockerEnterDetectionDelay;
    }

    private async void SetCustomizableDefaults_ExhaustFlapOnTheFly()
    {
        ExhaustFlapOnTheFlyMaxLoad = _customOptions.ExhaustFlapOnTheFlyMaxLoad;
    }

    private async void SetCustomizableDefaults_ValetMode()
    {
        ValetMaxPedal = _customOptions.ValetMaxPedal;
        ValetMaxEngineSpeed = _customOptions.ValetMaxEngineSpeed;
        ValetMaxVehicleSpeed = _customOptions.ValetMaxVehicleSpeed;
        ValetRegularTorqueLevel = _customOptions.ValetRegularTorqueLevel;
        ValetRestrictiveTorqueLevel = _customOptions.ValetRestrictiveTorqueLevel;
    }

    private async void SetCustomizableDefaults_TcuLimiterStatic()
    {
        TcuLimiterStaticTorque = Math.Min(_customOptions.TcuLimiterStaticTorque, _customOptions.TcuLimiterTorque); // merging old settings/values
    }

    private async void SetCustomizableDefaults_ColdStartDelete()
    {
        ColdStartDeleteAlways.IsChecked = _customOptions.ColdStartDeleteAlways;
        ColdStartDeleteIfWarm.IsChecked = _customOptions.ColdStartDeleteIfWarm;
    }

    private async void SetCustomizableDefaults_TorqueGauge()
    {
        // Adjust gauges to real values:
        TorqueGaugeMax_Nm = _customOptions.TorqueGaugeMax_Nm / CustomOptionsUserConfiguration.GaugeMultiplier10;
        TorqueGaugeMax_lbft = _customOptions.TorqueGaugeMax_lbft / CustomOptionsUserConfiguration.GaugeMultiplier10;
        TorqueGaugeMax_kgm = _customOptions.TorqueGaugeMax_kgm / CustomOptionsUserConfiguration.GaugeMultiplier5;
    }

    private async void SetCustomizableDefaults_PowerGauge()
    {
        // Adjust gauges to real values:
        PowerGaugeMax_kW = _customOptions.PowerGaugeMax_kW / CustomOptionsUserConfiguration.GaugeMultiplier10;
        PowerGaugeMax_hp = _customOptions.PowerGaugeMax_hp / CustomOptionsUserConfiguration.GaugeMultiplier10;
    }

    private async void SetCustomizableDefaults_Burble()
    {
        BurbleAggressionStyle = BurbleStyleList.ElementAtOrDefault(_customOptions.BurbleAggressionStyleIndex_Selected);
        BurbleHybridPatch.IsChecked = _customOptions.BurbleHybridPatch;
        BurbleSpecifyDrivingModes.IsChecked = _customOptions.BurbleSpecifyDrivingModes;
        BurbleEnableEcoDrivingMode.IsChecked = _customOptions.BurbleEnableEcoDrivingMode;
        BurbleEnableEcoPlusDrivingMode.IsChecked = _customOptions.BurbleEnableEcoPlusDrivingMode;
        BurbleEnableComfortDrivingMode.IsChecked = _customOptions.BurbleEnableComfortDrivingMode;
        BurbleEnableComfortPlusDrivingMode.IsChecked = _customOptions.BurbleEnableComfortPlusDrivingMode;
        BurbleEnableSportDrivingMode.IsChecked = _customOptions.BurbleEnableSportDrivingMode;
        BurbleEnableSportPlusDrivingMode.IsChecked = _customOptions.BurbleEnableSportPlusDrivingMode;
        Burble_CW_SOUND.IsChecked = _customOptions.Burble_CW_SOUND;
        Burble_CW_SOUND_Value = _customOptions.Burble_CW_SOUND_Value;
        BurbleStyle1Aggressiveness = _customOptions.BurbleStyle1Aggressiveness;
        BurbleStyle2Aggressiveness = _customOptions.BurbleStyle2Aggressiveness;
        BurbleStyle3NormalAggressiveness = _customOptions.BurbleStyle3NormalAggressiveness;
        BurbleStyle3SportAggressiveness = _customOptions.BurbleStyle3SportAggressiveness;
        BurbleDurationSport = _customOptions.BurbleDurationSport;
        BurbleDurationSportPlus = _customOptions.BurbleDurationSportPlus;
        BurbleRpmMin = _customOptions.BurbleRpmMin;
        BurbleRpmMax = _customOptions.BurbleRpmMax;
        BurbleSpeedMin = _customOptions.BurbleSpeedMin;
        BurbleSpeedMax = _customOptions.BurbleSpeedMax;
        BurbleTemperatureLimitIncrease.IsChecked = _customOptions.BurbleTemperatureLimitIncrease;
        BurbleUpShift.IsChecked = _customOptions.BurbleUpShift;
        BurbleUpShiftAggression = _customOptions.BurbleUpShiftAggression;
        BurbleForceMaxAggressionAndDurationFactor.IsChecked = _customOptions.BurbleForceMaxAggressionAndDurationFactor;
        BurbleFlame.IsChecked = _customOptions.BurbleFlame;
        BurbleFlameTiming = _customOptions.BurbleFlameTiming;
        BurbleFlameFuel = _customOptions.BurbleFlameFuel;
    }

    private async void SetCustomizableDefaults_Cooling()
    {
        MaxCooling.IsChecked = _customOptions.MaxCooling;
        MaxCoolingRegularTargetEngineTemperature = _customOptions.MaxCoolingRegularTargetEngineTemperature;
        MaxCoolingTargetEngineTemperature = _customOptions.MaxCoolingTargetEngineTemperature;
        MaxCoolingTargetIntercoolerCoolantVolumeFlow = _customOptions.MaxCoolingTargetIntercoolerCoolantVolumeFlow;
    }

    private async void SetCustomizableDefaults_StartupRoar()
    {
        StartupRoarDuration = _customOptions.StartupRoarDuration;
        StartupRoarAggression = _customOptions.StartupRoarAggression;
    }

    private async void SetCustomizableDefaults_TorqueByGear()
    {
        TorqueByGear_AllGears.IsChecked = _customOptions.TorqueByGear_AllGears;
        TorqueByGear_IndividualGears.IsChecked = _customOptions.TorqueByGear_IndividualGears;
        TorqueByGear_GearAll_MaxTorque = _customOptions.TorqueByGear_GearAll_MaxTorque;
        TorqueByGear_Gear0_MaxTorque = _customOptions.TorqueByGear_Gear0_MaxTorque;
        TorqueByGear_Gear1_MaxTorque = _customOptions.TorqueByGear_Gear1_MaxTorque;
        TorqueByGear_Gear2_MaxTorque = _customOptions.TorqueByGear_Gear2_MaxTorque;
        TorqueByGear_Gear3_MaxTorque = _customOptions.TorqueByGear_Gear3_MaxTorque;
        TorqueByGear_Gear4_MaxTorque = _customOptions.TorqueByGear_Gear4_MaxTorque;
        TorqueByGear_Gear5_MaxTorque = _customOptions.TorqueByGear_Gear5_MaxTorque;
        TorqueByGear_Gear6_MaxTorque = _customOptions.TorqueByGear_Gear6_MaxTorque;
        TorqueByGear_Gear7_MaxTorque = _customOptions.TorqueByGear_Gear7_MaxTorque;
        TorqueByGear_Gear8_MaxTorque = _customOptions.TorqueByGear_Gear8_MaxTorque;
        TorqueByGear_Gear9_MaxTorque = _customOptions.TorqueByGear_Gear9_MaxTorque;
    }

    private async void SetCustomizableDefaults_IdleRPM()
    {
        IdleRpmTargetBase = _customOptions.IdleRpmTargetBase;
        IdleRpm6mt.IsChecked = _customOptions.IdleRpm6mt;
        IdleRpmTargetClutch = _customOptions.IdleRpmTargetClutch;
        IdleRpmTargetClutchDelay = _customOptions.IdleRpmTargetClutchDelay;
        IdleRpmTargetClutchFast.IsChecked = _customOptions.IdleRpmTargetClutchFast;
    }

    private async void SetCustomizableDefaults_Antilag()
    {
        AntilagExpertOptions.IsChecked = _customOptions.AntilagExpertOptions;
        AntilagDevOptions.IsChecked = _customOptions.AntilagDevOptions;
        if (AntilagUseIgnitionCut.IsVisible)
        {
            AntilagUseIgnitionCut.IsChecked = _customOptions.AntilagUseIgnitionCut;
        }
        else
        {
            AntilagUseIgnitionCut.IsChecked = false;
        }

        AntilagTargetNumberOfCylindersToSuppress = _customOptions.AntilagTargetNumberOfCylindersToSuppress;
        AntilagPedal = _customOptions.AntilagPedal;
        AntilagRollingSpeedThreshold = _customOptions.AntilagRollingSpeedThreshold;
        AntilagActivationDelay = _customOptions.AntilagActivationDelay;
        AntilagMaxDuration = _customOptions.AntilagMaxDuration;
        AntilagFlatIgnRoll = _customOptions.AntilagFlatIgnRoll;
        AntilagFlatLambdaRoll = _customOptions.AntilagFlatLambdaRoll;
        AntilagCurveIgnRoll = _customOptions.AntilagCurveIgnRoll;
        AntilagCurveLambdaRoll = _customOptions.AntilagCurveLambdaRoll;
        AntilagAggressionRoll = _customOptions.AntilagAggressionRoll;
        AntilagTargetBoostRoll = _customOptions.AntilagTargetBoostRoll;
        AntilagRpmDiffRoll = _customOptions.AntilagRpmDiffRoll;
        AntilagRollRpmMin = _customOptions.AntilagRollRpmMin;
        AntilagRollRpmMax = _customOptions.AntilagRollRpmMax;
        AntilagFlatIgnStand = _customOptions.AntilagFlatIgnStand;
        AntilagCurveLambdaStand = _customOptions.AntilagCurveLambdaStand;
        AntilagCurveIgnStand = _customOptions.AntilagCurveIgnStand;
        AntilagFlatLambdaStand = _customOptions.AntilagFlatLambdaStand;
        AntilagAggressionStand = _customOptions.AntilagAggressionStand;
        AntilagTargetBoostStand = _customOptions.AntilagTargetBoostStand;
        AntilagRpmDiffStand = _customOptions.AntilagRpmDiffStand;
        AntilagRpmSetpoint = _customOptions.AntilagRpmSetpoint;
        AntilagCurveSafetyTorqueCap = _customOptions.AntilagCurveSafetyTorqueCap;
        AntilagFlatSafetyTorqueCap = _customOptions.AntilagFlatSafetyTorqueCap; // This option is not customizable by user
        AntilagCoolantTempMin = _customOptions.AntilagCoolantTempMin;
        AntilagCoolantTempMax = _customOptions.AntilagCoolantTempMax;
        AntilagEgtTempMax = _customOptions.AntilagEgtTempMax;
        AntilagIatTempMax = _customOptions.AntilagIatTempMax;
        AntilagOverBoostThreshold = _customOptions.AntilagOverBoostThreshold;
    }
}