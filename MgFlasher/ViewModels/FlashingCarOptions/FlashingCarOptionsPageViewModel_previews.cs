﻿using System.Collections.Generic;
using System.Linq;
using MgFlasher.Localization.Resources;

namespace MgFlasher.ViewModels.FlashingCarOptions;

public partial class FlashingCarOptionsPageViewModel : PageViewModel
{
    private static bool WhichPreviewButtonGroupIsSelected(Dictionary<string, List<bool>> groups, string groupName)
    {
        if (groups.TryGetValue(groupName, out var filters))
        {
            return filters.Any(f => f);
        }

        return false;
    }

    // TODO we have these lists here, and in SetHpfpAvailability(), maybe we can put them once in a better place?
    private Dictionary<string, List<bool>> _hpfpGroups => new Dictionary<string, List<bool>>
    {
        { "OEM", new List<bool>
        {
            _b58TU_Installed.IsChecked
        }},
        { "Spool", new List<bool>
        {
            _spoolPerformance_Fx150_Installed.IsChecked,
            _spoolPerformance_Fx170_Installed.IsChecked,
            _spoolPerformance_Fx180_Installed.IsChecked,
            _spoolPerformance_Fx200_Installed.IsChecked,
            _spoolPerformance_Fx350_Installed.IsChecked,
            _spoolPerformance_Fx400_Installed.IsChecked,
            _spoolPerformance_Fx400X_Installed.IsChecked
        }},
        { "Dorch", new List<bool>
        {
            _dorchEngineering_Stage1_DS1_Installed.IsChecked,
            _dorchEngineering_Stage15_DS15_Installed.IsChecked,
            _dorchEngineering_Stage2_DS2_Installed.IsChecked,
            _dorchEngineering_Stage25_DS25_250Bar_Installed.IsChecked,
            _dorchEngineering_Stage25_DS25_350Bar_Installed.IsChecked
        }},
        { "XtremeDI", new List<bool>
        {
            _xtremeDI_35_Installed.IsChecked,
            _xtremeDI_60_Installed.IsChecked,
            _xtremeDI_EVO_Installed.IsChecked
        }},
    };

    private Dictionary<string, List<bool>> _injectorsGroups => new Dictionary<string, List<bool>>
    {
        { "Spool", new List<bool>
        {
            _spoolPerformance_Ifx150_Installed.IsChecked,
            _spoolPerformance_Ifx350_Installed.IsChecked,
            _spoolPerformance_Ifx350X_Installed.IsChecked,
        }},
        { "Nostrum", new List<bool>
        {
            _nostrum_Stage1_Installed.IsChecked,
            _nostrum_Stage2_Installed.IsChecked,
        }},
        { "XtremeDI", new List<bool>
        {
            _xtremeDI_40_Installed.IsChecked,
            _xtremeDI_75_Installed.IsChecked,
        }},
    };

    // TODO fix them showing from the first page open, not after changing one
    public string HpfpSelectedPreview { get => HpfpInstalled.IsChecked ? string.Join(", ", _hpfpGroups.Keys.Where(x => WhichPreviewButtonGroupIsSelected(_hpfpGroups, x))) : AppResources.CustomOptions_Previews_Edit; }

    public string InjectorsSelectedPreview { get => InjectorsInstalled.IsChecked ? string.Join(", ", _injectorsGroups.Keys.Where(x => WhichPreviewButtonGroupIsSelected(_injectorsGroups, x))) : AppResources.CustomOptions_Previews_Edit; }

    public string AftermarketLSDPreview { get => LsdInstalled.IsChecked ? LsdInstalledFinalDrive.ToString() : AppResources.CustomOptions_Previews_Edit; }

    // TODO show either MotivReflexCanBusIdOutput or MotivReflexCanBusIdInput depending on what?
    public string MotivReflexPreview { get => MotivReflexInstalledCustomizable.IsChecked ? MotivReflexCanBusIdOutput : AppResources.CustomOptions_Previews_Edit; }

    public string ColdStartDeletePreview { get => ColdStartDelete.IsChecked ? ColdStartDeleteAlways.IsChecked ? AppResources.CO_ColdStartDeleteAlways : ColdStartDeleteIfWarm.IsChecked ? AppResources.CO_ColdStartDeleteIfWarm_Preview : AppResources.CustomOptions_Previews_Edit : AppResources.CustomOptions_Previews_Edit; }

    public string BurblePreview { get => Burble.IsChecked ? BurbleAggressionStyle?.Name ?? AppResources.CustomOptions_Previews_Edit : AppResources.CustomOptions_Previews_Edit; }

    public string StartupRoarPreview { get => StartupRoar.IsChecked ? $"0,{StartupRoarDuration}s / {StartupRoarAggression - 2}%" : AppResources.CustomOptions_Previews_Edit; }

    public string EthanolPreview { get => FlexFuelSensorInstalled.IsChecked ? FlexFuelSensorModel.Split(" ").FirstOrDefault() ?? AppResources.CustomOptions_Previews_Edit : AppResources.CustomOptions_Previews_Edit; }

    // TODO move this method and converter to single place with the value calculation
    public string ExhaustFlapOTFPreview { get => ExhaustFlapOnTheFly.IsChecked ? ((ExhaustFlapOnTheFlyMaxLoad * 5) + 50).ToString() : AppResources.CustomOptions_Previews_Edit; }

    public string CoolingPreview { get => MaxCooling.IsChecked ? MaxCoolingTargetEngineTemperature.ToString() + AppResources.Celsius : AppResources.CustomOptions_Previews_Edit; }

    public string IdleRPMPreview { get => IdleRpm.IsChecked ? $"{IdleRpmTargetBase}0 RPM" : AppResources.CustomOptions_Previews_Edit; }

    public string DTCCodesPreview { get => AppResources.Select; }
}