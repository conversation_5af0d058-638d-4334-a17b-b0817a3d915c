using System.Windows.Input;
using MgFlasher.Models;

namespace MgFlasher.ViewModels.FlashingCarOptions;

public class CustomOptionViewModel : BaseViewModel
{
    private bool _isChecked;
    private bool _isVisible = true;

    public string Option { get; }
    public string Info { get; }
    public ICommand CustomizeCommand { get; protected set; }
    public ICommand CheckCommand { get; protected set; }
    public string CustomizeCommandAutomationId { get; }
    public string CheckButtonAutomationId { get; }
    public bool IsCustomizeCommandVisible => CustomizeCommand is not null;

    public bool IsVisible
    {
        get => _isVisible;
        set => SetProperty(ref _isVisible, value);
    }

    public bool IsChecked
    {
        get => _isChecked;
        set => SetProperty(ref _isChecked, value);
    }

    public CustomOptionViewModel(string option, string info, ICommand customizeCommand = null)
    {
        Option = option;
        Info = info;
        CustomizeCommand = customizeCommand;
        CustomizeCommandAutomationId = $"FlashingCarOptionsPage_{option}_CustomizeButton";
        CheckButtonAutomationId = $"FlashingCarOptionsPage_{option}_CheckButton";
        CheckCommand = new TraceableCommand(() => IsChecked = !IsChecked, CheckButtonAutomationId);
    }
}