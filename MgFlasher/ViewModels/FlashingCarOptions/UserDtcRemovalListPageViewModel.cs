﻿using MgFlasher.CustomCode.Options.DTC;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.FlashingCarOptions;

public class UserDtcRemovalListPageViewModel : PageViewModel
{
    private readonly IDCANService _dCANService;
    private readonly IDtcCodesResolver _dtcCodesResolver;
    private readonly INavigationService _navigationService;
    private readonly IUserDialogs _userDialogs;
    private readonly IAppContext _appContext;
    private ObservableCollection<DtcItemViewModel> _dtcItems = new();
    private Car _car;

    public ObservableCollection<DtcItemViewModel> DtcItems
    {
        get => _dtcItems;
        private set => SetProperty(ref _dtcItems, value);
    }

    public ICommand SubmitCommand { get; }
    public ICommand DefaultCommand { get; }

    public UserDtcRemovalListPageViewModel(
        IDCANService dCANService,
        IDtcCodesResolver dtcCodesResolver,
        INavigationService navigationService,
        IUserDialogs userDialogs,
        IAppContext appContext)
    {
        _dCANService = dCANService;
        _dtcCodesResolver = dtcCodesResolver;
        _navigationService = navigationService;
        _userDialogs = userDialogs;
        _appContext = appContext;
        SubmitCommand = new TraceableCommand(OnSubmit, nameof(SubmitCommand));
        DefaultCommand = new TraceableCommand(OnDefault, nameof(DefaultCommand));
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        var args = (DtcRemovalListPageNavigationArgs)arg;
        _car = await _dCANService.GetConnectedCar();

        var availableDtcCodes = await _dtcCodesResolver.GetUserDtcCodesAsync(args.A2L);
        var removalList = args.RemovalList ?? new List<string>();
        DtcItems = new(availableDtcCodes
            .Select(x => new DtcItemViewModel(x, _appContext)
            {
                IsChecked = removalList.Contains(x.Code, StringComparer.InvariantCultureIgnoreCase)
            }));
    }

    private async void OnSubmit(object obj)
    {
        var selected = DtcItems.Where(x => x.IsChecked).Select(x => x.Code).ToList();
        var navArgs = new DtcRemovalListPageResult(selected);

        var confirmText = string.Format(AppResources.DtcRemovalListPage_Submit_Confirmation, string.Join(", ", selected));
        if (selected.Count > 0 && !await _userDialogs.ConfirmAsync(confirmText, okText: AppResources.DtcRemovalListPage_Submit_Confirmation_Confirmed, cancelText: AppResources.Cancel))
        {
            return;
        }

        await _navigationService.NavigateToPreviousAsync(navArgs);
    }

    private void OnDefault(object obj)
    {
        foreach (var item in DtcItems)
        {
            item.IsChecked = false;
        }
    }
}