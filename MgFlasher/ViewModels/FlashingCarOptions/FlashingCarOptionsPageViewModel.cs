﻿using MgFlasher.CustomCode;
using MgFlasher.CustomCode.Options;
using MgFlasher.CustomCode.Options.Configuration.User;
using MgFlasher.CustomCode.Options.DTC;
using MgFlasher.Flasher.Commands;
using MgFlasher.Flasher.Commands.Flash;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.Cars.HardwareCompatability;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services.Navigation;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Storage;
using Syncfusion.Maui.Popup;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using UNI_Flash;

namespace MgFlasher.ViewModels.FlashingCarOptions;

public partial class FlashingCarOptionsPageViewModel : PageViewModel
{
    private readonly INavigationService _navigationService;
    private readonly IDCANService _dCANService;
    private readonly IAppContext _appContext;
    private readonly IUserDialogs _userDialogs;
    private readonly ICarCompatabilityService _carCompatabilityService;
    private readonly ICustomOptionsUserConfigurationService _customOptionsUserConfigurationService;
    private readonly IDtcCodesResolver _dtcCodesResolver;
    private readonly ICarFlashCommandA2lExtractor _carFlashCommandA2LExtractor;
    private readonly ILogger<FlashingCarOptionsPageViewModel> _logger;
    private readonly int _wizardStepsCount = 7;
    private readonly string _wizardModePromptCacheKey = "CustomOptions_WizardMode_DontAsk";
    private CustomOptionsUserConfiguration _customOptions;
    private IContinousFlasherCommand _flasherCommand;
    private object _scrollTo;
    private bool _isWizardMode = false;
    private int _currentWizardStep = 1;
    private bool _finalWizardStepReached = false;
    private SfPopup _questionPopup;
    private bool _wizardmodePromptShown = false;

    private CustomOptionViewModel _prepareFileWithoutFlashing = new(AppResources.CO_PrepareFileWithoutFlashing, AppResources.CO_PrepareFileWithoutFlashing_Info);
    private CustomOptionViewModel _skipCrcCorrection = new(AppResources.CO_SkipCrcCorrection, AppResources.CO_SkipCrcCorrection_Info);
    private CustomOptionViewModel _deleteBenchEcuDtcs = new("Delete Bench ECU DTCs", "Dev only");
    private CustomizableCustomOptionViewModel _flashingSegmentOptions;
    private CustomOptionViewModel _forceFlashPST = new(AppResources.CO_FORCE_FLASH_PST, AppResources.ATTENTION_FLASING_DURATION);
    private CustomOptionViewModel _forceFlashPST_WithCustomCode = new(AppResources.CO_FORCE_FLASH_PST_WITH_CUSTOM_CODE, AppResources.CO_FORCE_FLASH_PST_WITH_CUSTOM_CODE_INFO);
    private CustomOptionViewModel _forceFlashPST_WithoutCustomCode = new(AppResources.CO_FORCE_FLASH_PST_WITHOUT_CUSTOM_CODE, AppResources.CO_FORCE_FLASH_PST_WITHOUT_CUSTOM_CODE_INFO);
    private CustomOptionViewModel _preventPSTFlash = new(AppResources.CO_PREVENT_PST_FLASH, AppResources.CO_PREVENT_PST_FLASH_INFO);
    private CustomOptionViewModel _switchableMaps = new(AppResources.CO_SWITCHABLE_MAPS, AppResources.CO_SWITCHABLE_MAPS_Info);

    private CustomizableCustomOptionViewModel _antilag;
    private int _antilagTargetBoostRoll;
    private int _antilagTargetBoostStand;
    private int _antilagRpmSetpoint;
    private int _antilagRollingSpeedThreshold;
    private int _antilagRollRpmMin;
    private int _antilagRollRpmMax;
    private int _antilagAggressionRoll;
    private int _antilagAggressionStand;
    private int _antilagPedal;
    private int _antilagActivationDelay;
    private int _antilagMaxDuration;
    private int _antilagEngineSlot;

    private CustomOptionViewModel _antilagExpertOptions = new(AppResources.CO_AntilagExpert, AppResources.CO_AntilagExpert_Info);
    private CustomOptionViewModel _antilagUseIgnitionCut = new(AppResources.CO_AntilagUseIgnitionCut, AppResources.CO_AntilagUseIgnitionCut_Info);
    private int _antilagTargetNumberOfCylindersToSuppress;
    private int _antilagRpmDiffRoll;
    private int _antilagRpmDiffStand;
    private int _antilagOverBoostThreshold;
    private int _antilagCoolantTempMin;
    private int _antilagCoolantTempMax;
    private int _antilagEgtTempMax;
    private int _antilagIatTempMax;
    private int _antilagCurveSafetyTorqueCap;
    private int _antilagCurveIgnStand;
    private int _antilagCurveIgnRoll;
    private int _antilagCurveLambdaStand;
    private int _antilagCurveLambdaRoll;

    private CustomOptionViewModel _antilagDevOptions = new(AppResources.CO_AntilagDevOptions, AppResources.CO_AntilagDevOptions_Info);
    private int _antilagFlatSafetyTorqueCap;
    private double _antilagFlatIgnStand;
    private double _antilagFlatIgnRoll;
    private double _antilagFlatLambdaStand;
    private double _antilagFlatLambdaRoll;

    private CustomizableCustomOptionViewModel _lsdInstalled;
    private double _lsdInstalledFinalDrive;

    private CustomizableCustomOptionViewModel _ethanolSupport;
    private CustomOptionViewModel _flexFuelBlending = new(AppResources.CO_FlexFuelBlending, AppResources.CO_FlexFuelBlending_Info);
    private CustomOptionViewModel _flexFuelSensorInstalled = new(AppResources.CO_FlexFuelSensorInstalled, AppResources.CO_FlexFuelSensorInstalled_Info);
    private string _flexFuelSensorModel;
    private string _flexFuelSensorCanBusId;
    private CustomOptionViewModel _flexFuelEthanolContent = new(AppResources.CO_FlexFuelEthanolContent, AppResources.CO_FlexFuelEthanolContent_Info);
    private bool _flexFuelEthanolContentAllowed;
    private int _flexFuelEthanolContentSlot0;
    private int _flexFuelEthanolContentSlot1;
    private int _flexFuelEthanolContentSlot2;
    private int _flexFuelEthanolContentSlot3;
    private int _flexFuelEthanolContentSlot4;
    private int _flexFuelDelayToShowOnDash;
    private bool _flexFuelDelayToShowOnDashAllowed;

    private CustomizableCustomOptionViewModel _customCodeMenu;
    private int _customCodeMenuDelayToEnter;
    private int _customCodeMenuTimeout;
    private CustomOptionViewModel _customCodeMenuForceAllowAllButtonConfigurations = new(AppResources.CO_CustomCodeMenuForceAllowAllButtonConfigurations, AppResources.CO_CustomCodeMenuForceAllowAllButtonConfigurations_Info);
    private CustomOptionViewModel _useRockerCombo = new(AppResources.CO_UseRockerCombo, AppResources.CO_UseRockerCombo_Info);
    private int _uniRockerEnterDetectionDelay;

    private CustomOptionViewModel _radiatorFlapOnTheFly;
    private CustomizableCustomOptionViewModel _exhaustFlapOnTheFly;
    private int _exhaustFlapOnTheFlyMaxLoad;

    private CustomizableCustomOptionViewModel _valetMode;
    private int _valetMaxPedal;
    private int _valetMaxEngineSpeed;
    private int _valetMaxVehicleSpeed;
    private int _valetRegularTorqueLevel;
    private int _valetRestrictiveTorqueLevel;

    private CustomOptionViewModel _mgfLimpModeProtectionEnabled = new CustomOptionViewModel(AppResources.CO_MgfLimpModeProtectionEnabled, AppResources.CO_MgfLimpModeProtectionEnabled_Info);
    private CustomOptionViewModel _lowPressureFuelPressureSensorInstalled = new CustomOptionViewModel(AppResources.CO_LowPressureFuelPressureSensorInstalled, AppResources.CO_LowPressureFuelPressureSensorInstalled_Info);

    // obsolete
    private CustomizableCustomOptionViewModel _motivReflexInstalledCustomizable;

    private CustomOptionViewModel _motivReflexDeepIntegration = new CustomOptionViewModel(AppResources.CO_MotivReflexDeepIntegration, AppResources.CO_MotivReflexDeepIntegration_Info);
    private string _motivReflexCanBusIdOutput; // Values sent from Motiv to DME
    private string _motivReflexCanBusIdInput; // Values sent from DME to Motiv

    private CustomOptionViewModel _intakeInstalled = new CustomOptionViewModel(AppResources.CO_IntakeInstalled, AppResources.CO_IntakeInstalled_Info);

    private CustomizableCustomOptionViewModel _hpfpInstalled;
    private CustomOptionViewModel _hpfpLambdaEnrichment = new CustomOptionViewModel(AppResources.CO_HpfpLambdaEncrichment, AppResources.CO_HpfpLambdaEncrichment_Info);
    private CustomOptionViewModel _b58TU_Installed = new CustomOptionViewModel(AppResources.CO_B58TU_Installed, null);
    private CustomOptionViewModel _spoolPerformance_Fx150_Installed = new CustomOptionViewModel(AppResources.CO_SpoolPerformance_Fx150_Installed, null);
    private CustomOptionViewModel _spoolPerformance_Fx170_Installed = new CustomOptionViewModel(AppResources.CO_SpoolPerformance_Fx170_Installed, null);
    private CustomOptionViewModel _spoolPerformance_Fx180_Installed = new CustomOptionViewModel(AppResources.CO_SpoolPerformance_Fx180_Installed, null);
    private CustomOptionViewModel _spoolPerformance_Fx200_Installed = new CustomOptionViewModel(AppResources.CO_SpoolPerformance_Fx200_Installed, null);
    private CustomOptionViewModel _spoolPerformance_Fx350_Installed = new CustomOptionViewModel(AppResources.CO_SpoolPerformance_Fx350_Installed, null);
    private CustomOptionViewModel _spoolPerformance_Fx400_Installed = new CustomOptionViewModel(AppResources.CO_SpoolPerformance_Fx400_Installed, null);
    private CustomOptionViewModel _spoolPerformance_Fx400X_Installed = new CustomOptionViewModel(AppResources.CO_SpoolPerformance_Fx400X_Installed, null);
    private CustomOptionViewModel _dorchEngineering_Stage1_DS1_Installed = new CustomOptionViewModel(AppResources.CO_DorchEngineering_Stage1_DS1_Installed, null);
    private CustomOptionViewModel _dorchEngineering_Stage15_DS15_Installed = new CustomOptionViewModel(AppResources.CO_DorchEngineering_Stage15_DS15_Installed, null);
    private CustomOptionViewModel _dorchEngineering_Stage2_DS2_Installed = new CustomOptionViewModel(AppResources.CO_DorchEngineering_Stage2_DS2_Installed, null);
    private CustomOptionViewModel _dorchEngineering_Stage25_DS25_250Bar_Installed = new CustomOptionViewModel(AppResources.CO_DorchEngineering_Stage25_DS25_250Bar_Installed, null);
    private CustomOptionViewModel _dorchEngineering_Stage25_DS25_350Bar_Installed = new CustomOptionViewModel(AppResources.CO_DorchEngineering_Stage25_DS25_350Bar_Installed, null);
    private CustomOptionViewModel _dorchEngineering_Stage3_DS3_Installed = new CustomOptionViewModel(AppResources.CO_DorchEngineering_Stage3_DS3_Installed, null);
    private CustomOptionViewModel _xtremeDI_35_Installed = new CustomOptionViewModel(AppResources.CO_XtremeDI_35_Installed, null);
    private CustomOptionViewModel _xtremeDI_60_Installed = new CustomOptionViewModel(AppResources.CO_XtremeDI_60_Installed, null);
    private CustomOptionViewModel _xtremeDI_EVO_Installed = new CustomOptionViewModel(AppResources.CO_XtremeDI_EVO_Installed, null);
    private CustomOptionViewModel _precisionRaceworks_HPFP_Installed = new CustomOptionViewModel(AppResources.CO_PrecisionRaceworks_HPFP_Installed, null);

    // obsolete
    private CustomOptionViewModel _spoolPerformance_Installed = new CustomOptionViewModel(AppResources.CO_SpoolPerformance_Installed, null);

    // obsolete
    private CustomOptionViewModel _dorch_Stage1_Installed = new CustomOptionViewModel(AppResources.CO_Dorch_Stage1_Installed, null);

    // obsolete
    private CustomOptionViewModel _dorch_Stage2_Installed = new CustomOptionViewModel(AppResources.CO_Dorch_Stage2_Installed, null);

    private CustomizableCustomOptionViewModel _injectorsInstalled;
    private CustomOptionViewModel _spoolPerformance_Ifx150_Installed = new CustomOptionViewModel(AppResources.CO_SpoolPerformance_Ifx150_Installed, null);
    private CustomOptionViewModel _spoolPerformance_Ifx350_Installed = new CustomOptionViewModel(AppResources.CO_SpoolPerformance_Ifx350_Installed, null);
    private CustomOptionViewModel _spoolPerformance_Ifx350X_Installed = new CustomOptionViewModel(AppResources.CO_SpoolPerformance_Ifx350X_Installed, null);
    private CustomOptionViewModel _xtremeDI_40_Installed = new CustomOptionViewModel(AppResources.CO_XtremeDI_40_Installed, null);
    private CustomOptionViewModel _xtremeDI_75_Installed = new CustomOptionViewModel(AppResources.CO_XtremeDI_75_Installed, null);
    private CustomOptionViewModel _nostrum_Stage1_Installed = new CustomOptionViewModel(AppResources.CO_Nostrum_Stage1_Installed, null);
    private CustomOptionViewModel _nostrum_Stage2_Installed = new CustomOptionViewModel(AppResources.CO_Nostrum_Stage2_Installed, null);

    private CustomOptionViewModel _opfDelete = new CustomOptionViewModel(AppResources.CO_OPF_Delete, AppResources.CO_OpfDelete_Info);
    private CustomizableCustomOptionViewModel _tcuLimiterStatic;
    private CustomOptionViewModel _tcuLimiterDynamic = new CustomOptionViewModel(AppResources.CO_TcuLimiterDynamic, AppResources.CO_TcuLimiterDynamic_Info);
    private int _tcuLimiterStaticTorque;
    private int _tcuLimiterTorqueStock;
    private string _tcuLimiterStatic_Name;
    private bool _tcuTorqueLimiterPatchMethodIsDynamic;
    private CustomizableCustomOptionViewModel _coldStartDelete;
    private CustomOptionViewModel _coldStartDeleteAlways = new CustomOptionViewModel(AppResources.CO_ColdStartDeleteAlways, AppResources.CO_ColdStartDeleteAlways_Info);
    private CustomOptionViewModel _coldStartDeleteIfWarm = new CustomOptionViewModel(AppResources.CO_ColdStartDeleteIfWarm, AppResources.CO_ColdStartDeleteIfWarm_Info);
    private CustomOptionViewModel _dcat = new CustomOptionViewModel(AppResources.CO_Dcat, AppResources.CO_Dcat_Info);
    private CustomOptionViewModel _vmax = new CustomOptionViewModel(AppResources.CO_Vmax_SpeedLimit, AppResources.CO_Vmax_Info);
    private CustomOptionViewModel _exhaustFlap = new CustomOptionViewModel(AppResources.CO_EXHAUST_FLAPS_ALWAYS_OPEN, AppResources.CO_ExhaustFlap_Info);
    private CustomOptionViewModel _radiatorFlap = new CustomOptionViewModel(AppResources.CO_RADIATOR_FLAPS, AppResources.CO_RadiatorFlap_Info);
    private CustomizableCustomOptionViewModel _torqueGauge;
    private int _torqueGaugeMax_Nm;
    private int _torqueGaugeMax_lbft;
    private int _torqueGaugeMax_kgm;
    private CustomizableCustomOptionViewModel _powerGauge;
    private int _powerGaugeMax_kW;
    private int _powerGaugeMax_hp;
    private CustomOptionViewModel _inductionNoise = new CustomOptionViewModel(AppResources.CO_InductionNoise, AppResources.CO_InductionNoise_Info);
    private CustomizableCustomOptionViewModel _burble;
    private CustomOptionViewModel _burbleSpecifyDrivingModes = new CustomOptionViewModel(AppResources.CO_BurbleSpecifyDrivingModes, AppResources.CO_BurbleSpecifyDrivingModes_Info);
    private CustomOptionViewModel _burbleEnableEcoDrivingMode = new CustomOptionViewModel(AppResources.CO_BurbleEnableEcoDrivingMode, AppResources.CO_BurbleEnableEcoDrivingMode_Info);
    private CustomOptionViewModel _burbleEnableEcoPlusDrivingMode = new CustomOptionViewModel(AppResources.CO_BurbleEnableEcoPlusDrivingMode, AppResources.CO_BurbleEnableEcoPlusDrivingMode_Info);
    private CustomOptionViewModel _burbleEnableComfortDrivingMode = new CustomOptionViewModel(AppResources.CO_BurbleEnableComfortDrivingMode, AppResources.CO_BurbleEnableComfortDrivingMode_Info);
    private CustomOptionViewModel _burbleEnableComfortPlusDrivingMode = new CustomOptionViewModel(AppResources.CO_BurbleEnableComfortPlusDrivingMode, AppResources.CO_BurbleEnableComfortPlusDrivingMode_Info);
    private CustomOptionViewModel _burbleEnableSportDrivingMode = new CustomOptionViewModel(AppResources.CO_BurbleEnableSportDrivingMode, AppResources.CO_BurbleEnableSportDrivingMode_Info);
    private CustomOptionViewModel _burbleEnableSportPlusDrivingMode = new CustomOptionViewModel(AppResources.CO_BurbleEnableSportPlusDrivingMode, AppResources.CO_BurbleEnableSportPlusDrivingMode_Info);
    private CustomOptionViewModel _burbleUpShift = new CustomOptionViewModel(AppResources.CO_BurbleUpShift, AppResources.CO_BurbleUpShift_Info);
    private string _burbleUpShiftAggression;
    private CustomOptionViewModel _burbleHybridPatch = new CustomOptionViewModel(AppResources.CO_BurbleHybridPatch, AppResources.CO_BurbleHybridPatch_Info);
    private CustomOptionViewModel _burbleTemperatureLimitIncrease = new CustomOptionViewModel(AppResources.CO_BurbleTemperatureLimitIncrease, AppResources.CO_BurbleTemperatureLimitIncrease_Info);
    private CustomOptionViewModel _burbleForceMaxAggressionAndDurationFactor = new CustomOptionViewModel(AppResources.CO_BurbleForceMaxAggressionAndDurationFactor, AppResources.CO_BurbleForceMaxAggressionAndDurationFactor_Info);
    private CustomOptionViewModel _burble_CW_SOUND = new CustomOptionViewModel(AppResources.CO_Burble_CW_SOUND, AppResources.CO_Burble_CW_SOUND_Info);
    private int _burble_CW_SOUND_Value;
    private ObservableCollection<FlashingCarOptionBurbleStyleItemViewModel> _burbleStyleList;
    private int _burbleStyle1Aggressiveness;
    private int _burbleStyle2Aggressiveness;
    private int _burbleStyle3SportAggressiveness;
    private int _burbleStyle3NormalAggressiveness;
    private int _burbleDurationSport;
    private int _burbleDurationSportPlus;
    private int _burbleRpmMin;
    private int _burbleRpmMax;
    private int _burbleSpeedMin;
    private int _burbleSpeedMax;
    private FlashingCarOptionBurbleStyleItemViewModel _burbleAggressionStyle;
    private Car _car;
    private Version _installedCustomCodeVersion;
    private CustomizableCustomOptionViewModel _cooling;
    private CustomOptionViewModel _maxCooling = new CustomOptionViewModel(AppResources.CO_Max_Cooling_OnTheFly, AppResources.CO_Max_Cooling_Info);
    private int _maxCoolingRegularTargetEngineTemperature;
    private int _maxCoolingTargetEngineTemperature;
    private int _maxCoolingTargetIntercoolerCoolantVolumeFlow;
    private CustomOptionViewModel _burbleFlame = new CustomOptionViewModel(AppResources.CO_BurbleFlame, AppResources.CO_BurbleFlame_Info);
    private int _burbleFlameTiming;
    private int _burbleFlameFuel;
    private CustomizableCustomOptionViewModel _startupRoar;
    private int _startupRoarDuration;
    private int _startupRoarAggression;
    private CustomizableCustomOptionViewModel _torqueByGear;
    private CustomOptionViewModel _torqueByGear_AllGears = new CustomOptionViewModel(AppResources.CO_TorqueByGear_AllGears, AppResources.CO_TorqueByGear_AllGears_Info);
    private CustomOptionViewModel _torqueByGear_IndividualGears = new CustomOptionViewModel(AppResources.CO_TorqueByGear_IndividualGears, AppResources.CO_TorqueByGear_IndividualGears_Info);
    private int _torqueByGear_GearAll_MaxTorque;
    private int _torqueByGear_Gear0_MaxTorque;
    private int _torqueByGear_Gear1_MaxTorque;
    private int _torqueByGear_Gear2_MaxTorque;
    private int _torqueByGear_Gear3_MaxTorque;
    private int _torqueByGear_Gear4_MaxTorque;
    private int _torqueByGear_Gear5_MaxTorque;
    private int _torqueByGear_Gear6_MaxTorque;
    private int _torqueByGear_Gear7_MaxTorque;
    private int _torqueByGear_Gear8_MaxTorque;
    private int _torqueByGear_Gear9_MaxTorque;
    private CustomOptionViewModel _removeGeneralDtcCodes = new CustomOptionViewModel(AppResources.CO_RemoveGeneralDtcCodes, AppResources.CO_RemoveGeneralDtcCodes_Info);
    private CustomOptionViewModel _removeUserDtcCodes = new CustomOptionViewModel(AppResources.CO_RemoveUserDtcCodes, AppResources.CO_RemoveUserDtcCodes_Info);

    private CustomOptionViewModel _tunerOverrideDst = new CustomOptionViewModel(AppResources.CO_TunerOverrideDst, AppResources.CO_TunerOverrideDst_Info);

    private CustomizableCustomOptionViewModel _idleRpm;
    private int _idleRpmTargetBase;
    private CustomOptionViewModel _idleRpm6mt = new CustomOptionViewModel(AppResources.CO_IdleRpm6mt, AppResources.CO_IdleRpm6mt_Info);
    private int _idleRpmTargetClutchDelay;
    private int _idleRpmTargetClutch;
    private CustomOptionViewModel _idleRpmTargetClutchFast = new CustomOptionViewModel(AppResources.CO_IdleRpmTargetClutchFast, AppResources.CO_IdleRpmTargetClutchFast_Info);

    private int? _selectedMapId;
    private Version _targetCustomCodeVersion = new Version(0, 0, 0);
    private string _targetCustomCodeVersionString = string.Empty;
    private bool _hpfpStageRequired = false;
    private bool _customStage = false;
    private bool NotifyUserSwitchableMapsAreAvailable = false;

    public CustomOptionViewModel SkipCrcCorrection { get => _skipCrcCorrection; set => SetProperty(ref _skipCrcCorrection, value); }
    public CustomOptionViewModel DeleteBenchEcuDtcs { get => _deleteBenchEcuDtcs; set => SetProperty(ref _deleteBenchEcuDtcs, value); }
    public CustomOptionViewModel PrepareFileWithoutFlashing { get => _prepareFileWithoutFlashing; set => SetProperty(ref _prepareFileWithoutFlashing, value); }
    public CustomizableCustomOptionViewModel FlashingSegmentOptions { get => _flashingSegmentOptions; set => SetProperty(ref _flashingSegmentOptions, value); }
    public CustomOptionViewModel ForceFlashPST { get => _forceFlashPST; set => SetProperty(ref _forceFlashPST, value); }
    public CustomOptionViewModel ForceFlashPST_WithCustomCode { get => _forceFlashPST_WithCustomCode; set => SetProperty(ref _forceFlashPST_WithCustomCode, value); }
    public CustomOptionViewModel ForceFlashPST_WithoutCustomCode { get => _forceFlashPST_WithoutCustomCode; set => SetProperty(ref _forceFlashPST_WithoutCustomCode, value); }
    public CustomOptionViewModel PreventPSTFlash { get => _preventPSTFlash; set => SetProperty(ref _preventPSTFlash, value); }
    public CustomOptionViewModel SwitchableMaps { get => _switchableMaps; set => SetProperty(ref _switchableMaps, value); }

    public CustomizableCustomOptionViewModel LsdInstalled { get => _lsdInstalled; set => SetProperty(ref _lsdInstalled, value); }
    public double LsdInstalledFinalDrive { get => _lsdInstalledFinalDrive; set => SetProperty(ref _lsdInstalledFinalDrive, value); }

    public CustomizableCustomOptionViewModel EthanolSupport { get => _ethanolSupport; set => SetProperty(ref _ethanolSupport, value); }
    public CustomOptionViewModel FlexFuelBlending { get => _flexFuelBlending; set => SetProperty(ref _flexFuelBlending, value); }
    public CustomOptionViewModel FlexFuelSensorInstalled { get => _flexFuelSensorInstalled; set => SetProperty(ref _flexFuelSensorInstalled, value); }
    public string FlexFuelSensorModel { get => _flexFuelSensorModel; set => SetProperty(ref _flexFuelSensorModel, value); }
    public string FlexFuelSensorCanBusId { get => _flexFuelSensorCanBusId; set => SetProperty(ref _flexFuelSensorCanBusId, value); }
    public CustomOptionViewModel FlexFuelEthanolContent { get => _flexFuelEthanolContent; set => SetProperty(ref _flexFuelEthanolContent, value); }
    public bool FlexFuelEthanolContentAllowed { get => _flexFuelEthanolContentAllowed; set => SetProperty(ref _flexFuelEthanolContentAllowed, value); }
    public int FlexFuelEthanolContentSlot0 { get => _flexFuelEthanolContentSlot0; set => SetProperty(ref _flexFuelEthanolContentSlot0, value); }
    public int FlexFuelEthanolContentSlot1 { get => _flexFuelEthanolContentSlot1; set => SetProperty(ref _flexFuelEthanolContentSlot1, value); }
    public int FlexFuelEthanolContentSlot2 { get => _flexFuelEthanolContentSlot2; set => SetProperty(ref _flexFuelEthanolContentSlot2, value); }
    public int FlexFuelEthanolContentSlot3 { get => _flexFuelEthanolContentSlot3; set => SetProperty(ref _flexFuelEthanolContentSlot3, value); }
    public int FlexFuelEthanolContentSlot4 { get => _flexFuelEthanolContentSlot4; set => SetProperty(ref _flexFuelEthanolContentSlot4, value); }
    public int FlexFuelDelayToShowOnDash { get => _flexFuelDelayToShowOnDash; set => SetProperty(ref _flexFuelDelayToShowOnDash, value); }
    public bool FlexFuelDelayToShowOnDashAllowed { get => _flexFuelDelayToShowOnDashAllowed; set => SetProperty(ref _flexFuelDelayToShowOnDashAllowed, value); }

    public ObservableCollection<string> FlexFuelSensorModelList { get; } = new()
    {
        AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_Zeitronix_ECA,
        AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_FuelIt_FlexFuel_Kit,
        AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_DorchEngineering_FlexFuel_Kit,
        AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_MHD_FlexFuel_Kit,
        AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_BM3_FlexFuel_Kit,
        AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_MotivReflex,
        AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_Universal_0x00EC,
        AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_Universal_0x00EB,
        AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_Universal_0x04CB,
    };

    public CustomizableCustomOptionViewModel CustomCodeMenu { get => _customCodeMenu; set => SetProperty(ref _customCodeMenu, value); }
    public int CustomCodeMenuDelayToEnter { get => _customCodeMenuDelayToEnter; set => SetProperty(ref _customCodeMenuDelayToEnter, value); }
    public int CustomCodeMenuTimeout { get => _customCodeMenuTimeout; set => SetProperty(ref _customCodeMenuTimeout, value); }
    public CustomOptionViewModel CustomCodeMenuForceAllowAllButtonConfigurations { get => _customCodeMenuForceAllowAllButtonConfigurations; set => SetProperty(ref _customCodeMenuForceAllowAllButtonConfigurations, value); }
    public CustomOptionViewModel UseRockerCombo { get => _useRockerCombo; set => SetProperty(ref _useRockerCombo, value); }
    public int UniRockerEnterDetectionDelay { get => _uniRockerEnterDetectionDelay; set => SetProperty(ref _uniRockerEnterDetectionDelay, value); }

    public CustomOptionViewModel RadiatorFlapOnTheFly { get => _radiatorFlapOnTheFly; set => SetProperty(ref _radiatorFlapOnTheFly, value); }
    public CustomizableCustomOptionViewModel ExhaustFlapOnTheFly { get => _exhaustFlapOnTheFly; set => SetProperty(ref _exhaustFlapOnTheFly, value); }
    public int ExhaustFlapOnTheFlyMaxLoad { get => _exhaustFlapOnTheFlyMaxLoad; set => SetProperty(ref _exhaustFlapOnTheFlyMaxLoad, value); }

    public CustomizableCustomOptionViewModel ValetMode { get => _valetMode; set => SetProperty(ref _valetMode, value); }
    public int ValetMaxPedal { get => _valetMaxPedal; set => SetProperty(ref _valetMaxPedal, value); }
    public int ValetMaxEngineSpeed { get => _valetMaxEngineSpeed; set => SetProperty(ref _valetMaxEngineSpeed, value); }
    public int ValetMaxVehicleSpeed { get => _valetMaxVehicleSpeed; set => SetProperty(ref _valetMaxVehicleSpeed, value); }
    public int ValetRegularTorqueLevel { get => _valetRegularTorqueLevel; set => SetProperty(ref _valetRegularTorqueLevel, value); }
    public int ValetRestrictiveTorqueLevel { get => _valetRestrictiveTorqueLevel; set => SetProperty(ref _valetRestrictiveTorqueLevel, value); }

    public CustomOptionViewModel MgfLimpModeProtectionEnabled { get => _mgfLimpModeProtectionEnabled; set => SetProperty(ref _mgfLimpModeProtectionEnabled, value); }
    public CustomOptionViewModel LowPressureFuelPressureSensorInstalled { get => _lowPressureFuelPressureSensorInstalled; set => SetProperty(ref _lowPressureFuelPressureSensorInstalled, value); }
    public CustomOptionViewModel MotivReflexDeepIntegration { get => _motivReflexDeepIntegration; set => SetProperty(ref _motivReflexDeepIntegration, value); }
    public CustomizableCustomOptionViewModel MotivReflexInstalledCustomizable { get => _motivReflexInstalledCustomizable; set => SetProperty(ref _motivReflexInstalledCustomizable, value); }
    public string MotivReflexCanBusIdInput { get => _motivReflexCanBusIdInput; set => SetProperty(ref _motivReflexCanBusIdInput, value); }
    public string MotivReflexCanBusIdOutput { get => _motivReflexCanBusIdOutput; set => SetProperty(ref _motivReflexCanBusIdOutput, value); }

    public ObservableCollection<string> MotivReflexCanBusIdOutputList { get; } = new()
    {
        AppResources.FlashingCarOptionsPage_MotivReflexCanBusIdOutput_0x00EC,
        AppResources.FlashingCarOptionsPage_MotivReflexCanBusIdOutput_0x04CB,
        AppResources.FlashingCarOptionsPage_MotivReflexCanBusIdOutput_0x0334,
    };

    public ObservableCollection<string> MotivReflexCanBusIdInputList { get; } = new()
    {
        AppResources.FlashingCarOptionsPage_MotivReflexCanBusIdInput_0x00EB,
        AppResources.FlashingCarOptionsPage_MotivReflexCanBusIdInput_0x0335,
    };

    public CustomOptionViewModel IntakeInstalled { get => _intakeInstalled; set => SetProperty(ref _intakeInstalled, value); }

    public CustomizableCustomOptionViewModel HpfpInstalled { get => _hpfpInstalled; set => SetProperty(ref _hpfpInstalled, value); }
    public CustomOptionViewModel HpfpLambdaEnrichment { get => _hpfpLambdaEnrichment; set => SetProperty(ref _hpfpLambdaEnrichment, value); }
    public CustomOptionViewModel B58TU_Installed { get => _b58TU_Installed; set => SetProperty(ref _b58TU_Installed, value); }
    public CustomOptionViewModel SpoolPerformance_Fx150_Installed { get => _spoolPerformance_Fx150_Installed; set => SetProperty(ref _spoolPerformance_Fx150_Installed, value); }
    public CustomOptionViewModel SpoolPerformance_Fx170_Installed { get => _spoolPerformance_Fx170_Installed; set => SetProperty(ref _spoolPerformance_Fx170_Installed, value); }
    public CustomOptionViewModel SpoolPerformance_Fx180_Installed { get => _spoolPerformance_Fx180_Installed; set => SetProperty(ref _spoolPerformance_Fx180_Installed, value); }
    public CustomOptionViewModel SpoolPerformance_Fx200_Installed { get => _spoolPerformance_Fx200_Installed; set => SetProperty(ref _spoolPerformance_Fx200_Installed, value); }
    public CustomOptionViewModel SpoolPerformance_Fx350_Installed { get => _spoolPerformance_Fx350_Installed; set => SetProperty(ref _spoolPerformance_Fx350_Installed, value); }
    public CustomOptionViewModel SpoolPerformance_Fx400_Installed { get => _spoolPerformance_Fx400_Installed; set => SetProperty(ref _spoolPerformance_Fx400_Installed, value); }
    public CustomOptionViewModel SpoolPerformance_Fx400X_Installed { get => _spoolPerformance_Fx400X_Installed; set => SetProperty(ref _spoolPerformance_Fx400X_Installed, value); }
    public CustomOptionViewModel DorchEngineering_Stage1_DS1_Installed { get => _dorchEngineering_Stage1_DS1_Installed; set => SetProperty(ref _dorchEngineering_Stage1_DS1_Installed, value); }
    public CustomOptionViewModel DorchEngineering_Stage15_DS15_Installed { get => _dorchEngineering_Stage15_DS15_Installed; set => SetProperty(ref _dorchEngineering_Stage15_DS15_Installed, value); }
    public CustomOptionViewModel DorchEngineering_Stage2_DS2_Installed { get => _dorchEngineering_Stage2_DS2_Installed; set => SetProperty(ref _dorchEngineering_Stage2_DS2_Installed, value); }
    public CustomOptionViewModel DorchEngineering_Stage25_DS25_250Bar_Installed { get => _dorchEngineering_Stage25_DS25_250Bar_Installed; set => SetProperty(ref _dorchEngineering_Stage25_DS25_250Bar_Installed, value); }
    public CustomOptionViewModel DorchEngineering_Stage25_DS25_350Bar_Installed { get => _dorchEngineering_Stage25_DS25_350Bar_Installed; set => SetProperty(ref _dorchEngineering_Stage25_DS25_350Bar_Installed, value); }
    public CustomOptionViewModel DorchEngineering_Stage3_DS3_Installed { get => _dorchEngineering_Stage3_DS3_Installed; set => SetProperty(ref _dorchEngineering_Stage3_DS3_Installed, value); }
    public CustomOptionViewModel XtremeDI_35_Installed { get => _xtremeDI_35_Installed; set => SetProperty(ref _xtremeDI_35_Installed, value); }
    public CustomOptionViewModel XtremeDI_60_Installed { get => _xtremeDI_60_Installed; set => SetProperty(ref _xtremeDI_60_Installed, value); }
    public CustomOptionViewModel XtremeDI_EVO_Installed { get => _xtremeDI_EVO_Installed; set => SetProperty(ref _xtremeDI_EVO_Installed, value); }
    public CustomOptionViewModel PrecisionRaceworks_HPFP_Installed { get => _precisionRaceworks_HPFP_Installed; set => SetProperty(ref _precisionRaceworks_HPFP_Installed, value); }
    public bool HpfpAvailableFromOEM { get; set; }
    public bool HpfpAvailableFromSpoolPerformance { get; set; }
    public bool HpfpAvailableFromDorchEngineering { get; set; }
    public bool HpfpAvailableFromXtremeDI { get; set; }
    public bool HpfpAvailableFromPrecisionRaceworks { get; set; }

    public CustomizableCustomOptionViewModel InjectorsInstalled { get => _injectorsInstalled; set => SetProperty(ref _injectorsInstalled, value); }
    public CustomOptionViewModel SpoolPerformance_Ifx150_Installed { get => _spoolPerformance_Ifx150_Installed; set => SetProperty(ref _spoolPerformance_Ifx150_Installed, value); }
    public CustomOptionViewModel SpoolPerformance_Ifx350_Installed { get => _spoolPerformance_Ifx350_Installed; set => SetProperty(ref _spoolPerformance_Ifx350_Installed, value); }
    public CustomOptionViewModel SpoolPerformance_Ifx350X_Installed { get => _spoolPerformance_Ifx350X_Installed; set => SetProperty(ref _spoolPerformance_Ifx350X_Installed, value); }
    public CustomOptionViewModel XtremeDI_40_Installed { get => _xtremeDI_40_Installed; set => SetProperty(ref _xtremeDI_40_Installed, value); }
    public CustomOptionViewModel XtremeDI_75_Installed { get => _xtremeDI_75_Installed; set => SetProperty(ref _xtremeDI_75_Installed, value); }
    public CustomOptionViewModel Nostrum_Stage1_Installed { get => _nostrum_Stage1_Installed; set => SetProperty(ref _nostrum_Stage1_Installed, value); }
    public CustomOptionViewModel Nostrum_Stage2_Installed { get => _nostrum_Stage2_Installed; set => SetProperty(ref _nostrum_Stage2_Installed, value); }
    public bool InjectorsAvailableFromSpoolPerformance { get; set; }
    public bool InjectorsAvailableFromXtremeDI { get; set; }
    public bool InjectorsAvailableFromNostrum { get; set; }

    public CustomOptionViewModel OpfDelete { get => _opfDelete; set => SetProperty(ref _opfDelete, value); }
    public CustomOptionViewModel TcuLimiterDynamic { get => _tcuLimiterDynamic; set => SetProperty(ref _tcuLimiterDynamic, value); }
    public CustomizableCustomOptionViewModel TcuLimiterStatic { get => _tcuLimiterStatic; set => SetProperty(ref _tcuLimiterStatic, value); }
    public int TcuLimiterStaticTorque { get => _tcuLimiterStaticTorque; set => SetProperty(ref _tcuLimiterStaticTorque, value); }
    public int TcuLimiterTorqueStock { get => _tcuLimiterTorqueStock; set => SetProperty(ref _tcuLimiterTorqueStock, value); }
    public string TcuLimiterStatic_Name { get => _tcuLimiterStatic_Name; set => SetProperty(ref _tcuLimiterStatic_Name, value); }
    public bool TcuTorqueLimiterPatchMethodIsDynamic { get => _tcuTorqueLimiterPatchMethodIsDynamic; set => SetProperty(ref _tcuTorqueLimiterPatchMethodIsDynamic, value); }
    public CustomizableCustomOptionViewModel ColdStartDelete { get => _coldStartDelete; set => SetProperty(ref _coldStartDelete, value); }
    public CustomOptionViewModel ColdStartDeleteAlways { get => _coldStartDeleteAlways; set => SetProperty(ref _coldStartDeleteAlways, value); }
    public CustomOptionViewModel ColdStartDeleteIfWarm { get => _coldStartDeleteIfWarm; set => SetProperty(ref _coldStartDeleteIfWarm, value); }
    public CustomOptionViewModel Dcat { get => _dcat; set => SetProperty(ref _dcat, value); }
    public CustomOptionViewModel Vmax { get => _vmax; set => SetProperty(ref _vmax, value); }
    public CustomOptionViewModel ExhaustFlap { get => _exhaustFlap; set => SetProperty(ref _exhaustFlap, value); }
    public CustomOptionViewModel RadiatorFlap { get => _radiatorFlap; set => SetProperty(ref _radiatorFlap, value); }
    public CustomizableCustomOptionViewModel TorqueGauge { get => _torqueGauge; set => SetProperty(ref _torqueGauge, value); }
    public int TorqueGaugeMax_Nm { get => _torqueGaugeMax_Nm; set => SetProperty(ref _torqueGaugeMax_Nm, value); }
    public int TorqueGaugeMax_lbft { get => _torqueGaugeMax_lbft; set => SetProperty(ref _torqueGaugeMax_lbft, value); }
    public int TorqueGaugeMax_kgm { get => _torqueGaugeMax_kgm; set => SetProperty(ref _torqueGaugeMax_kgm, value); }
    public CustomizableCustomOptionViewModel PowerGauge { get => _powerGauge; set => SetProperty(ref _powerGauge, value); }
    public int PowerGaugeMax_kW { get => _powerGaugeMax_kW; set => SetProperty(ref _powerGaugeMax_kW, value); }
    public int PowerGaugeMax_hp { get => _powerGaugeMax_hp; set => SetProperty(ref _powerGaugeMax_hp, value); }
    public CustomOptionViewModel InductionNoise { get => _inductionNoise; set => SetProperty(ref _inductionNoise, value); }
    public CustomizableCustomOptionViewModel Burble { get => _burble; set => SetProperty(ref _burble, value); }
    public CustomOptionViewModel BurbleSpecifyDrivingModes { get => _burbleSpecifyDrivingModes; set => SetProperty(ref _burbleSpecifyDrivingModes, value); }
    public CustomOptionViewModel BurbleEnableEcoDrivingMode { get => _burbleEnableEcoDrivingMode; set => SetProperty(ref _burbleEnableEcoDrivingMode, value); }
    public CustomOptionViewModel BurbleEnableEcoPlusDrivingMode { get => _burbleEnableEcoPlusDrivingMode; set => SetProperty(ref _burbleEnableEcoPlusDrivingMode, value); }
    public CustomOptionViewModel BurbleEnableComfortDrivingMode { get => _burbleEnableComfortDrivingMode; set => SetProperty(ref _burbleEnableComfortDrivingMode, value); }
    public CustomOptionViewModel BurbleEnableComfortPlusDrivingMode { get => _burbleEnableComfortPlusDrivingMode; set => SetProperty(ref _burbleEnableComfortPlusDrivingMode, value); }
    public CustomOptionViewModel BurbleEnableSportDrivingMode { get => _burbleEnableSportDrivingMode; set => SetProperty(ref _burbleEnableSportDrivingMode, value); }
    public CustomOptionViewModel BurbleEnableSportPlusDrivingMode { get => _burbleEnableSportPlusDrivingMode; set => SetProperty(ref _burbleEnableSportPlusDrivingMode, value); }
    public CustomOptionViewModel BurbleUpShift { get => _burbleUpShift; set => SetProperty(ref _burbleUpShift, value); }

    public ObservableCollection<string> BurbleUpShiftAggressionList { get; } = new()
    {
        AppResources.FlashingCarOptionsPage_BurbleUpShift_OEM,
        AppResources.FlashingCarOptionsPage_BurbleUpShift_Soft,
        AppResources.FlashingCarOptionsPage_BurbleUpShift_Hard,
    };

    public string BurbleUpShiftAggression { get => _burbleUpShiftAggression; set => SetProperty(ref _burbleUpShiftAggression, value); }
    public CustomOptionViewModel BurbleHybridPatch { get => _burbleHybridPatch; set => SetProperty(ref _burbleHybridPatch, value); }
    public CustomOptionViewModel BurbleTemperatureLimitIncrease { get => _burbleTemperatureLimitIncrease; set => SetProperty(ref _burbleTemperatureLimitIncrease, value); }
    public CustomOptionViewModel BurbleForceMaxAggressionAndDurationFactor { get => _burbleForceMaxAggressionAndDurationFactor; set => SetProperty(ref _burbleForceMaxAggressionAndDurationFactor, value); }
    public CustomOptionViewModel Burble_CW_SOUND { get => _burble_CW_SOUND; set => SetProperty(ref _burble_CW_SOUND, value); }
    public int Burble_CW_SOUND_Value { get => _burble_CW_SOUND_Value; set => SetProperty(ref _burble_CW_SOUND_Value, value); }
    public ObservableCollection<FlashingCarOptionBurbleStyleItemViewModel> BurbleStyleList { get => _burbleStyleList; set => SetProperty(ref _burbleStyleList, value); }
    public int BurbleStyle1Aggressiveness { get => _burbleStyle1Aggressiveness; set => SetProperty(ref _burbleStyle1Aggressiveness, value); }
    public int BurbleStyle2Aggressiveness { get => _burbleStyle2Aggressiveness; set => SetProperty(ref _burbleStyle2Aggressiveness, value); }
    public int BurbleStyle3SportAggressiveness { get => _burbleStyle3SportAggressiveness; set => SetProperty(ref _burbleStyle3SportAggressiveness, value); }
    public int BurbleStyle3NormalAggressiveness { get => _burbleStyle3NormalAggressiveness; set => SetProperty(ref _burbleStyle3NormalAggressiveness, value); }
    public int BurbleDurationSport { get => _burbleDurationSport; set => SetProperty(ref _burbleDurationSport, value); }
    public int BurbleDurationSportPlus { get => _burbleDurationSportPlus; set => SetProperty(ref _burbleDurationSportPlus, value); }
    public int BurbleRpmMin { get => _burbleRpmMin; set => SetProperty(ref _burbleRpmMin, value); }
    public int BurbleRpmMax { get => _burbleRpmMax; set => SetProperty(ref _burbleRpmMax, value); }
    public int BurbleSpeedMin { get => _burbleSpeedMin; set => SetProperty(ref _burbleSpeedMin, value); }
    public int BurbleSpeedMax { get => _burbleSpeedMax; set => SetProperty(ref _burbleSpeedMax, value); }

    public FlashingCarOptionBurbleStyleItemViewModel BurbleAggressionStyle
    {
        get => _burbleAggressionStyle;
        set => SetProperty(ref _burbleAggressionStyle, value);
    }

    public CustomizableCustomOptionViewModel Cooling { get => _cooling; set => SetProperty(ref _cooling, value); }
    public CustomOptionViewModel MaxCooling { get => _maxCooling; set => SetProperty(ref _maxCooling, value); }
    public int MaxCoolingRegularTargetEngineTemperature { get => _maxCoolingRegularTargetEngineTemperature; set => SetProperty(ref _maxCoolingRegularTargetEngineTemperature, value); }
    public int MaxCoolingTargetEngineTemperature { get => _maxCoolingTargetEngineTemperature; set => SetProperty(ref _maxCoolingTargetEngineTemperature, value); }
    public int MaxCoolingTargetIntercoolerCoolantVolumeFlow { get => _maxCoolingTargetIntercoolerCoolantVolumeFlow; set => SetProperty(ref _maxCoolingTargetIntercoolerCoolantVolumeFlow, value); }
    public CustomOptionViewModel BurbleFlame { get => _burbleFlame; set => SetProperty(ref _burbleFlame, value); }
    public int BurbleFlameTiming { get => _burbleFlameTiming; set => SetProperty(ref _burbleFlameTiming, value); }
    public int BurbleFlameFuel { get => _burbleFlameFuel; set => SetProperty(ref _burbleFlameFuel, value); }
    public CustomizableCustomOptionViewModel StartupRoar { get => _startupRoar; set => SetProperty(ref _startupRoar, value); }
    public int StartupRoarDuration { get => _startupRoarDuration; set => SetProperty(ref _startupRoarDuration, value); }
    public int StartupRoarAggression { get => _startupRoarAggression; set => SetProperty(ref _startupRoarAggression, value); }
    public CustomizableCustomOptionViewModel TorqueByGear { get => _torqueByGear; set => SetProperty(ref _torqueByGear, value); }
    public CustomOptionViewModel TorqueByGear_AllGears { get => _torqueByGear_AllGears; set => SetProperty(ref _torqueByGear_AllGears, value); }
    public CustomOptionViewModel TorqueByGear_IndividualGears { get => _torqueByGear_IndividualGears; set => SetProperty(ref _torqueByGear_IndividualGears, value); }
    public int TorqueByGear_GearAll_MaxTorque { get => _torqueByGear_GearAll_MaxTorque; set => SetProperty(ref _torqueByGear_GearAll_MaxTorque, value); }
    public int TorqueByGear_Gear0_MaxTorque { get => _torqueByGear_Gear0_MaxTorque; set => SetProperty(ref _torqueByGear_Gear0_MaxTorque, value); }
    public int TorqueByGear_Gear1_MaxTorque { get => _torqueByGear_Gear1_MaxTorque; set => SetProperty(ref _torqueByGear_Gear1_MaxTorque, value); }
    public int TorqueByGear_Gear2_MaxTorque { get => _torqueByGear_Gear2_MaxTorque; set => SetProperty(ref _torqueByGear_Gear2_MaxTorque, value); }
    public int TorqueByGear_Gear3_MaxTorque { get => _torqueByGear_Gear3_MaxTorque; set => SetProperty(ref _torqueByGear_Gear3_MaxTorque, value); }
    public int TorqueByGear_Gear4_MaxTorque { get => _torqueByGear_Gear4_MaxTorque; set => SetProperty(ref _torqueByGear_Gear4_MaxTorque, value); }
    public int TorqueByGear_Gear5_MaxTorque { get => _torqueByGear_Gear5_MaxTorque; set => SetProperty(ref _torqueByGear_Gear5_MaxTorque, value); }
    public int TorqueByGear_Gear6_MaxTorque { get => _torqueByGear_Gear6_MaxTorque; set => SetProperty(ref _torqueByGear_Gear6_MaxTorque, value); }
    public int TorqueByGear_Gear7_MaxTorque { get => _torqueByGear_Gear7_MaxTorque; set => SetProperty(ref _torqueByGear_Gear7_MaxTorque, value); }
    public int TorqueByGear_Gear8_MaxTorque { get => _torqueByGear_Gear8_MaxTorque; set => SetProperty(ref _torqueByGear_Gear8_MaxTorque, value); }
    public int TorqueByGear_Gear9_MaxTorque { get => _torqueByGear_Gear9_MaxTorque; set => SetProperty(ref _torqueByGear_Gear9_MaxTorque, value); }
    public CustomOptionViewModel RemoveGeneralDtcCodes { get => _removeGeneralDtcCodes; set => SetProperty(ref _removeGeneralDtcCodes, value); }
    public CustomOptionViewModel RemoveUserDtcCodes { get => _removeUserDtcCodes; set => SetProperty(ref _removeUserDtcCodes, value); }
    public CustomOptionViewModel TunerOverrideDst { get => _tunerOverrideDst; set => SetProperty(ref _tunerOverrideDst, value); }

    public CustomizableCustomOptionViewModel Antilag { get => _antilag; set => SetProperty(ref _antilag, value); }
    public int AntilagTargetBoostRoll { get => _antilagTargetBoostRoll; set => SetProperty(ref _antilagTargetBoostRoll, value); }
    public int AntilagTargetBoostStand { get => _antilagTargetBoostStand; set => SetProperty(ref _antilagTargetBoostStand, value); }
    public int AntilagRpmSetpoint { get => _antilagRpmSetpoint; set => SetProperty(ref _antilagRpmSetpoint, value); }
    public int AntilagRollingSpeedThreshold { get => _antilagRollingSpeedThreshold; set => SetProperty(ref _antilagRollingSpeedThreshold, value); }
    public int AntilagRollRpmMin { get => _antilagRollRpmMin; set => SetProperty(ref _antilagRollRpmMin, value); }
    public int AntilagRollRpmMax { get => _antilagRollRpmMax; set => SetProperty(ref _antilagRollRpmMax, value); }
    public int AntilagAggressionRoll { get => _antilagAggressionRoll; set => SetProperty(ref _antilagAggressionRoll, value); }
    public int AntilagAggressionStand { get => _antilagAggressionStand; set => SetProperty(ref _antilagAggressionStand, value); }
    public int AntilagPedal { get => _antilagPedal; set => SetProperty(ref _antilagPedal, value); }
    public int AntilagActivationDelay { get => _antilagActivationDelay; set => SetProperty(ref _antilagActivationDelay, value); }
    public int AntilagMaxDuration { get => _antilagMaxDuration; set => SetProperty(ref _antilagMaxDuration, value); }
    public int AntilagEngineSlot { get => _antilagEngineSlot; set => SetProperty(ref _antilagEngineSlot, value); }
    public CustomOptionViewModel AntilagExpertOptions { get => _antilagExpertOptions; set => SetProperty(ref _antilagExpertOptions, value); }
    public CustomOptionViewModel AntilagUseIgnitionCut { get => _antilagUseIgnitionCut; set => SetProperty(ref _antilagUseIgnitionCut, value); }
    public bool CanAdjustAntilagTargetNumberOfCylindersToSuppress { get; set; }
    public int NumberOfCylindersInEngine { get; set; }
    public int AntilagTargetNumberOfCylindersToSuppress { get => _antilagTargetNumberOfCylindersToSuppress; set => SetProperty(ref _antilagTargetNumberOfCylindersToSuppress, value); }

    public bool CanAdjustAntilagRpmDiff { get; set; }
    public int AntilagRpmDiffRoll { get => _antilagRpmDiffRoll; set => SetProperty(ref _antilagRpmDiffRoll, value); }
    public int AntilagRpmDiffStand { get => _antilagRpmDiffStand; set => SetProperty(ref _antilagRpmDiffStand, value); }
    public int AntilagOverBoostThreshold { get => _antilagOverBoostThreshold; set => SetProperty(ref _antilagOverBoostThreshold, value); }
    public int AntilagCoolantTempMin { get => _antilagCoolantTempMin; set => SetProperty(ref _antilagCoolantTempMin, value); }
    public int AntilagCoolantTempMax { get => _antilagCoolantTempMax; set => SetProperty(ref _antilagCoolantTempMax, value); }
    public int AntilagEgtTempMax { get => _antilagEgtTempMax; set => SetProperty(ref _antilagEgtTempMax, value); }
    public int AntilagIatTempMax { get => _antilagIatTempMax; set => SetProperty(ref _antilagIatTempMax, value); }
    public int AntilagCurveSafetyTorqueCap { get => _antilagCurveSafetyTorqueCap; set => SetProperty(ref _antilagCurveSafetyTorqueCap, value); }
    public int AntilagCurveIgnStand { get => _antilagCurveIgnStand; set => SetProperty(ref _antilagCurveIgnStand, value); }
    public int AntilagCurveIgnRoll { get => _antilagCurveIgnRoll; set => SetProperty(ref _antilagCurveIgnRoll, value); }
    public int AntilagCurveLambdaStand { get => _antilagCurveLambdaStand; set => SetProperty(ref _antilagCurveLambdaStand, value); }
    public int AntilagCurveLambdaRoll { get => _antilagCurveLambdaRoll; set => SetProperty(ref _antilagCurveLambdaRoll, value); }
    public CustomOptionViewModel AntilagDevOptions { get => _antilagDevOptions; set => SetProperty(ref _antilagDevOptions, value); }
    public int AntilagFlatSafetyTorqueCap { get => _antilagFlatSafetyTorqueCap; set => SetProperty(ref _antilagFlatSafetyTorqueCap, value); }
    public double AntilagFlatIgnStand { get => _antilagFlatIgnStand; set => SetProperty(ref _antilagFlatIgnStand, value); }
    public double AntilagFlatIgnRoll { get => _antilagFlatIgnRoll; set => SetProperty(ref _antilagFlatIgnRoll, value); }
    public double AntilagFlatLambdaStand { get => _antilagFlatLambdaStand; set => SetProperty(ref _antilagFlatLambdaStand, value); }
    public double AntilagFlatLambdaRoll { get => _antilagFlatLambdaRoll; set => SetProperty(ref _antilagFlatLambdaRoll, value); }

    public CustomizableCustomOptionViewModel IdleRpm { get => _idleRpm; set => SetProperty(ref _idleRpm, value); }
    public int IdleRpmTargetBase { get => _idleRpmTargetBase; set => SetProperty(ref _idleRpmTargetBase, value); }
    public CustomOptionViewModel IdleRpm6mt { get => _idleRpm6mt; set => SetProperty(ref _idleRpm6mt, value); }
    public int IdleRpmTargetClutchDelay { get => _idleRpmTargetClutchDelay; set => SetProperty(ref _idleRpmTargetClutchDelay, value); }
    public int IdleRpmTargetClutch { get => _idleRpmTargetClutch; set => SetProperty(ref _idleRpmTargetClutch, value); }
    public CustomOptionViewModel IdleRpmTargetClutchFast { get => _idleRpmTargetClutchFast; set => SetProperty(ref _idleRpmTargetClutchFast, value); }

    public bool IsWizardMode
    {
        get => _isWizardMode;
        set
        {
            SetProperty(ref _isWizardMode, value);
            OnPropertyChanged(nameof(IsNextStepAvailable));
            OnPropertyChanged(nameof(IsPreviousStepAvailable));
            OnPropertyChanged(nameof(IsDeveloperOnlyVisible));
        }
    }

    public bool IsNextStepAvailable { get => IsWizardMode && CurrentWizardStep < _wizardStepsCount; }
    public bool IsPreviousStepAvailable { get => IsWizardMode && CurrentWizardStep > 1; }

    public int CurrentWizardStep
    {
        get => _currentWizardStep;
        set
        {
            SetProperty(ref _currentWizardStep, value);
            OnPropertyChanged(nameof(IsNextStepAvailable));
            OnPropertyChanged(nameof(IsPreviousStepAvailable));
        }
    }

    public bool IsDeveloperOnlyVisible
    {
        get => GetDeveloperOnlyVisible();
    }

    public ICommand SubmitCommand { get; }
    public ICommand MiscCommand { get; set; }
    public ICommand NextCommand { get; set; }
    public ICommand PreviousCommand { get; set; }
    public ICommand ProgressBarStepTappedCommand { get; set; }

    public FlashingCarOptionsPageViewModel(
        INavigationService navigationService,
        IDCANService dCANService,
        IAppContext appContext,
        IUserDialogs userDialogs,
        ICarCompatabilityService carCompatabilityService,
        ICustomOptionsUserConfigurationService customOptionsUserConfigurationService,
        IDtcCodesResolver dtcCodesResolver,
        IFileDecryptor fileDecryptor,
        ICarFlashCommandA2lExtractor carFlashCommandA2LExtractor,
        ILogger<FlashingCarOptionsPageViewModel> logger)
    {
        _torqueByGear = new(AppResources.CO_TorqueByGear, AppResources.CO_TorqueByGear_Info, navigationService, SetCustomizableDefaults_TorqueByGear);
        _lsdInstalled = new(AppResources.CO_LsdInstalled, AppResources.CO_LsdInstalled_Info, navigationService, SetCustomizableDefaults_LsdInstalled);
        _startupRoar = new(AppResources.CO_StartupRoar, AppResources.CO_StartupRoar_Info, navigationService, SetCustomizableDefaults_StartupRoar);
        _cooling = new(AppResources.CO_Cooling, AppResources.CO_Cooling_Info, navigationService, SetCustomizableDefaults_Cooling);
        _burble = new(AppResources.CO_Burble, AppResources.CO_Burble_Info, navigationService, SetCustomizableDefaults_Burble);
        _torqueGauge = new(AppResources.CO_TorqueGauge, AppResources.CO_TorqueGauge_Info, navigationService, SetCustomizableDefaults_TorqueGauge);
        _powerGauge = new(AppResources.CO_PowerGauge, AppResources.CO_PowerGauge_Info, navigationService, SetCustomizableDefaults_PowerGauge);
        _coldStartDelete = new(AppResources.CO_Cold_Start_Delete, AppResources.CO_ColdStartDelete_Info, navigationService, SetCustomizableDefaults_ColdStartDelete);
        _tcuLimiterStatic = new(AppResources.CO_TcuLimiterStatic, AppResources.CO_TcuLimiterStatic_Info, navigationService, SetCustomizableDefaults_TcuLimiterStatic);
        _motivReflexInstalledCustomizable = new(AppResources.CO_MotivReflexInstalled, AppResources.CO_MotivReflexInstalled_Info, navigationService, SetCustomizableDefaults_MotivReflex);
        _hpfpInstalled = new(AppResources.CO_HpfpInstalled, AppResources.CO_HpfpInstalled_Info, navigationService, SetCustomizableDefaults_HpfpInstalled);
        _injectorsInstalled = new(AppResources.CO_InjectorInstalled, AppResources.CO_InjectorInstalled_Info, navigationService, SetCustomizableDefaults_InjectorsInstalled);
        _antilag = new(AppResources.CO_Antilag, AppResources.CO_Antilag_Info, navigationService, SetCustomizableDefaults_Antilag);
        _valetMode = new(AppResources.CO_ValetMode, AppResources.CO_ValetMode_Info, navigationService, SetCustomizableDefaults_ValetMode);
        _radiatorFlapOnTheFly = new(AppResources.CO_RadiatorFlapOnTheFly, AppResources.CO_RadiatorFlapOnTheFly_Info);
        _exhaustFlapOnTheFly = new(AppResources.CO_ExhaustFlapOnTheFly, AppResources.CO_ExhaustFlapOnTheFly_Info, navigationService, SetCustomizableDefaults_ExhaustFlapOnTheFly);
        _customCodeMenu = new(AppResources.CO_CustomCodeMenu, AppResources.CO_CustomCodeMenu_Info, navigationService, SetCustomizableDefaults_CustomCodeMenu);
        _ethanolSupport = new(AppResources.CO_Ethanol, AppResources.CO_Ethanol_Info, navigationService, SetCustomizableDefaults_EthanolSupport);
        _idleRpm = new(AppResources.CO_IdleRpm, AppResources.CO_IdleRpm_Info, navigationService, SetCustomizableDefaults_IdleRPM);
        _flashingSegmentOptions = new(AppResources.CO_FlashingSegmentOptions, AppResources.CO_FlashingSegmentOptions_Info, navigationService, SetCustomizableDefaults_FlashingSegmentOptions);
        _navigationService = navigationService;
        _dCANService = dCANService;
        _appContext = appContext;
        _userDialogs = userDialogs;
        _carCompatabilityService = carCompatabilityService;
        _customOptionsUserConfigurationService = customOptionsUserConfigurationService;
        _dtcCodesResolver = dtcCodesResolver;
        _carFlashCommandA2LExtractor = carFlashCommandA2LExtractor;
        _logger = logger;

        _removeUserDtcCodes = new CustomOptionViewModel(AppResources.CO_RemoveUserDtcCodes, AppResources.CO_RemoveUserDtcCodes_Info,
            new TraceableCommand(CustomizeUserDtcCodes, x => _removeUserDtcCodes?.IsChecked ?? false, nameof(CustomizeUserDtcCodes)));

        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));
        MiscCommand = new TraceableCommand(ShowMiscOptionsDialog, nameof(ShowMiscOptionsDialog));
        NextCommand = new TraceableCommand(NextWizardStep, nameof(NextWizardStep));
        PreviousCommand = new TraceableCommand(PreviousWizardStep, nameof(PreviousWizardStep));
        ProgressBarStepTappedCommand = new TraceableCommand(ProgressBarStepTapped, nameof(ProgressBarStepTapped));
    }

    private async void NextWizardStep()
    {
        CurrentWizardStep++;
        if (CurrentWizardStep >= _wizardStepsCount)
        {
            CurrentWizardStep = _wizardStepsCount;
            _finalWizardStepReached = true;
        }
    }

    private async void PreviousWizardStep()
    {
        CurrentWizardStep--;
        if (CurrentWizardStep < 1)
        {
            CurrentWizardStep = 1;
        }
    }

    private void ProgressBarStepTapped(object param)
    {
        if (param is string paramString && int.TryParse(paramString, out int number))
        {
            CurrentWizardStep = number;
            if (CurrentWizardStep < 1)
            {
                CurrentWizardStep = 1;
            }
            else if (CurrentWizardStep >= _wizardStepsCount)
            {
                CurrentWizardStep = _wizardStepsCount;
                _finalWizardStepReached = true;
            }
        }
    }

    private bool GetDeveloperOnlyVisible()
    {
        return !IsWizardMode &&
                            (RemoveGeneralDtcCodes.IsVisible
                          || SkipCrcCorrection.IsVisible
                          || DeleteBenchEcuDtcs.IsVisible
                          || PrepareFileWithoutFlashing.IsVisible
                          || FlashingSegmentOptions.IsVisible
                          || TunerOverrideDst.IsVisible);
    }

    private async void ShowMiscOptionsDialog(object obj)
    {
        var result = await _userDialogs.ActionSheetAsync(AppResources.ChooseCarAction, AppResources.Cancel, null, null, [
            AppResources.ResetToDefaultOptions,
            AppResources.WizardMode_Toggle,
            AppResources.WizardMode_ShowPromptAgain
        ]);

        if (result.Contains(AppResources.ResetToDefaultOptions))
        {
            OnDefault();
        }
        else if (result.Contains(AppResources.WizardMode_Toggle))
        {
            ToggleWizardMode();
        }
        else if (result.Contains(AppResources.WizardMode_ShowPromptAgain))
        {
            Preferences.Set(_wizardModePromptCacheKey, false);
        }
    }

    private async void ToggleWizardMode()
    {
        IsWizardMode = !IsWizardMode;
        if (IsWizardMode)
        {
            CurrentWizardStep = 1;
        }
    }

    private async void OnDefault()
    {
        var isConfirmed = await _userDialogs.ConfirmAsync(
            AppResources.FlashingCarOptionsPage_RestoreDefaultConfirmationPrompt,
            okText: AppResources.Ok,
            cancelText: AppResources.Cancel);

        if (!isConfirmed)
        {
            return;
        }

        _customOptions = new CustomOptionsUserConfiguration();
        await SetInitialCustomOptions();
        OnPropertyChanged(nameof(IsDeveloperOnlyVisible));
    }

    private async void Submit()
    {
        if (IsWizardMode && !_finalWizardStepReached)
        {
            if (!await _userDialogs.ConfirmAsync(
                message: AppResources.WizardMode_SkipAndFlashQuestion,
                title: AppResources.WizardMode_SkipAndFlashTitle,
                okText: AppResources.WizardMode_SkipAndFlashContinue,
                cancelText: AppResources.WizardMode_SkipAndFlashGoBack))
            {
                return;
            }
        }

        _customOptions.SkipCrcCorrection = SkipCrcCorrection.IsChecked;
        _customOptions.DeleteBenchEcuDtcs = DeleteBenchEcuDtcs.IsChecked;
        _customOptions.PrepareFileWithoutFlashing = PrepareFileWithoutFlashing.IsChecked;
        _customOptions.FlashingSegmentOptions = FlashingSegmentOptions.IsChecked;
        _customOptions.ForceFlashPST = ForceFlashPST.IsChecked;
        _customOptions.ForceFlashPST_WithCustomCode = ForceFlashPST_WithCustomCode.IsChecked;
        _customOptions.ForceFlashPST_WithoutCustomCode = ForceFlashPST_WithoutCustomCode.IsChecked;
        _customOptions.PreventPSTFlash = PreventPSTFlash.IsChecked;
        _customOptions.SwitchableMaps = SwitchableMaps.IsChecked;
        _customOptions.MgfLimpModeProtectionEnabled = MgfLimpModeProtectionEnabled.IsChecked;
        _customOptions.LowPressureFuelPressureSensorInstalled = LowPressureFuelPressureSensorInstalled.IsChecked;
        _customOptions.MotivReflexInstalledCustomizable = MotivReflexInstalledCustomizable.IsChecked;
        _customOptions.MotivReflexCanBusIdInput = MotivReflexCanBusIdInput;
        _customOptions.MotivReflexCanBusIdOutput = MotivReflexCanBusIdOutput;
        _customOptions.MotivReflexDeepIntegration = MotivReflexDeepIntegration.IsChecked;
        _customOptions.IntakeInstalled = IntakeInstalled.IsChecked;

        _customOptions.HpfpInstalled = HpfpInstalled.IsChecked;
        _customOptions.HpfpLambdaEnrichment = HpfpLambdaEnrichment.IsChecked;
        _customOptions.B58TU_Installed = B58TU_Installed.IsChecked;
        //_customOptions.SpoolPerformance_Installed = SpoolPerformance_Installed.IsChecked; // remove obsolete
        //_customOptions.Dorch_Stage1_Installed = Dorch_Stage1_Installed.IsChecked; // remove obsolete
        //_customOptions.Dorch_Stage2_Installed = Dorch_Stage2_Installed.IsChecked; // remove obsolete
        _customOptions.SpoolPerformance_Fx150_Installed = SpoolPerformance_Fx150_Installed.IsChecked;
        _customOptions.SpoolPerformance_Fx170_Installed = SpoolPerformance_Fx170_Installed.IsChecked;
        _customOptions.SpoolPerformance_Fx180_Installed = SpoolPerformance_Fx180_Installed.IsChecked;
        _customOptions.SpoolPerformance_Fx200_Installed = SpoolPerformance_Fx200_Installed.IsChecked;
        _customOptions.SpoolPerformance_Fx350_Installed = SpoolPerformance_Fx350_Installed.IsChecked;
        _customOptions.SpoolPerformance_Fx400_Installed = SpoolPerformance_Fx400_Installed.IsChecked;
        _customOptions.SpoolPerformance_Fx400X_Installed = SpoolPerformance_Fx400X_Installed.IsChecked;
        _customOptions.DorchEngineering_Stage1_DS1_Installed = DorchEngineering_Stage1_DS1_Installed.IsChecked;
        _customOptions.DorchEngineering_Stage15_DS15_Installed = DorchEngineering_Stage15_DS15_Installed.IsChecked;
        _customOptions.DorchEngineering_Stage2_DS2_Installed = DorchEngineering_Stage2_DS2_Installed.IsChecked;
        _customOptions.DorchEngineering_Stage25_DS25_250Bar_Installed = DorchEngineering_Stage25_DS25_250Bar_Installed.IsChecked;
        _customOptions.DorchEngineering_Stage25_DS25_350Bar_Installed = DorchEngineering_Stage25_DS25_350Bar_Installed.IsChecked;
        _customOptions.DorchEngineering_Stage3_DS3_Installed = DorchEngineering_Stage3_DS3_Installed.IsChecked;
        _customOptions.XtremeDI_35_Installed = XtremeDI_35_Installed.IsChecked;
        _customOptions.XtremeDI_60_Installed = XtremeDI_60_Installed.IsChecked;
        _customOptions.XtremeDI_EVO_Installed = XtremeDI_EVO_Installed.IsChecked;
        _customOptions.PrecisionRaceworks_HPFP_Installed = PrecisionRaceworks_HPFP_Installed.IsChecked;

        _customOptions.InjectorsInstalled = InjectorsInstalled.IsChecked;
        _customOptions.SpoolPerformance_Ifx150_Installed = SpoolPerformance_Ifx150_Installed.IsChecked;
        _customOptions.SpoolPerformance_Ifx350_Installed = SpoolPerformance_Ifx350_Installed.IsChecked;
        _customOptions.SpoolPerformance_Ifx350X_Installed = SpoolPerformance_Ifx350X_Installed.IsChecked;
        _customOptions.XtremeDI_40_Installed = XtremeDI_40_Installed.IsChecked;
        _customOptions.XtremeDI_75_Installed = XtremeDI_75_Installed.IsChecked;
        _customOptions.Nostrum_Stage1_Installed = Nostrum_Stage1_Installed.IsChecked;
        _customOptions.Nostrum_Stage2_Installed = Nostrum_Stage2_Installed.IsChecked;

        _customOptions.OpfDelete = OpfDelete.IsChecked;
        _customOptions.LsdInstalled = LsdInstalled.IsChecked;
        _customOptions.LsdInstalledFinalDrive = LsdInstalledFinalDrive;

        _customOptions.EthanolSupport = EthanolSupport.IsChecked;
        _customOptions.FlexFuelBlending = FlexFuelBlending.IsChecked;
        _customOptions.FlexFuelSensorInstalled = FlexFuelSensorInstalled.IsChecked;
        _customOptions.FlexFuelSensorModel = FlexFuelSensorModel;
        _customOptions.FlexFuelSensorCanBusId = GetCanBusIdStringFromUserSelection(FlexFuelSensorModel);
        _customOptions.FlexFuelEthanolContent = FlexFuelEthanolContent.IsChecked;
        _customOptions.FlexFuelEthanolContentSlot0 = FlexFuelEthanolContentSlot0;
        _customOptions.FlexFuelEthanolContentSlot1 = FlexFuelEthanolContentSlot1;
        _customOptions.FlexFuelEthanolContentSlot2 = FlexFuelEthanolContentSlot2;
        _customOptions.FlexFuelEthanolContentSlot3 = FlexFuelEthanolContentSlot3;
        _customOptions.FlexFuelEthanolContentSlot4 = FlexFuelEthanolContentSlot4;
        _customOptions.FlexFuelDelayToShowOnDash = FlexFuelDelayToShowOnDash;

        _customOptions.CustomCodeMenu = CustomCodeMenu.IsChecked;
        _customOptions.CustomCodeMenuDelayToEnter = CustomCodeMenuDelayToEnter;
        _customOptions.CustomCodeMenuTimeout = CustomCodeMenuTimeout;
        _customOptions.UseRockerCombo = UseRockerCombo.IsChecked;
        _customOptions.UniRockerEnterDetectionDelay = UniRockerEnterDetectionDelay;
        _customOptions.CustomCodeMenuForceAllowAllButtonConfigurations = CustomCodeMenuForceAllowAllButtonConfigurations.IsChecked;

        _customOptions.RadiatorFlapOnTheFly = RadiatorFlapOnTheFly.IsChecked;
        _customOptions.ExhaustFlapOnTheFly = ExhaustFlapOnTheFly.IsChecked;
        _customOptions.ExhaustFlapOnTheFlyMaxLoad = ExhaustFlapOnTheFlyMaxLoad;

        _customOptions.ValetMode = ValetMode.IsChecked;
        _customOptions.ValetMaxPedal = ValetMaxPedal;
        _customOptions.ValetMaxEngineSpeed = ValetMaxEngineSpeed;
        _customOptions.ValetMaxVehicleSpeed = ValetMaxVehicleSpeed;
        _customOptions.ValetRegularTorqueLevel = ValetRegularTorqueLevel;
        _customOptions.ValetRestrictiveTorqueLevel = ValetRestrictiveTorqueLevel;

        //_customOptions.TcuLimiter = null; // remove obsolete
        //_customOptions.TcuLimiterTorque = null; // remove obsolete
        _customOptions.TcuLimiterStatic = TcuLimiterStatic.IsChecked;
        _customOptions.TcuLimiterStaticTorque = TcuLimiterStaticTorque;
        _customOptions.TcuLimiterDynamic = TcuLimiterDynamic.IsChecked;
        _customOptions.TcuTorqueLimiterPatchMethodIsDynamic = TcuTorqueLimiterPatchMethodIsDynamic;
        _customOptions.TcuLimiterTorqueStock = TcuLimiterTorqueStock;

        _customOptions.ColdStartDelete = ColdStartDelete.IsChecked;
        _customOptions.ColdStartDeleteAlways = ColdStartDeleteAlways.IsChecked;
        _customOptions.ColdStartDeleteIfWarm = ColdStartDeleteIfWarm.IsChecked;
        _customOptions.Dcat = Dcat.IsChecked;
        _customOptions.Vmax = Vmax.IsChecked;
        _customOptions.ExhaustFlapAlwaysOpen = ExhaustFlap.IsChecked;
        _customOptions.RadiatorFlap = RadiatorFlap.IsChecked;
        _customOptions.TorqueGauge = TorqueGauge.IsChecked;
        _customOptions.PowerGauge = PowerGauge.IsChecked;
        // Adjust gauges to real values:
        _customOptions.TorqueGaugeMax_Nm = TorqueGaugeMax_Nm * CustomOptionsUserConfiguration.GaugeMultiplier10;
        _customOptions.TorqueGaugeMax_lbft = TorqueGaugeMax_lbft * CustomOptionsUserConfiguration.GaugeMultiplier10;
        _customOptions.TorqueGaugeMax_kgm = TorqueGaugeMax_kgm * CustomOptionsUserConfiguration.GaugeMultiplier5;
        _customOptions.PowerGaugeMax_kW = PowerGaugeMax_kW * CustomOptionsUserConfiguration.GaugeMultiplier10;
        _customOptions.PowerGaugeMax_hp = PowerGaugeMax_hp * CustomOptionsUserConfiguration.GaugeMultiplier10;

        if (InductionNoise.IsVisible)
        {
            _customOptions.InductionNoise = InductionNoise.IsChecked;
        }
        else
        {
            _customOptions.InductionNoise = false;
        }
        //
        _customOptions.Burble = Burble.IsChecked;
        _customOptions.BurbleSpecifyDrivingModes = BurbleSpecifyDrivingModes.IsChecked;
        _customOptions.BurbleEnableEcoDrivingMode = BurbleEnableEcoDrivingMode.IsChecked;
        _customOptions.BurbleEnableEcoPlusDrivingMode = BurbleEnableEcoPlusDrivingMode.IsChecked;
        _customOptions.BurbleEnableComfortDrivingMode = BurbleEnableComfortDrivingMode.IsChecked;
        _customOptions.BurbleEnableComfortPlusDrivingMode = BurbleEnableComfortPlusDrivingMode.IsChecked;
        _customOptions.BurbleEnableSportDrivingMode = BurbleEnableSportDrivingMode.IsChecked;
        _customOptions.BurbleEnableSportPlusDrivingMode = BurbleEnableSportPlusDrivingMode.IsChecked;
        _customOptions.BurbleUpShift = BurbleUpShift.IsChecked;
        _customOptions.BurbleUpShiftAggression = BurbleUpShiftAggression;
        _customOptions.BurbleHybridPatch = BurbleHybridPatch.IsChecked;
        _customOptions.BurbleTemperatureLimitIncrease = BurbleTemperatureLimitIncrease.IsChecked;
        _customOptions.BurbleForceMaxAggressionAndDurationFactor = BurbleForceMaxAggressionAndDurationFactor.IsChecked;
        if (Burble_CW_SOUND.IsVisible)
        {
            _customOptions.Burble_CW_SOUND = Burble_CW_SOUND.IsChecked;
            _customOptions.Burble_CW_SOUND_Value = Burble_CW_SOUND_Value;
        }
        else
        {
            _customOptions.Burble_CW_SOUND = false;
            _customOptions.Burble_CW_SOUND_Value = 0x2B;
        }

        _customOptions.BurbleStyle1Aggressiveness = BurbleStyle1Aggressiveness;
        _customOptions.BurbleStyle2Aggressiveness = BurbleStyle2Aggressiveness;
        _customOptions.BurbleStyle3SportAggressiveness = BurbleStyle3SportAggressiveness;
        _customOptions.BurbleStyle3NormalAggressiveness = BurbleStyle3NormalAggressiveness;
        _customOptions.BurbleDurationSport = BurbleDurationSport;
        _customOptions.BurbleDurationSportPlus = BurbleDurationSportPlus;
        _customOptions.BurbleRpmMin = BurbleRpmMin;
        _customOptions.BurbleRpmMax = BurbleRpmMax;
        _customOptions.BurbleSpeedMin = BurbleSpeedMin;
        _customOptions.BurbleSpeedMax = BurbleSpeedMax;
        _customOptions.BurbleAggressionStyleIndex_Selected = BurbleStyleList.IndexOf(BurbleAggressionStyle);
        _customOptions.Cooling = Cooling.IsChecked;
        _customOptions.MaxCooling = MaxCooling.IsChecked;
        _customOptions.MaxCoolingRegularTargetEngineTemperature = MaxCoolingRegularTargetEngineTemperature;
        _customOptions.MaxCoolingTargetEngineTemperature = MaxCoolingTargetEngineTemperature;
        _customOptions.MaxCoolingTargetIntercoolerCoolantVolumeFlow = MaxCoolingTargetIntercoolerCoolantVolumeFlow;
        _customOptions.BurbleFlame = BurbleFlame.IsChecked;
        _customOptions.BurbleFlameTiming = BurbleFlameTiming;
        _customOptions.BurbleFlameFuel = BurbleFlameFuel;
        _customOptions.StartupRoar = StartupRoar.IsChecked;
        _customOptions.StartupRoarDuration = StartupRoarDuration;
        _customOptions.StartupRoarAggression = StartupRoarAggression;
        _customOptions.TorqueByGear = TorqueByGear.IsChecked;
        _customOptions.TorqueByGear_AllGears = TorqueByGear_AllGears.IsChecked;
        _customOptions.TorqueByGear_IndividualGears = TorqueByGear_IndividualGears.IsChecked;
        _customOptions.TorqueByGear_GearAll_MaxTorque = TorqueByGear_GearAll_MaxTorque;
        _customOptions.TorqueByGear_Gear0_MaxTorque = TorqueByGear_Gear0_MaxTorque;
        _customOptions.TorqueByGear_Gear1_MaxTorque = TorqueByGear_Gear1_MaxTorque;
        _customOptions.TorqueByGear_Gear2_MaxTorque = TorqueByGear_Gear2_MaxTorque;
        _customOptions.TorqueByGear_Gear3_MaxTorque = TorqueByGear_Gear3_MaxTorque;
        _customOptions.TorqueByGear_Gear4_MaxTorque = TorqueByGear_Gear4_MaxTorque;
        _customOptions.TorqueByGear_Gear5_MaxTorque = TorqueByGear_Gear5_MaxTorque;
        _customOptions.TorqueByGear_Gear6_MaxTorque = TorqueByGear_Gear6_MaxTorque;
        _customOptions.TorqueByGear_Gear7_MaxTorque = TorqueByGear_Gear7_MaxTorque;
        _customOptions.TorqueByGear_Gear8_MaxTorque = TorqueByGear_Gear8_MaxTorque;
        _customOptions.TorqueByGear_Gear9_MaxTorque = TorqueByGear_Gear9_MaxTorque;

        _customOptions.IdleRpm = IdleRpm.IsChecked;
        _customOptions.IdleRpmTargetBase = IdleRpmTargetBase;
        _customOptions.IdleRpm6mt = IdleRpm6mt.IsChecked;
        _customOptions.IdleRpmTargetClutchDelay = IdleRpmTargetClutchDelay;
        _customOptions.IdleRpmTargetClutch = IdleRpmTargetClutch;
        _customOptions.IdleRpmTargetClutchFast = IdleRpmTargetClutchFast.IsChecked;

        _customOptions.TunerOverrideDst = TunerOverrideDst.IsChecked;

        _customOptions.DtcCodeMasking = RemoveGeneralDtcCodes.IsChecked;
        _customOptions.GeneralDtcCodesToRemove = _customOptions.DtcCodeMasking
            ? (await _dtcCodesResolver.GetGeneralDtcCodesAsync(await _carFlashCommandA2LExtractor.GetAsync(_car, _flasherCommand))).Select(x => x.Code).ToList()
            : new();
        _customOptions.UserDtcCodesDeletion = RemoveUserDtcCodes.IsChecked;
        _customOptions.UserDtcCodesToRemove ??= new();

        _customOptions.Antilag = Antilag.IsChecked;
        _customOptions.AntilagTargetBoostRoll = AntilagTargetBoostRoll;
        _customOptions.AntilagTargetBoostStand = AntilagTargetBoostStand;
        _customOptions.AntilagRpmSetpoint = AntilagRpmSetpoint;
        _customOptions.AntilagRollingSpeedThreshold = AntilagRollingSpeedThreshold;
        _customOptions.AntilagRollRpmMin = AntilagRollRpmMin;
        _customOptions.AntilagRollRpmMax = AntilagRollRpmMax;
        _customOptions.AntilagAggressionRoll = AntilagAggressionRoll;
        _customOptions.AntilagAggressionStand = AntilagAggressionStand;
        _customOptions.AntilagPedal = AntilagPedal;
        _customOptions.AntilagActivationDelay = AntilagActivationDelay;
        _customOptions.AntilagMaxDuration = AntilagMaxDuration;
        _customOptions.AntilagEngineSlot = AntilagEngineSlot;

        _customOptions.AntilagDevOptions = AntilagDevOptions.IsChecked;
        _customOptions.AntilagExpertOptions = AntilagExpertOptions.IsChecked;
        if (_customOptions.AntilagExpertOptions)
        {
            if (AntilagUseIgnitionCut.IsVisible)
            {
                _customOptions.AntilagUseIgnitionCut = AntilagUseIgnitionCut.IsChecked;
            }
            else
            {
                _customOptions.AntilagUseIgnitionCut = false;
            }

            _customOptions.AntilagTargetNumberOfCylindersToSuppress = AntilagTargetNumberOfCylindersToSuppress;
            _customOptions.AntilagRpmDiffRoll = AntilagRpmDiffRoll;
            _customOptions.AntilagRpmDiffStand = AntilagRpmDiffStand;
            _customOptions.AntilagOverBoostThreshold = AntilagOverBoostThreshold;
            _customOptions.AntilagCoolantTempMin = AntilagCoolantTempMin;
            _customOptions.AntilagCoolantTempMax = AntilagCoolantTempMax;
            _customOptions.AntilagIatTempMax = AntilagIatTempMax;
            _customOptions.AntilagEgtTempMax = AntilagEgtTempMax;
            if (_customOptions.AntilagEgtTempMax > 55)
            {
                _customOptions.AntilagEgtTempMax = 55;
            }

            _customOptions.AntilagCurveSafetyTorqueCap = AntilagCurveSafetyTorqueCap;
            _customOptions.AntilagCurveIgnStand = AntilagCurveIgnStand;
            _customOptions.AntilagCurveIgnRoll = AntilagCurveIgnRoll;
            _customOptions.AntilagCurveLambdaStand = AntilagCurveLambdaStand;
            _customOptions.AntilagCurveLambdaRoll = AntilagCurveLambdaRoll;

            _customOptions.AntilagFlatSafetyTorqueCap = AntilagFlatSafetyTorqueCap;
            _customOptions.AntilagFlatIgnStand = AntilagFlatIgnStand;
            _customOptions.AntilagFlatIgnRoll = AntilagFlatIgnRoll;
            _customOptions.AntilagFlatLambdaStand = AntilagFlatLambdaStand;
            _customOptions.AntilagFlatLambdaRoll = AntilagFlatLambdaRoll;
        }
        else
        {
            _customOptions.SetAntilagSimpleMode();
        }

        if (FlashingSegmentOptions.IsChecked && ForceFlashPST.IsChecked && !ForceFlashPST_WithCustomCode.IsChecked && !ForceFlashPST_WithoutCustomCode.IsChecked)
        {
            await _userDialogs.AlertAsync(AppResources.CO_ForceFlashPst_Warning,
                okText: AppResources.Continue,
                title: AppResources.CO_ForceFlashPst_Warning_Title);

            _logger.LogInformation("User selected Force Flash PST without selecting if they want/don't want custom code applied");
            return;
        }

        if (CustomCodeMenu.IsChecked
            && ValetMode.IsChecked
            && ValetRestrictiveTorqueLevel > ValetRegularTorqueLevel)
        {
            await _userDialogs.AlertAsync(AppResources.CO_ValetRegularGreaterThanRestrictiveTorqueLevel_Warning,
                okText: AppResources.Continue,
                title: AppResources.CO_ValetRegularGreaterThanRestrictiveTorqueLevel_Warning_Title);
            _logger.LogInformation(AppResources.CO_ValetRegularGreaterThanRestrictiveTorqueLevel_Warning_Title);
            return;
        }

        if (_targetCustomCodeVersion != null
            && _targetCustomCodeVersion >= new Version(7, 1)
            && CustomCodeMenu.IsVisible
            && CustomCodeMenu.IsChecked
            && UseRockerCombo.IsVisible
            && !UseRockerCombo.IsChecked)
        {
            var key = $"CustomCodeMenu_UniRockerAsDefault-shown";
            if (!Preferences.Get(key, false))
            {
                var rememberPromptWasShown = await _userDialogs.ConfirmAsync(AppResources.CO_CustomCodeMenuUniRockerAsDefault_Info,
                    okText: AppResources.DontShowMeAgain,
                    cancelText: AppResources.Continue,
                    title: AppResources.CO_CustomCodeMenuRockerComboIsAvailable_Title);

                if (rememberPromptWasShown)
                {
                    Preferences.Set(key, true);
                }
            }
        }

        if (SwitchableMaps.IsChecked
            && !HpfpInstalled.IsChecked
            && _hpfpStageRequired
            && _car.IsB5X()
            && _car.EcuMaster?.SweVersions != null
            && _car.EcuMaster.SweVersions.Any(x => x?.ProcessClass == ProcessClassType.SWFL && (x.IsGxx_Gen1() || x.IsFxx_Gen1())))
        {
            var noHPFP_confirmation = await _userDialogs.ConfirmAsync(AppResources.CO_SWITCHABLE_MAPS_Warning,
                okText: AppResources.Continue,
                cancelText: AppResources.Cancel,
                title: AppResources.CO_SWITCHABLE_MAPS_Warning_Title);
            if (!noHPFP_confirmation)
            {
                return;
            }
            else
            {
                _logger.LogInformation("User acknowledged the absence of a HPFP when flashing Stage 2/2.5 with switchable maps enabled");
            }

            _logger.LogInformation(AppResources.CO_SWITCHABLE_MAPS_Warning);
        }

        if ((HpfpInstalled.IsChecked
                && (DorchEngineering_Stage1_DS1_Installed.IsChecked || DorchEngineering_Stage15_DS15_Installed.IsChecked))
            && (InjectorsInstalled.IsChecked
                && (XtremeDI_40_Installed.IsChecked
                    || XtremeDI_75_Installed.IsChecked)))
        {
            await _userDialogs.AlertAsync(AppResources.CO_HpfpAndInjectorCombinationCausesCalibrationConflict_Warning,
                okText: AppResources.Ok,
                title: AppResources.CO_HpfpAndInjectorCombinationCausesCalibrationConflict_Warning_Title);
            _logger.LogInformation(AppResources.CO_HpfpAndInjectorCombinationCausesCalibrationConflict_Warning_Title);
            return;
        }

        if (HpfpInstalled.IsVisible
            && HpfpInstalled.IsChecked
            && HpfpLambdaEnrichment.IsVisible
            && !HpfpLambdaEnrichment.IsChecked
            && !_appContext.IsEcuTestUser())
        {
            var key = $"CustomOptions_HpfpLambdaEnrichmentAvailable-shown";
            if (!Preferences.Get(key, false))
            {
                var rememberPromptWasShown = await _userDialogs.ConfirmAsync(AppResources.CO_HpfpLambdaEnrichmentAvailable_Info,
                    okText: AppResources.DontShowMeAgain,
                    cancelText: AppResources.Continue,
                    title: AppResources.CO_HpfpLambdaEnrichmentAvailable_Title);

                if (rememberPromptWasShown)
                {
                    Preferences.Set(key, true);
                }
            }
        }

        if (TunerOverrideDst.IsChecked && _customStage)
        {
            var noCCdata_confirmation = await _userDialogs.ConfirmAsync(AppResources.CO_TunerOverride_Warning,
                okText: AppResources.Continue,
                cancelText: AppResources.Cancel,
                title: AppResources.CO_TunerOverride_Warning_Title);
            if (!noCCdata_confirmation)
            {
                return;
            }
            else
            {
                _logger.LogInformation("User acknowledged the absence of Custom Code data (burbles + max cooling)");
            }

            _logger.LogInformation(AppResources.CO_TunerOverride_Warning);
        }

        if (Antilag.IsChecked && Dcat.IsChecked)
        {
            var antilag_confirmation = await _userDialogs.ConfirmAsync(AppResources.CO_Antilag_Warning,
                okText: AppResources.Continue,
                cancelText: AppResources.Cancel,
                title: AppResources.CO_Antilag_Warning_Title);
            if (!antilag_confirmation)
            {
                return;
            }
            else
            {
                _logger.LogInformation("User acknowledged the risks of using Antilag");
            }

            _logger.LogInformation(AppResources.CO_Antilag_Warning_Title);

            if (_customOptions.AntilagTargetBoostRoll > CustomOptionsUserConfiguration.AntilagTargetBoostWarningLimit ||
                _customOptions.AntilagTargetBoostStand > CustomOptionsUserConfiguration.AntilagTargetBoostWarningLimit)
            {
                var antilag_targetBoost_confirmation = await _userDialogs.ConfirmAsync(AppResources.CO_Antilag_HighBoost_Warning,
                    okText: AppResources.Continue,
                    cancelText: AppResources.Cancel,
                    title: AppResources.CO_Antilag_HighBoost_Warning_Title);
                if (!antilag_targetBoost_confirmation)
                {
                    return;
                }
                else
                {
                    _logger.LogInformation("User acknowledged the risks of using increased boost for Antilag");
                }

                _logger.LogInformation(AppResources.CO_Antilag_HighBoost_Warning_Title);
            }

            if (AntilagUseIgnitionCut.IsChecked)
            {
                var antilag_IgnitionCut_confirmation = await _userDialogs.ConfirmAsync(AppResources.CO_Antilag_IgnitionCut_Warning,
                    okText: AppResources.Continue,
                    cancelText: AppResources.Cancel,
                    title: AppResources.CO_Antilag_IgnitionCut_Warning_Title);
                if (!antilag_IgnitionCut_confirmation)
                {
                    return;
                }
                else
                {
                    _logger.LogInformation("User acknowledged the risks of using ignition cut for Antilag");
                }

                _logger.LogInformation(AppResources.CO_Antilag_IgnitionCut_Warning_Title);
            }
        }

        if (Burble.IsChecked && Dcat.IsChecked && BurbleFlame.IsChecked)
        {
            var flameMap_confirmation = await _userDialogs.ConfirmAsync(AppResources.CO_BurbleFlame_Warning,
                okText: AppResources.Continue,
                cancelText: AppResources.Cancel,
                title: AppResources.CO_BurbleFlame_Warning_Title);
            if (!flameMap_confirmation)
            {
                return;
            }
            else
            {
                _logger.LogInformation("User acknowledged the risks of using a flame map");
            }

            _logger.LogInformation(AppResources.CO_BurbleFlame_Warning);
        }

        if (Burble.IsChecked && BurbleTemperatureLimitIncrease.IsChecked && !Dcat.IsChecked && !OpfDelete.IsChecked)
        {
            _logger.LogInformation(AppResources.CO_BurbleTemperature_DcatRequirement);
            await _userDialogs.AlertAsync(AppResources.CO_BurbleTemperature_DcatRequirement,
                okText: AppResources.Ok,
                title: AppResources.CO_BurbleTemperature_DcatRequirement_Title);
            return;
        }

        if (LsdInstalled.IsChecked)
        {
            var lsdInstalled_confirmation = await _userDialogs.ConfirmAsync(AppResources.CO_LsdInstalled_Warning,
                okText: AppResources.Continue,
                cancelText: AppResources.Cancel,
                title: AppResources.CO_LsdInstalled_Warning_Title);
            if (!lsdInstalled_confirmation)
            {
                return;
            }
            else
            {
                _logger.LogInformation("User acknowledged the risks of using incorrect final drive ratios");
            }

            _logger.LogInformation(AppResources.CO_LsdInstalled_Warning_Title);

            if (LsdInstalledFinalDrive < 2.0 || LsdInstalledFinalDrive > 5.0)
            {
                await _userDialogs.AlertAsync(AppResources.CO_LsdInstalledFinalDrive_Warning,
                    okText: AppResources.Cancel,
                    title: AppResources.CO_LsdInstalledFinalDrive_Warning_Title);
                _logger.LogInformation(AppResources.CO_LsdInstalledFinalDrive_Warning_Title);
                return;
            }
        }

        if (CustomCodeMenu.IsChecked
            && ExhaustFlapOnTheFly.IsChecked
            && ExhaustFlapOnTheFlyMaxLoad > 10)
        {
            var exhaustFlapOnTheFlyMaxLoad_confirmation = await _userDialogs.ConfirmAsync(AppResources.CO_ExhaustFlapOnTheFlyMaxLoad_Warning,
                okText: AppResources.Continue,
                cancelText: AppResources.Cancel,
                title: AppResources.CO_ExhaustFlapOnTheFlyMaxLoad_Warning_Title);
            if (!exhaustFlapOnTheFlyMaxLoad_confirmation)
            {
                return;
            }
            else
            {
                _logger.LogInformation("User acknowledged the risks of using incorrect max load for opening exhaust flaps");
            }

            _logger.LogInformation(AppResources.CO_ExhaustFlapOnTheFlyMaxLoad_Warning_Title);
        }

        if (_targetCustomCodeVersion != null
            && _targetCustomCodeVersion >= new Version(7, 2, 1)
            && MgfLimpModeProtectionEnabled.IsVisible
            && !MgfLimpModeProtectionEnabled.IsChecked
            && (LowPressureFuelPressureSensorInstalled.IsChecked
                || MotivReflexInstalledCustomizable.IsChecked
                || FlexFuelSensorInstalled.IsChecked)
                )
        {
            var mgfLimpModeProtectionEnabled_confirmation = await _userDialogs.ConfirmAsync(AppResources.CO_MgfLimpModeProtectionEnabled_Warning,
                okText: AppResources.Yes,
                cancelText: AppResources.No,
                title: AppResources.CO_MgfLimpModeProtectionEnabled_Warning_Title);
            if (!mgfLimpModeProtectionEnabled_confirmation)
            {
                return;
            }
            else
            {
                _logger.LogInformation("User acknowledged the risks of disabling MGF's limp mode protection system");
            }

            _logger.LogInformation(AppResources.CO_MgfLimpModeProtectionEnabled_Warning_Title);
        }

        if (_targetCustomCodeVersion != null
            && _targetCustomCodeVersion >= new Version(7, 2, 1)
            && MotivReflexInstalledCustomizable.IsVisible
            && MotivReflexInstalledCustomizable.IsChecked
            && !MotivReflexCanBusIdOutput.Equals(AppResources.FlashingCarOptionsPage_MotivReflexCanBusIdOutput_0x00EC, StringComparison.InvariantCultureIgnoreCase))
        {
            var key = $"FlashingOptions-MotivCanBusIdConflictNonDefault-shown";
            if (!Preferences.Get(key, false))
            {
                var rememberPromptWasShown = await _userDialogs.ConfirmAsync(AppResources.CO_MotivCanBusConflictNonDefault_Info,
                    okText: AppResources.DontShowMeAgain,
                    cancelText: AppResources.Continue,
                    title: AppResources.CO_MotivCanBusConflict_Title);

                if (rememberPromptWasShown)
                {
                    Preferences.Set(key, true);
                }
            }
        }

        if (_targetCustomCodeVersion != null
            && _targetCustomCodeVersion >= new Version(7, 2, 1)
            && MotivReflexInstalledCustomizable.IsVisible
            && MotivReflexInstalledCustomizable.IsChecked
            && FlexFuelSensorInstalled.IsVisible
            && FlexFuelSensorInstalled.IsChecked
            && !string.IsNullOrWhiteSpace(FlexFuelSensorModel)
            && !FlexFuelSensorModel.Equals(AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_MotivReflex, StringComparison.InvariantCultureIgnoreCase)
            && (GetCanBusIdStringFromUserSelection(FlexFuelSensorModel).Equals(GetCanBusIdStringFromUserSelection(MotivReflexCanBusIdOutput), StringComparison.InvariantCultureIgnoreCase)
                || GetCanBusIdStringFromUserSelection(FlexFuelSensorModel).Equals(GetCanBusIdStringFromUserSelection(MotivReflexCanBusIdInput), StringComparison.InvariantCultureIgnoreCase))
            )
        {
            await _userDialogs.AlertAsync(AppResources.CO_MotivCanBusConflictWithEca_Info,
                title: AppResources.CO_MotivCanBusConflict_Title,
                okText: AppResources.Ok);
            return;
        }

        await _customOptionsUserConfigurationService.SetAsync(_car.VIN, _customOptions);

        var flashingCarPageNavigationArguments = new FlashingCarPageNavigationArgs()
        {
            ContinousCommand = _flasherCommand,
            Options = _customOptions
        };
        await _navigationService.NavigateAsync(PageType.FlashingCarWarningInfo, flashingCarPageNavigationArguments);
    }

    public override Task OnNavigatedFromAsync(PageType nextPage)
    {
        ScrollTo = null;
        return Task.CompletedTask;
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (previousPage == PageType.UserDtcRemovalList)
        {
            ApplyDtcRemovalListSelection(arg);
            return;
        }

        if (previousPage == PageType.FlashingCarOptionsItem)
        {
            ScrollTo = arg;
            return;
        }

        _car = await _dCANService.GetConnectedCar();
        NumberOfCylindersInEngine = (_car.IsB5X() || _car.IsS5X()) ? 6 : _car.IsB4X() ? 4 : _car.IsS6X() ? 8 : 0;
        _installedCustomCodeVersion = CustomCodeVersions.GetCustomCodeVersionFromString(_car.EcuMaster.CustomCodeVersionInstalled);

        if (arg is UniFlashStageFlashCommand uniFlashStageFlashCommand)
        {
            _selectedMapId = uniFlashStageFlashCommand.MapId;
        }

        if (arg is UniFlashFlashingCommandSupport uniFlashFlashingCommandSupport)
        {
            _targetCustomCodeVersionString = uniFlashFlashingCommandSupport.TargetCustomCodeVersionString;
            _targetCustomCodeVersion = CustomCodeVersions.GetCustomCodeVersionFromString(_targetCustomCodeVersionString);
            _logger.LogInformation($"Custom Code version available in app: [v{_targetCustomCodeVersion}]");
        }

        if (arg is IContinousFlasherCommand continousFlasherCommand)
        {
            _flasherCommand = continousFlasherCommand;
        }

        _customOptions = await _customOptionsUserConfigurationService.GetOrDefaultAsync(_car.VIN);
        await SetInitialCustomOptions();
        OnPropertyChanged(nameof(IsDeveloperOnlyVisible));

        if (NotifyUserSwitchableMapsAreAvailable == true)
        {
            await _userDialogs.AlertAsync(AppResources.SwitchableMaps_OTSminVersionWarning, title: AppResources.SwitchableMaps_OTSminVersionWarning_Title);
            _logger.LogInformation(AppResources.SwitchableMaps_OTSminVersionWarning);
        }

        if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled) && _car.EcuMaster.CustomCodeVersionInstalled == "v6.9")
        {
            await _userDialogs.AlertAsync(AppResources.CustomCode_AntilagUpdateFuelCut, title: AppResources.CustomCode_AntilagUpdateFuelCut_Title);
            _logger.LogInformation(AppResources.CustomCode_AntilagUpdateFuelCut);
        }

        await base.OnNavigatedToAsync(previousPage, arg);
    }

    public object ScrollTo
    {
        get => _scrollTo;
        private set
        {
            _scrollTo = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Ask the user if they want to use the Guided Mode, or stay in Expert Mode.
    /// </summary>
    public void ShowGuidedModePrompt()
    {
        if (!_wizardmodePromptShown && !Preferences.Get(_wizardModePromptCacheKey, false))
        {
            _wizardmodePromptShown = true;
            _questionPopup = PopupHelper.GenerateGuidedModePrompt(HandleResponse);
            _questionPopup.Show();
        }
    }

    private void HandleResponse(string response)
    {
        _questionPopup.Dismiss();

        if (response == AppResources.PopupTutorials_DontShowAgain)
        {
            Preferences.Set(_wizardModePromptCacheKey, true);
        }
        else if (response == AppResources.Yes)
        {
            IsWizardMode = true;
            CurrentWizardStep = 1;
        }
        else if (response == AppResources.No)
        {
            IsWizardMode = false;
        }
    }

    private void ApplyDtcRemovalListSelection(object arg)
    {
        if (arg is not DtcRemovalListPageResult result)
        {
            return;
        }

        _customOptions.UserDtcCodesToRemove = result.RemovalList?.ToList() ?? new();
    }

    private async Task SetInitialCustomOptions()
    {
        _hpfpStageRequired = CheckHpfpStageRequirements();
        _customStage = CheckIfCustomStage();

        _customOptions.AntilagEngineSlot = _carCompatabilityService.GetAntilagEngineSlot(_car);
        _customOptions.SetAntilagDefaults();
        _customOptions.SetCoolingDefaults();
        _customOptions.SetGaugeDefaults();
        _customOptions.SetBurble_CW_Sound_Value_Defaults();
        _customOptions.SetBurbleUpShiftAggressionDefaults();
        SetBurbleStyleList();
        SetHpfpAvailability();
        SetInjectorAvailability();
        _customOptions.SetBurbleStyle1AggressionDefaults();
        _customOptions.SetBurbleStyle2AggressionDefaults();
        _customOptions.SetBurbleStyle3AggressionDefaults();
        _customOptions.SetBurbleDurationSportDefaults();
        _customOptions.SetBurbleDurationSportPlusDefaults();
        _customOptions.SetBurbleRpmMaxDefaults();
        _customOptions.SetBurbleRpmMinDefaults();
        _customOptions.SetBurbleSpeedMaxDefaults();
        _customOptions.SetBurbleSpeedMinDefaults();
        _customOptions.SetBurbleAggressionStyleDefaults();
        _customOptions.SetTcuLimiterStaticDefaults();
        _customOptions.SetBurbleFlameDefaults();
        _customOptions.SetStartupRoarDefaults();
        _customOptions.SetTorqueByGearDefaults();
        _customOptions.SetIdleRpmDefaults();
        _customOptions.SetEcaSettingDefaults();
        _customOptions.SetEcaCanBusIdDefaults();

        _customOptions.GeneralDtcCodesToRemove ??= (await _dtcCodesResolver.GetGeneralDtcCodesAsync(await _carFlashCommandA2LExtractor.GetAsync(_car, _flasherCommand))).Select(x => x.Code).ToList();
        _customOptions.UserDtcCodesToRemove ??= new();

        SkipCrcCorrection.IsVisible = SetSkipCrcCorrectionVisibility();
        DeleteBenchEcuDtcs.IsVisible = SetDeleteBenchEcuDtcsVisibility();
#if DEBUG
        PrepareFileWithoutFlashing.IsVisible = true;
#else
            PrepareFileWithoutFlashing.IsVisible = _appContext.IsDeveloper();
#endif
        FlashingSegmentOptions.IsVisible = _appContext.IsDeveloper() || _appContext.IsTesterPST();
        SwitchableMaps.IsVisible = SetSwitchableMapsVisibility();
        MgfLimpModeProtectionEnabled.IsVisible = CustomCodeVersions.IsAtLeastCustomCodev721("v" + _targetCustomCodeVersion);
        LowPressureFuelPressureSensorInstalled.IsVisible = CustomCodeVersions.IsAtLeastCustomCodev721("v" + _targetCustomCodeVersion);
        MotivReflexInstalledCustomizable.IsVisible = CustomCodeVersions.IsAtLeastCustomCodev721("v" + _targetCustomCodeVersion);
        MotivReflexDeepIntegration.IsVisible = false; // Once CC is developed for deep Motiv ReFlex integration, set the min version
        _customOptions.SetMotivReflexCanBusIdDefaults();
        IntakeInstalled.IsVisible = true;
        HpfpInstalled.IsVisible = IsHpfpSupportedCar();
        InjectorsInstalled.IsVisible = IsInjectorSupportedCar();
        OpfDelete.IsVisible = true; // TODO: check car if it comes with OPF

        _customOptions.TcuLimiterTorqueStock =
            _car.IsB4X() ? 440
            : _car.IsB58() ? 550
            : _car.IsS58() ? 650
            : _car.IsN63() ? 750
            : _car.IsN74() ? 900
            : _car.IsS63() ? 800
            : 1000;
        TcuLimiterStatic_Name = AppResources.FlashingCarOptionsPage_TcuLimiterStatic + "\n(" + AppResources.FlashingCarOptionsPage_TcuLimiterStatic_DefaultLimit + "1000 Nm)";
        TcuLimiterDynamic.IsVisible = SetTcuLimiterDynamicVisibility();
        TcuLimiterStatic.IsVisible = SetTcuLimiterStaticVisibility();

        RadiatorFlap.IsVisible = SetRadiatorFlapVisibility();
        TorqueGauge.IsVisible = true;
        PowerGauge.IsVisible = true;
        BurbleSpecifyDrivingModes.IsVisible = !_car.IsB48H();

        BurbleUpShift.IsVisible = true;
        Burble_CW_SOUND.IsVisible = _appContext.IsDeveloper() || _appContext.IsTroubleshooter() || _appContext.IsTuner();
        Cooling.IsVisible = true;
        MaxCooling.IsVisible = _carCompatabilityService.IsMaxCoolingSupportedCar(_car, _targetCustomCodeVersion);
        BurbleFlame.IsVisible = _carCompatabilityService.IsFlameMapSupportedCar(_car);
        StartupRoar.IsVisible = _carCompatabilityService.IsStartupRoarSupportedCar(_car);

        InductionNoise.IsVisible = _carCompatabilityService.IsInductionNoiseSupported(_car);

        RemoveGeneralDtcCodes.IsVisible = _appContext.IsDeveloper() || _appContext.IsTroubleshooter() || _appContext.IsTuner();
        RemoveUserDtcCodes.IsVisible = true;

        TunerOverrideDst.IsVisible = SetTunerOverrideDstVisibility();
        Antilag.IsVisible = SetAntilagVisibility();

        AntilagExpertOptions.IsVisible = Antilag.IsVisible;

        AntilagUseIgnitionCut.IsVisible = Antilag.IsVisible && SetAntilagIgnitionCutVisibility();
        CanAdjustAntilagRpmDiff = SetAntilagRpmDiffVisibility();
        CanAdjustAntilagTargetNumberOfCylindersToSuppress = SetAntilagTargetNumberOfCylindersToSuppressVisibility();

        AntilagDevOptions.IsVisible = _appContext.IsDeveloper();

        SkipCrcCorrection.IsChecked = _customOptions.SkipCrcCorrection;
        DeleteBenchEcuDtcs.IsChecked = _customOptions.DeleteBenchEcuDtcs;
        PrepareFileWithoutFlashing.IsChecked = _customOptions.PrepareFileWithoutFlashing;
        FlashingSegmentOptions.IsChecked = false; // forces the user to enable for each flash;
        SetCustomizableDefaults_FlashingSegmentOptions(); // forces the user to disable for each flash
        SwitchableMaps.IsChecked = (_appContext.IsDeveloper() || _appContext.IsTesterSwitchableMaps() || !CheckIfCustomStage() || CustomCodeVersions.IsAtLeastCustomCodev7x("v" + _targetCustomCodeVersion)) && _customOptions.SwitchableMaps; // set to false if not X user or in custom flash

        MgfLimpModeProtectionEnabled.IsChecked = _customOptions.MgfLimpModeProtectionEnabled;
        LowPressureFuelPressureSensorInstalled.IsChecked = _customOptions.LowPressureFuelPressureSensorInstalled;
        MotivReflexInstalledCustomizable.IsChecked = _customOptions.MotivReflexInstalledCustomizable;
        SetCustomizableDefaults_MotivReflex();

        IntakeInstalled.IsChecked = _customOptions.IntakeInstalled;
        HpfpInstalled.IsChecked = _customOptions.HpfpInstalled;
        OnPropertyChanged(nameof(HpfpSelectedPreview));
        SetCustomizableDefaults_HpfpInstalled();
        InjectorsInstalled.IsChecked = _customOptions.InjectorsInstalled;
        SetCustomizableDefaults_InjectorsInstalled();

        OpfDelete.IsChecked = _customOptions.OpfDelete;
        LsdInstalled.IsChecked = _customOptions.LsdInstalled;
        SetCustomizableDefaults_LsdInstalled();
        EthanolSupport.IsChecked = _customOptions.EthanolSupport;
        SetCustomizableDefaults_EthanolSupport();

        EthanolSupport.IsVisible = SetEthanolSupportVisibility();
        FlexFuelEthanolContentAllowed = SetFlexFuelEthanolContentVisibility();
        FlexFuelDelayToShowOnDashAllowed = SetFlexFuelDelayToShowOnDashVisibility();

        CustomCodeMenu.IsVisible = SetCustomCodeMenuVisibility();
        CustomCodeMenu.IsChecked = _customOptions.CustomCodeMenu;

        CustomCodeMenuForceAllowAllButtonConfigurations.IsVisible = SetCustomCodeMenuForceAllowAllButtonConfigurationsVisibility();
        UseRockerCombo.IsVisible = SetUseRockerComboVisibility();
        SetCustomizableDefaults_CustomCodeMenu();

        RadiatorFlapOnTheFly.IsVisible = SetRadiatorFlapOnTheFlyVisibility();
        RadiatorFlapOnTheFly.IsChecked = _customOptions.RadiatorFlapOnTheFly;
        ExhaustFlapOnTheFly.IsVisible = SetExhaustFlapOnTheFlyVisibility();
        ExhaustFlapOnTheFly.IsChecked = _customOptions.ExhaustFlapOnTheFly;
        SetCustomizableDefaults_ExhaustFlapOnTheFly();

        ValetMode.IsVisible = SetValetModeVisibility();
        ValetMode.IsChecked = _customOptions.ValetMode;
        SetCustomizableDefaults_ValetMode();

        TcuLimiterStatic.IsChecked = _customOptions.TcuLimiterStatic || _customOptions.TcuLimiter; // merging old settings/values
        SetCustomizableDefaults_TcuLimiterStatic();
        TcuTorqueLimiterPatchMethodIsDynamic = _customOptions.TcuTorqueLimiterPatchMethodIsDynamic;
        TcuLimiterDynamic.IsChecked = _customOptions.TcuLimiterDynamic;
        TcuLimiterTorqueStock = _customOptions.TcuLimiterTorqueStock;

        ColdStartDelete.IsChecked = _customOptions.ColdStartDelete;
        SetCustomizableDefaults_ColdStartDelete();
        Dcat.IsChecked = _customOptions.Dcat;
        Vmax.IsChecked = _customOptions.Vmax;
        ExhaustFlap.IsChecked = _customOptions.ExhaustFlapAlwaysOpen;
        RadiatorFlap.IsChecked = _customOptions.RadiatorFlap;

        TorqueGauge.IsChecked = _customOptions.TorqueGauge;
        SetCustomizableDefaults_TorqueGauge();
        PowerGauge.IsChecked = _customOptions.PowerGauge;
        SetCustomizableDefaults_PowerGauge();

        InductionNoise.IsChecked = _customOptions.InductionNoise;

        Burble.IsChecked = _customOptions.Burble;
        SetCustomizableDefaults_Burble();
        Cooling.IsChecked = _customOptions.Cooling;
        SetCustomizableDefaults_Cooling();
        StartupRoar.IsChecked = _customOptions.StartupRoar;
        SetCustomizableDefaults_StartupRoar();
        TorqueByGear.IsChecked = _customOptions.TorqueByGear;
        SetCustomizableDefaults_TorqueByGear();
        IdleRpm.IsChecked = _customOptions.IdleRpm;
        SetCustomizableDefaults_IdleRPM();

        RemoveGeneralDtcCodes.IsChecked = !_appContext.IsDeveloper() || _customOptions.DtcCodeMasking; // set to true if not X user
        RemoveUserDtcCodes.IsChecked = _customOptions.UserDtcCodesDeletion;

        TunerOverrideDst.IsChecked = SetTunerOverrideDstVisibility() && _customOptions.TunerOverrideDst; // set to false if not X user or in custom flash

        Antilag.IsChecked = Antilag.IsVisible && _customOptions.Antilag; // Only enable antilag on supported vehicles (reset B4x selections)
        AntilagEngineSlot = _carCompatabilityService.GetAntilagEngineSlot(_car);
        SetCustomizableDefaults_Antilag();
    }

    private bool SetSkipCrcCorrectionVisibility()
    {
        var visible = CheckIfCustomStage() && _appContext.IsDeveloper();
        return visible;
    }

    private bool SetDeleteBenchEcuDtcsVisibility()
    {
        var visible = _appContext.IsDeveloper();
        if (!visible)
        {
            return visible;
        }

        if (!_car.VIN.ToUpper().Equals("WBA8B7G51HNU37556".ToUpper()) // PPC
            && !_car.VIN.ToUpper().Equals("WBA5U9C07LA383408".ToUpper())) // Aurix
        {
            visible = false;
        }

        return visible;
    }

    private bool SetRadiatorFlapVisibility()
    {
        var visible = _carCompatabilityService.IsRadiatorFlapSupportedCar(_car);

        if (visible)
        {
            _logger.LogInformation("Radiator Flap is available for this car/software");
        }
        else
        {
            _logger.LogInformation("Radiator Flap unavailable for this car/software");
        }

        return visible;
    }

    private bool SetTcuLimiterStaticVisibility()
    {
        bool isVisible = false;

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _carCompatabilityService.IsTcuLimiterStaticMethodSupportedCustomCodeVersion(_installedCustomCodeVersion)
                && _carCompatabilityService.IsTcuLimiterBypassSupportedCar(_car)
               )
            {
                isVisible = true;
                _customOptions.TcuTorqueLimiterPatchMethodIsDynamic = false;
            }
        }
        else if (_carCompatabilityService.IsTcuLimiterStaticMethodSupportedCustomCodeVersion(_targetCustomCodeVersion)
                 && _carCompatabilityService.IsTcuLimiterBypassSupportedCar(_car)
                )
        {
            isVisible = true;
            _customOptions.TcuTorqueLimiterPatchMethodIsDynamic = false;
        }

        _logger.LogInformation($"Tcu Limiter Static is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return isVisible;
    }

    private bool SetTcuLimiterDynamicVisibility()
    {
        bool isVisible = false;
        var minCustomCodeVersion = Version.Parse("0.5");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion >= minCustomCodeVersion
               )
            {
                isVisible = true;
                _customOptions.TcuTorqueLimiterPatchMethodIsDynamic = true;
            }
        }
        else if (_targetCustomCodeVersion >= minCustomCodeVersion
                 && !_carCompatabilityService.IsTcuLimiterStaticMethodSupportedCustomCodeVersion(_targetCustomCodeVersion)
                 && _carCompatabilityService.IsTcuLimiterBypassSupportedCar(_car)
                )
        {
            isVisible = true;
            _customOptions.TcuTorqueLimiterPatchMethodIsDynamic = true;
        }

        _logger.LogInformation($"Tcu Limiter Dynamic is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return isVisible;
    }

    private bool SetEthanolSupportVisibility()
    {
        bool isVisible = false;
        var minCustomCodeVersion = Version.Parse("7.0");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion >= minCustomCodeVersion
               )
            {
                isVisible = true;
            }
        }
        else if (_targetCustomCodeVersion >= minCustomCodeVersion)
        {
            isVisible = true;
        }

        _logger.LogInformation($"Ethanol Support is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
               || isVisible;
    }

    private bool SetFlexFuelDelayToShowOnDashVisibility()
    {
        bool isVisible = false;
        var minCustomCodeVersion = Version.Parse("7.1");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion >= minCustomCodeVersion)
            {
                isVisible = true;
            }
        }
        else if (_targetCustomCodeVersion >= minCustomCodeVersion)
        {
            isVisible = true;
        }

        _logger.LogInformation($"FlexFuelDelayToShowOnDash is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
               || isVisible;
    }

    private bool SetFlexFuelEthanolContentVisibility()
    {
        bool isVisible = false;
        var minCustomCodeVersion = Version.Parse("7.3");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion >= minCustomCodeVersion)
            {
                isVisible = true;
            }
        }
        else if (_targetCustomCodeVersion >= minCustomCodeVersion)
        {
            isVisible = true;
        }

        _logger.LogInformation($"FlexFuelEthanolContent is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
               || isVisible;
    }

    private bool SetCustomCodeMenuVisibility()
    {
        bool isVisible = false;
        var minCustomCodeVersion = Version.Parse("7.1");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion >= minCustomCodeVersion
               )
            {
                isVisible = true;
            }
        }
        else if (_targetCustomCodeVersion >= minCustomCodeVersion)
        {
            isVisible = true;
        }

        _logger.LogInformation($"CustomCodeMenu is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
            || isVisible;
    }

    private bool SetUseRockerComboVisibility()
    {
        var isVisible = false;
        var minCustomCodeVersion = Version.Parse("7.1");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion >= minCustomCodeVersion
                && _car.BdcSupportsAllCanbusCruiseControlData
               )
            {
                isVisible = true;
            }
        }
        else if (_targetCustomCodeVersion >= minCustomCodeVersion
                 && _car.BdcSupportsAllCanbusCruiseControlData)
        {
            isVisible = true;
        }

        _logger.LogInformation($"UseRockerCombo is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
            || _appContext.CanTestRockerCombo()
            || isVisible;
    }

    private bool SetCustomCodeMenuForceAllowAllButtonConfigurationsVisibility()
    {
        return _appContext.CanTestRockerCombo();
    }

    private bool SetRadiatorFlapOnTheFlyVisibility()
    {
        bool isVisible = false;
        var minCustomCodeVersion = Version.Parse("7.1");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion >= minCustomCodeVersion
                && CustomCodeMenu.IsChecked
               )
            {
                isVisible = true;
            }
        }
        else if (_targetCustomCodeVersion >= minCustomCodeVersion)
        {
            isVisible = true;
        }

        if (!_car.IsAurix)
        {
            _logger.LogInformation("RadiatorFlapOnTheFly is only available for aurix ECUs!");
            isVisible = false;
        }

        _logger.LogInformation($"RadiatorFlapOnTheFly is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
               || isVisible;
    }

    private bool SetExhaustFlapOnTheFlyVisibility()
    {
        bool isVisible = false;
        var minCustomCodeVersion = Version.Parse("7.1");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion >= minCustomCodeVersion
                && CustomCodeMenu.IsChecked
               )
            {
                isVisible = true;
            }
        }
        else if (_targetCustomCodeVersion >= minCustomCodeVersion)
        {
            isVisible = true;
        }

        _logger.LogInformation($"ExhaustFlapOnTheFly is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
               || isVisible;
    }

    private bool SetValetModeVisibility()
    {
        bool isVisible = false;
        var minCustomCodeVersion = Version.Parse("7.1");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion >= minCustomCodeVersion
                && CustomCodeMenu.IsChecked
               )
            {
                isVisible = true;
            }
        }
        else if (_targetCustomCodeVersion >= minCustomCodeVersion)
        {
            isVisible = true;
        }

        _logger.LogInformation($"ValetMode is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
               || isVisible;
    }

    private bool SetAntilagVisibility()
    {
        bool isVisible = false;
        var minCustomCodeVersion = Version.Parse("6.9");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion >= minCustomCodeVersion
                && _carCompatabilityService.IsAntilagSupportedCar(_car, _installedCustomCodeVersion)
               )
            {
                isVisible = true;
            }
        }
        else if (_targetCustomCodeVersion >= minCustomCodeVersion
                 && _carCompatabilityService.IsAntilagSupportedCar(_car, _targetCustomCodeVersion)
                )
        {
            isVisible = true;
        }

        _logger.LogInformation($"Antilag is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
               || _appContext.CanTestAntilagCustomCodeV69()
               || isVisible;
    }

    private bool SetAntilagRpmDiffVisibility()
    {
        var isVisible = false;
        var maxCustomCodeVersion = Version.Parse("7.0.4");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion <= maxCustomCodeVersion
               )
            {
                isVisible = true;
            }
        }
        else if (_targetCustomCodeVersion <= maxCustomCodeVersion)
        {
            isVisible = true;
        }

        _logger.LogInformation($"AntilagRpmDiff is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
            || isVisible;
    }

    private bool SetAntilagTargetNumberOfCylindersToSuppressVisibility()
    {
        var isVisible = false;
        var minCustomCodeVersion = Version.Parse("7.0.5");

        if (_car.AnyEcuReqiresWave3Unlock)
        {
            _logger.LogInformation($"Custom Code cannot be changed on Wave3 unlocked ECUs, current Custom Code installed: [{_car.EcuMaster.CustomCodeVersionInstalled}]");
            if (!string.IsNullOrWhiteSpace(_car.EcuMaster.CustomCodeVersionInstalled)
                && !_car.EcuMaster.CustomCodeVersionInstalled.ToUpper().Contains("NOTFOUND")
                && _installedCustomCodeVersion >= minCustomCodeVersion
               )
            {
                isVisible = true;
            }
        }
        else if (_targetCustomCodeVersion >= minCustomCodeVersion)
        {
            isVisible = true;
        }

        _logger.LogInformation($"AntilagTargetNumberOfCylindersToSuppress is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
            || isVisible;
    }

    private bool SetAntilagIgnitionCutVisibility()
    {
        var isVisible = _carCompatabilityService.IsAntilagIgnitionCutSupportedCar(_car);

        _logger.LogInformation($"Antilag Ignition cut is [{(isVisible ? "available" : "unavailable")}] for this car/software");
        return _appContext.IsDeveloper()
               || _appContext.IsTuner()
               || _appContext.IsTesterAntilag()
               || isVisible;
    }

    private bool CheckHpfpStageRequirements()
    {
        return _flasherCommand is IStageSecondFlashCommand || _flasherCommand is IStageSecondPointFiveFlashCommand;
    }

    private bool CheckIfCustomStage()
    {
        return _flasherCommand is ICustomMapFlashCommand;
    }

    private void SetBurbleStyleList()
    {
        var result = new ObservableCollection<FlashingCarOptionBurbleStyleItemViewModel> {
            new(AppResources.FlashingCarOptionsPage_BurbleAggressionStyle_Off, AppResources.FlashingCarOptionsPage_Aggressiveness_StyleOff_Description),
            new(AppResources.FlashingCarOptionsPage_BurbleAggressionStyle_1, AppResources.FlashingCarOptionsPage_Aggressiveness_Style1_Description),
            new(AppResources.FlashingCarOptionsPage_BurbleAggressionStyle_2, AppResources.FlashingCarOptionsPage_Aggressiveness_Style2_Description)
        };
        if (_appContext.IsDeveloper() ||
            !_carCompatabilityService.IsBurbleOTFSupportedCar(_car, _targetCustomCodeVersion))
        {
            result.Add(new(AppResources.FlashingCarOptionsPage_BurbleAggressionStyle_3, AppResources.FlashingCarOptionsPage_Aggressiveness_Style3_Description));
        }

        BurbleStyleList = result;
    }

    private bool IsHpfpSupportedCar()
    {
        var result = new List<bool>
        {
            HpfpAvailableFromSpoolPerformance,
            HpfpAvailableFromDorchEngineering,
            HpfpAvailableFromXtremeDI,
            HpfpAvailableFromPrecisionRaceworks,
        };
        return result.Any();
    }

    private void SetHpfpAvailability()
    {
        HpfpLambdaEnrichment.IsVisible = _carCompatabilityService.IsHpfpLambdaEnrichmentSupported(_car);

        B58TU_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.OEM_B58TU);
        SpoolPerformance_Fx150_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.SpoolPerformance_Fx150);
        SpoolPerformance_Fx170_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.SpoolPerformance_Fx170);
        SpoolPerformance_Fx180_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.SpoolPerformance_Fx180);
        SpoolPerformance_Fx200_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.SpoolPerformance_Fx200);
        SpoolPerformance_Fx350_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.SpoolPerformance_Fx350);
        SpoolPerformance_Fx400_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.SpoolPerformance_Fx400);
        SpoolPerformance_Fx400X_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.SpoolPerformance_Fx400X);
        DorchEngineering_Stage1_DS1_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.DorchEngineering_Stage1_DS1);
        DorchEngineering_Stage15_DS15_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.DorchEngineering_Stage15_DS15);
        DorchEngineering_Stage2_DS2_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.DorchEngineering_Stage2_DS2);
        DorchEngineering_Stage25_DS25_250Bar_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.DorchEngineering_Stage25_DS25_250Bar);
        DorchEngineering_Stage25_DS25_350Bar_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.DorchEngineering_Stage25_DS25_350Bar);
        DorchEngineering_Stage3_DS3_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.DorchEngineering_Stage3_DS3);
        XtremeDI_35_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.XtremeDI_35);
        XtremeDI_60_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.XtremeDI_60);
        XtremeDI_EVO_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.XtremeDI_EVO);
        PrecisionRaceworks_HPFP_Installed.IsVisible = _carCompatabilityService.IsHpfpSupported(_car, HpfpModel.PrecisionRaceworks_HPFP);

        var oemList = new List<bool>
        {
            B58TU_Installed.IsVisible,
        };
        HpfpAvailableFromOEM = oemList.Any(x => x);

        // TODO we have these lists here, which could also come in handy for Customize text previews, because I doubled them already...
        var spoolPerformanceList = new List<bool>
        {
            SpoolPerformance_Fx150_Installed.IsVisible,
            SpoolPerformance_Fx170_Installed.IsVisible,
            SpoolPerformance_Fx180_Installed.IsVisible,
            SpoolPerformance_Fx200_Installed.IsVisible,
            SpoolPerformance_Fx350_Installed.IsVisible,
            SpoolPerformance_Fx400_Installed.IsVisible,
            SpoolPerformance_Fx400X_Installed.IsVisible,
        };
        HpfpAvailableFromSpoolPerformance = spoolPerformanceList.Any(x => x);

        var dorchEngineeringList = new List<bool>
        {
            DorchEngineering_Stage1_DS1_Installed.IsVisible,
            DorchEngineering_Stage15_DS15_Installed.IsVisible,
            DorchEngineering_Stage2_DS2_Installed.IsVisible,
            DorchEngineering_Stage25_DS25_250Bar_Installed.IsVisible,
            DorchEngineering_Stage25_DS25_350Bar_Installed.IsVisible,
            DorchEngineering_Stage3_DS3_Installed.IsVisible,
        };
        HpfpAvailableFromDorchEngineering = dorchEngineeringList.Any(x => x);

        var xtremeDIList = new List<bool>
        {
            XtremeDI_35_Installed.IsVisible,
            XtremeDI_60_Installed.IsVisible,
            XtremeDI_EVO_Installed.IsVisible,
        };
        HpfpAvailableFromXtremeDI = xtremeDIList.Any(x => x);

        var precisionRaceworksList = new List<bool>
        {
            PrecisionRaceworks_HPFP_Installed.IsVisible,
        };
        HpfpAvailableFromPrecisionRaceworks = precisionRaceworksList.Any(x => x);
    }

    private bool IsInjectorSupportedCar()
    {
        var result = new List<bool>
        {
            InjectorsAvailableFromSpoolPerformance,
            InjectorsAvailableFromXtremeDI,
            InjectorsAvailableFromNostrum,
        };
        return result.Any();
    }

    private void SetInjectorAvailability()
    {
        SpoolPerformance_Ifx150_Installed.IsVisible = _carCompatabilityService.IsInjectorSupported(_car, InjectorModel.SpoolPerformance_Ifx150);
        SpoolPerformance_Ifx350_Installed.IsVisible = _carCompatabilityService.IsInjectorSupported(_car, InjectorModel.SpoolPerformance_Ifx350);
        SpoolPerformance_Ifx350X_Installed.IsVisible = _carCompatabilityService.IsInjectorSupported(_car, InjectorModel.SpoolPerformance_Ifx350X);
        XtremeDI_40_Installed.IsVisible = _carCompatabilityService.IsInjectorSupported(_car, InjectorModel.XtremeDI_40);
        XtremeDI_75_Installed.IsVisible = _carCompatabilityService.IsInjectorSupported(_car, InjectorModel.XtremeDI_75);
        Nostrum_Stage1_Installed.IsVisible = _carCompatabilityService.IsInjectorSupported(_car, InjectorModel.Nostrum_Stage1);
        Nostrum_Stage2_Installed.IsVisible = _carCompatabilityService.IsInjectorSupported(_car, InjectorModel.Nostrum_Stage2);

        var spoolPerformanceList = new List<bool>
        {
            SpoolPerformance_Ifx150_Installed.IsVisible,
            SpoolPerformance_Ifx350_Installed.IsVisible,
            SpoolPerformance_Ifx350X_Installed.IsVisible,
        };
        InjectorsAvailableFromSpoolPerformance = spoolPerformanceList.Any(x => x);

        var xtremeDIList = new List<bool>
        {
            XtremeDI_40_Installed.IsVisible,
            XtremeDI_75_Installed.IsVisible,
        };
        InjectorsAvailableFromXtremeDI = xtremeDIList.Any(x => x);

        var nostrumList = new List<bool>
        {
            Nostrum_Stage1_Installed.IsVisible,
            Nostrum_Stage2_Installed.IsVisible,
        };
        InjectorsAvailableFromNostrum = nostrumList.Any(x => x);
    }

    private bool SetTunerOverrideDstVisibility()
    {
        var visible = CheckIfCustomStage() && (_appContext.IsDeveloper() || _appContext.IsTuner() || _appContext.IsTroubleshooter());
        return visible;
    }

    private bool SetSwitchableMapsVisibility()
    {
        bool isVisible = false;
        bool _notifyUserSwitchableMapsAreAvailable = false;
        var minChangesetVersionNumber = _carCompatabilityService.GetSwtichableMapMinSupportedVersion(_car, _targetCustomCodeVersion);

        if (_carCompatabilityService.IsSwitchableMapSupportedCar(_car, _targetCustomCodeVersion))
        {
            // Fxx B46/B48/B58
            if (CustomCodeVersions.IsAtLeastCustomCodev7x("v" + _targetCustomCodeVersion))
            {
                _logger.LogInformation("Switchable Maps are available in custom code versions greater than 7.0!");
                isVisible = true;
            }
            else if (_flasherCommand is IStageFirstFlashCommand || _flasherCommand is IStageSecondFlashCommand || _flasherCommand is IStageSecondPointFiveFlashCommand)
            {
                var changesetVersionNumber = GetChangesetVersionNumber();
                _logger.LogInformation("Selected changeset: v{changesetVersionNumber}", changesetVersionNumber);
                if (changesetVersionNumber >= minChangesetVersionNumber)
                {
                    _logger.LogInformation("Switchable Maps are available! selected v{changesetVersionNumber} >= minimum v{minChangesetVersionNumber}", changesetVersionNumber, minChangesetVersionNumber);
                    isVisible = true;
                }
                else
                {
                    _logger.LogInformation("Target OTS map/changeset version must be a minimum of {minChangesetVersionNumber} to allow Switchable maps", minChangesetVersionNumber);
                    _notifyUserSwitchableMapsAreAvailable = true;
                }
            }
            else
            {
                _logger.LogInformation("Switchable maps unavailable for non OTS flashing when regular user (not developer/SwitchableMapsTester)");
            }
        }
        else
        {
            _logger.LogInformation("Switchable maps unavailable on cars other than Fxx B58");
        }

        if (_appContext.IsDeveloper() || _appContext.IsTesterSwitchableMaps() || _appContext.CanFlashCustomCodeV60WithCustomBin())
        {
            NotifyUserSwitchableMapsAreAvailable = false;
            return true;
        }
        else
        {
            NotifyUserSwitchableMapsAreAvailable = _notifyUserSwitchableMapsAreAvailable;
            return isVisible;
        }
    }

    private double GetChangesetVersionNumber()
    {
        try
        {
            var changeset = _car.Changesets.GetChangeset(_selectedMapId.Value);
            var changesetVersionNumber = changeset.VersionNumber;
            if (!string.IsNullOrEmpty(changesetVersionNumber))
            {
                return Convert.ToDouble(changesetVersionNumber, CultureInfo.InvariantCulture);
            }
            else
            {
                _logger.LogInformation("Changeset version string is null or empty! Returning '0.0'");
                return 0.0;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unable to retrieve changeset version number");
            return 0.0;
        }
    }

    private async void CustomizeUserDtcCodes(object obj)
    {
        var a2l = await _carFlashCommandA2LExtractor.GetAsync(_car, _flasherCommand);
        await _navigationService.NavigateAsync(PageType.UserDtcRemovalList, new DtcRemovalListPageNavigationArgs(_customOptions.UserDtcCodesToRemove, a2l));
    }

    private string GetCanBusIdStringFromUserSelection(string userSelection)
    {
        if (string.IsNullOrWhiteSpace(userSelection))
        {
            return string.Empty;
        }

        if (userSelection == AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_Zeitronix_ECA
            || userSelection == AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_MotivReflex
            || userSelection == AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_FuelIt_FlexFuel_Kit
            || userSelection == AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_MHD_FlexFuel_Kit
            || userSelection == AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_BM3_FlexFuel_Kit
            || userSelection == AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_DorchEngineering_FlexFuel_Kit
            || userSelection == AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_Universal_0x00EC
            || userSelection == AppResources.FlashingCarOptionsPage_MotivReflexCanBusIdOutput_0x00EC
            || userSelection == CanBusIds.Id_0x00EC
            )
        {
            return CanBusIds.Id_0x00EC;
        }
        else if (userSelection == AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_Universal_0x00EB
            || userSelection == AppResources.FlashingCarOptionsPage_MotivReflexCanBusIdInput_0x00EB
            || userSelection == CanBusIds.Id_0x00EB
            )
        {
            return CanBusIds.Id_0x00EB;
        }
        else if (userSelection == AppResources.FlashingCarOptionsPage_FlexFuelSensorModel_Universal_0x04CB
            || userSelection == AppResources.FlashingCarOptionsPage_MotivReflexCanBusIdOutput_0x04CB
            || userSelection == CanBusIds.Id_0x04CB
            )
        {
            return CanBusIds.Id_0x04CB;
        }
        else if (userSelection == AppResources.FlashingCarOptionsPage_MotivReflexCanBusIdOutput_0x0334
            || userSelection == CanBusIds.Id_0x0334
            )
        {
            return CanBusIds.Id_0x0334;
        }
        else if (userSelection == AppResources.FlashingCarOptionsPage_MotivReflexCanBusIdInput_0x0335
            || userSelection == CanBusIds.Id_0x0335)
        {
            return CanBusIds.Id_0x0335;
        }

        return string.Empty;
    }
}