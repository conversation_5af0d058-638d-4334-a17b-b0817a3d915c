using MgFlasher.Models;
using MgFlasher.ViewModels.FlashingCarOptions.Models;
using Microsoft.Maui.Controls;
using MgFlasher.Services.Navigation;
using System.Windows.Input;
using System;

namespace MgFlasher.ViewModels.FlashingCarOptions;

public class CustomizableCustomOptionViewModel : CustomOptionViewModel
{
    private readonly INavigationService _navigationService;
    private DataTemplate _itemTemplate;
    private object _itemTemplateViewModel;

    public ICommand DefaultsCommand { get; protected set; }

    public DataTemplate ItemTemplate
    {
        get => _itemTemplate;
        set => SetProperty(ref _itemTemplate, value);
    }

    public object ItemTemplateViewModel
    {
        get => _itemTemplateViewModel;
        set => SetProperty(ref _itemTemplateViewModel, value);
    }

    public CustomizableCustomOptionViewModel(string option, string info, INavigationService navigationService, Action defaultsCommand) : base(option, info)
    {
        _navigationService = navigationService;
        CustomizeCommand = new TraceableCommand(CustomizeOptionItem, x => IsChecked, nameof(CustomizeOptionItem));
        if (defaultsCommand is not null)
        {
            DefaultsCommand = new TraceableCommand(defaultsCommand, nameof(defaultsCommand));
        }
        else
        {
            DefaultsCommand = null;
        }
    }

    private async void CustomizeOptionItem(object obj)
    {
        await _navigationService.NavigateAsync(PageType.FlashingCarOptionsItem, new FlashingCarOptionsItemPageNavigationArgs(this));
    }
}