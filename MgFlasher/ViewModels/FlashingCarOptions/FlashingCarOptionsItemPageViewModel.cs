using System.Threading.Tasks;
using MgFlasher.Models;
using MgFlasher.ViewModels.FlashingCarOptions.Models;
using System.Windows.Input;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.FlashingCarOptions;

public class FlashingCarOptionsItemPageViewModel : PageViewModel
{
    private const int ButtonDelayInMilliseconds = 100;
    private readonly INavigationService _navigationService;
    private CustomizableCustomOptionViewModel _viewModel;

    public ICommand BackCommand { get; }

    public CustomizableCustomOptionViewModel ViewModel
    {
        get => _viewModel;
        private set => SetProperty(ref _viewModel, value);
    }

    public FlashingCarOptionsItemPageViewModel(INavigationService navigationService) : base()
    {
        _navigationService = navigationService;
        BackCommand = new TraceableCommand(Back, nameof(Back));
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        ViewModel = ((FlashingCarOptionsItemPageNavigationArgs)arg).ViewModel;
        return base.OnNavigatedToAsync(previousPage, arg);
    }

    private async void Back(object obj)
    {
        // TODO Why do we add delay on buttons?
        await Task.Delay(ButtonDelayInMilliseconds);
        await _navigationService.NavigateToPreviousAsync();
    }
}