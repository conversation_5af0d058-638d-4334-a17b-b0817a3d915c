﻿using System.Text;
using MgFlasher.CustomCode.Options.DTC.Models;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.User;

namespace MgFlasher.ViewModels.FlashingCarOptions;

public class DtcItemViewModel : OptionalItemViewModel
{
    public string Code { get; }

    public DtcItemViewModel(DtcCode code, IAppContext appContext)
        : base(GetDisplayName(code, appContext))
    {
        Code = code.Code;
    }

    private static string GetDisplayName(DtcCode code, IAppContext context)
    {
        var sb = new StringBuilder();
        sb.Append(code.Code);
        if (context.CanSeeA2lNameOfDtcItem() && !string.IsNullOrEmpty(code.DtcmName))
        {
            sb.Append($"\r\n{code.DtcmName}");
        }
        return sb.ToString();
    }
}