﻿using MgFlasher.Flasher.Services.CarLogger.Exporters;
using MgFlasher.Flasher.Services.CarLogger.Exporters.Models;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Maui;
using Syncfusion.Maui.DataSource.Extensions;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels;

public class SettingsPageViewModel : PageViewModel
{
    private readonly IExporterProviderSettingsService _exporterProviderSettingsService;
    private readonly INavigationService _navigationService;
    private readonly IUserDialogs _userDialogs;
    private ObservableCollection<ExternalServiceItemViewModel> _externalServiceItems;
    private PageType _previousPage;

    public ObservableCollection<ExternalServiceItemViewModel> ExternalServiceItems
    {
        get => _externalServiceItems;
        set => SetProperty(ref _externalServiceItems, value);
    }

    public bool CanSubmit => !IsBusy && ExternalServiceItems.Any(si => si.Modified);

    public ICommand SubmitCommand { get; }
    public ICommand ShowDualEcuLoggingInfoCommand { get; }

    public SettingsPageViewModel(
        IExporterProviderSettingsService exporterProviderSettingsService,
        INavigationService navigationService,
        IUserDialogs userDialogs)
    {
        _exporterProviderSettingsService = exporterProviderSettingsService;
        _navigationService = navigationService;
        _userDialogs = userDialogs;
        _externalServiceItems = new ObservableCollection<ExternalServiceItemViewModel>();
        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));
        ShowDualEcuLoggingInfoCommand = new TraceableCommand(ShowDualEcuLoggingInfo, nameof(ShowDualEcuLoggingInfo));
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _previousPage = previousPage;

        var settings = await _exporterProviderSettingsService.GetAll();

        var vms = settings.Where(x => x.Type != ExporterProviderType.MgFlasherLogsCloud).Select(s => new ExternalServiceItemViewModel(s)).ToList();
        ExternalServiceItems = new ObservableCollection<ExternalServiceItemViewModel>(vms);
        ExternalServiceItems.ForEach(vm => vm.PropertyChanged += OnItemPropertyChanged);

        OnPropertyChanged(nameof(CanSubmit));
    }

    public override Task OnNavigatedFromAsync(PageType nextPage)
    {
        ExternalServiceItems?.ForEach(vm => vm.PropertyChanged -= OnItemPropertyChanged);
        ExternalServiceItems?.Clear();

        return base.OnNavigatedFromAsync(nextPage);
    }

    private async void ShowDualEcuLoggingInfo(object obj)
    {
        await _userDialogs.AlertAsync(AppResources.DualEcuLogging_Info, null, null);
    }

    private async void ShowEnetInternetPreferenceInfo(object obj)
    {
        await _userDialogs.AlertAsync(AppResources.EnetInternetSwitchPreference_Info, null, null);
    }

    private void OnItemPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(ExternalServiceItemViewModel.Modified))
        {
            OnPropertyChanged(nameof(CanSubmit));
        }
    }

    private async void Submit()
    {
        try
        {
            IsBusy = true;
            OnPropertyChanged(nameof(CanSubmit));

            if (ExternalServiceItems.Any(x => x.Modified))
            {
                var models = ExternalServiceItems.Select(si => si.GetModel()).ToList();
                var invalid = models.Any(c => c.AuthenticationType == ExporterProviderAuthenticationType.User && string.IsNullOrEmpty(c.UserApiKey));

                if (invalid)
                {
                    await _userDialogs.AlertAsync(AppResources.ExternalServices_ApiKeyMustBeFilled, AppResources.Error, AppResources.Ok);
                    return;
                }

                await _exporterProviderSettingsService.SetProviderSettings(models);
            }

            if (_previousPage == PageType.Login)
            {
                await _navigationService.NavigateAsync(PageType.MyCars);
            }
            else
            {
                await _navigationService.NavigateToPreviousAsync();
            }
        }
        finally
        {
            IsBusy = false;
            OnPropertyChanged(nameof(CanSubmit));
        }
    }
}