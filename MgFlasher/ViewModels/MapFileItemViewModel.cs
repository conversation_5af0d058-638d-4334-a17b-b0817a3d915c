﻿using MgFlasher.CustomCode.Services;
using MgFlasher.Files;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.CustomCode;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services.Navigation;
using Microsoft.Extensions.Logging;
using NLog.LayoutRenderers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels;

public class MapFileItemViewModel : OptionalItemViewModel
{
    private readonly IShareService _shareService;
    private readonly ICarDirectoryPathFactory _directoryPathFactory;
    private readonly IReferenceFileVersionExtractor _referenceFileVersionExtractor;
    private readonly IFileDescriptorRepository _fileDescriptorRepository;
    private readonly IUserDialogs _userDialogs;
    private readonly IFileDecryptor _fileDecryptor;
    private readonly Car _connectedCar;
    private readonly ILogger<MapFileItemViewModel> _logger;

    public FileDescriptor FileDescriptor { get; }
    public ICommand MiscCommand { get; }

    public event EventHandler DeleteActionOccurred;

    public MapFileItemViewModel(FileDescriptor mapFile,
        IShareService shareService,
        ICarDirectoryPathFactory directoryPathFactory,
        IReferenceFileVersionExtractor referenceFileVersionExtractor,
        IUserDialogs userDialogs,
        IFileDecryptor fileDecryptor,
        Car connectedCar,
        ILogger<MapFileItemViewModel> logger,
        IFileDescriptorRepository fileDescriptorRepository) : base(mapFile.ToDisplayName(connectedCar))
    {
        MiscCommand = new TraceableCommand(MiscMenu, nameof(MiscMenu));
        FileDescriptor = mapFile;
        _shareService = shareService;
        _directoryPathFactory = directoryPathFactory;
        _referenceFileVersionExtractor = referenceFileVersionExtractor;
        _userDialogs = userDialogs;
        _fileDecryptor = fileDecryptor;
        _connectedCar = connectedCar;
        _logger = logger;
        _fileDescriptorRepository = fileDescriptorRepository;
    }

    protected virtual void OnActionOccurred()
    {
        if (DeleteActionOccurred != null)
        {
            DeleteActionOccurred(this, EventArgs.Empty);
        }
    }

    private async void MiscMenu()
    {
        var options = new Dictionary<string, Action>
        {
            { AppResources.Misc_FileInfo, FileInfo },
            { AppResources.Misc_Share, async () => await Share() },
            { AppResources.Misc_Rename, Rename },
            { AppResources.Misc_DeleteSingle, Delete }
        };
        var result = await _userDialogs.ActionSheetAsync(AppResources.Misc_Title, AppResources.Cancel, null, null, options.Keys.ToArray());
        if (options.ContainsKey(result))
        {
            options[result]();
        }
    }

    private async void Delete()
    {
        var message = string.Format(AppResources.ConfirmFileDeleteByName, Option);
        if (!await _userDialogs.ConfirmAsync(message, okText: AppResources.Yes, cancelText: AppResources.No))
        {
            await _userDialogs.AlertAsync(AppResources.DeletionCancelled, okText: AppResources.Ok);
            return;
        }

        await _fileDescriptorRepository.DeleteDescriptorAsync(_connectedCar.Fingerprint, FileDescriptor.Id,
            FileDescriptor.Type, _connectedCar.VIN);

        await _userDialogs.AlertAsync(AppResources.DeletionFileCompleted, okText: AppResources.Ok);
        OnActionOccurred();
    }

    private async void Rename()
    {
        var promptResult = await _userDialogs.PromptAsync(new PromptConfig
        {
            OkText = AppResources.Map_Rename_Ok,
            Title = AppResources.Map_Rename_Title,
            Text = Option
        });

        if (promptResult.Ok && !string.IsNullOrWhiteSpace(promptResult.Text))
        {
            FileDescriptor.Rename(_connectedCar.VIN, promptResult.Text, _directoryPathFactory);

            Option = FileDescriptor.Filename;

            await _fileDescriptorRepository.UpdateFileNameAsync(_connectedCar.Fingerprint, _connectedCar.VIN, FileDescriptor.Id, FileDescriptor.Filename);
        }
    }

    public async Task Share()
    {
        await _shareService.Share(_connectedCar, FileDescriptor);
    }

    private async void FileInfo()
    {
        try
        {
            var data = await _fileDecryptor.Decrypt(_connectedCar.VIN, FileDescriptor);
            _logger.LogInformation("Extracting info from file");
            var referenceFileVersion = _referenceFileVersionExtractor.Extract(
                data,
                CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(_connectedCar.EcuMaster?.ProcessorArchitectureType));
            var downloadedAt = File.GetCreationTime(FileDescriptor.GetFilePath(_connectedCar.VIN, _directoryPathFactory));
            // TODO do we need "modifiedAt"? in Windows this date only changes, when content of the file changes. We only allow
            // renaming file inside the app, that does not change the date. So will downloadedAt and modifiedAt ever be different?
            var modifiedAt = File.GetLastWriteTime(FileDescriptor.GetFilePath(_connectedCar.VIN, _directoryPathFactory));
            if (FileDescriptor.CreatedAt is null)
            {
                var createdAtDate = _connectedCar.CustomMaps.Where(x => x.Id == FileDescriptor.Id).FirstOrDefault()?.CreatedAt;
                if (createdAtDate is not null)
                {
                    FileDescriptor.CreatedAt = createdAtDate;
                    await _fileDescriptorRepository.UpdateCreatedAtAsync(_connectedCar.Fingerprint, _connectedCar.VIN, FileDescriptor.Id, FileDescriptor.CreatedAt.Value);
                }
            }

            var createdAt = FileDescriptor.CreatedAt.ToString();
            if (string.IsNullOrEmpty(createdAt))
            {
                createdAt = "N/A";
            }

            var fileInfoString = $"Name: {FileDescriptor.Filename}\r\n" +
                                 $"Created at: {createdAt}\r\n" +
                                 $"Downloaded at: {downloadedAt}\r\n" +
                                 $"Modified at: {modifiedAt}\r\n" +
                                 $"A2L: {referenceFileVersion.A2L}\r\n" +
                                 (referenceFileVersion.CustomCodeId == null ? "" : ($"Custom Code: v{referenceFileVersion.CustomCodeId.Version}\r\n")) +
                                 "SWE:\r\n" +
                                 $"\tBTLD: {referenceFileVersion.Btld}\r\n" +
                                 $"\tSWFL: {referenceFileVersion.Pst}\r\n" +
                                 $"\tSWFK: {referenceFileVersion.Dst}";

            _logger.LogInformation(fileInfoString);
            await _userDialogs.AlertAsync(message: fileInfoString,
                title: AppResources.Misc_FileInfo,
                okText: AppResources.Ok);
        }
        catch (MgFlasherException ex)
        {
            await _userDialogs.AlertAsync(message: ex.Message, title: AppResources.Misc_FileInfo, okText: AppResources.Ok);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception while checking file info {Id} {Filename}", FileDescriptor.Id, FileDescriptor.Filename);
        }
    }
}