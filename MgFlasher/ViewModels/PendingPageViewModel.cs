﻿using CommunityToolkit.Mvvm.Messaging;
using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services.Navigation;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Dispatching;
using System;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels;

public class PendingPageViewModel : PageViewModel, IRecipient<MessagingCenterIdentifiers.PendingPageMessage>
{
    private readonly INavigationService _navigationService;
    private PendingPageNavigationArgs _arguments;
    private string _text;
    private bool _showProgressCounter;
    private int _progress;

    public string Text
    {
        get => _text;
        set => SetProperty(ref _text, value);
    }

    public bool ShowProgressCounter
    {
        get => _showProgressCounter;
        private set => SetProperty(ref _showProgressCounter, value);
    }

    public int Progress
    {
        get => _progress;
        private set => SetProperty(ref _progress, value);
    }

    public PendingPageViewModel(INavigationService navigationService)
    {
        _navigationService = navigationService;
        WeakReferenceMessenger.Default.Register(this);
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        SetViewModelArguments(arg as PendingPageNavigationArgs);
        return base.OnNavigatedToAsync(previousPage, arg);
    }

    public void Receive(MessagingCenterIdentifiers.PendingPageMessage message)
    {
        switch (message.Key)
        {
            case MessagingCenterIdentifiers.PendingPageUpdateText:
                PendingPageUpdateNavigationMessage(message.Value as PendingPageUpdateArgs);
                break;

            case MessagingCenterIdentifiers.PendingPageCloseNavigation:
                PendingPageCloseNavigation();
                break;

            case MessagingCenterIdentifiers.PendingPageBackNavigation:
                PendingPageBackNavigation();
                break;

            default: break;
        }
    }

    private async void PendingPageUpdateNavigationMessage(PendingPageUpdateArgs arg)
    {
        await Application.Current.Dispatcher.DispatchAsync(() =>
        {
            Text = arg.Text;
            Progress = Math.Max(Progress, arg.Progress);
        });
    }

    private async void PendingPageCloseNavigation()
    {
        await Application.Current.Dispatcher.DispatchAsync(async () =>
        {
            if (_arguments.NextPageTabbedComponent is not null)
            {
                var methodInfo = typeof(INavigationService).GetMethod(nameof(INavigationService.NavigateToTabbedComponent));
                var genericMethod = methodInfo.MakeGenericMethod(_arguments.NextPageTabbedComponent);
                await (Task)genericMethod.Invoke(_navigationService, [_arguments.NextPage, _arguments.NextPageArguments]);
            }
            else
            {
                await _navigationService.NavigateAsync(_arguments.NextPage, _arguments.NextPageArguments);
            }
        });
    }

    private async void PendingPageBackNavigation()
    {
        await Application.Current.Dispatcher.DispatchAsync(async () =>
            await _navigationService.NavigateAsync(_arguments.BackPage, _arguments.BackPageArguments ?? _arguments.NextPageArguments));
    }

    private void SetViewModelArguments(PendingPageNavigationArgs arg)
    {
        if (arg is PendingPageNavigationArgs navigationArgs)
        {
            _arguments = navigationArgs;
            Text = navigationArgs.Text;
            ShowProgressCounter = navigationArgs.EnableProgressBar;
            Progress = 0;
        }
        else
        {
            Text = null;
            ShowProgressCounter = false;
            Progress = 0;
        }
    }
}