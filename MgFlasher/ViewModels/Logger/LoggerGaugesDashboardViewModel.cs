﻿using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Flasher.Services.CarLogger;
using MgFlasher.Flasher.Services.CarLogger.Gauges;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.Logger.Models;
using MgFlasher.ViewModels.Logger.Services;
using MgFlasher.ViewModels.MyCar;
using MgFlasher.Views.Logger.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;
using Syncfusion.Maui.DataSource.Extensions;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels.Logger;

public class LoggerGaugesDashboardViewModel : BaseViewModel
{
    private readonly ILoggerService _loggerService;
    private readonly IGaugeItemViewModelsFactory _gaugeItemViewModelsFactory;
    private readonly ILoggerPreferenceService _loggerPreferenceService;
    private readonly IGaugeLogDefinitionConfigurationService _gaugeLogDefinitionConfigurationService;
    private readonly IUserDialogs _userDialogs;
    private readonly INavigationService _navigationService;
    private readonly ILogger<LoggerGaugesDashboardViewModel> _logger;
    private readonly ConcurrentDictionary<(ModuleId ModuleId, string Code), GaugeItemViewModel> _referencesCache = new();
    private readonly GaugeViewLayoutPositioner _positioner;
    private ObservableCollection<GaugeItemViewModel> _gaugeItems;
    private Dictionary<string, Action> _contextOptions;

    public ObservableCollection<GaugeItemViewModel> GaugeItems
    {
        get => _gaugeItems;
        private set => SetProperty(ref _gaugeItems, value);
    }

    public Dictionary<string, Action> ContextOptions
    {
        get => _contextOptions;
        private set => SetProperty(ref _contextOptions, value);
    }

    public ICommand SwitchModeCommand { get; set; }

    public ICommand GaugeItemsAutoAdjustedCommand { get; }

    public LoggerGaugesDashboardViewModel(
        ILoggerService loggerService,
        IGaugeItemViewModelsFactory gaugeItemViewModelsFactory,
        ILoggerPreferenceService loggerPreferenceService,
        IGaugeLogDefinitionConfigurationService gaugeLogDefinitionConfigurationService,
        IUserDialogs userDialogs,
        INavigationService navigationService,
        ILogger<LoggerGaugesDashboardViewModel> logger)
    {
        _loggerService = loggerService;
        _gaugeItemViewModelsFactory = gaugeItemViewModelsFactory;
        _loggerPreferenceService = loggerPreferenceService;
        _gaugeLogDefinitionConfigurationService = gaugeLogDefinitionConfigurationService;
        _userDialogs = userDialogs;
        _navigationService = navigationService;
        _logger = logger;
        _loggerService.GaugeDefinitionsUpdated += OnGaugeDefinitionsUpdated;
        _loggerService.GaugeMeasured += OnGaugeMeasured;
        _loggerService.LoggingToFileChanged += (o, e) => InitContextOptions();
        _loggerService.ConnectionLost += (o, e) => InitContextOptions();
        _loggerPreferenceService.CarConfigChanged += OnCarConfigChanged;
        _positioner = new GaugeViewLayoutPositioner();

        GaugeItemsAutoAdjustedCommand = new TraceableCommand(GaugeItemsAutoAdjusted, nameof(GaugeItemsAutoAdjusted));

        InitContextOptions();
    }

    private void InitContextOptions()
    {
        var options = new Dictionary<string, Action>();

        if ((GaugeItems?.Any(x => x.Visible) ?? false) && _loggerService.IsGaugeModeSelected)
        {
            if (GaugesDashboardTouchManager.IsUnlocked)
            {
                options[AppResources.GaugesDashboard_ContextOptions_Lock] = () =>
                {
                    GaugesDashboardTouchManager.IsUnlocked = false;
                    InitContextOptions();
                };
            }
            else
            {
                options[AppResources.GaugesDashboard_ContextOptions_Unlock] = () =>
                {
                    GaugesDashboardTouchManager.IsUnlocked = true;
                    InitContextOptions();
                };
            }

            options[AppResources.GaugesDashboard_ContextOptions_AutoAdjust] = async () => await AutoAdjustAsync();
        }

        if (_loggerService.IsLoggerStarted && _loggerService.IsAutoLogToFileEnabled)
        {
            options[AppResources.GaugesDashboard_ContextOptions_AutoLogToFile_Disable] = () =>
            {
                _loggerService.ToggleAutoLogToFile();
                InitContextOptions();
            };
        }
        else if (_loggerService.IsLoggerStarted)
        {
            options[AppResources.GaugesDashboard_ContextOptions_AutoLogToFile_Enable] = () =>
            {
                _loggerService.ToggleAutoLogToFile();
                InitContextOptions();
            };
        }

        if (!_loggerService.IsLoggingToFile)
        {
            options[AppResources.GaugesDashboard_ContextOptions_LoggerParameters] = async () => await _navigationService.NavigateAsync(PageType.LoggerParameters);
            options[AppResources.GaugesDashboard_ContextOptions_LoggerUnits] = async () => await _navigationService.NavigateAsync(PageType.LoggerUnits);
            options[AppResources.GaugesDashboard_ContextOptions_LoggerDisplay] = async () => await _navigationService.NavigateAsync(PageType.LoggerDisplay);
            options[AppResources.GaugesDashboard_ContextOptions_LoggerAlerts] = async () => await _navigationService.NavigateAsync(PageType.LoggerAlerts);
            options[AppResources.GaugesDashboard_ContextOptions_CustomCodeFeatureDiagnostics] = async () => await _navigationService.NavigateToTabbedComponent<MyCarDiagnosticsCustomViewModel>(PageType.MyCar);
            if (SwitchModeCommand is not null)
            {
                string key = _loggerService.IsGaugeModeSelected
                       ? AppResources.GaugesDashboard_ContextOptions_SwitchMode_List
                       : AppResources.GaugesDashboard_ContextOptions_SwitchMode_Gauges;

                options[key] = async () =>
                {
                    SwitchModeCommand.Execute(null);
                    await UpdateGaugeItemsAfterChanges();
                    InitContextOptions();
                };
            }
        }

        ContextOptions = options;
    }

    private async Task AutoAdjustAsync()
    {
        var gaugeItems = GaugeItems.Where(x => x.Visible).ToList();

        if (gaugeItems.Count == 0)
        {
            await _userDialogs.AlertAsync(AppResources.LoggerDisplayGauges_NoVisibleParameters, okText: AppResources.Ok);
            return;
        }

        _positioner.AutoAdjustByGrid(gaugeItems.OfType<IShapeHolderViewModel>());

        var configs = gaugeItems.Select(x => x.GetConfig());
        await _gaugeLogDefinitionConfigurationService.SetAsync(configs);
    }

    private void OnCarConfigChanged(object sender, CarLoggerPreferenceConfiguration e)
    {
        foreach (var item in _gaugeItems ?? new ObservableCollection<GaugeItemViewModel>())
        {
            item.UpdateGaugeName(e.PresentA2lMeasurementName);
            item.UpdateGaugeValueFormat(e.PresentHexValues);
        }
    }

    private async void OnGaugeDefinitionsUpdated(object sender, IReadOnlyList<GaugeLogDefinition> e)
    {
        _referencesCache.Clear();

        var vms = await _gaugeItemViewModelsFactory.CreateAsync(e, OnGaugeRemoved);

        foreach (var vm in vms)
        {
            _referencesCache[(vm.ModuleId, vm.DefinitionCode)] = vm;
        }

        Application.Current.Dispatcher.Dispatch(() =>
        {
            GaugeItems = new ObservableCollection<GaugeItemViewModel>(vms);
            InitContextOptions();
        });
    }

    private void OnGaugeMeasured(object sender, IEnumerable<GaugeLogMeasurement> e)
    {
        try
        {
            foreach (var measurement in e)
            {
                if (_referencesCache.TryGetValue((measurement.ModuleId, measurement.DefinitionCode), out var vm) && vm.Visible)
                {
                    vm.Update(measurement);
                }
            }
        }
        catch (InvalidOperationException ex) //collection modified
        {
            _logger.LogWarning(ex, "OnGaugeMeasured error");
        }
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, object arg)
    {
        if (previousPage == PageType.LoggerDisplay && arg is bool submitted && submitted)
        {
            await UpdateGaugeItemsAfterChanges();
        }
        else if (previousPage == PageType.LoggerDisplayGaugeItem && arg is GaugeUserInterfaceConfig updateResult)
        {
            await UpdateGaugeItemViewModelAsync(updateResult);
        }
        else if (previousPage == PageType.LoggerReplaceGaugeItem && arg is LoggerReplaceGaugeItemSubmitResult replaceResult)
        {
            await ReplaceGaugeItemViewModelAsync(replaceResult);
        }

        InitContextOptions();
    }

    private async Task UpdateGaugeItemViewModelAsync(GaugeUserInterfaceConfig config)
    {
        var vm = GaugeItems.FirstOrDefault(e => e.ModuleId == config.ModuleId && e.DefinitionCode == config.DefinitionCode);

        if (vm is null)
        {
            return;
        }

        await vm.GaugeChangePromptSubmittedAsync(config);
    }

    private async Task ReplaceGaugeItemViewModelAsync(LoggerReplaceGaugeItemSubmitResult config)
    {
        var newVmLogDefinition = _loggerService.GaugeDefinitions.FirstOrDefault(x => x.ModuleId == config.New.ModuleId && x.DefinitionCode == config.New.DefinitionCode);
        if (newVmLogDefinition is null)
        {
            _logger.LogWarning("Could not find gauge definition to replace [{Old}] to [{New}]", config.Old.DefinitionCode, config.New.DefinitionCode);
            return;
        }

        var newVm = await _gaugeItemViewModelsFactory.CreateAsync(newVmLogDefinition, OnGaugeRemoved);

        var oldVm = GaugeItems.FirstOrDefault(e => e.ModuleId == config.Old.ModuleId && e.DefinitionCode == config.Old.DefinitionCode);
        if (oldVm is not null)
        {
            GaugeItems.Remove(oldVm);
            _referencesCache.TryRemove((oldVm.ModuleId, oldVm.DefinitionCode), out var _);
        }

        if (GaugeItems.FirstOrDefault(e => e.ModuleId == newVm.ModuleId && e.DefinitionCode == newVm.DefinitionCode) is GaugeItemViewModel existingNewVm)
        {
            GaugeItems.Remove(existingNewVm);
            _referencesCache.TryRemove((existingNewVm.ModuleId, existingNewVm.DefinitionCode), out var _);
        }

        GaugeItems.Add(newVm);
        _referencesCache[(newVm.ModuleId, newVm.DefinitionCode)] = newVm;
    }

    private async Task UpdateGaugeItemsAfterChanges()
    {
        if (_loggerService.GaugeDefinitions.Count == 0)
        {
            return;
        }

        var newGaugeItems = await _gaugeItemViewModelsFactory.CreateAsync(_loggerService.GaugeDefinitions, OnGaugeRemoved);

        var toBeUpdated = newGaugeItems.ToDictionary(
                x => x,
                x => GaugeItems.FirstOrDefault(e =>
                    e.ModuleId == x.ModuleId &&
                    e.DefinitionCode == x.DefinitionCode))
            .ToList();

        var toBeAdded = toBeUpdated.Where(x => x.Value is null).ToList();
        var toBeDeleted = GaugeItems.Where(x => !newGaugeItems.Any(v => x.ModuleId == v.ModuleId && x.DefinitionCode == v.DefinitionCode))
                                    .ToList();

        //add
        foreach (var toBeAddedItem in toBeAdded.ToList())
        {
            GaugeItems.Add(toBeAddedItem.Key);
            _referencesCache[(toBeAddedItem.Key.ModuleId, toBeAddedItem.Key.DefinitionCode)] = toBeAddedItem.Key;
        }

        //delete
        foreach (var toBeDeletedItem in toBeDeleted.ToList())
        {
            GaugeItems.Remove(toBeDeletedItem);
            _referencesCache.TryRemove((toBeDeletedItem.ModuleId, toBeDeletedItem.DefinitionCode), out var _);
        }

        //update
        foreach (var mergedItem in toBeUpdated.Where(x => x.Value != null))
        {
            mergedItem.Value.Visible = mergedItem.Key.Visible;
            mergedItem.Value.PresentationType = mergedItem.Key.PresentationType;
            mergedItem.Value.Shape = mergedItem.Key.Shape;
            mergedItem.Value.GaugeColor = mergedItem.Key.GaugeColor;
        }
    }

    private void OnGaugeRemoved(GaugeItemViewModel removedVm)
    {
        var existing = GaugeItems.FirstOrDefault(x => x.ModuleId == removedVm.ModuleId && x.DefinitionCode == removedVm.DefinitionCode);
        if (existing is not null)
        {
            GaugeItems.Remove(existing);
            _referencesCache.TryRemove((existing.ModuleId, existing.DefinitionCode), out var _);
            InitContextOptions();
        }
    }

    private async void GaugeItemsAutoAdjusted(object obj)
    {
        if (obj is not IReadOnlyList<GaugeItemViewModel> updated)
        {
            return;
        }

        var gaugeConfig = updated.Select(x => x.GetConfig()).ToList();
        await _gaugeLogDefinitionConfigurationService.SetAsync(gaugeConfig);
    }

    public void ClearGaugeItems()
    {
        _referencesCache?.Clear();
        GaugeItems?.Clear();
        InitContextOptions();
    }
}