﻿using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.Logger.Models;
using System;
using System.Linq;
using System.Windows.Input;

namespace MgFlasher.ViewModels.Logger;

public class LoggerAlertDefinitionConfigurationViewModel : BaseViewModel, IFilterableViewModel
{
    private readonly bool _dualEcusAvailable;
    private readonly bool _presentA2lName;
    private readonly INavigationService _navigationService;
    private LoggerAlertUserConfiguration _config;
    private bool _enabled;
    private PreferenceTypeViewModel _alertType;

    public string ModuleName => _config.ModuleId.ToModuleName();
    public ModuleId ModuleId => _config.ModuleId;
    public string DefinitionCode => _config.DefinitionCode;
    public string Name => _config.ToDisplayName(_presentA2lName, _dualEcusAvailable);

    public string DisplayName => !string.IsNullOrEmpty(_config.AlertName) ?
        _config.AlertName :
        _config.ToDisplayName(_presentA2lName, _dualEcusAvailable);

    public PreferenceTypeViewModel AlertType
    {
        get => _alertType;
        private set => SetProperty(ref _alertType, value);
    }

    public string Arguments => (LoggerAlertType?)AlertType?.PreferenceType switch
    {
        LoggerAlertType.OutsideRange or LoggerAlertType.InsideRange =>
            $"{_config.Arguments.ElementAt(0):F2} <> {_config.Arguments.ElementAt(1):F2}",
        _ => _config.Arguments.ElementAt(0).ToString("F2")
    };

    public bool Enabled
    {
        get => _enabled;
        set => SetProperty(ref _enabled, value);
    }

    public ICommand GoToAlertSettingsCommand { get; }
    public ICommand ChangeAlertTypeCommand { get; }
    public ICommand ChangeEnabledStateCommand { get; }

    public Guid CorrelationId { get; } = Guid.NewGuid();

    public LoggerAlertDefinitionConfigurationViewModel(
        INavigationService navigationService,
        LoggerAlertUserConfiguration config,
        bool presentA2lName,
        bool dualEcusAvailable)
    {
        _presentA2lName = presentA2lName;
        _dualEcusAvailable = dualEcusAvailable;
        _navigationService = navigationService;
        _config = config;

        GoToAlertSettingsCommand = new TraceableCommand(GoToAlertSettings, nameof(GoToAlertSettings));
        ChangeAlertTypeCommand = new TraceableCommand(ChangeAlertType, nameof(ChangeAlertType));
        ChangeEnabledStateCommand = new TraceableCommand(() => _config.Enabled = Enabled = !Enabled, "ChangeEnabledState");

        Update(config);
    }

    public void Update(LoggerAlertUserConfiguration config)
    {
        _config = config;

        AlertType = new PreferenceTypeViewModel(_config.Type);
        Enabled = _config.Enabled;
        OnPropertyChanged(nameof(Name));
        OnPropertyChanged(nameof(Arguments));
        OnPropertyChanged(nameof(DisplayName));
    }

    public void Update(LoggerAlertType type)
    {
        _config.Type = type;
        AlertType = new PreferenceTypeViewModel(_config.Type);
        OnPropertyChanged(nameof(Arguments));
    }

    public LoggerAlertUserConfiguration GetFinalConfiguration()
    {
        var config = _config.Clone();

        config.Type = (LoggerAlertType)AlertType.PreferenceType;
        config.Enabled = Enabled;

        return config;
    }

    private void ChangeAlertType(object obj)
    {
        var available = Enum.GetValues(typeof(LoggerAlertType)).Cast<LoggerAlertType>().ToList();
        var current = (LoggerAlertType)AlertType.PreferenceType;

        var next = available.IndexOf(current) + 1;
        Update(next >= available.Count ? LoggerAlertType.Above : available[next]);
    }

    private async void GoToAlertSettings(object obj)
    {
        await _navigationService.NavigateAsync(PageType.LoggerAlertItem, LoggerAlertItemPageNavigationArgs.Edit(_config, CorrelationId));
    }
}