﻿using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Ecu.Logger;
using MgFlasher.Ecu.Logger.Configuration.Dynamic;
using MgFlasher.Ecu.Logger.Configuration.Logger;
using MgFlasher.Ecu.Logger.Configuration.Templates;
using MgFlasher.Ecu.Logger.Configuration.Templates.Files;
using MgFlasher.Ecu.Logger.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger;
using MgFlasher.Flasher.Services.CarLogger.Gauges;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.CustomCode;
using MgFlasher.Flasher.Services.DevMode;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels.Logger;

public class LoggerParametersModulesViewModel : BaseViewModel
{
    private ObservableCollection<LoggerParametersModuleViewModel> _modules;
    private readonly IUserDialogs _userDialogs;
    private readonly ILoggerService _loggerService;
    private readonly ITemplateFileRepository _templateFileRepository;
    private readonly IDevModeService _devModeService;
    private readonly IDCANService _dCANService;
    private readonly ILoggerDefinitionsUserConfigurationService _userConfig;
    private readonly ILoggerDefinitionTemplateRepository _loggerDefinitionTemplateRepository;
    private readonly ILoggerDefinitionBuffersDistributionFactory _loggerDefinitionBuffersDistributionFactory;
    private readonly ILogger<LoggerParametersModulesViewModel> _logger;
    private bool _visited;
    private CarLoggerPreferenceConfiguration _loggerPreferenceConfiguration;
    private Car _connectedCar;
    private int _selectedModuleIndex;

    public ObservableCollection<LoggerParametersModuleViewModel> Modules
    {
        get => _modules;
        private set => SetProperty(ref _modules, value);
    }

    public LoggerParametersModuleViewModel SelectedModule =>
        _selectedModuleIndex >= 0 ? Modules[_selectedModuleIndex] : null;

    public int SelectedModuleIndex
    {
        get => _selectedModuleIndex;
        set => SetProperty(ref _selectedModuleIndex, value);
    }

    public CarLoggerPreferenceConfiguration LoggerPreferenceConfiguration
    {
        get => _loggerPreferenceConfiguration;
        set => SetProperty(ref _loggerPreferenceConfiguration, value);
    }

    public LoggerParametersModulesViewModel(
        IUserDialogs userDialogs,
        ILoggerService loggerService,
        ITemplateFileRepository templateFileRepository,
        IDevModeService devModeService,
        IDCANService dCANService,
        ILoggerDefinitionsUserConfigurationService userConfig,
        ILoggerDefinitionTemplateRepository loggerDefinitionTemplateRepository,
        ILoggerDefinitionBuffersDistributionFactory loggerDefinitionBuffersDistributionFactory,
        ILogger<LoggerParametersModulesViewModel> logger)
    {
        _userDialogs = userDialogs;
        _loggerService = loggerService;
        _templateFileRepository = templateFileRepository;
        _devModeService = devModeService;
        _dCANService = dCANService;
        _userConfig = userConfig;
        _loggerDefinitionTemplateRepository = loggerDefinitionTemplateRepository;
        _loggerDefinitionBuffersDistributionFactory = loggerDefinitionBuffersDistributionFactory;
        _logger = logger;
        _templateFileRepository.Initialized += async (o, e) => await InitializeModules();
    }

    public async Task OnNavigatedToAsync()
    {
        _visited = true;
        _connectedCar = await _dCANService.GetConnectedCar();

        await InitializeModules();
    }

    public async Task<bool> SubmitAsync()
    {
        if (Modules.SelectMany(x => x.Items).All(o => !o.IsChecked))
        {
            _logger.LogInformation(AppResources.Logger_NoMarkedParameters);
            await _userDialogs.AlertAsync(AppResources.Logger_NoMarkedParameters, okText: AppResources.Ok);
            return false;
        }

        if (!await EnsureValidDefinitionConfigForDevSlaveLogModuleAsync())
        {
            return false;
        }

        if (!await EnsureAnyParametersSelectedForSelectedModulesAsync())
        {
            return false;
        }

        if (!await EnsureValidParametersSizeAsync())
        {
            return false;
        }

        await SetLoggerDefinitionsAsync();

        return true;
    }

    public async Task Defaults()
    {
        if (SelectedModule is null)
        {
            return;
        }

        await SelectedModule.Defaults(LoggerPreferenceConfiguration, _connectedCar);
    }

    private async Task<List<LoggerParametersModuleViewModel>> CreateModuleViewModels()
    {
        var result = new List<LoggerParametersModuleViewModel>();

        var masterModule = await LoggerParametersModuleViewModel.CreateMasterModuleViewModelAsync(LoggerPreferenceConfiguration, _connectedCar, _userConfig);
        var slaveModule = await LoggerParametersModuleViewModel.CreateSlaveModuleViewModelAsync(LoggerPreferenceConfiguration, _connectedCar, _devModeService, _userConfig);

        result.Add(masterModule);
        result.Add(slaveModule);

        foreach (var module in result)
        {
            module.CheckedChanged += (o, e) => OnPropertyChanged("IsChecked");
        }

        return result;
    }

    public void UpdateGaugeNames()
    {
        if (Modules is null)
        {
            return;
        }

        var items = Modules.SelectMany(x => x.Items);
        var dualEcusAvailable = Modules.Where(x => x.IsModuleEnabled).Count() > 1;

        foreach (var item in items)
        {
            item.UpdateGaugeName(LoggerPreferenceConfiguration.PresentA2lMeasurementName, dualEcusAvailable);
        }
    }

    public void UpdateModulesEnabledState()
    {
        if (Modules is null)
        {
            return;
        }

        var masterModule = Modules.First(x => x.ModuleId == ModuleId.DME_MASTER);
        masterModule.IsModuleEnabled = CarLoggerPreferenceConfiguration.ShouldLogMasterModule(LoggerPreferenceConfiguration.ModuleLoggingPreference);

        var slaveModule = Modules.First(x => x.ModuleId == ModuleId.DME_SLAVE);
        var shouldLog = CarLoggerPreferenceConfiguration.ShouldLogSlaveModule(LoggerPreferenceConfiguration.ModuleLoggingPreference, _connectedCar);
        var shouldDevLog = CarLoggerPreferenceConfiguration.ShouldDevLogSlaveModule(LoggerPreferenceConfiguration.ModuleLoggingPreference, _devModeService);
        slaveModule.IsModuleEnabled = shouldLog || shouldDevLog;
    }

    public void RemoveAll()
    {
        if (SelectedModule is null)
        {
            return;
        }

        SelectedModule.RemoveAll();
    }

    private async Task SetLoggerDefinitionsAsync()
    {
        var semaphore = new SemaphoreSlim(0);

        void OnGaugeDefinitionsUpdated(object o, IReadOnlyList<GaugeLogDefinition> e) => semaphore.Release();

        try
        {
            _loggerService.GaugeDefinitionsUpdated += OnGaugeDefinitionsUpdated;

            await _userConfig.SetAsync(
                _connectedCar.VIN, _connectedCar.EngineCode, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(_connectedCar.ProcessorArchitectureType),
                Modules.ToDictionary(x => x.ModuleId, x => x.Items.ToDictionary(x => x.DefinitionCode, x => x.IsChecked)));

            if (!_loggerService.IsLoggerStarted)
            {
                return;
            }

            await semaphore.WaitAsync(750);
        }
        finally
        {
            _loggerService.GaugeDefinitionsUpdated -= OnGaugeDefinitionsUpdated;
        }
    }

    private async Task<bool> EnsureAnyParametersSelectedForSelectedModulesAsync()
    {
        bool AnyChecked(ModuleId moduleId) =>
            Modules.Where(x => x.ModuleId == moduleId).SelectMany(x => x.Items).Any(x => x.IsChecked);

        if (LoggerPreferenceConfiguration.ShouldLogMasterModule())
        {
            var anyChecked = AnyChecked(ModuleId.DME_MASTER);
            if (!anyChecked)
            {
                await _userDialogs.AlertAsync(
                    string.Format(AppResources.LoggerParametersPage_PleaseSelectAnyParameterForModuleToStartLog, "MASTER"), okText: AppResources.Ok);
                return false;
            }
        }
        if (LoggerPreferenceConfiguration.ShouldLogSlaveModule(_connectedCar) || LoggerPreferenceConfiguration.ShouldDevLogSlaveModule(_devModeService))
        {
            var anyChecked = AnyChecked(ModuleId.DME_SLAVE);
            if (!anyChecked)
            {
                await _userDialogs.AlertAsync(
                    string.Format(AppResources.LoggerParametersPage_PleaseSelectAnyParameterForModuleToStartLog, "SLAVE"), okText: AppResources.Ok);
                return false;
            }
        }

        return true;
    }

    private async Task<bool> EnsureValidDefinitionConfigForDevSlaveLogModuleAsync()
    {
        if (!LoggerPreferenceConfiguration.ShouldLogMasterModule() || !LoggerPreferenceConfiguration.ShouldDevLogSlaveModule(_devModeService))
        {
            return true;
        }

        foreach (var module in Modules)
        {
            var valid = Modules
                .Where(x => x != module)
                .SelectMany(x => x.Items)
                .Where(x => x.IsChecked)
                .Select(x => x.DefinitionCode)
                .SequenceEqual(module.Items.Where(x => x.IsChecked).Select(x => x.DefinitionCode));

            if (!valid)
            {
                await _userDialogs.AlertAsync(
                    AppResources.LoggerParametersPage_CouldNotRunLoggerDueToDevMasterAsSlave, okText: AppResources.Ok);
                return false;
            }
        }

        return true;
    }

    private async Task InitializeModules()
    {
        if (!_visited)
        {
            return;
        }

        var vms = await CreateModuleViewModels();
        Modules = new ObservableCollection<LoggerParametersModuleViewModel>(vms);
    }

    private async Task<bool> EnsureValidParametersSizeAsync()
    {
        var masterModule = Modules.First(x => x.ModuleId == ModuleId.DME_MASTER);
        if (masterModule.IsModuleEnabled && !await EnsureValidParametersSizeForModuleAsync(masterModule))
        {
            return false;
        }

        var slaveModule = Modules.First(x => x.ModuleId == ModuleId.DME_SLAVE);
        if (slaveModule.IsModuleEnabled && !await EnsureValidParametersSizeForModuleAsync(slaveModule))
        {
            return false;
        }

        return true;
    }

    private async Task<bool> EnsureValidParametersSizeForModuleAsync(LoggerParametersModuleViewModel module)
    {
        var selected = module.Items.Where(x => x.IsChecked).Select(x => x.DefinitionCode);
        var definitions = await _loggerDefinitionTemplateRepository.GetAsync(_connectedCar.EngineCode, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(_connectedCar.ProcessorArchitectureType));
        var selectedDefinitions = definitions.Where(x => selected.Contains(x.Code)).ToList();

        try
        {
            var loggerCarModuleInfo = module.ModuleId == ModuleId.DME_SLAVE ?
                (_connectedCar.EcuSlave ?? _connectedCar.EcuMaster).GetLoggerCarModuleInfo() :
                _connectedCar.EcuMaster.GetLoggerCarModuleInfo();

            var distribution = await _loggerDefinitionBuffersDistributionFactory
                .CreateAsync(selectedDefinitions, new List<DynamicLoggerDefinitionRecord>(), loggerCarModuleInfo);

            if (distribution.BuffersDefinitions.Count > 1)
            {
                await _userDialogs.AlertAsync(string.Format(AppResources.LoggerParametersModules_PossibleLoggerPerformanceDegradation, distribution.BuffersDefinitions.Count, module.ModuleId), okText: AppResources.Ok);
            }

            return true;
        }
        catch (LoggerException ex) when (ex.ErrorCode == LoggerException.ErrorCodes.CannotDistributeLoggerParams)
        {
            _logger.LogError(ex, "EnsureValidParametersSizeForModuleAsync error");

            await _userDialogs.AlertAsync(string.Format(AppResources.LoggerParametersModules_TooManyParametersSelected, module.ModuleId), okText: AppResources.Ok);
            return false;
        }
        catch (LoggerException ex)
        {
            _logger.LogError(ex, "EnsureValidParametersSizeForModuleAsync error");

            await _userDialogs.AlertAsync(string.Format(AppResources.LoggerParametersModules_CannotPrepareSelected, module.ModuleId), okText: AppResources.Ok);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "EnsureValidParametersSizeForModuleAsync error");
            await _userDialogs.AlertAsync(string.Format(AppResources.LoggerParametersModules_UnknownErrorOccured, module.ModuleId, ex.Message), okText: AppResources.Ok);
            return false;
        }
    }
}