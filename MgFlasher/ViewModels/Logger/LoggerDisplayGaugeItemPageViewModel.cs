﻿using MgFlasher.Ecu.Logger.Configuration.Templates;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User.Storage;
using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Services;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.Maui.Controls;
using Microsoft.Maui;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.Logger;

public class LoggerDisplayGaugeItemPageViewModel : PageViewModel
{
    private GaugeUserInterfaceConfig _config;
    private PreferenceTypeViewModel _presentationType;
    private double _scaleW, _scaleH;
    private bool _visible;
    private bool _keepAspectRatio;
    private readonly INavigationService _navigationService;
    private readonly ILoggerDefinitionDisplayTemplateRepository _loggerDefinitionDisplayTemplateRepository;

    public ObservableCollection<PreferenceTypeViewModel> PresentationTypes { get; }

    public string ModuleName => _config?.ModuleId.ToModuleName();
    public string Name => _config?.Name;
    public string A2lMeasurementName => _config?.A2lMeasurementName;

    public bool Visible
    {
        get => _visible;
        set => SetProperty(ref _visible, value);
    }

    public PreferenceTypeViewModel PresentationType
    {
        get => _presentationType;
        set => SetProperty(ref _presentationType, value);
    }

    public bool KeepAspectRatio
    {
        get => _keepAspectRatio;
        set => SetProperty(ref _keepAspectRatio, value);
    }

    public double ScaleW
    {
        get => _scaleW;
        set
        {
            var scaleH = ScaleH;
            SetProperty(ref _scaleW, value);
            if (KeepAspectRatio && scaleH != value)
            {
                ScaleH = value;
            }
        }
    }

    public double ScaleH
    {
        get => _scaleH;
        set
        {
            var scaleW = ScaleW;
            SetProperty(ref _scaleH, value);
            if (KeepAspectRatio && scaleW != value)
            {
                ScaleW = value;
            }
        }
    }

    public ICommand DefaultsCommand { get; }
    public ICommand SubmitCommand { get; }

    public LoggerDisplayGaugeItemPageViewModel(
        INavigationService navigationService,
        ILoggerDefinitionDisplayTemplateRepository loggerDefinitionDisplayTemplateRepository)
    {
        _navigationService = navigationService;
        _loggerDefinitionDisplayTemplateRepository = loggerDefinitionDisplayTemplateRepository;

        ScaleW = 1;
        ScaleH = 1;
        KeepAspectRatio = true;

        PresentationTypes = new ObservableCollection<PreferenceTypeViewModel>()
        {
            new PreferenceTypeViewModel(GaugePresentationType.Circular),
            new PreferenceTypeViewModel(GaugePresentationType.Rectangle),
        };

        DefaultsCommand = new TraceableCommand(Defaults, nameof(Defaults));
        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));
    }

    private async void Submit(object obj)
    {
        _config.Shape ??= new GaugeLogShape();
        _config.Shape.ScaleW = ScaleW;
        _config.Shape.ScaleH = ScaleH;
        _config.PresentationType = (GaugePresentationType)PresentationType.PreferenceType;
        _config.Visible = Visible;

        await _navigationService.NavigateToPreviousAsync(_config);
    }

    private async void Defaults(object obj)
    {
        KeepAspectRatio = true;
        ScaleW = 1;
        ScaleH = 1;
        PresentationType = PresentationTypes.First(x => (GaugePresentationType)x.PreferenceType == GaugePresentationType.Circular);

        var definitions = await _loggerDefinitionDisplayTemplateRepository.GetAsync();
        Visible = definitions.FirstOrDefault(x => x.DefinitionCode == _config.DefinitionCode)?.InitiallyVisible ?? false;
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (arg is GaugeUserInterfaceConfig config)
        {
            _config = config;

            KeepAspectRatio = config.Shape?.ScaleW == config.Shape?.ScaleH;
            ScaleW = config.Shape?.ScaleW ?? 1;
            ScaleH = config.Shape?.ScaleH ?? 1;

            PresentationType = PresentationTypes.First(x => (GaugePresentationType)x.PreferenceType == _config.PresentationType);
            Visible = _config.Visible;

            OnPropertyChanged(nameof(ModuleName));
            OnPropertyChanged(nameof(A2lMeasurementName));
            OnPropertyChanged(nameof(Name));
        }

        return Task.CompletedTask;
    }
}