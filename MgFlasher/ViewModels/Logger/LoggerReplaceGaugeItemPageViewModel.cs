﻿using MgFlasher.Flasher.Services.CarLogger;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.ViewModels.Logger.Models;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.Maui.Controls;
using Microsoft.Maui;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.Logger;

public class LoggerReplaceGaugeItemPageViewModel : PageViewModel
{
    private readonly INavigationService _navigationService;
    private readonly ILoggerService _loggerService;
    private readonly IGaugeLogDefinitionConfigurationService _gaugeLogDefinitionConfigurationService;
    private readonly ILoggerPreferenceService _loggerPreferenceService;
    private GaugeLogDefinitionConfigurationViewModel _selectedItem;
    private GaugeUserInterfaceConfig _toReplace;

    private ObservableCollection<GaugeLogDefinitionConfigurationViewModel> _items;

    public ObservableCollection<GaugeLogDefinitionConfigurationViewModel> Items
    {
        get => _items;
        private set => SetProperty(ref _items, value);
    }

    public GaugeLogDefinitionConfigurationViewModel SelectedItem
    {
        get => _selectedItem;
        set
        {
            SetProperty(ref _selectedItem, value);
            OnPropertyChanged(nameof(CanSubmit));
        }
    }

    public bool CanSubmit => SelectedItem != null;

    public ICommand SubmitCommand { get; }

    public LoggerReplaceGaugeItemPageViewModel(
        INavigationService navigationService,
        ILoggerService loggerService,
        IGaugeLogDefinitionConfigurationService gaugeLogDefinitionConfigurationService,
        ILoggerPreferenceService loggerPreferenceService)
    {
        _navigationService = navigationService;
        _loggerService = loggerService;
        _gaugeLogDefinitionConfigurationService = gaugeLogDefinitionConfigurationService;
        _loggerPreferenceService = loggerPreferenceService;
        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));

        _loggerPreferenceService.CarConfigChanged += OnCarConfigChanged;
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (arg is GaugeUserInterfaceConfig config)
        {
            _toReplace = config;

            var prefs = await _loggerPreferenceService.GetCarConfiguration();
            var configs = await _gaugeLogDefinitionConfigurationService.GetAllAsync();
            var vms = configs
                .Where(x => !(x.ModuleId == config.ModuleId && x.DefinitionCode == config.DefinitionCode))
                .Where(x => !x.Visible)
                .Where(x => _loggerService.GaugeDefinitions.Any(c => c.ModuleId == x.ModuleId && c.DefinitionCode == x.DefinitionCode))
                .Select(x => new GaugeLogDefinitionConfigurationViewModel(_navigationService, x, prefs.PresentA2lMeasurementName, prefs.DualEcusAvailable));

            Items = new ObservableCollection<GaugeLogDefinitionConfigurationViewModel>(vms);
            SelectedItem = Items.FirstOrDefault();
        }
    }

    private async void Submit(object obj)
    {
        if (SelectedItem is null)
        {
            return;
        }

        //update old gauge
        _toReplace.Visible = false;
        await _gaugeLogDefinitionConfigurationService
            .SetAsync(_toReplace.ModuleId, _toReplace.DefinitionCode, _toReplace.Shape?.Clone(), _toReplace.PresentationType, _toReplace.Visible);

        //update new gauge
        var replacedBy = SelectedItem.GetFinalConfiguration();
        replacedBy.Visible = true;
        replacedBy.Shape = _toReplace.Shape?.Clone();
        await _gaugeLogDefinitionConfigurationService
            .SetAsync(replacedBy.ModuleId, replacedBy.DefinitionCode, replacedBy.Shape?.Clone(), replacedBy.PresentationType, replacedBy.Visible);

        //return
        var result = new LoggerReplaceGaugeItemSubmitResult(_toReplace, replacedBy);
        await _navigationService.NavigateToPreviousAsync(result);
    }

    private void OnCarConfigChanged(object sender, CarLoggerPreferenceConfiguration e)
    {
        foreach (var item in Items)
        {
            item.UpdateGaugeName(e.PresentA2lMeasurementName);
        }
    }
}