﻿using MgFlasher.Ecu.Logger.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.DevMode;
using MgFlasher.Flasher.Services.Models;
using System.Collections.ObjectModel;
using MgFlasher.Ecu.Logger.Configuration.Templates.Files;
using System.Threading.Tasks;
using System.Collections.Generic;
using MgFlasher.Services;
using System;
using MgFlasher.Localization.Resources;
using MgFlasher.ViewModels.Logger.Models;
using System.Linq;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.Logger;

public class LoggerAlertsModulesViewModel : BaseViewModel
{
    private ObservableCollection<LoggerAlertsModuleViewModel> _modules;
    private readonly ITemplateFileRepository _templateFileRepository;
    private readonly IDevModeService _devModeService;
    private readonly IDCANService _dCANService;
    private readonly ILoggerAlertsUserConfigurationService _userConfig;
    private readonly ILoggerPreferenceService _loggerPreferenceService;
    private readonly INavigationService _navigationService;
    private bool _visited;
    private Car _connectedCar;
    private int _selectedModuleIndex;
    private Dictionary<string, Action> _contextOptions;

    public Dictionary<string, Action> ContextOptions
    {
        get => _contextOptions;
        private set => SetProperty(ref _contextOptions, value);
    }

    public ObservableCollection<LoggerAlertsModuleViewModel> Modules
    {
        get => _modules;
        private set => SetProperty(ref _modules, value);
    }

    public LoggerAlertsModuleViewModel SelectedModule => _selectedModuleIndex >= 0 ? Modules[_selectedModuleIndex] : null;

    public int SelectedModuleIndex
    {
        get => _selectedModuleIndex;
        set => SetProperty(ref _selectedModuleIndex, value);
    }

    public LoggerAlertsModulesViewModel(
        ITemplateFileRepository templateFileRepository,
        IDevModeService devModeService,
        IDCANService dCANService,
        ILoggerAlertsUserConfigurationService userConfig,
        ILoggerPreferenceService loggerPreferenceService,
        INavigationService navigationService)
    {
        _templateFileRepository = templateFileRepository;
        _devModeService = devModeService;
        _dCANService = dCANService;
        _userConfig = userConfig;
        _loggerPreferenceService = loggerPreferenceService;
        _navigationService = navigationService;
        _templateFileRepository.Initialized += async (o, e) => await InitializeModules();

        InitContextOptions();
    }

    public async Task OnNavigatedToAsync()
    {
        _visited = true;
        _connectedCar = await _dCANService.GetConnectedCar();

        await InitializeModules();
    }

    public void UpdateAlerts(LoggerAlertItemPageSubmitResult result)
    {
        var module = Modules.First(x => x.ModuleId == result.Item.ModuleId);
        module.UpdateAlerts(result);
    }

    public async Task SubmitAsync()
    {
        var configs = Modules.SelectMany(x => x.Items)
            .Select(x => x.GetFinalConfiguration())
            .ToList();

        await _userConfig.SetAsync(_connectedCar.VIN, configs);
    }

    private async Task InitializeModules()
    {
        if (!_visited)
        {
            return;
        }

        var vms = await CreateModuleViewModels();
        Modules = new ObservableCollection<LoggerAlertsModuleViewModel>(vms);
    }

    private async Task<List<LoggerAlertsModuleViewModel>> CreateModuleViewModels()
    {
        var result = new List<LoggerAlertsModuleViewModel>();

        var config = await _loggerPreferenceService.GetCarConfiguration();
        var masterModule = await LoggerAlertsModuleViewModel.CreateMasterModuleViewModelAsync(config, _connectedCar, _userConfig, _navigationService);
        var slaveModule = await LoggerAlertsModuleViewModel.CreateSlaveModuleViewModelAsync(config, _connectedCar, _devModeService, _userConfig, _navigationService);

        result.Add(masterModule);
        result.Add(slaveModule);

        return result;
    }

    private void InitContextOptions()
    {
        var options = new Dictionary<string, Action>();

        //add
        options[AppResources.LoggerAlertsModulesView_ContextOptions_Add] = async () =>
        {
            await SelectedModule.AddAlertAsync();
        };
        //enable all
        options[AppResources.LoggerAlertsModulesView_ContextOptions_EnableAll] = () =>
        {
            SelectedModule.SetEnabled(true);
        };
        //disable all
        options[AppResources.LoggerAlertsModulesView_ContextOptions_DisableAll] = () =>
        {
            SelectedModule.SetEnabled(false);
        };

        ContextOptions = options;
    }
}