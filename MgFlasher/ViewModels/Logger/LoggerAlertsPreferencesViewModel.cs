﻿using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels.Logger;

public class LoggerAlertsPreferencesViewModel : BaseViewModel
{
    private bool _selected;
    private ObservableCollection<LoggerAlertDestinationTypeItemViewModel> _alertDestinations;
    private readonly ILoggerPreferenceService _loggerPreferenceService;

    public bool Selected
    {
        get => _selected;
        set => SetProperty(ref _selected, value);
    }

    public ObservableCollection<LoggerAlertDestinationTypeItemViewModel> AlertDestinations
    {
        get => _alertDestinations;
        private set => SetProperty(ref _alertDestinations, value);
    }

    public LoggerAlertsPreferencesViewModel(ILoggerPreferenceService loggerPreferenceService)
    {
        _loggerPreferenceService = loggerPreferenceService;
    }

    public async Task OnNavigatedToAsync()
    {
        var preferences = await _loggerPreferenceService.GetCarConfiguration();
        AlertDestinations = new ObservableCollection<LoggerAlertDestinationTypeItemViewModel>(Enum.GetValues(typeof(LoggerAlertDestinationType))
            .Cast<LoggerAlertDestinationType>()
            .Select(x => new LoggerAlertDestinationTypeItemViewModel(x)
            {
                IsChecked = preferences.AlertDestinationTypes.Contains(x),
            }));
    }

    public async Task SubmitAsync()
    {
        var config = await _loggerPreferenceService.GetCarConfiguration();
        config.AlertDestinationTypes = AlertDestinations.Where(x => x.IsChecked).Select(x => x.Type).ToList();
        await _loggerPreferenceService.ApplyConfiguration(config);
    }
}