﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MgFlasher.Flasher.Services.CarLogger.Parser;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.ApplicationModel.DataTransfer;

namespace MgFlasher.ViewModels.Logger;

public class LoggerFileChartViewModel : BaseViewModel, ITabComponent, IContextOptionsViewModel
{
    private LoggerFileContent _content;
    private string _url;
    private readonly IUserDialogs _userDialogs;

    public Dictionary<string, Action> ContextOptions { get; }

    public string Url
    {
        get => _url;
        private set => SetProperty(ref _url, value);
    }

    public LoggerFileChartViewModel(IUserDialogs userDialogs)
    {
        _userDialogs = userDialogs;
        ContextOptions = new Dictionary<string, Action>()
        {
            [AppResources.LoggerFileChartView_CopyToClipboard] = CopyToClipboard,
            [AppResources.LoggerFileChartView_OpenInBrowser] = OpenInBrowser,
            [AppResources.LoggerFileChartView_RefreshChart] = RefreshChart,
        };
    }

    private void RefreshChart()
    {
        OnPropertyChanged(nameof(Url));
    }

    public Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        _content = (LoggerFileContent)arg;
        Url = BuildWebViewUrl();
        return Task.CompletedTask;
    }

    private string BuildWebViewUrl() =>
        !string.IsNullOrEmpty(_content?.Attributes?.MgFlasherLogsCloudUrl) ?
            _content?.Attributes?.MgFlasherLogsCloudUrl + "?mode=webview" : null;

    private async void CopyToClipboard()
    {
        await _userDialogs.Toast(new(AppResources.Log_Share_Success_Link_Copied, false));
        await Clipboard.SetTextAsync(_content?.Attributes?.MgFlasherLogsCloudUrl);
    }

    private async void OpenInBrowser()
    {
        await Launcher.OpenAsync(new Uri(_content?.Attributes?.MgFlasherLogsCloudUrl));
    }
}