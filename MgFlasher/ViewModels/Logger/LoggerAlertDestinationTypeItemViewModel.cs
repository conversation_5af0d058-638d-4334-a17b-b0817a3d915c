﻿using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Localization;

namespace MgFlasher.ViewModels.Logger;

public class LoggerAlertDestinationTypeItemViewModel : OptionalItemViewModel
{
    public LoggerAlertDestinationType Type { get; }

    public LoggerAlertDestinationTypeItemViewModel(LoggerAlertDestinationType type) :
        base(TranslateHelper.GetString($"LoggerAlertsPreferencesView_AlertDestinations_{type}"))
    {
        Type = type;
    }
}