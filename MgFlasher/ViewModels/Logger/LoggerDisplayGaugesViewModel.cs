﻿using MgFlasher.Flasher.Services.CarLogger;
using MgFlasher.Flasher.Services.CarLogger.Gauges;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User.Storage;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Views.Logger.Services;
using Syncfusion.Maui.DataSource.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.Logger;

public class LoggerDisplayGaugesViewModel : BaseViewModel
{
    private readonly INavigationService _navigationService;
    private readonly IGaugeLogDefinitionConfigurationService _gaugeLogDefinitionConfigurationService;
    private readonly ILoggerService _loggerService;
    private readonly IUserDialogs _userDialogs;
    private readonly ILoggerPreferenceService _loggerPreferenceService;
    private readonly GaugeViewLayoutPositioner _positioner;

    private ObservableCollection<GaugeLogDefinitionConfigurationViewModel> _items;

    public ObservableCollection<GaugeLogDefinitionConfigurationViewModel> Items
    {
        get => _items;
        private set => SetProperty(ref _items, value);
    }

    public LoggerDisplayGaugesViewModel(
        INavigationService navigationService,
        IGaugeLogDefinitionConfigurationService gaugeLogDefinitionConfigurationService,
        ILoggerService loggerService,
        IUserDialogs userDialogs,
        ILoggerPreferenceService loggerPreferenceService)
    {
        _navigationService = navigationService;
        _gaugeLogDefinitionConfigurationService = gaugeLogDefinitionConfigurationService;
        _loggerService = loggerService;
        _userDialogs = userDialogs;
        _loggerPreferenceService = loggerPreferenceService;
        _positioner = new GaugeViewLayoutPositioner();

        _loggerPreferenceService.CarConfigChanged += OnLoggerPrefConfigChanged;
    }

    public async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (previousPage == PageType.LoggerDisplayGaugeItem && arg is GaugeUserInterfaceConfig config)
        {
            UpdateViewModelItem(config);
            return;
        }
        else if (previousPage == PageType.LoggerDisplayGaugeItem)
        {
            return;
        }

        await InitializeItemsAsync();
    }

    public void SetAllToType(GaugePresentationType presentationType)
    {
        foreach (var item in Items)
        {
            item.Update(presentationType);
        }
    }

    public void AutoAdjustToGrid()
    {
        _positioner.AutoAdjustByGrid(Items.Where(x => x.Visible).ToList().OfType<IShapeHolderViewModel>());
    }

    public async void Defaults()
    {
        var configs = await _gaugeLogDefinitionConfigurationService.GetDefaultsAsync();

        var configWithVms = configs.Select(c =>
        (
            Config: c,
            VM: Items.FirstOrDefault(x => x.ModuleId == c.ModuleId && x.DefinitionCode == c.DefinitionCode)
        )).Where(x => x.VM != null).ToList();

        foreach (var item in configWithVms)
        {
            item.VM.Update(item.Config);
        }

        Items = new ObservableCollection<GaugeLogDefinitionConfigurationViewModel>(configWithVms.Select(x => x.VM));

        if (Items.All(x => !x.Visible))
        {
            Items.Take(GridAutoLayoutPositionerPlatformSettings.GaugeCountLimit)
                .ForEach(x => x.Visible = true);
        }

        AutoAdjustToGrid();
    }

    public async Task<bool> SubmitAsync()
    {
        // Validate
        if (Items.All(o => !o.Visible))
        {
            await _userDialogs.AlertAsync(AppResources.LoggerDisplayGauges_NoVisibleParameters, okText: AppResources.Ok);
            return false;
        }

        var visibleCount = Items.Count(x => x.Visible);
        var maxVisibleCount = GridAutoLayoutPositionerPlatformSettings.GaugeCountLimit;
        // List Mode view can have unlimited list of parameters, Gauge Mode can have only as much as the grid allows.
        if (visibleCount > maxVisibleCount && _loggerService.IsGaugeModeSelected)
        {
            bool result = await _userDialogs.ConfirmAsync(
                message: string.Format(AppResources.LoggerDisplayGauges_TooManyParametersSelected, maxVisibleCount),
                okText: AppResources.LoggerDisplayGauges_Confirm_CorrectMe,
                cancelText: AppResources.Cancel);
            if (!result)
            {
                return false;
            }

            // Decrease visible items count
            Items.Where(x => x.Visible).Skip(maxVisibleCount).ForEach(x => x.Visible = false);
        }

        // Prepare
        if (Items.Where(x => x.Visible).Any(x => x.Shape is null) && _loggerService.IsGaugeModeSelected)
        {
            AutoAdjustToGrid();
        }

        // Apply
        var configs = Items.Select(x => x.GetFinalConfiguration());

        await _gaugeLogDefinitionConfigurationService.SetAsync(configs);

        return true;
    }

    public void SetDisplayName(bool presentA2lName)
    {
        foreach (var item in Items)
        {
            item.UpdateGaugeName(presentA2lName);
        }
    }

    private void UpdateViewModelItem(GaugeUserInterfaceConfig config)
    {
        var vm = Items.First(x => x.ModuleId == config.ModuleId && x.DefinitionCode == config.DefinitionCode);
        vm.Update(config);
    }

    private void OnLoggerPrefConfigChanged(object sender, CarLoggerPreferenceConfiguration e)
    {
        Items?.ForEach(x => x.UpdateGaugeName(e.PresentA2lMeasurementName));
    }

    private async Task InitializeItemsAsync()
    {
        var preferences = await _loggerPreferenceService.GetCarConfiguration();

        var currentMeasured = _loggerService.GaugeDefinitions ?? new List<GaugeLogDefinition>();
        var allItems = await _gaugeLogDefinitionConfigurationService.GetAllAsync();

        var items = allItems
            .Where(x => !currentMeasured.Any() ||
                        currentMeasured.Any(gd =>
                            gd.ModuleId == x.ModuleId && gd.DefinitionCode == x.DefinitionCode));
        var vms = items
            .Select(x => new GaugeLogDefinitionConfigurationViewModel(
                _navigationService, x, preferences.PresentA2lMeasurementName, preferences.DualEcusAvailable));

        Items = new ObservableCollection<GaugeLogDefinitionConfigurationViewModel>(vms);
    }
}