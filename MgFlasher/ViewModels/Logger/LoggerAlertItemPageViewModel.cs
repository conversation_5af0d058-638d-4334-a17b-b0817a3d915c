﻿using MgFlasher.Ecu.Logger.Configuration.User;
using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using MgFlasher.Flasher.Services.CarLogger.Gauges;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.CustomCode;
using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.Logger.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels.Logger;

public class LoggerAlertItemPageViewModel : PageViewModel
{
    private readonly IDCANService _dCANService;
    private readonly IGaugeLogDefinitionConfigurationService _definitionConfigurationService;
    private readonly ILoggerUnitsUserConfigurationService _unitsConfigurationService;
    private readonly ILoggerDefinitionsUserConfigurationService _definitionService;
    private readonly IGaugeLogMeasurementTransformer _measurementTransformer;
    private readonly ILoggerPreferenceService _loggerPreferenceService;
    private readonly INavigationService _navigationService;
    private ObservableCollection<PreferenceTypeViewModel> _availableAlertTypes;
    private PreferenceTypeViewModel _selectedAlertType;
    private ObservableCollection<LoggerDefinitionViewModel> _availableLoggerDefinitions;
    private LoggerDefinitionViewModel _selectedLoggerDefinition;
    private LoggerAlertItemPageNavigationArgs _config;
    private double _alertArgument0, _alertArgument1, _argumentMin = -3000, _argumentMax = 3000;
    private bool _enabled;
    private string _alertName;
    private string _unit;
    private int _displayPrecision;

    public string ModuleName => _config?.Item?.ModuleId.ToModuleName();

    public ObservableCollection<PreferenceTypeViewModel> AvailableAlertTypes
    {
        get => _availableAlertTypes;
        private set => SetProperty(ref _availableAlertTypes, value);
    }

    public PreferenceTypeViewModel SelectedAlertType
    {
        get => _selectedAlertType;
        set => SetProperty(ref _selectedAlertType, value);
    }

    public double AlertArgument0
    {
        get => _alertArgument0;
        set => SetProperty(ref _alertArgument0, value);
    }

    public double AlertArgument1
    {
        get => _alertArgument1;
        set => SetProperty(ref _alertArgument1, value);
    }

    public double ArgumentMin
    {
        get => _argumentMin;
        private set => SetProperty(ref _argumentMin, value);
    }

    public double ArgumentMax
    {
        get => _argumentMax;
        private set => SetProperty(ref _argumentMax, value);
    }

    public string Unit
    {
        get => _unit;
        private set => SetProperty(ref _unit, value);
    }

    public int DisplayPrecision
    {
        get => _displayPrecision;
        private set => SetProperty(ref _displayPrecision, value);
    }

    public bool Enabled
    {
        get => _enabled;
        set => SetProperty(ref _enabled, value);
    }

    public ObservableCollection<LoggerDefinitionViewModel> AvailableLoggerDefinitions
    {
        get => _availableLoggerDefinitions;
        private set => SetProperty(ref _availableLoggerDefinitions, value);
    }

    public LoggerDefinitionViewModel SelectedLoggerDefinition
    {
        get => _selectedLoggerDefinition;
        set
        {
            SetProperty(ref _selectedLoggerDefinition, value);

            ArgumentMin = SelectedLoggerDefinition?.Min ?? -3000;
            ArgumentMax = SelectedLoggerDefinition?.Max ?? 30000;
            AlertArgument0 = Math.Min(ArgumentMax, Math.Max(ArgumentMin, AlertArgument0));
            AlertArgument1 = Math.Min(ArgumentMax, Math.Max(ArgumentMin, AlertArgument1));
            Unit = SelectedLoggerDefinition?.Unit;
            DisplayPrecision = SelectedLoggerDefinition?.DisplayPrecision ?? 2;
        }
    }

    public string AlertName
    {
        get => _alertName;
        set => SetProperty(ref _alertName, value);
    }

    public ICommand SubmitCommand { get; }

    public LoggerAlertItemPageViewModel(
        IDCANService dCANService,
        IGaugeLogDefinitionConfigurationService gaugeDefinitionService,
        ILoggerUnitsUserConfigurationService unitsConfigurationService,
        ILoggerDefinitionsUserConfigurationService definitionService,
        IGaugeLogMeasurementTransformer measurementTransformer,
        ILoggerPreferenceService loggerPreferenceService,
        INavigationService navigationService)
    {
        _dCANService = dCANService;
        _definitionConfigurationService = gaugeDefinitionService;
        _unitsConfigurationService = unitsConfigurationService;
        _definitionService = definitionService;
        _measurementTransformer = measurementTransformer;
        _loggerPreferenceService = loggerPreferenceService;
        _navigationService = navigationService;

        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));

        AvailableAlertTypes = new ObservableCollection<PreferenceTypeViewModel>(Enum.GetValues(typeof(LoggerAlertType)).Cast<LoggerAlertType>()
            .Select(x => new PreferenceTypeViewModel(x)));
        AvailableLoggerDefinitions = new ObservableCollection<LoggerDefinitionViewModel>();
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _config = (LoggerAlertItemPageNavigationArgs)arg;
        await InitializeAvailableLoggerDefinitions();

        UpdateState(_config.Item);
    }

    private async Task InitializeAvailableLoggerDefinitions()
    {
        var car = await _dCANService.GetConnectedCar();
        var carConfig = await _loggerPreferenceService.GetCarConfiguration();

        var userDefinitions = await _definitionService.GetAsync(_config.Item.ModuleId, car.VIN, car.EngineCode, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(car.ProcessorArchitectureType));
        var locators = await _definitionConfigurationService.GetCompleteAllAsync();
        var units = await _unitsConfigurationService.GetAsync(car.VIN, car.EngineCode, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(car.ProcessorArchitectureType));

        var vms = userDefinitions
            .Where(x => x.Selected || x.DefinitionCode == _config.Item.DefinitionCode)
            .Select(u => locators.FirstOrDefault(x => x.ModuleId == _config.Item.ModuleId && x.Item1.Code == u.DefinitionCode))
            .Where(u => u.Item1 is not null)
            .Select(u => (Locator: u.Item1, Display: u.Item2, Unit: units.FirstOrDefault(x => x.SourceGroup == u.Item1.Group)))
            .Where(u => u.Unit is not null)
            .Select(x =>
            {
                double min = x.Display.Min ?? -3000, max = x.Display.Max ?? 3000;
                int displayPrecision = 0;

                if (x.Display.Min.HasValue && x.Display.Max.HasValue && x.Locator.Unit != x.Unit.DestinationUnit)
                {
                    (min, max, displayPrecision) = _measurementTransformer.TransformByUnit(x.Locator.Group,
                        x.Locator.Unit, x.Display.Min.Value, x.Display.Max.Value,
                        x.Unit.DestinationUnit);
                }

                return new LoggerDefinitionViewModel(carConfig,
                    x.Locator, x.Unit.DestinationUnit, min, max, displayPrecision, _config.Item.ModuleId);
            });
        AvailableLoggerDefinitions = new ObservableCollection<LoggerDefinitionViewModel>(vms);
    }

    private void UpdateState(LoggerAlertUserConfiguration state)
    {
        OnPropertyChanged(nameof(ModuleName));
        Enabled = state.Enabled;
        SelectedAlertType = AvailableAlertTypes.First(x => (LoggerAlertType)x.PreferenceType == state.Type);
        SelectedLoggerDefinition = AvailableLoggerDefinitions.FirstOrDefault(x => x.DefinitionCode == state.DefinitionCode)
                                   ?? AvailableLoggerDefinitions.First();
        AlertArgument0 = state.Arguments.ElementAtOrDefault(0);
        AlertArgument1 = state.Arguments.ElementAtOrDefault(1);
        AlertName = state.AlertName;
    }

    private async void Submit(object obj)
    {
        var config = new LoggerAlertUserConfiguration()
        {
            ModuleId = _config.Item.ModuleId,
            A2lMeasurementName = SelectedLoggerDefinition.A2lMeasurementName,
            Name = SelectedLoggerDefinition.Name,
            Arguments = new List<double>() { AlertArgument0, AlertArgument1 },
            DefinitionCode = SelectedLoggerDefinition.DefinitionCode,
            Enabled = Enabled,
            Type = (LoggerAlertType)SelectedAlertType.PreferenceType,
            AlertName = AlertName
        };

        await _navigationService.NavigateToPreviousAsync(new LoggerAlertItemPageSubmitResult(config, _config.AddMode, _config.CorrelationId));
    }
}