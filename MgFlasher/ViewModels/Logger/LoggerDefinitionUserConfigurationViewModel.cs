﻿using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using MgFlasher.Models;

namespace MgFlasher.ViewModels.Logger;

public class LoggerDefinitionUserConfigurationViewModel : OptionalItemViewModel, IFilterableViewModel
{
    private readonly LoggerDefinitionUserConfiguration _config;

    public string DefinitionCode => _config.DefinitionCode;

    public string[] DependendOn => _config.DependendOn ?? new string[0];

    public string A2lMeasurementName => _config.A2lMeasurementName;

    public string Name => _config.Name;

    bool IFilterableViewModel.Match(string text)
    {
        var option = Name?.ToLower() ?? string.Empty;
        var option2 = A2lMeasurementName?.ToLower() ?? string.Empty;
        var phrase = text?.ToLower() ?? string.Empty;

        return option.Contains(phrase) || option2.Contains(phrase);
    }

    public LoggerDefinitionUserConfigurationViewModel(LoggerDefinitionUserConfiguration config, bool presentA2lName, bool dualEcusAvailable)
        : base(config.Name)
    {
        _config = config;

        IsChecked = config.Selected;

        UpdateGaugeName(presentA2lName, dualEcusAvailable);
    }

    public void UpdateGaugeName(bool presentA2lName, bool dualEcusAvailable)
    {
        Option = _config.ToDisplayName(presentA2lName, dualEcusAvailable);
    }
}