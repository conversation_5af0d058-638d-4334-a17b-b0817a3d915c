using MgFlasher.Flasher.Services.CarLogger;
using MgFlasher.Flasher.Services.CarLogger.Gauges;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.Logger;

public class LoggerParametersPageViewModel : PageViewModel
{
    private readonly INavigationService _navigationService;
    private readonly ILoggerService _loggerService;
    private readonly IUserDialogs _userDialogs;
    private readonly ILogger<LoggerParametersPageViewModel> _logger;
    private LoggerParametersPreferencesViewModel _loggerParametersPreferences;
    private LoggerParametersModulesViewModel _loggerParametersModules;
    private int _selectedTabIndex;

    public int SelectedTabIndex
    {
        get => _selectedTabIndex;
        set
        {
            SetProperty(ref _selectedTabIndex, value);
            _loggerParametersModules.SelectedModuleIndex = _selectedTabIndex < 2 ? _selectedTabIndex : -1;
            _loggerParametersPreferences.Selected = _selectedTabIndex == 2;
            UpdateEnabledState();
        }
    }

    public ICommand LoggerDefaultsCommand { get; }
    public ICommand LoggerRemoveAllCommand { get; }
    public ICommand SubmitCommand { get; }

    public LoggerParametersPreferencesViewModel LoggerParametersPreferences
    {
        get => _loggerParametersPreferences;
        private set => SetProperty(ref _loggerParametersPreferences, value);
    }

    public LoggerParametersModulesViewModel LoggerParametersModules
    {
        get => _loggerParametersModules;
        private set => SetProperty(ref _loggerParametersModules, value);
    }

    public bool IsDefaultsEnabled
    {
        get
        {
            if (IsBusy)
            {
                return false;
            }

            if (LoggerParametersPreferences.Selected)
            {
                return true;
            }
            else if (LoggerParametersModules.Modules != null &&
                     LoggerParametersModules.SelectedModule != null &&
                     LoggerParametersModules.SelectedModule.IsModuleEnabled)
            {
                return true;
            }

            return false;
        }
    }

    public bool IsRemoveAllEnabled
    {
        get
        {
            if (IsBusy)
            {
                return false;
            }

            if (LoggerParametersModules.Modules != null &&
                LoggerParametersModules.SelectedModule != null &&
                LoggerParametersModules.SelectedModule.IsModuleEnabled &&
                LoggerParametersModules.SelectedModule.Items.Any(x => x.IsChecked))
            {
                return true;
            }

            return false;
        }
    }

    public LoggerParametersPageViewModel(
        INavigationService navigationService,
        ILoggerService loggerService,
        IUserDialogs userDialogs,
        LoggerParametersPreferencesViewModel loggerParametersPreferences,
        LoggerParametersModulesViewModel loggerParametersModules,
        ILogger<LoggerParametersPageViewModel> logger)
    {
        _navigationService = navigationService;
        _loggerService = loggerService;
        _userDialogs = userDialogs;
        _logger = logger;

        LoggerParametersPreferences = loggerParametersPreferences;
        LoggerParametersModules = loggerParametersModules;
        LoggerDefaultsCommand = new TraceableCommand(LoggerDefaults, nameof(LoggerDefaults));
        LoggerRemoveAllCommand = new TraceableCommand(LoggerRemoveAll, nameof(LoggerRemoveAll));
        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));
        PropertyChanged += (o, e) =>
        {
            if (e.PropertyName == nameof(IsBusy))
            {
                UpdateEnabledState();
            }
        };

        loggerParametersPreferences.PropertyChanged += OnLoggerParametersPreferencesViewModelChanged;
        loggerParametersModules.PropertyChanged += (o, e) =>
        {
            if (e.PropertyName == "IsChecked")
            {
                UpdateEnabledState();
            }
        };
    }

    public async void Submit()
    {
        LoggerParametersModules.LoggerPreferenceConfiguration = LoggerParametersPreferences.GetDraftConfig();

        if (!await LoggerParametersModules.SubmitAsync())
        {
            return;
        }

        var preferencesSubmitResult = await LoggerParametersPreferences.SubmitAsync();
        if (!preferencesSubmitResult.Valid)
        {
            return;
        }

        if (preferencesSubmitResult.ShouldRestartLogger)
        {
            await RestartLoggerAsync();
        }

        await _navigationService.NavigateToPreviousAsync(true);
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        await LoggerParametersPreferences.OnNavigatedToAsync();

        LoggerParametersModules.LoggerPreferenceConfiguration = LoggerParametersPreferences.GetDraftConfig();
        await LoggerParametersModules.OnNavigatedToAsync();

        UpdateEnabledState();

        await base.OnNavigatedToAsync(previousPage, arg);
    }

    private async void LoggerDefaults(object obj)
    {
        LoggerParametersPreferences.Defaults();
        await LoggerParametersModules.Defaults();
    }

    private void LoggerRemoveAll()
    {
        LoggerParametersModules.RemoveAll();
    }

    private void OnLoggerParametersPreferencesViewModelChanged(object sender, PropertyChangedEventArgs e)
    {
        var draftConfig = LoggerParametersPreferences.GetDraftConfig();
        if (draftConfig is null)
        {
            return;
        }

        LoggerParametersModules.LoggerPreferenceConfiguration = draftConfig;

        if (e.PropertyName == nameof(LoggerParametersPreferencesViewModel.SelectedLoggerPreference) ||
            e.PropertyName == nameof(LoggerParametersPreferencesViewModel.LogMasterModuleAsSlave))
        {
            LoggerParametersModules.UpdateModulesEnabledState();
        }
        else if (e.PropertyName == nameof(LoggerParametersPreferencesViewModel.PresentA2lMeasurementName))
        {
            LoggerParametersModules.UpdateGaugeNames();
        }
        else if (e.PropertyName == nameof(LoggerParametersPreferencesViewModel.IsBusy))
        {
            IsBusy = LoggerParametersPreferences.IsBusy;
        }

        UpdateEnabledState();
    }

    private async Task RestartLoggerAsync()
    {
        var semaphore = new SemaphoreSlim(0, 1);

        void OnGaugeDefinitionsUpdated(object sender, IReadOnlyList<GaugeLogDefinition> e)
        {
            semaphore.Release();
        }

        try
        {
            if (!_loggerService.IsLoggerStarted)
            {
                return;
            }

            using (_userDialogs.Loading(AppResources.LoggerParametersPage_RestartingLogger))
            {
                await _loggerService.StopAsync();
                await Task.Delay(2000);

                _loggerService.GaugeDefinitionsUpdated += OnGaugeDefinitionsUpdated;
                await _loggerService.StartAsync();
                await semaphore.WaitAsync(5000);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "RestartLoggerAsync error");
        }
        finally
        {
            _loggerService.GaugeDefinitionsUpdated -= OnGaugeDefinitionsUpdated;
        }
    }

    private void UpdateEnabledState()
    {
        OnPropertyChanged(nameof(IsDefaultsEnabled));
        OnPropertyChanged(nameof(IsRemoveAllEnabled));
    }
}