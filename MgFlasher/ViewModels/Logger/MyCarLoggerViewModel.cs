﻿using MgFlasher.Flasher.Services.CarLogger;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Flasher.Services.Cars.Connectivity;
using MgFlasher.Flasher.Services.FlashCarAllowance;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using MgFlasher.Views.Logger;
using MgFlasher.Views.Logger.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels.Logger;

/// <summary>
/// View Model for the <see cref="MyCarLoggerView" />.
/// </summary>
public class MyCarLoggerViewModel : BaseViewModel, IContextOptionsViewModel, ILoggerSleeper, ITabComponent
{
    private static readonly PageType[] SubPages =
    [
        PageType.LoggerUnits,
        PageType.LoggerParameters,
        PageType.LoggerDisplay,
        PageType.LoggerDisplayGaugeItem,
        PageType.LoggerReplaceGaugeItem,
        PageType.LoggerAlerts,
        PageType.LoggerAlertItem
    ];

    private readonly IEcuPermissionHandler _ecuPermissionHandler;
    private readonly ILoggerService _loggerService;
    private readonly IUserDialogs _userDialogs;
    private readonly ICarConnectivityStatusChecker _carConnectivityStatusChecker; // TODO Remove unused?
    private readonly ILoggerPreferenceService _loggerPreferenceService; // TODO Remove unused?
    private readonly ILogger<MyCarLoggerViewModel> _logger;
    private readonly ShellTitleViewModel _shellTitleViewModel;
    private bool _loggingToFile;
    private bool _loggerStarted;

    /// <summary>
    /// Gets or sets a value indicating whether Logger is saving logs to a file.
    /// </summary>
    public bool LoggingToFile
    {
        get => _loggingToFile;
        private set => SetProperty(ref _loggingToFile, value);
    }

    public bool LoggerStarted
    {
        get => _loggerStarted;
        private set
        {
            SetProperty(ref _loggerStarted, value);
            StartLogToFileCommand.ChangeCanExecute();
            StopLogToFileCommand.ChangeCanExecute();
        }
    }

    public bool IsGaugeModeSelected
    {
        get => _loggerService.IsGaugeModeSelected;
    }

    public bool IsListModeSelected
    {
        get => !_loggerService.IsGaugeModeSelected;
    }

    public LoggerGaugesDashboardViewModel GaugesDashboardViewModel { get; }

    /// <summary>
    /// A command for starting saving Logger's logs to a file.
    /// </summary>
    public TraceableCommand StartLogToFileCommand { get; }

    /// <summary>
    /// A command for stoping saving Logger's logs to a file.
    /// </summary>
    public TraceableCommand StopLogToFileCommand { get; }

    /// <summary>
    /// A command to start logging.
    /// </summary>
    public TraceableCommand TryStartLoggerCommand { get; }

    public TraceableCommand GoToLogsCommand { get; }
    public TraceableCommand MiscCommand { get; }

    public Dictionary<string, Action> ContextOptions => GaugesDashboardViewModel.ContextOptions;

    public MyCarLoggerViewModel(
        IEcuPermissionHandler ecuPermissionHandler,
        ILoggerService loggerService,
        IUserDialogs userDialogs,
        ICarConnectivityStatusChecker carConnectivityStatusChecker,
        ILoggerPreferenceService loggerPreferenceService,
        INavigationService navigationService,
        ILogger<MyCarLoggerViewModel> logger,
        LoggerGaugesDashboardViewModel gaugesDashboardViewModel,
        ShellTitleViewModel shellTitleViewModel)
    {
        GaugesDashboardViewModel = gaugesDashboardViewModel;
        GaugesDashboardViewModel.SwitchModeCommand = new TraceableCommand(SwitchMode, nameof(SwitchMode));

        StartLogToFileCommand = new TraceableCommand(StartLogToFile, x => LoggerStarted, nameof(StartLogToFile));
        StopLogToFileCommand = new TraceableCommand(StopLogToFile, x => LoggerStarted, nameof(StopLogToFile));
        TryStartLoggerCommand = new TraceableCommand(async () => await TryStartLoggerServiceFromUserRequest(), nameof(TryStartLoggerServiceFromUserRequest));
        GoToLogsCommand = new TraceableCommand(async () => await navigationService.NavigateToTabbedComponent<MyCarLoggerFilesViewModel>(PageType.MyCar), nameof(GoToLogsCommand));
        MiscCommand = new TraceableCommand(Misc, nameof(Misc));

        _ecuPermissionHandler = ecuPermissionHandler;
        _loggerService = loggerService;
        _userDialogs = userDialogs;
        _carConnectivityStatusChecker = carConnectivityStatusChecker;
        _loggerPreferenceService = loggerPreferenceService;
        _logger = logger;
        _shellTitleViewModel = shellTitleViewModel;
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        await GaugesDashboardViewModel.OnNavigatedToAsync(previousPage, arg);

        if (previousPage is null || !previousPage.Value.In(SubPages))
        {
            _loggerService.LoggingToFileChanged += OnLoggingToFileChanged;
            _loggerService.ConnectionLost += OnLoggerConnectionLost;
            _loggerService.VinMismatchDetected += OnVinMismatchDetected;
            _ecuPermissionHandler.AcquirerChanged += OnEcuAcquirerChanged;

            LoggerStarted = _loggerService.IsLoggerStarted;
        }
    }

    public async Task OnNavigatedFromAsync(PageType? nextPage, ITabComponent nextPart)
    {
        if (nextPage is null || !nextPage.Value.In(SubPages))
        {
            await TryStopLoggerServiceAsync();
        }
    }

    private async void OnEcuAcquirerChanged(object sender, EcuAcquirer e)
    {
        if (e.ItsMe)
        {
            return;
        }

        await TryStopLoggerServiceAsync();
        await _userDialogs.AlertAsync(string.Format(AppResources.Logger_Msg_EcuAcquiredBySomeoneElse, e.Message), okText: AppResources.Ok);
    }

    private void Misc()
    {
        _shellTitleViewModel.ContextOptionsCommand.Execute(null);
    }

    private void SwitchMode()
    {
        _loggerService.IsGaugeModeSelected = !_loggerService.IsGaugeModeSelected;
        OnPropertyChanged(nameof(IsGaugeModeSelected));
        OnPropertyChanged(nameof(IsListModeSelected));
    }

    /// <summary>
    /// A method that starts saving Logger's log to a file when user clicks a button to do so.
    /// </summary>
    /// <param name="obj"></param>
    private async void StartLogToFile(object obj)
    {
        if (_loggerService.IsLoggingToFile)
        {
            _logger.LogInformation("Logging to file already started");

            return;
        }

        // If it is not already started, then start.
        await _loggerService.StartLoggingToFileAsync();
        LoggingToFile = _loggerService.IsLoggingToFile;
    }

    /// <summary>
    /// A method that stops saving Logger's log to a file when user clicks a button to do so.
    /// </summary>
    /// <param name="obj"></param>
    private async void StopLogToFile(object obj)
    {
        if (!_loggerService.IsLoggingToFile)
        {
            _logger.LogInformation("Logging to file already stopped");

            return;
        }

        await _loggerService.StopLoggingToFileAsync();
        LoggingToFile = _loggerService.IsLoggingToFile;
    }

    private void OnLoggingToFileChanged(object sender, bool e)
    {
        Application.Current.Dispatcher.Dispatch(() =>
        {
            LoggingToFile = e;
        });
    }

    private void OnLoggerConnectionLost(object sender, EventArgs e)
    {
        Application.Current.Dispatcher.Dispatch(() =>
        {
            GaugesDashboardViewModel.ClearGaugeItems();

            LoggerStarted = _loggerService.IsLoggerStarted;
            LoggingToFile = _loggerService.IsLoggingToFile;
        });
    }

    private void OnVinMismatchDetected(object sender, EventArgs e)
    {
        Application.Current.Dispatcher.Dispatch(async () =>
        {
            await _userDialogs.AlertAsync(AppResources.Logger_Cannot_StartLog_VinMismatch, okText: AppResources.Ok);
        });
    }

    /// <summary>
    /// A method that tries to start logger after a user click a Start button.
    /// </summary>
    /// <returns></returns>
    private async Task TryStartLoggerServiceFromUserRequest()
    {
        try
        {
            using (var loading = _userDialogs.Loading(AppResources.LoggerPage_StartingLogger))
            {
                // Check if TEST ECU is free, if we are connecting to it.
                await _ecuPermissionHandler.AcquireAsync();
                // Start logging.
                await _loggerService.StartAsync();
            }
        }
        catch (EcuPermissionException ex)
        {
            _logger.LogError(ex, "Could not start logger - ecu acquirement failed");
            await _userDialogs.AlertAsync(string.Format(AppResources.Logger_Msg_EcuAcquiredBySomeoneElse, ex.Message), okText: AppResources.Ok);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Could not start logger");

            await _userDialogs.AlertAsync(AppResources.Logger_Msg_CommunicationError, okText: AppResources.Ok);
        }
        finally
        {
            LoggerStarted = _loggerService.IsLoggerStarted;
        }
    }

    /// <summary>
    /// A method that tries to stop the logger when navigating away, or another dev acquired access to the ECU.
    /// </summary>
    /// <returns></returns>
    public async Task TryStopLoggerServiceAsync()
    {
        try
        {
            _loggerService.LoggingToFileChanged -= OnLoggingToFileChanged;
            _loggerService.ConnectionLost -= OnLoggerConnectionLost;
            _loggerService.VinMismatchDetected -= OnVinMismatchDetected;
            _ecuPermissionHandler.AcquirerChanged -= OnEcuAcquirerChanged;

            GaugesDashboardViewModel.ClearGaugeItems();

            var timerTask = Task.Delay(TimeSpan.FromSeconds(3));
            var stopTask = _loggerService.StopAsync();

            await Task.WhenAny(timerTask, stopTask);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Could not stop logger");
        }
        finally
        {
            LoggerStarted = _loggerService.IsLoggerStarted;
            LoggingToFile = _loggerService.IsLoggingToFile;
        }
    }
}