﻿using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.Flasher.Services.CarLogger.Parser;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.Logger.Models;
using Microsoft.Maui.Devices.Sensors;

namespace MgFlasher.ViewModels.Logger;

public class LoggerFileMapViewModel : BaseViewModel, ITabComponent
{
    private ObservableCollection<ILoggerFileMapItemModel> _points = [];
    private LoggerFileContent _content;

    public IEnumerable<LoggerFileMapPointItemModel> Pins => MapItems.OfType<LoggerFileMapPointItemModel>();
    public IEnumerable<LoggerFileMapPolylineItemModel> Polylines => MapItems.OfType<LoggerFileMapPolylineItemModel>();

    public ObservableCollection<ILoggerFileMapItemModel> MapItems
    {
        get => _points;
        private set
        {
            SetProperty(ref _points, value);
            OnPropertyChanged(nameof(Pins));
            OnPropertyChanged(nameof(Polylines));
        }
    }

    public Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        _content = (LoggerFileContent)arg;
        InitializePoints();
        return Task.CompletedTask;
    }

    private void InitializePoints()
    {
        var latitudes = _content.Series.FirstOrDefault(x => x.Name == "Latitude")?.Values ?? [];
        var longitudes = _content.Series.FirstOrDefault(x => x.Name == "Longitude")?.Values ?? [];

        if (latitudes.Count == 0 || latitudes.Count != longitudes.Count)
        {
            MapItems?.Clear();
            return;
        }

        if (latitudes.Count == 1)
        {
            MapItems = new([new LoggerFileMapPointItemModel(new(latitudes[0], longitudes[0]), "")]);
            return;
        }

        MapItems = new ObservableCollection<ILoggerFileMapItemModel>
        {
            new LoggerFileMapPointItemModel(new Location(latitudes[0], longitudes[0]), "Start"),
            new LoggerFileMapPolylineItemModel(latitudes[1..^1].Zip(longitudes[1..^1], (lat, lon) => new Location(lat, lon))),
            new LoggerFileMapPointItemModel(new Location(latitudes[^1], longitudes[^1]), "End")
        };
    }
}