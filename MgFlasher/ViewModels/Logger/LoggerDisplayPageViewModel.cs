﻿using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User.Storage;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Views.Logger.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Maui;
using Syncfusion.Maui.DataSource.Extensions;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.Logger;

public class LoggerDisplayPageViewModel : PageViewModel
{
    private readonly IUserDialogs _userDialogs;
    private readonly INavigationService _navigationService;
    private GaugeColor _selectedColor;

    public LoggerDisplayGaugesViewModel Gauges { get; }
    public LoggerDisplayPreferencesViewModel Preferences { get; }

    public ICommand MiscCommand { get; }
    public ICommand DefaultsCommand { get; }
    public ICommand SubmitCommand { get; }

    public bool IsRedSelected
    {
        get
        {
            return _selectedColor is GaugeColor.Red;
        }
        set
        {
            if (value)
            {
                _selectedColor = GaugeColor.Red;
                OnPropertyChanged(nameof(IsRedSelected));
            }
        }
    }

    public bool IsGreySelected
    {
        get
        {
            return _selectedColor is GaugeColor.Grey;
        }
        set
        {
            if (value)
            {
                _selectedColor = GaugeColor.Grey;
                OnPropertyChanged(nameof(IsGreySelected));
            }
        }
    }

    public bool IsBlueSelected
    {
        get
        {
            return _selectedColor is GaugeColor.Blue;
        }
        set
        {
            if (value)
            {
                _selectedColor = GaugeColor.Blue;
                OnPropertyChanged(nameof(IsBlueSelected));
            }
        }
    }

    public LoggerDisplayPageViewModel(
        LoggerDisplayGaugesViewModel gauges,
        LoggerDisplayPreferencesViewModel preferences,
        IUserDialogs userDialogs,
        INavigationService navigationService)
    {
        _userDialogs = userDialogs;
        _navigationService = navigationService;
        Gauges = gauges;
        Preferences = preferences;

        DefaultsCommand = new TraceableCommand(Defaults, nameof(Defaults));
        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));
        MiscCommand = new TraceableCommand(Misc, nameof(Misc));
    }

    private async void Misc(object obj)
    {
        var commands = new Dictionary<string, Action>
        {
            [AppResources.LoggerDisplayPage_Misc_SetAllToCircularType] = () => Gauges.SetAllToType(GaugePresentationType.Circular),
            [AppResources.LoggerDisplayPage_Misc_SetAllToRectangleType] = () => Gauges.SetAllToType(GaugePresentationType.Rectangle),
        };

        if (Gauges.Items.Any(x => !x.Visible))
        {
            commands.Add(AppResources.LoggerDisplayPage_Misc_MarkAllAsVisible, () => Gauges.Items.ForEach(x => x.Visible = true));
        }

        if (Gauges.Items.Any(x => x.Visible))
        {
            commands.Add(AppResources.LoggerDisplayPage_Misc_MarkAllAsHidden, () => Gauges.Items.ForEach(x => x.Visible = false));
        }

        commands.Add(
            string.Format(AppResources.LoggerDisplayPage_Misc_AutoAdjustToGrid, GridAutoLayoutPositionerPlatformSettings.GaugeCountLimit),
            Gauges.AutoAdjustToGrid);

        var result = await _userDialogs.ActionSheetAsync(null, AppResources.Cancel, null, null, commands.Keys.ToArray());
        if (commands.ContainsKey(result ?? ""))
        {
            commands[result].Invoke();
        }
    }

    private void Defaults(object obj)
    {
        Gauges.Defaults();
        Preferences.Defaults();
        _selectedColor = GaugeColor.Grey;
    }

    private async void Submit(object obj)
    {
        using var loading = _userDialogs.Loading(AppResources.Saving);
        Gauges.Items.ForEach(x => x.UpdateColor(_selectedColor));

        if (!await Gauges.SubmitAsync())
        {
            return;
        }

        await Preferences.SubmitAsync();
        await _navigationService.NavigateToPreviousAsync(true);
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        await Gauges.OnNavigatedToAsync(previousPage, arg);
        await Preferences.OnNavigatedToAsync(previousPage, arg);

        _selectedColor = Gauges.Items.FirstOrDefault()?.GaugeColor ?? GaugeColor.Grey;

        OnPropertyChanged(nameof(IsBlueSelected));
        OnPropertyChanged(nameof(IsRedSelected));
        OnPropertyChanged(nameof(IsGreySelected));
    }
}