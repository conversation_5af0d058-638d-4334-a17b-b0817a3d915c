﻿using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User.Storage;
using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using System.Windows.Input;

namespace MgFlasher.ViewModels.Logger;

public class GaugeLogDefinitionConfigurationViewModel : BaseViewModel, IShapeHolderViewModel, IFilterableViewModel
{
    private readonly INavigationService _navigationService;
    private bool _presentA2lName;
    private bool _dualEcusAvailable;
    private GaugeUserInterfaceConfig _config;
    private PreferenceTypeViewModel _presentationType;
    private GaugeLogShape _shape;
    private bool _visible;

    public string ModuleName => _config.ModuleId.ToModuleName();
    public ModuleId ModuleId => _config.ModuleId;
    public string DefinitionCode => _config.DefinitionCode;
    public string Name => _config.ToDisplayName(_presentA2lName, _dualEcusAvailable);

    public PreferenceTypeViewModel PresentationType
    {
        get => _presentationType;
        private set => SetProperty(ref _presentationType, value);
    }

    public GaugeLogShape Shape
    {
        get => _shape;
        set => SetProperty(ref _shape, value);
    }

    public bool Visible
    {
        get => _visible;
        set => SetProperty(ref _visible, value);
    }

    public GaugeColor GaugeColor { get; set; }

    public ICommand GoToGaugeSettingsCommand { get; }
    public ICommand ChangePresentationTypeStateCommand { get; }
    public ICommand ChangeVisibleStateCommand { get; }

    string IFilterableViewModel.DisplayName => Name;

    public GaugeLogDefinitionConfigurationViewModel(
        INavigationService navigationService,
        GaugeUserInterfaceConfig config,
        bool presentA2lName,
        bool dualEcusAvailable)
    {
        _presentA2lName = presentA2lName;
        _dualEcusAvailable = dualEcusAvailable;
        _navigationService = navigationService;

        GoToGaugeSettingsCommand = new TraceableCommand(GoToGaugeSettings, nameof(GoToGaugeSettings));
        ChangePresentationTypeStateCommand = new TraceableCommand(() =>
        {
            if ((GaugePresentationType)PresentationType.PreferenceType == GaugePresentationType.Circular)
            {
                Update(GaugePresentationType.Rectangle);
            }
            else
            {
                Update(GaugePresentationType.Circular);
            }
        }, "ChangePresentationTypeState");
        ChangeVisibleStateCommand = new TraceableCommand(() => _config.Visible = Visible = !Visible, "ChangeVisibleState");

        Update(config);
    }

    public GaugeUserInterfaceConfig GetFinalConfiguration()
    {
        var config = _config.Clone();

        config.PresentationType = (GaugePresentationType)PresentationType.PreferenceType;
        config.Shape = Shape?.Clone();
        config.Visible = Visible;

        return config;
    }

    public void Update(GaugeUserInterfaceConfig config)
    {
        _config = config;

        PresentationType = new PreferenceTypeViewModel(_config.PresentationType);
        Shape = _config.Shape?.Clone();
        Visible = _config.Visible;
        GaugeColor = _config.GaugeColor;
        OnPropertyChanged(nameof(Name));
    }

    public void Update(GaugePresentationType type)
    {
        _config.PresentationType = type;

        PresentationType = new PreferenceTypeViewModel(_config.PresentationType);
    }

    public void Update(GaugeLogShape shape)
    {
        _config.Shape = shape?.Clone();

        Shape = shape;
    }

    public void UpdateColor(GaugeColor color)
    {
        _config.GaugeColor = color;
    }

    public void UpdateGaugeName(bool presentA2lName)
    {
        _presentA2lName = presentA2lName;

        OnPropertyChanged(nameof(Name));
    }

    private async void GoToGaugeSettings(object obj)
    {
        await _navigationService.NavigateAsync(PageType.LoggerDisplayGaugeItem, _config);
    }
}