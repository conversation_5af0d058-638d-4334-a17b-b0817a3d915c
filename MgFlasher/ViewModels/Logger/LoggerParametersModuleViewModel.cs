﻿using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Ecu.Logger.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Flasher.Services.CustomCode;
using MgFlasher.Flasher.Services.DevMode;
using MgFlasher.Flasher.Services.Models;
using Syncfusion.Maui.DataSource.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels.Logger;

public class LoggerParametersModuleViewModel : BaseViewModel
{
    private readonly ILoggerDefinitionsUserConfigurationService _userConfig;
    private bool _isModuleEnabled;
    private ObservableCollection<LoggerDefinitionUserConfigurationViewModel> _items;

    public event EventHandler CheckedChanged;

    public ModuleId ModuleId { get; }

    public bool IsModuleEnabled
    {
        get => _isModuleEnabled;
        set => SetProperty(ref _isModuleEnabled, value);
    }

    public ObservableCollection<LoggerDefinitionUserConfigurationViewModel> Items
    {
        get => _items;
        private set => SetProperty(ref _items, value);
    }

    private LoggerParametersModuleViewModel(
        bool isModuleEnabled, ModuleId moduleId,
        IEnumerable<LoggerDefinitionUserConfigurationViewModel> vms,
        ILoggerDefinitionsUserConfigurationService userConfig)
    {
        _userConfig = userConfig;

        Items = new ObservableCollection<LoggerDefinitionUserConfigurationViewModel>(vms);
        IsModuleEnabled = isModuleEnabled;
        ModuleId = moduleId;
        Items.ForEach(x => x.PropertyChanged += OnLoggerDefinitionUserConfigurationPropertyChanged);
    }

    public async Task Defaults(CarLoggerPreferenceConfiguration loggerPreferenceConfiguration, Car car)
    {
        //take defaults from master if master module is not enabled but slave is enabled
        var sourceModuleId = ModuleId;
        if (sourceModuleId == ModuleId.DME_SLAVE)
        {
            sourceModuleId = loggerPreferenceConfiguration.ShouldLogMasterModule() ?
                ModuleId.DME_SLAVE : ModuleId.DME_MASTER;
        }

        var defaults = await _userConfig.GetDefaultAsync(sourceModuleId, ModuleId, car.EngineCode, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(car.ProcessorArchitectureType));
        var vms = defaults
            .Select(x => new LoggerDefinitionUserConfigurationViewModel(
                x, loggerPreferenceConfiguration.PresentA2lMeasurementName, loggerPreferenceConfiguration.DualEcusAvailable));

        Items = new ObservableCollection<LoggerDefinitionUserConfigurationViewModel>(vms);
        CheckedChanged?.Invoke(this, EventArgs.Empty);
    }

    public void RemoveAll()
    {
        foreach (var vm in Items)
        {
            vm.IsChecked = false;
        }
        CheckedChanged?.Invoke(this, EventArgs.Empty);
    }

    private void OnLoggerDefinitionUserConfigurationPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        var currentVm = sender as LoggerDefinitionUserConfigurationViewModel;

        if (e.PropertyName == nameof(LoggerDefinitionUserConfigurationViewModel.IsChecked))
        {
            var dependendVms = Items.Where(x => x.DependendOn.Contains(currentVm.DefinitionCode));
            foreach (var dependendVm in dependendVms)
            {
                dependendVm.IsChecked = Items
                    .Where(x => dependendVm.DependendOn.Contains(x.DefinitionCode))
                    .All(x => x.IsChecked);
            }

            if (currentVm.IsChecked)
            {
                var parentVms = Items.Where(x => currentVm.DependendOn.Contains(x.DefinitionCode));
                foreach (var parentVm in parentVms)
                {
                    parentVm.IsChecked = true;
                }
            }

            CheckedChanged?.Invoke(this, EventArgs.Empty);
        }
    }

    public async static Task<LoggerParametersModuleViewModel> CreateMasterModuleViewModelAsync(CarLoggerPreferenceConfiguration config, Car car,
        ILoggerDefinitionsUserConfigurationService userConfig)
    {
        var masterEnabled = config.ShouldLogMasterModule();
        var masterDefinitions = await userConfig.GetAsync(ModuleId.DME_MASTER, car.VIN, car.EngineCode, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(car.ProcessorArchitectureType));
        var masterVms = masterDefinitions
            .Select(x => new LoggerDefinitionUserConfigurationViewModel(x, config.PresentA2lMeasurementName, config.DualEcusAvailable));
        return new LoggerParametersModuleViewModel(masterEnabled, ModuleId.DME_MASTER, masterVms, userConfig);
    }

    public async static Task<LoggerParametersModuleViewModel> CreateSlaveModuleViewModelAsync(CarLoggerPreferenceConfiguration config, Car car,
        IDevModeAccessor devModeAccessor, ILoggerDefinitionsUserConfigurationService userConfig)
    {
        var slaveEnabled = config.ShouldLogSlaveModule(car) || config.ShouldDevLogSlaveModule(devModeAccessor);
        var slaveDefinitions = await userConfig.GetAsync(ModuleId.DME_SLAVE, car.VIN, car.EngineCode, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(car.ProcessorArchitectureType));
        var slaveVms = slaveDefinitions
            .Select(x => new LoggerDefinitionUserConfigurationViewModel(x, config.PresentA2lMeasurementName, config.DualEcusAvailable));
        return new LoggerParametersModuleViewModel(slaveEnabled, ModuleId.DME_SLAVE, slaveVms, userConfig);
    }
}