﻿using System.Windows.Input;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.Logger;

public class LoggerFileItemViewModel : OptionalItemViewModel
{
    private readonly INavigationService _navigationService;
    public ICommand GoToDetailsCommand { get; }

    public LoggerFileItemViewModel(string fileName, INavigationService navigationService) : base(fileName)
    {
        _navigationService = navigationService;
        GoToDetailsCommand = new TraceableCommand(GoToDetails, nameof(GoToDetails));
    }

    private async void GoToDetails()
    {
        await _navigationService.NavigateToTabbedComponent<LoggerFileGeneralViewModel>(PageType.LoggerFile, Option);
    }
}