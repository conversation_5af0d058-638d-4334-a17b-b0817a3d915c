﻿using MgFlasher.Ecu.Logger.Configuration.Templates.Files;
using MgFlasher.Flasher.Services;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.AppFiles;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.DevMode;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Localization.Resources;
using MgFlasher.ViewModels.Logger.Models;
using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Storage;

namespace MgFlasher.ViewModels.Logger;

public class LoggerParametersPreferencesViewModel : BaseViewModel
{
    private readonly IDevModeService _devModeService;
    private readonly ILoggerPreferenceService _loggerPreferenceService;
    private readonly ILoggerTemplateUserFileProvider _loggerTemplateUserFileProvider;
    private readonly IAppContext _appContext;
    private readonly IUserDialogs _userDialogs;
    private readonly ITemplateFileRepository _templateFileRepository;
    private readonly IShareService _shareService;
    private readonly IDCANService _dCANService;
    private readonly ILogger<LoggerParametersPreferencesViewModel> _logger;
    private Car _connectedCar;
    private CarLoggerPreferenceConfiguration _loggerPreferences;
    private ObservableCollection<PreferenceTypeViewModel> _moduleLoggingPreferenceTypes;
    private PreferenceTypeViewModel _selectedLoggerPreference;
    private bool _uploadLoggerVariablesVisible;
    private bool _clearCustomLoggerVariablesVisible;
    private bool _logMasterModuleAsSlaveVisible;
    private bool _fakeValueLoggingVisible;
    private bool _a2lNamesVisible;
    private bool _moduleLoggingPreferenceSelectionVisible;
    private bool _logEcuCommunicationVisible;
    private bool _presentA2lMeasurementName;
    private bool _presentHexValues;
    private bool _loggerFileBufferSizeVisible;
    private bool _isBusy;
    private bool _selected;
    private bool _fakeValueLogging, _logEcuCommunication, _trackCarLocation;
    private int _loggerFileBufferSize;

    public ObservableCollection<PreferenceTypeViewModel> ModuleLoggingPreferenceTypes
    {
        get => _moduleLoggingPreferenceTypes;
        private set => SetProperty(ref _moduleLoggingPreferenceTypes, value);
    }

    public PreferenceTypeViewModel SelectedLoggerPreference
    {
        get => _selectedLoggerPreference;
        set => SetProperty(ref _selectedLoggerPreference, value);
    }

    public bool PresentA2lMeasurementName
    {
        get => _presentA2lMeasurementName;
        set => SetProperty(ref _presentA2lMeasurementName, value);
    }

    public bool PresentHexValues
    {
        get => _presentHexValues;
        set => SetProperty(ref _presentHexValues, value);
    }

    public bool LogMasterModuleAsSlave
    {
        get => _devModeService.ShouldLogMasterAsSlave();
        set
        {
            _devModeService.SetMasterAsSlaveLogging(value);
            SelectedLoggerPreference = ModuleLoggingPreferenceTypes
                .First(x => (ModuleLoggingPreferenceType)x.PreferenceType == ModuleLoggingPreferenceType.MasterOnly);
            ModuleLoggingPreferenceSelectionVisible = _loggerPreferences?.CanSelectModuleLoggingPreferences(_connectedCar, _devModeService) ?? false;

            OnPropertyChanged(nameof(LogMasterModuleAsSlave));
        }
    }

    public bool FakeValueLogging
    {
        get => _fakeValueLogging;
        set => SetProperty(ref _fakeValueLogging, value);
    }

    public int LoggerFileBufferSize
    {
        get => _loggerFileBufferSize;
        set => SetProperty(ref _loggerFileBufferSize, value);
    }

    public bool LogEcuCommunication
    {
        get => _logEcuCommunication;
        set => SetProperty(ref _logEcuCommunication, value);
    }

    public bool ModuleLoggingPreferenceSelectionVisible
    {
        get => _moduleLoggingPreferenceSelectionVisible;
        private set => SetProperty(ref _moduleLoggingPreferenceSelectionVisible, value);
    }

    public bool FakeValueLoggingVisible
    {
        get => _fakeValueLoggingVisible;
        private set => SetProperty(ref _fakeValueLoggingVisible, value);
    }

    public bool A2LNamesVisible
    {
        get => _a2lNamesVisible;
        private set => SetProperty(ref _a2lNamesVisible, value);
    }

    public bool LogMasterModuleAsSlaveVisible
    {
        get => _logMasterModuleAsSlaveVisible;
        private set => SetProperty(ref _logMasterModuleAsSlaveVisible, value);
    }

    public bool UploadLoggerVariablesVisible
    {
        get => _uploadLoggerVariablesVisible;
        private set => SetProperty(ref _uploadLoggerVariablesVisible, value);
    }

    public bool ClearCustomLoggerVariablesVisible
    {
        get => _clearCustomLoggerVariablesVisible;
        private set => SetProperty(ref _clearCustomLoggerVariablesVisible, value);
    }

    public bool LogEcuCommunicationVisible
    {
        get => _logEcuCommunicationVisible;
        private set => SetProperty(ref _logEcuCommunicationVisible, value);
    }

    public bool LoggerFileBufferSizeVisible
    {
        get => _loggerFileBufferSizeVisible;
        private set => SetProperty(ref _loggerFileBufferSizeVisible, value);
    }

    public bool TrackCarLocation
    {
        get => _trackCarLocation;
        set => SetProperty(ref _trackCarLocation, value);
    }

    public bool IsBusy
    {
        get => _isBusy;
        private set => SetProperty(ref _isBusy, value);
    }

    public ICommand UploadLoggerVariablesCommand { get; }
    public ICommand ClearCustomLoggerVariablesCommand { get; }
    public ICommand ShareCustomLoggerVariablesCommand { get; }
    public ICommand ShowDualEcuLoggingInfoCommand { get; }

    public bool Selected
    {
        get => _selected;
        set => SetProperty(ref _selected, value);
    }

    public LoggerParametersPreferencesViewModel(
        IDevModeService devModeService,
        ILoggerPreferenceService loggerPreferenceService,
        ILoggerTemplateUserFileProvider loggerTemplateUserFileProvider,
        IAppContext appContext,
        IUserDialogs userDialogs,
        ITemplateFileRepository templateFileRepository,
        IShareService shareService,
        IDCANService dCANService,
        ILogger<LoggerParametersPreferencesViewModel> logger)
    {
        _devModeService = devModeService;
        _loggerPreferenceService = loggerPreferenceService;
        _loggerTemplateUserFileProvider = loggerTemplateUserFileProvider;
        _appContext = appContext;
        _userDialogs = userDialogs;
        _templateFileRepository = templateFileRepository;
        _shareService = shareService;
        _dCANService = dCANService;
        _logger = logger;
        ModuleLoggingPreferenceTypes = new ObservableCollection<PreferenceTypeViewModel>()
        {
            new(ModuleLoggingPreferenceType.Both),
            new(ModuleLoggingPreferenceType.MasterOnly),
            new(ModuleLoggingPreferenceType.SlaveOnly),
        };

        UploadLoggerVariablesCommand = new TraceableCommand(UploadLoggerVariables, nameof(UploadLoggerVariables));
        ShowDualEcuLoggingInfoCommand = new TraceableCommand(ShowDualEcuLoggingInfo, nameof(ShowDualEcuLoggingInfo));
        ClearCustomLoggerVariablesCommand = new TraceableCommand(ClearCustomLoggerVariables, nameof(ClearCustomLoggerVariables));
        ShareCustomLoggerVariablesCommand = new TraceableCommand(ShareCustomLoggerVariables, nameof(ShareCustomLoggerVariables));
    }

    public void Defaults()
    {
        if (!Selected)
        {
            return;
        }

        SelectedLoggerPreference = ModuleLoggingPreferenceTypes.First(x => (ModuleLoggingPreferenceType)x.PreferenceType == ModuleLoggingPreferenceType.MasterOnly);
        PresentA2lMeasurementName = false;
        PresentHexValues = false;
        LoggerFileBufferSize = 10000;
        FakeValueLogging = false;
        LogEcuCommunication = false;
        TrackCarLocation = true;
    }

    public CarLoggerPreferenceConfiguration GetDraftConfig()
    {
        if (_loggerPreferences is null)
        {
            return null;
        }

        var cloned = _loggerPreferences.Clone();

        var selectedLoggerPreference = ((ModuleLoggingPreferenceType?)SelectedLoggerPreference?.PreferenceType) ?? ModuleLoggingPreferenceType.MasterOnly;
        cloned.ModuleLoggingPreference = selectedLoggerPreference;
        cloned.PresentA2lMeasurementName = PresentA2lMeasurementName;
        cloned.PresentHexValues = PresentHexValues;
        cloned.TrackCarLocation = TrackCarLocation;

        return cloned;
    }

    public async Task<LoggerParametersPreferencesSubmitResult> SubmitAsync()
    {
        var carLoggerPref = await _loggerPreferenceService.GetCarConfiguration();

        var selectedLoggerPreference = (ModuleLoggingPreferenceType)SelectedLoggerPreference.PreferenceType;
        var shouldRestartLogger = selectedLoggerPreference != carLoggerPref.ModuleLoggingPreference;

        carLoggerPref.ModuleLoggingPreference = selectedLoggerPreference;
        carLoggerPref.PresentA2lMeasurementName = PresentA2lMeasurementName;
        carLoggerPref.PresentHexValues = PresentHexValues;
        carLoggerPref.TrackCarLocation = TrackCarLocation;

        await _loggerPreferenceService.ApplyConfiguration(carLoggerPref);

        _devModeService.SetFakeLogging(FakeValueLogging);
        _devModeService.SetLoggerFileBufferSize(LoggerFileBufferSize);
        _devModeService.SetLogEcuCommunication(LogEcuCommunication);

        return new LoggerParametersPreferencesSubmitResult(true, shouldRestartLogger);
    }

    private async void UploadLoggerVariables(object obj)
    {
        try
        {
            IsBusy = true;

            var options = new PickOptions
            {
                PickerTitle = "Select a xlsx file to import"
            };

            var fileResult = await FilePicker.PickAsync(options).ConfigureAwait(false);

            if (fileResult?.FileName is null)
            {
                return;
            }

            if (!fileResult.FileName.EndsWith(".xlsx"))
            {
                throw new Exception("File must be of type xlsx");
            }

            await _loggerTemplateUserFileProvider.TrySetAsync(fileResult.FullPath).ConfigureAwait(false);
            await _templateFileRepository.UpdateFromFileProvidersAsync().ConfigureAwait(false);
            await Task.Delay(500).ConfigureAwait(false);

            Application.Current.Dispatcher.Dispatch(async () => await _userDialogs.AlertAsync(AppResources.Logger_Upload_XLSX_Success, okText: AppResources.Ok));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "UploadLoggerVariables error");
            Application.Current.Dispatcher.Dispatch(async () => await _userDialogs.AlertAsync(ex.Message, title: AppResources.Logger_Upload_Failed, AppResources.Ok));
        }
        finally
        {
            await SetLoggerCustomFileButtonStateAsync().ConfigureAwait(false);

            IsBusy = false;
        }
    }

    private async void ShareCustomLoggerVariables(object obj)
    {
        var path = await _loggerTemplateUserFileProvider.GetFilePath();

        await _shareService.Share(Path.GetFileName(path), path);
    }

    private async void ClearCustomLoggerVariables(object obj)
    {
        try
        {
            IsBusy = true;

            using (var loading = _userDialogs.Loading(AppResources.Loading))
            {
                await _loggerTemplateUserFileProvider.TryRemoveAsync();
                await _templateFileRepository.UpdateFromFileProvidersAsync();
            }

            await SetLoggerCustomFileButtonStateAsync();
        }
        finally
        {
            IsBusy = false;
        }
    }

    public async Task OnNavigatedToAsync()
    {
        _connectedCar = await _dCANService.GetConnectedCar();
        _loggerPreferences = await _loggerPreferenceService.GetCarConfiguration();

        LogMasterModuleAsSlaveVisible = _devModeService.IsDevMode();
        FakeValueLoggingVisible = _appContext.IsDeveloper();
        A2LNamesVisible = _appContext.IsDeveloper() || _appContext.IsDealer() || _appContext.IsTuner();
        LogEcuCommunicationVisible = _appContext.CanLogEcuFrames();
        LoggerFileBufferSizeVisible = _appContext.CanAdjustLoggerFileBufferSize();

        PresentA2lMeasurementName = A2LNamesVisible && _loggerPreferences.PresentA2lMeasurementName;
        PresentHexValues = _loggerPreferences.PresentHexValues;
        TrackCarLocation = _loggerPreferences.TrackCarLocation;

        FakeValueLogging = FakeValueLoggingVisible && _devModeService.IsFakeLogging();
        LoggerFileBufferSize = _devModeService.GetLoggerFileBufferSize();
        LogEcuCommunication = _devModeService.ShouldLogEcuCommunication();

        await SetLoggerCustomFileButtonStateAsync();
        InitLoggerPreferenceSelection();
    }

    private async Task SetLoggerCustomFileButtonStateAsync()
    {
        UploadLoggerVariablesVisible =
            _appContext.AllowToUploadCustomLoggerVariablesFile() &&
            !await _loggerTemplateUserFileProvider.ExistsAsync();
        ClearCustomLoggerVariablesVisible = await _loggerTemplateUserFileProvider.ExistsAsync();
    }

    private void InitLoggerPreferenceSelection()
    {
        if (_connectedCar == null || _loggerPreferences is null)
        {
            return;
        }

        ModuleLoggingPreferenceSelectionVisible = _loggerPreferences.CanSelectModuleLoggingPreferences(_connectedCar, _devModeService);
        SelectedLoggerPreference = ModuleLoggingPreferenceTypes
            .FirstOrDefault(x => ((ModuleLoggingPreferenceType)x.PreferenceType) == _loggerPreferences.ModuleLoggingPreference);
    }

    private async void ShowDualEcuLoggingInfo(object obj)
    {
        await _userDialogs.AlertAsync(AppResources.DualEcuLogging_Info, null, null);
    }
}