﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MgFlasher.Flasher.Services.CarLogger;
using MgFlasher.Flasher.Services.CarLogger.Parser;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.Logger;

public class LoggerFilePageViewModel : PageViewModel, ITabsComponent, IContextOptionsViewModel
{
    private readonly IUserDialogs _userDialogs;
    private readonly ILoggerFileService _loggerFileService;
    private readonly IDCANService _dcanService;
    private readonly ILoggerFileContentParser _loggerFileContentParser;
    private Car _connectedCar;

    private int _selectedTabIndex;
    private ITabComponent _currentTab;
    private PageType? _previousPage;

    private string _fileName;
    private LoggerFileContent _content;

    public IList<ITabComponent> Tabs => new List<ITabComponent> { General, Chart, Map };
    public LoggerFileGeneralViewModel General { get; }
    public LoggerFileChartViewModel Chart { get; }
    public LoggerFileMapViewModel Map { get; }

    public int SelectedTabIndex
    {
        get => _selectedTabIndex;
        set => SetProperty(ref _selectedTabIndex, value);
    }

    public Dictionary<string, Action> ContextOptions => _currentTab is IContextOptionsViewModel vm ? vm.ContextOptions : new Dictionary<string, Action>();

    public LoggerFilePageViewModel(
        IUserDialogs userDialogs,
        ILoggerFileService loggerFileService,
        IDCANService dcanService,
        ILoggerFileContentParser loggerFileContentParser,
        LoggerFileGeneralViewModel general,
        LoggerFileChartViewModel chart,
        LoggerFileMapViewModel map)
    {
        _userDialogs = userDialogs;
        _loggerFileService = loggerFileService;
        _dcanService = dcanService;
        _loggerFileContentParser = loggerFileContentParser;
        General = general;
        Chart = chart;
        Map = map;
        _currentTab = General;
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _previousPage = previousPage;
        _connectedCar = await _dcanService.GetConnectedCar();

        if (arg is string filename && filename != _fileName)
        {
            _fileName = filename;
            _content = await _loggerFileContentParser.ParseAsync(_connectedCar.VIN, _fileName);
        }

        await _currentTab.OnNavigatedToAsync(_previousPage, null, _content);
        OnPropertyChanged(nameof(ContextOptions));
    }

    public async Task OnNavigatedToTabAsync(ITabComponent next)
    {
        if (_currentTab == next)
            return;
        var old = _currentTab;
        if (old is not null)
            await _currentTab.OnNavigatedFromAsync(null, next);
        _currentTab = next;
        await next.OnNavigatedToAsync(null, old, _content);
        OnPropertyChanged(nameof(ContextOptions));
    }

    public (bool CanSelect, Func<Task> canSelectUserAction) CanSelect(ITabComponent tab) => tab switch
    {
        LoggerFileChartViewModel when string.IsNullOrEmpty(_content.Attributes?.MgFlasherLogsCloudUrl) => (false, PleaseUploadToLogsCloudFirst),
        _ => (true, null)
    };

    private async Task PleaseUploadToLogsCloudFirst()
    {
        await _userDialogs.AlertAsync(AppResources.LoggerFilePageViewModel_PleaseUploadToLogsCloudFirst, AppResources.MG_Flasher, AppResources.Ok);
        SelectedTabIndex = 0;
    }
}