﻿using System.Collections.Generic;
using MgFlasher.Ecu.Logger.Configuration.User.Storage;

namespace MgFlasher.ViewModels.Logger;

public class LoggerUnitUserConfigurationOptionalItemViewModel : OptionalItemViewModel
{
    private readonly LoggerUnitUserConfiguration _configuration;

    public string SourceGroup => _configuration.SourceGroup;
    public IReadOnlyList<string> AvailableUnits { get; }

    public LoggerUnitUserConfigurationOptionalItemViewModel(
        LoggerUnitUserConfiguration configuration,
        IReadOnlyList<string> availableUnits) : base(configuration.DestinationUnit)
    {
        _configuration = configuration;
        AvailableUnits = availableUnits;
    }
}