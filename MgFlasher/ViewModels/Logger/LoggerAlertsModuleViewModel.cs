﻿using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Ecu.Logger.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Flasher.Services.DevMode;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.Logger.Models;
using Syncfusion.Maui.DataSource.Extensions;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels.Logger;

public class LoggerAlertsModuleViewModel : BaseViewModel
{
    private readonly INavigationService _navigationService;
    private readonly CarLoggerPreferenceConfiguration _loggerPreferenceConfig;
    private bool _isModuleEnabled;
    private ObservableCollection<LoggerAlertDefinitionConfigurationViewModel> _items;

    public ModuleId ModuleId { get; }

    public bool IsModuleEnabled
    {
        get => _isModuleEnabled;
        set => SetProperty(ref _isModuleEnabled, value);
    }

    public ObservableCollection<LoggerAlertDefinitionConfigurationViewModel> Items
    {
        get => _items;
        private set => SetProperty(ref _items, value);
    }

    private LoggerAlertsModuleViewModel(
        bool isModuleEnabled, ModuleId moduleId,
        IEnumerable<LoggerAlertDefinitionConfigurationViewModel> vms,
        INavigationService navigationService, CarLoggerPreferenceConfiguration loggerPreferenceConfig)
    {
        Items = new ObservableCollection<LoggerAlertDefinitionConfigurationViewModel>(vms);
        IsModuleEnabled = isModuleEnabled;
        ModuleId = moduleId;
        _navigationService = navigationService;
        _loggerPreferenceConfig = loggerPreferenceConfig;
    }

    public async static Task<LoggerAlertsModuleViewModel> CreateMasterModuleViewModelAsync(CarLoggerPreferenceConfiguration config, Car car,
        ILoggerAlertsUserConfigurationService userConfig, INavigationService navigationService)
    {
        var masterEnabled = config.ShouldLogMasterModule();
        var masterDefinitions = (await userConfig.GetAsync(car.VIN)).Where(x => x.ModuleId == ModuleId.DME_MASTER).ToList();
        var masterVms = masterDefinitions
            .Select(x => new LoggerAlertDefinitionConfigurationViewModel(navigationService, x, config.PresentA2lMeasurementName, config.DualEcusAvailable));
        return new LoggerAlertsModuleViewModel(masterEnabled, ModuleId.DME_MASTER, masterVms, navigationService, config);
    }

    public async static Task<LoggerAlertsModuleViewModel> CreateSlaveModuleViewModelAsync(CarLoggerPreferenceConfiguration config, Car car,
        IDevModeAccessor devModeAccessor, ILoggerAlertsUserConfigurationService userConfig, INavigationService navigationService)
    {
        var slaveEnabled = config.ShouldLogSlaveModule(car) || config.ShouldDevLogSlaveModule(devModeAccessor);
        var slaveDefinitions = (await userConfig.GetAsync(car.VIN)).Where(x => x.ModuleId == ModuleId.DME_SLAVE).ToList();
        var slaveVms = slaveDefinitions
            .Select(x => new LoggerAlertDefinitionConfigurationViewModel(navigationService, x, config.PresentA2lMeasurementName, config.DualEcusAvailable));
        return new LoggerAlertsModuleViewModel(slaveEnabled, ModuleId.DME_SLAVE, slaveVms, navigationService, config);
    }

    public async Task AddAlertAsync()
    {
        var args = LoggerAlertItemPageNavigationArgs.New(ModuleId);
        await _navigationService.NavigateAsync(PageType.LoggerAlertItem, args);
    }

    public void SetEnabled(bool state)
    {
        Items.ForEach(x => x.Enabled = state);
    }

    public void UpdateAlerts(LoggerAlertItemPageSubmitResult result)
    {
        if (result.IsNew)
        {
            var vm = new LoggerAlertDefinitionConfigurationViewModel(_navigationService, result.Item, _loggerPreferenceConfig.PresentA2lMeasurementName, _loggerPreferenceConfig.DualEcusAvailable);
            Items.Insert(0, vm);
        }
        else if (Items.FirstOrDefault(x => x.CorrelationId == result.CorrelationId) is LoggerAlertDefinitionConfigurationViewModel vm)
        {
            vm.Update(result.Item);
        }
    }
}