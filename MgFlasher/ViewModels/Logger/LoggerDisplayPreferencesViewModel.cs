﻿using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Models;
using System;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels.Logger;

public class LoggerDisplayPreferencesViewModel : BaseViewModel
{
    public Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        return Task.CompletedTask;
    }

    public Task SubmitAsync()
    {
        return Task.CompletedTask;
    }

    public void Defaults()
    {
    }
}