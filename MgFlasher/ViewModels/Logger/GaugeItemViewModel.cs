﻿using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using MgFlasher.Flasher.Services.CarLogger.Gauges;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User.Storage;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Views.Logger.Controls;
using MgFlasher.Views.Logger.Services;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Devices;
using System.Diagnostics;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.Logger;

public class GaugeItemViewModel : BaseViewModel, IShapeHolderViewModel
{
    private readonly ILogger<GaugeItemViewModel> _logger;
    private readonly IGaugeLogDefinitionConfigurationService _gaugeLogDefinitionConfigurationService;
    private readonly INavigationService _navigationService;
    private readonly IUserDialogs _userDialogs;
    private readonly Action<GaugeItemViewModel> _removedCallback;
    private readonly GaugeLogDefinition _definition;
    private bool _dualEcusAvailable;
    private GaugePresentationType _presentationType;
    private GaugeLogShape _shape;
    private GaugeLogMeasurement _value;
    private bool _presentA2LName, _presentHexValues;
    private string _titleShortened;
    private bool _visible;
    private bool _isAnimationEnabled = true;
    private int _animationDuration = 300;
    private double _sessionMaxPointer = 0;
    private readonly Stopwatch _sessionMaxPointerHoldTimer = new Stopwatch();
    private readonly int _sessionMaxPointerHoldTimeInMs = 3000;
    private readonly double _sessionMaxPointerReductionScale = 0.8d;
    private GaugeColor _gaugeColor;

    public bool PresentHexValues
    {
        get => _presentHexValues;
        private set => SetProperty(ref _presentHexValues, value);
    }

    public string DefinitionCode => _definition.DefinitionCode;
    public ModuleId ModuleId => _value.ModuleId;
    public string Title => _definition.ToDisplayName(_presentA2LName, _dualEcusAvailable);
    public string A2LName => _definition.ToDisplayName(true, _dualEcusAvailable);
    public string MGName => _definition.ToDisplayName(false, _dualEcusAvailable);

    public string TitleShortened
    {
        get
        {
            if (_titleShortened != null)
            {
                return _titleShortened;
            }

            var text = Title;
            text = string.Join(" ", text.Split(' ').Distinct());
            text = text.Replace(":", "");

            var hashSet = new HashSet<string>(text.Split(" ", StringSplitOptions.RemoveEmptyEntries));

            _titleShortened = string.Join(" ", hashSet);

            return _titleShortened;
        }
    }

    public string Unit => _value.Unit;
    public int DisplayPrecision => _value.DisplayPrecision;
    public double Min => _value.Min;
    public double Max => _value.Max;
    public double SessionMin => _value.SessionMin ?? Min;
    public double SessionMax => _value.SessionMax ?? Max;

    public double SessionMaxPointer
    {
        get
        {
            return _sessionMaxPointer;
        }
        set
        {
            _sessionMaxPointerHoldTimer.Restart();
            _sessionMaxPointer = value;
            OnPropertyChanged(nameof(SessionMaxPointer));
        }
    }

    public string CurrentValueString
    {
        get
        {
            if (PresentHexValues)
            {
                return ((int)Math.Round(_value.Value, 0)).ToString("X2");
            }

            if (_value.Value < 100)
            {
                return _value.Value.ToString(CultureInfo.InvariantCulture);
            }

            return _value.Value.ToString("0.", CultureInfo.InvariantCulture);
        }
    }

    public double CircularValue => CircularGaugeViewConstants.CircularMax * _value.Value / Math.Max(Max, 1);

    public ICommand GaugeChangePromptCommand { get; }
    public ICommand SaveModifiedShapeCommand { get; }

    public double ProgressValue
    {
        get
        {
            var max = Max;
            var currentValue = _value.Value;
            var diff = max - _value.Min;

            if (diff <= 0)
            {
                return 0;
            }

            var result = 1 - ((max - currentValue) / diff);
            return result >= 0 ? result : 0;
        }
    }

    /// <summary>
    /// Template selector - rectangle vs circular gauge view.
    /// </summary>
    public GaugePresentationType PresentationType
    {
        get => _presentationType;
        set => SetProperty(ref _presentationType, value);
    }

    public GaugeLogShape Shape
    {
        get => _shape;
        set => SetProperty(ref _shape, value);
    }

    public bool Visible
    {
        get => _visible;
        set => SetProperty(ref _visible, value);
    }

    public int AnimationDuration
    {
        get => _animationDuration;
        set => SetProperty(ref _animationDuration, value);
    }

    public bool IsAnimationEnabled
    {
        get => _isAnimationEnabled;
        set => SetProperty(ref _isAnimationEnabled, value);
    }

    public GaugeColor GaugeColor
    {
        get => _gaugeColor;
        set
        {
            _gaugeColor = value;
            OnPropertyChanged(nameof(GaugeColor));
        }
    }

    public GaugeItemViewModel(
        ILogger<GaugeItemViewModel> logger,
        GaugeLogDefinition definition,
        GaugeUserInterfaceConfig userInterfaceConfig,
        IGaugeLogDefinitionConfigurationService gaugeLogDefinitionConfigurationService,
        INavigationService navigationService,
        IUserDialogs userDialogs,
        Action<GaugeItemViewModel> removedCallback,
        bool presentA2lName,
        bool presentHexValues,
        bool dualEcusAvailable)
    {
        _logger = logger;
        _gaugeLogDefinitionConfigurationService = gaugeLogDefinitionConfigurationService;
        _navigationService = navigationService;
        _userDialogs = userDialogs;
        _removedCallback = removedCallback;
        _presentA2LName = presentA2lName;
        _definition = definition;
        _dualEcusAvailable = dualEcusAvailable;

        PresentHexValues = presentHexValues;
        PresentationType = userInterfaceConfig.PresentationType;
        Shape = userInterfaceConfig.Shape?.Clone();
        Visible = userInterfaceConfig.Visible;
        _gaugeColor = userInterfaceConfig.GaugeColor;

        _value = _definition.GetDefaultMeasurement();

        GaugeChangePromptCommand = new TraceableCommand(GaugeChangePrompt, nameof(GaugeChangePrompt));
        SaveModifiedShapeCommand = new TraceableCommand(SaveModifiedShape, nameof(SaveModifiedShape));
    }

    public void Update(GaugeLogMeasurement newValue)
    {
        if (_value is null)
        {
            _logger.LogWarning("Value should not be null for {ModuleId}-{DefinitionCode}", ModuleId, DefinitionCode);
            return;
        }

        var oldValue = _value;
        _value = newValue.Clone();

        void ConditionalPropertyChanged(string propertyName, double oldValue, double newValue)
        {
            if (oldValue != newValue)
            {
                OnPropertyChanged(propertyName);
            }
        }

        void ConditionalNullPropertyChanged(string propertyName, double? oldValue, double? newValue)
        {
            if (oldValue != newValue)
            {
                OnPropertyChanged(propertyName);
            }
        }

        ConditionalPropertyChanged(nameof(Min), oldValue.Min, _value.Min);
        ConditionalPropertyChanged(nameof(Max), oldValue.Max, _value.Max);
        ConditionalNullPropertyChanged(nameof(SessionMin), oldValue.SessionMin, _value.SessionMin);
        ConditionalNullPropertyChanged(nameof(SessionMax), oldValue.SessionMax, _value.SessionMax);
        ConditionalPropertyChanged(nameof(CircularValue), oldValue.Value, _value.Value);
        ConditionalPropertyChanged(nameof(ProgressValue), oldValue.Value, _value.Value);
        ConditionalPropertyChanged(nameof(CurrentValueString), oldValue.Value, _value.Value);
        if (oldValue.Unit != _value.Unit)
        {
            OnPropertyChanged(nameof(Unit));
        }

        UpdateSessionMaxPointer();
    }

    private void UpdateSessionMaxPointer()
    {
        var valueInScale = CircularGaugeViewConstants.CircularMax * _value.Value / Math.Max(Max, 1);
        if (valueInScale > SessionMaxPointer)
        {
            SessionMaxPointer = valueInScale;
        }
        else if (valueInScale < SessionMaxPointer
            && _sessionMaxPointerHoldTimer.ElapsedMilliseconds > _sessionMaxPointerHoldTimeInMs)
        {
            SessionMaxPointer = SessionMaxPointer * _sessionMaxPointerReductionScale;
        }
    }

    public void UpdateGaugeName(bool presentA2lName)
    {
        _presentA2LName = presentA2lName;

        _titleShortened = null;

        OnPropertyChanged(nameof(Title));
        OnPropertyChanged(nameof(A2LName));
        OnPropertyChanged(nameof(MGName));
        OnPropertyChanged(nameof(TitleShortened));
    }

    public void UpdateGaugeValueFormat(bool presentHexValues)
    {
        PresentHexValues = presentHexValues;
        OnPropertyChanged(nameof(CurrentValueString));
    }

    public async Task GaugeChangePromptSubmittedAsync(GaugeUserInterfaceConfig config)
    {
        Shape = config.Shape?.Clone();
        PresentationType = config.PresentationType;
        Visible = config.Visible;

        await _gaugeLogDefinitionConfigurationService.SetAsync(ModuleId, DefinitionCode, Shape?.Clone(), PresentationType, Visible);
    }

    private async void SaveModifiedShape(object args)
    {
        var collision = args as CollisionConfigurationArgs;

        var config = GetConfig();

        await _gaugeLogDefinitionConfigurationService.SetAsync(config, collision.Brothers);
    }

    private async void GaugeChangePrompt(object obj)
    {
#if !WINDOWS
        Vibration.Vibrate(100);
#endif

        var commands = new Dictionary<string, Action>
        {
            [AppResources.GaugeItem_Misc_Edit] = async () => await EditPromptAsync(),
            [AppResources.GaugeItem_Misc_Replace] = async () => await ReplacePromptAsync(),
            [AppResources.GaugeItem_Misc_Remove] = async () => await RemovePromptAsync(),
            [AppResources.GaugeItem_Misc_IncreaseSizeBy50Percent] = async () => await ScaleShapeByAsync(1.5),
        };

        if (Shape != null && Shape.ScaleW > 0.7d && Shape.ScaleH > 0.7d)
        {
            commands[AppResources.GaugeItem_Misc_DecreaseSizeBy50Percent] = async () => await ScaleShapeByAsync(0.5);
        }

        if (Shape != null && Shape.ScaleW != 1d && Shape.ScaleH != 1d)
        {
            commands[AppResources.GaugeItem_Misc_ScaleToDefaultSize] = async () => await ScaleShapeByAsync();
        }

        var result = await _userDialogs.ActionSheetAsync(null, AppResources.Cancel, null, null, commands.Keys.ToArray());
        if (commands.ContainsKey(result ?? ""))
        {
            commands[result].Invoke();
        }
    }

    private async Task ReplacePromptAsync()
    {
        var config = GetConfig();

        await _navigationService.NavigateAsync(PageType.LoggerReplaceGaugeItem, config);
    }

    private async Task EditPromptAsync()
    {
        var config = GetConfig();

        await _navigationService.NavigateAsync(PageType.LoggerDisplayGaugeItem, config);
    }

    private async Task ScaleShapeByAsync(double? factor = null)
    {
        var shape = Shape?.Clone();
        if (shape is null)
        {
            return;
        }

        if (!factor.HasValue)
        {
            shape.ScaleH = 1d;
            shape.ScaleW = 1d;
        }
        else
        {
            shape.ScaleH *= factor.Value;
            shape.ScaleW *= factor.Value;
        }

        Shape = shape;

        await _gaugeLogDefinitionConfigurationService.SetAsync(ModuleId, DefinitionCode, Shape, PresentationType, Visible);
    }

    private async Task RemovePromptAsync()
    {
        await _gaugeLogDefinitionConfigurationService.SetAsync(ModuleId, DefinitionCode, Shape, PresentationType, false);

        _removedCallback.Invoke(this);
    }

    public GaugeUserInterfaceConfig GetConfig()
    {
        var config = new GaugeUserInterfaceConfig()
        {
            A2lMeasurementName = _definition.A2lMeasurementName,
            DefinitionCode = _definition.DefinitionCode,
            ModuleId = _definition.ModuleId,
            Name = _definition.Name,
            PresentationType = PresentationType,
            Visible = Visible,
            Shape = Shape?.Clone(),
            GaugeColor = _gaugeColor,
        };

        return config;
    }
}