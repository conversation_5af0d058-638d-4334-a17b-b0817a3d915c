﻿using MgFlasher.Flasher.Services.CarLogger;
using MgFlasher.Models;
using MgFlasher.Services;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Linq;
using System.Windows.Input;
using System;
using MgFlasher.Localization.Resources;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Localization;
using MgFlasher.Flasher.Services.CarLogger.Exporters;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.ApplicationModel.DataTransfer;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.Logger;

public class MyCarLoggerFilesViewModel : BaseViewModel, ITabComponent, IContextOptionsViewModel
{
    private readonly INavigationService _navigationService;
    private readonly ILoggerFileService _loggerFileService;
    private readonly IExporterProvidersFactory _exporterProvidersFactory;
    private readonly IUserDialogs _userDialogs;
    private readonly IDCANService _dCANService;
    private readonly ILogger<MyCarLoggerFilesViewModel> _logger;
    private Car _connectedCar;
    private ObservableCollection<LoggerFileItemViewModel> _optionalItemViewModels;

    public Dictionary<string, Action> ContextOptions { get; }

    public ObservableCollection<LoggerFileItemViewModel> OptionalItemViewModels
    {
        get => _optionalItemViewModels;
        protected set => SetProperty(ref _optionalItemViewModels, value);
    }

    public ICommand GoToLoggerCommand { get; }
    public ICommand ShareCommand { get; }

    public MyCarLoggerFilesViewModel(
        INavigationService navigationService,
        ILoggerFileService loggerFileService,
        IExporterProvidersFactory exporterProvidersFactory,
        IUserDialogs userDialogs,
        IDCANService dCANService,
        ILogger<MyCarLoggerFilesViewModel> logger)
    {
        _navigationService = navigationService;
        _loggerFileService = loggerFileService;
        _exporterProvidersFactory = exporterProvidersFactory;
        _userDialogs = userDialogs;
        _dCANService = dCANService;
        _logger = logger;

        ShareCommand = new TraceableCommand(async () => await ShareAll(), nameof(ShareAll));
        GoToLoggerCommand = new TraceableCommand(async () => await _navigationService.NavigateToTabbedComponent<MyCarLoggerViewModel>(PageType.MyCar), "GoToLogger");
        ContextOptions = new Dictionary<string, Action>()
        {
            [AppResources.Misc_MarkAll] = MarkAll,
            [AppResources.Misc_Share] = async () => await ShareAll(),
            [AppResources.Misc_Rename] = async () => await Rename(),
            [AppResources.Misc_Delete] = async () => await Delete()
        };
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previous, object arg)
    {
        _connectedCar = await _dCANService.GetConnectedCar();

        var loggerFiles = await _loggerFileService.GetLogFileNamesAsync(_connectedCar.VIN);
        if (OptionalItemViewModels is null || OptionalItemViewModels.Count != loggerFiles.Count || !loggerFiles.All(f => OptionalItemViewModels.Any(o => o.Option == f)))
        {
            var vms = loggerFiles.Select(s => new LoggerFileItemViewModel(s, _navigationService));
            OptionalItemViewModels = new ObservableCollection<LoggerFileItemViewModel>(vms);
        }
    }

    private void MarkAll()
    {
        foreach (var item in OptionalItemViewModels)
        {
            item.IsChecked = true;
        }
    }

    private async Task Rename()
    {
        var @checked = OptionalItemViewModels.OfType<LoggerFileItemViewModel>().Where(x => x.IsChecked).ToList();

        if (@checked.Count != 1)
        {
            await _userDialogs.AlertAsync(AppResources.LoggerFiles_PleaseSelectSingleFile, okText: AppResources.Ok);
            return;
        }

        var file = @checked.First();

        var promptResult = await _userDialogs.PromptAsync(new PromptConfig
        {
            OkText = AppResources.Map_Rename_Ok,
            Title = AppResources.Map_Rename_Title,
            Text = file.Option
        });

        if (promptResult.Ok && !string.IsNullOrWhiteSpace(promptResult.Text))
        {
            if (OptionalItemViewModels.OfType<LoggerFileItemViewModel>().Any(x => x.Option == promptResult.Text))
            {
                await _userDialogs.AlertAsync(AppResources.LoggerFiles_CannotRenameFileAlreadyExists, okText: AppResources.Ok);
                return;
            }

            var confirmMessage = AppResources.ConfirmFileRename + file.Option + " -> " + promptResult.Text;
            var renameApproved = await _userDialogs.ConfirmAsync(confirmMessage, okText: AppResources.Yes, cancelText: AppResources.No);
            if (renameApproved)
            {
                await _loggerFileService.RenameAsync(_connectedCar.VIN, file.Option, promptResult.Text);
                file.Option = promptResult.Text;
            }
        }
    }

    private async Task Delete()
    {
        var @checked = OptionalItemViewModels.OfType<LoggerFileItemViewModel>().Where(x => x.IsChecked).ToList();

        if (!@checked.Any())
        {
            await _userDialogs.AlertAsync(AppResources.SelectAtLeastOneFile, okText: AppResources.Ok);
            return;
        }

        var message = string.Format(AppResources.ConfirmFileDelete, @checked.Count());
        var deleteApproved = await _userDialogs.ConfirmAsync(message, okText: AppResources.Yes, cancelText: AppResources.No);
        if (deleteApproved)
        {
            foreach (var vm in @checked)
            {
                await _loggerFileService.DeleteAsync(_connectedCar.VIN, vm.Option);
                OptionalItemViewModels.Remove(vm);
            }
        }
    }

    private async Task ShareAll()
    {
        var selectedItems = OptionalItemViewModels.OfType<LoggerFileItemViewModel>().Where(vm => vm.IsChecked).ToList();

        if (!selectedItems.Any())
        {
            await _userDialogs.AlertAsync(AppResources.SelectAtLeastOneFile, okText: AppResources.Ok);
            return;
        }

        await Share(selectedItems);
    }

    private async Task Share(List<LoggerFileItemViewModel> items)
    {
        var options = await GetShareOptionProviders(items.Select(s => s.Option).ToArray());
        var result = await _userDialogs.ActionSheetAsync(AppResources.Log_Share_Title, AppResources.Cancel, null, null, options.Keys.ToArray());
        if (options.ContainsKey(result))
        {
            await options[result]();
        }
    }

    private async Task<Dictionary<string, Func<Task>>> GetShareOptionProviders(string[] selectedItems)
    {
        var providers = await _exporterProvidersFactory.Create();

        var options = providers.ToDictionary(
            x => TranslateHelper.GetString($"Log_Share_{x.Type}"),
            x => new Func<Task>(() => ExportFiles(x, selectedItems)));

        options.Add(AppResources.Log_Share_Configuration, async () => await _navigationService.NavigateAsync(PageType.Settings));

        return options;
    }

    private async Task ExportFiles(IExporterProvider provider, string[] selectedItems)
    {
        if (provider.Validate(selectedItems) is string error)
        {
            await _userDialogs.AlertAsync(error, AppResources.Error, AppResources.Ok);
            return;
        }

        try
        {
            string result;

            using (_userDialogs.Loading(AppResources.Spoolstreet_Sending_Loading))
            {
                result = await provider.Export(_connectedCar.VIN, selectedItems);
            }

            if (!string.IsNullOrEmpty(result))
            {
                await Clipboard.SetTextAsync(result);
                var websiteShouldBeOpenned = await _userDialogs.ConfirmAsync(AppResources.Log_Share_Success_Link, okText: AppResources.Yes, cancelText: AppResources.No);
                if (websiteShouldBeOpenned)
                {
                    await Launcher.OpenAsync(new Uri(result));
                }
            }
        }
        catch (MgFlasherException ex)
        {
            _logger.LogWarning(ex, "Error while exporting log to external provider");
            await _userDialogs.AlertAsync(ex.Message, okText: AppResources.Ok);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error while exporting log to external provider");
            await _userDialogs.AlertAsync(AppResources.Error_Occured, okText: AppResources.Ok);
        }
    }
}