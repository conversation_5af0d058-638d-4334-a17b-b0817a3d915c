﻿using MgFlasher.Ecu.Logger.Configuration.User;
using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.CustomCode;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels.Logger;

public class LoggerUnitsPageViewModel : BaseOptionsPageViewModel
{
    private readonly IDCANService _dCANService;
    private readonly ILoggerUnitsUserConfigurationService _loggerUnitsUserConfigurationService;
    private Car _connectedCar;
    public ICommand LoggerUnitDefaultsCommand { get; }

    public LoggerUnitsPageViewModel(
        INavigationService navigationService,
        IDCANService dCANService,
        ILoggerUnitsUserConfigurationService loggerUnitsUserConfigurationService)
        : base(navigationService)
    {
        _dCANService = dCANService;
        _loggerUnitsUserConfigurationService = loggerUnitsUserConfigurationService;
        LoggerUnitDefaultsCommand = new TraceableCommand(LoggerUnitDefaults, nameof(LoggerUnitDefaults));
    }

    private async void LoggerUnitDefaults(object obj)
    {
        var config = await _loggerUnitsUserConfigurationService.GetDefaultAsync(_connectedCar.VIN, _connectedCar.EngineCode, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(_connectedCar.ProcessorArchitectureType));
        await SetOptionsAsync(config);
    }

    protected async override void Submit(IEnumerable<OptionalItemViewModel> selectedOptions, INavigationService navigationService)
    {
        var vms = OptionalItemViewModels.OfType<LoggerUnitUserConfigurationOptionalItemViewModel>();
        var selected = vms.ToDictionary(x => x.SourceGroup, x => x.Option);

        await _loggerUnitsUserConfigurationService.SetAsync(_connectedCar.VIN, _connectedCar.EngineCode, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(_connectedCar.ProcessorArchitectureType), selected);

        await navigationService.NavigateToPreviousAsync();
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _connectedCar = await _dCANService.GetConnectedCar();

        var config = await _loggerUnitsUserConfigurationService.GetAsync(_connectedCar.VIN, _connectedCar.EngineCode, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(_connectedCar.ProcessorArchitectureType));
        await SetOptionsAsync(config);

        await base.OnNavigatedToAsync(previousPage, arg);
    }

    private async Task SetOptionsAsync(IEnumerable<LoggerUnitUserConfiguration> config)
    {
        var vms = new List<LoggerUnitUserConfigurationOptionalItemViewModel>();
        foreach (var item in config)
        {
            var availableUnits = await _loggerUnitsUserConfigurationService.GetAvailableUnits(
                _connectedCar.VIN, _connectedCar.EngineCode, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(_connectedCar.ProcessorArchitectureType), item.SourceGroup);
            vms.Add(new LoggerUnitUserConfigurationOptionalItemViewModel(item, availableUnits));
        }
        OptionalItemViewModels = new ObservableCollection<OptionalItemViewModel>(vms);
    }
}