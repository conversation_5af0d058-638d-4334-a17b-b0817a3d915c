﻿using MgFlasher.Flasher.Services.CarLogger.Gauges;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels.Logger.Services;

public class GaugeItemViewModelsFactory : IGaugeItemViewModelsFactory
{
    private readonly INavigationService _navigationService;
    private readonly IGaugeLogDefinitionConfigurationService _gaugeLogDefinitionConfigurationService;
    private readonly IUserDialogs _userDialogs;
    private readonly ILoggerPreferenceService _loggerPreferenceService;
    private readonly ILogger<GaugeItemViewModel> _itemLogger;

    public GaugeItemViewModelsFactory(
        INavigationService navigationService,
        IGaugeLogDefinitionConfigurationService gaugeLogDefinitionConfigurationService,
        IUserDialogs userDialogs,
        ILoggerPreferenceService loggerPreferenceService,
        ILoggerFactory loggerFactory)
    {
        _gaugeLogDefinitionConfigurationService = gaugeLogDefinitionConfigurationService;
        _userDialogs = userDialogs;
        _loggerPreferenceService = loggerPreferenceService;
        _navigationService = navigationService;
        _itemLogger = loggerFactory.CreateLogger<GaugeItemViewModel>();
    }

    public async Task<GaugeItemViewModel> CreateAsync(GaugeLogDefinition item, Action<GaugeItemViewModel> removed)
    {
        var preferences = await _loggerPreferenceService.GetCarConfiguration();
        var userInterfaceConfig = await _gaugeLogDefinitionConfigurationService.GetAsync(item.ModuleId, item.DefinitionCode, item);

        var vm = new GaugeItemViewModel(
            _itemLogger,
            item,
            userInterfaceConfig,
            _gaugeLogDefinitionConfigurationService,
            _navigationService,
            _userDialogs,
            removed,
            preferences.PresentA2lMeasurementName,
            preferences.PresentHexValues,
            preferences.DualEcusAvailable);

        return vm;
    }

    public async Task<List<GaugeItemViewModel>> CreateAsync(IReadOnlyList<GaugeLogDefinition> gaugeDefinitions, Action<GaugeItemViewModel> removed)
    {
        var preferences = await _loggerPreferenceService.GetCarConfiguration();
        var userInterfaceConfigs = await _gaugeLogDefinitionConfigurationService.GetAllAsync();
        var vms = new List<GaugeItemViewModel>();

        foreach (var item in gaugeDefinitions)
        {
            var userInterfaceConfig = userInterfaceConfigs.FirstOrDefault(x => x.ModuleId == item.ModuleId && x.DefinitionCode == item.DefinitionCode);

            var vm = new GaugeItemViewModel(
                _itemLogger,
                item,
                userInterfaceConfig,
                _gaugeLogDefinitionConfigurationService,
                _navigationService,
                _userDialogs,
                removed,
                preferences.PresentA2lMeasurementName,
                preferences.PresentHexValues,
                preferences.DualEcusAvailable);
            vms.Add(vm);
        }

        return vms;
    }
}