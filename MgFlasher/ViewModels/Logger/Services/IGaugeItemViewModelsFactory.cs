﻿using MgFlasher.Flasher.Services.CarLogger.Gauges;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MgFlasher.ViewModels.Logger.Services;

public interface IGaugeItemViewModelsFactory
{
    Task<GaugeItemViewModel> CreateAsync(GaugeLogDefinition gaugeDefinition, Action<GaugeItemViewModel> removed);

    Task<List<GaugeItemViewModel>> CreateAsync(IReadOnlyList<GaugeLogDefinition> gaugeDefinitions, Action<GaugeItemViewModel> removed);
}