﻿using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.Logger.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels.Logger;

public class LoggerAlertsPageViewModel : PageViewModel, IContextOptionsViewModel
{
    private readonly INavigationService _navigationService;
    private LoggerAlertsPreferencesViewModel _loggerAlertsPreferences;
    private LoggerAlertsModulesViewModel _loggerAlertsModules;
    private int _selectedTabIndex;

    public Dictionary<string, Action> ContextOptions => LoggerAlertsModules.ContextOptions;

    public int SelectedTabIndex
    {
        get => _selectedTabIndex;
        set
        {
            SetProperty(ref _selectedTabIndex, value);
            _loggerAlertsModules.SelectedModuleIndex = _selectedTabIndex < 2 ? _selectedTabIndex : -1;
            _loggerAlertsPreferences.Selected = _selectedTabIndex == 2;
        }
    }

    public ICommand SubmitCommand { get; }

    public LoggerAlertsPreferencesViewModel LoggerAlertsPreferences
    {
        get => _loggerAlertsPreferences;
        private set => SetProperty(ref _loggerAlertsPreferences, value);
    }

    public LoggerAlertsModulesViewModel LoggerAlertsModules
    {
        get => _loggerAlertsModules;
        private set => SetProperty(ref _loggerAlertsModules, value);
    }

    public LoggerAlertsPageViewModel(
        INavigationService navigationService,
        LoggerAlertsPreferencesViewModel loggerAlertsPreferences,
        LoggerAlertsModulesViewModel loggerAlertsModules)
    {
        _navigationService = navigationService;

        LoggerAlertsPreferences = loggerAlertsPreferences;
        LoggerAlertsModules = loggerAlertsModules;
        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));
    }

    public async void Submit()
    {
        await LoggerAlertsPreferences.SubmitAsync();
        await LoggerAlertsModules.SubmitAsync();

        await _navigationService.NavigateToPreviousAsync(true);
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (previousPage == PageType.LoggerAlertItem && arg is LoggerAlertItemPageSubmitResult result)
        {
            LoggerAlertsModules.UpdateAlerts(result);
            return;
        }
        else if (previousPage == PageType.LoggerAlertItem)
        {
            return;
        }

        await LoggerAlertsPreferences.OnNavigatedToAsync();
        await LoggerAlertsModules.OnNavigatedToAsync();
    }
}