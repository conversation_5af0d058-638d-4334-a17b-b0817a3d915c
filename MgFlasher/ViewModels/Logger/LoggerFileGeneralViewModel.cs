﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.Flasher.Services.CarLogger;
using MgFlasher.Flasher.Services.CarLogger.Exporters;
using MgFlasher.Flasher.Services.CarLogger.Exporters.Models;
using MgFlasher.Flasher.Services.CarLogger.Parser;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.ApplicationModel.DataTransfer;

namespace MgFlasher.ViewModels.Logger;

public class LoggerFileGeneralViewModel : BaseViewModel, ITabComponent
{
    private readonly IExporterProvidersFactory _exporterProvidersFactory;
    private readonly INavigationService _navigationService;
    private readonly IUserDialogs _userDialogs;
    private readonly ILoggerFileService _loggerFileService;
    private readonly IDCANService _dCANService;
    private readonly ILogger<LoggerFileGeneralViewModel> _logger;
    private IReadOnlyCollection<IExporterProvider> _exporters;
    private Car _connectedCar;
    private LoggerFileContent _content;

    public string Filename => _content?.Filename;
    public string CustomCodeVersion => _content?.CustomCodeVersion;
    public string AppVersion => _content?.AppVersion;
    public string Platform => _content?.Platform;
    public bool HasFlashContext => !string.IsNullOrEmpty(_content?.Attributes?.FlashContext?.Id);
    public bool HasLogsCloudUrl => !string.IsNullOrEmpty(_content?.Attributes?.MgFlasherLogsCloudUrl);

    public TraceableCommand GoToFlashHistoryDetailsCommand { get; }
    public TraceableCommand RenameCommand { get; }
    public TraceableCommand DeleteCommand { get; }
    public TraceableCommand ShareCommand { get; }

    public LoggerFileGeneralViewModel(
        IExporterProvidersFactory exporterProvidersFactory,
        INavigationService navigationService,
        IUserDialogs userDialogs,
        ILoggerFileService loggerFileService,
        IDCANService dCANService,
        ILogger<LoggerFileGeneralViewModel> logger)
    {
        _exporterProvidersFactory = exporterProvidersFactory;
        _navigationService = navigationService;
        _userDialogs = userDialogs;
        _loggerFileService = loggerFileService;
        _dCANService = dCANService;
        _logger = logger;
        GoToFlashHistoryDetailsCommand = new TraceableCommand(GoToFlashHistoryDetails, _ => HasFlashContext, nameof(GoToFlashHistoryDetails));
        RenameCommand = new TraceableCommand(Rename, nameof(Rename));
        DeleteCommand = new TraceableCommand(Delete, nameof(Delete));
        ShareCommand = new TraceableCommand(Share, nameof(Share));
    }

    public async Task OnNavigatedToAsync(PageType? previousPage, ITabComponent previousPart, object arg)
    {
        _exporters = await _exporterProvidersFactory.Create();
        _connectedCar = await _dCANService.GetConnectedCar();
        _content = (LoggerFileContent)arg;

        OnPropertyChanged(nameof(Filename));
        OnPropertyChanged(nameof(CustomCodeVersion));
        OnPropertyChanged(nameof(AppVersion));
        OnPropertyChanged(nameof(Platform));
        OnPropertyChanged(nameof(HasFlashContext));
        OnPropertyChanged(nameof(HasLogsCloudUrl));
        GoToFlashHistoryDetailsCommand.ChangeCanExecute();
    }

    private async void GoToFlashHistoryDetails(object obj)
    {
        await _navigationService.NavigateAsync(PageType.MyCarFlashHistoryItemDetail, _content.Attributes.FlashContext);
    }

    private async void Delete()
    {
        var message = string.Format(AppResources.ConfirmFileDeleteSingle, _content.Filename);
        if (!await _userDialogs.ConfirmAsync(message, okText: AppResources.Yes, cancelText: AppResources.No))
        {
            return;
        }

        await _loggerFileService.DeleteAsync(_connectedCar.VIN, _content.Filename);
        await _navigationService.NavigateToPreviousAsync();
    }

    private async void Rename()
    {
        var promptResult = await _userDialogs.PromptAsync(new PromptConfig
        {
            OkText = AppResources.Map_Rename_Ok,
            Title = AppResources.Map_Rename_Title,
            Text = _content.Filename
        });

        if (!promptResult.Ok || string.IsNullOrWhiteSpace(promptResult.Text))
        {
            return;
        }

        var exisingFileNames = await _loggerFileService.GetLogFileNamesAsync(_connectedCar.VIN);
        if (exisingFileNames.Contains(promptResult.Text, StringComparer.InvariantCultureIgnoreCase))
        {
            await _userDialogs.AlertAsync(AppResources.LoggerFiles_CannotRenameFileAlreadyExists, okText: AppResources.Ok);
            return;
        }

        var confirmMessage = AppResources.ConfirmFileRename + _content.Filename + " -> " + promptResult.Text;
        var renameApproved = await _userDialogs.ConfirmAsync(confirmMessage, okText: AppResources.Yes, cancelText: AppResources.No);
        if (renameApproved)
        {
            await _loggerFileService.RenameAsync(_connectedCar.VIN, _content.Filename, promptResult.Text);
            _content = _content with { Filename = promptResult.Text };
        }
    }

    private async void Share(object arg)
    {
        var option = (ExporterProviderType)arg;

        if (await ShouldAskToOpenLogsCloudFile(option))
        {
            await CopyToClipboardAsync(_content.Attributes.MgFlasherLogsCloudUrl);
            await OpenAsync(_content.Attributes.MgFlasherLogsCloudUrl, option);
            return;
        }

        var provider = _exporters.First(x => x.Type == option);
        try
        {
            string result;
            using (_userDialogs.Loading(AppResources.Spoolstreet_Sending_Loading))
            {
                result = await provider.Export(_connectedCar.VIN, [_content.Filename]);
            }

            if (string.IsNullOrEmpty(result))
            {
                return;
            }

            await CopyToClipboardAsync(result);
            if (!await _userDialogs.ConfirmAsync(AppResources.Log_Share_Success_Link, okText: AppResources.Yes, cancelText: AppResources.No))
            {
                return;
            }

            await OpenAsync(result, option);
        }
        catch (MgFlasherException ex)
        {
            _logger.LogWarning(ex, "Error while exporting log to external provider");
            await _userDialogs.AlertAsync(ex.Message, okText: AppResources.Ok);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error while exporting log to external provider");
            await _userDialogs.AlertAsync(AppResources.Error_Occured, okText: AppResources.Ok);
        }
    }

    private async Task CopyToClipboardAsync(string result)
    {
        await _userDialogs.Toast(new(AppResources.Log_Share_Success_Link_Copied, false));
        await Clipboard.SetTextAsync(result);
    }

    private async Task OpenAsync(string url, ExporterProviderType provider)
    {
        if (provider == ExporterProviderType.MgFlasherLogsCloud)
        {
            _content.Attributes.MgFlasherLogsCloudUrl = url;
            OnPropertyChanged(nameof(HasLogsCloudUrl));
            await _navigationService.NavigateToTabbedComponent<LoggerFileChartViewModel>(PageType.LoggerFile);
        }
        else
        {
            await Launcher.OpenAsync(new Uri(url));
        }
    }

    private async Task<bool> ShouldAskToOpenLogsCloudFile(ExporterProviderType option)
    {
        return option == ExporterProviderType.MgFlasherLogsCloud &&
            !string.IsNullOrEmpty(_content.Attributes?.MgFlasherLogsCloudUrl) &&
            await _userDialogs.ConfirmAsync(
                AppResources.LoggerFileGeneralView_LogsCloudAlreadyExported,
                okText: AppResources.LoggerFileGeneralView_LogsCloud_Open,
                cancelText: AppResources.LoggerFileGeneralView_LogsCloud_Reupload);
    }
}