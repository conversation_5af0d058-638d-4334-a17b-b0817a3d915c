﻿using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using System;

namespace MgFlasher.ViewModels.Logger.Models;

public class LoggerAlertItemPageSubmitResult
{
    public LoggerAlertUserConfiguration Item { get; }
    public bool IsNew { get; }
    public Guid? CorrelationId { get; }

    public LoggerAlertItemPageSubmitResult(LoggerAlertUserConfiguration item, bool isNew, Guid? correlationId)
    {
        Item = item;
        IsNew = isNew;
        CorrelationId = correlationId;
    }
}