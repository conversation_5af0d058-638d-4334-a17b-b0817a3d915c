﻿using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using System;

namespace MgFlasher.ViewModels.Logger.Models;

public class LoggerAlertItemPageNavigationArgs
{
    public LoggerAlertUserConfiguration Item { get; }
    public bool AddMode { get; }
    public Guid? CorrelationId { get; }

    private LoggerAlertItemPageNavigationArgs(LoggerAlertUserConfiguration item, bool addMode, Guid? correlationId = null)
    {
        Item = item;
        AddMode = addMode;
        CorrelationId = correlationId;
    }

    public static LoggerAlertItemPageNavigationArgs New(ModuleId moduleId)
    {
        return new LoggerAlertItemPageNavigationArgs(new LoggerAlertUserConfiguration
        {
            ModuleId = moduleId,
            Enabled = true
        }, true, null);
    }

    public static LoggerAlertItemPageNavigationArgs Edit(LoggerAlertUserConfiguration config, Guid correlationId)
    {
        return new LoggerAlertItemPageNavigationArgs(config, false, correlationId);
    }
}