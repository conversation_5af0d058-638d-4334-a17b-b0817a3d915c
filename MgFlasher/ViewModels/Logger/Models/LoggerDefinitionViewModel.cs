﻿using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Ecu.Logger.Configuration.Templates.Storage;
using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using MgFlasher.Flasher.Services.CarLogger.LoggerPreferences;
using MgFlasher.Flasher.Services.Cars;

namespace MgFlasher.ViewModels.Logger.Models;

public class LoggerDefinitionViewModel : ILoggerDefinitionName
{
    private readonly CarLoggerPreferenceConfiguration _carConfig;
    private readonly LoggerDefinitionRecord _locator;

    public ModuleId ModuleId { get; }
    public string DefinitionCode => _locator.Code;
    public string Name => _locator.Name;
    public string A2lMeasurementName => _locator.A2lMeasurementName;
    public string DisplayName => this.ToDisplayName(_carConfig.PresentA2lMeasurementName, _carConfig.DualEcusAvailable);

    public string Unit { get; }
    public double Min { get; }
    public double Max { get; }
    public int DisplayPrecision { get; }

    public LoggerDefinitionViewModel(CarLoggerPreferenceConfiguration carConfig, LoggerDefinitionRecord locator, string unit, double min, double max, int displayPrecision, ModuleId moduleId)
    {
        _carConfig = carConfig;
        _locator = locator;
        Unit = unit;
        Min = min;
        Max = max;
        DisplayPrecision = displayPrecision;
        ModuleId = moduleId;
    }
}