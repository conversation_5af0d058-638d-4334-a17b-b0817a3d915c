﻿using MgFlasher.Flasher.Services.CarLogger.Gauges;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User;

namespace MgFlasher.ViewModels.Logger.Models;

public class LoggerReplaceGaugeItemSubmitResult
{
    public GaugeUserInterfaceConfig Old { get; }
    public GaugeUserInterfaceConfig New { get; }

    public LoggerReplaceGaugeItemSubmitResult(GaugeUserInterfaceConfig old, GaugeUserInterfaceConfig @new)
    {
        Old = old;
        New = @new;
    }
}