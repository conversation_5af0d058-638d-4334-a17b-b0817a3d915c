﻿using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.Connectivity;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services;
using System;
using System.Linq;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Maui.Controls;
using MgFlasher.ViewModels.MyCar;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels;

public class ShellTitleViewModel : BaseViewModel
{
    private const int ButtonDelayInMiliseconds = 100;

    private readonly INavigationService _navigationService;
    private readonly IDCANService _dCANService;
    private readonly ICarConnectivityStatusChecker _carConnectivityStatusChecker;
    private readonly IUserDialogs _userDialogs;
    private readonly ILogger<ShellTitleViewModel> _logger;
    private CarConnectivity _carConnectivity = CarConnectivity.NotConnected;

    public CarConnectivity CarConnectivity
    {
        get => _carConnectivity;
        set => SetProperty(ref _carConnectivity, value);
    }

    public ICommand BackCommand { get; }
    public ICommand MenuCommand { get; }
    public ICommand ConnectivityCommand { get; }
    public ICommand ContextOptionsCommand { get; }

    public ShellTitleViewModel(
        INavigationService navigationService,
        IDCANService dCANService,
        ICarConnectivityStatusChecker carConnectivityStatusChecker,
        IUserDialogs userDialogs,
        ILogger<ShellTitleViewModel> logger)
    {
        _navigationService = navigationService;
        _dCANService = dCANService;
        _carConnectivityStatusChecker = carConnectivityStatusChecker;
        _userDialogs = userDialogs;
        _logger = logger;

        BackCommand = new TraceableCommand(Back, nameof(Back));
        MenuCommand = new TraceableCommand(Menu, nameof(Menu));
        ConnectivityCommand = new TraceableCommand(Connectivity, nameof(Connectivity));
        ContextOptionsCommand = new TraceableCommand(ContextOptions, nameof(ContextOptions));

        _carConnectivityStatusChecker.StatusChanged += OnCarConnectivityStatusChanged;
        CarConnectivity = _carConnectivityStatusChecker.Status;
    }

    private void OnCarConnectivityStatusChanged(object sender, CarConnectivity e)
    {
        Application.Current.Dispatcher.Dispatch(() => CarConnectivity = e);
    }

    private async void Connectivity(object obj)
    {
        await Task.Delay(ButtonDelayInMiliseconds);

        if (!string.IsNullOrEmpty(_dCANService.ConnectedCarFingerprint) && _carConnectivityStatusChecker.Status == CarConnectivity.CarConnected)
        {
            await _navigationService.NavigateToTabbedComponent<MyCarHomeOverviewViewModel>(PageType.MyCar);
        }
        else
        {
            await _navigationService.NavigateAsync(PageType.MyCars);
        }
    }

    private async void ContextOptions(object obj)
    {
        await Task.Delay(ButtonDelayInMiliseconds);

        if (_navigationService.CurrentViewModel is not IContextOptionsViewModel contextOptionsVm)
        {
            return;
        }

        if (contextOptionsVm.ContextOptions is null || contextOptionsVm.ContextOptions.Count == 0)
        {
            return;
        }

        try
        {
            var commands = contextOptionsVm.ContextOptions;
            var result = await _userDialogs.ActionSheetAsync(null, AppResources.Cancel, null, null, commands.Keys.ToArray());
            if (commands.ContainsKey(result ?? ""))
            {
                commands[result].Invoke();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ContextOptions error {obj}", obj);
        }
    }

    private async void Menu(object obj)
    {
        await Task.Delay(ButtonDelayInMiliseconds);

        _navigationService.OpenFlyout();
    }

    private async void Back(object obj)
    {
        await Task.Delay(ButtonDelayInMiliseconds);

        await _navigationService.NavigateToPreviousAsync();
    }
}