﻿using MgFlasher.Flasher.Services.Backend;
using MgFlasher.Flasher.Client.Common;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Services.Navigation;
using Microsoft.Extensions.Logging;
using MgFlasher.Files;
using MgFlasher.Flasher.Services.User.Feedback;

namespace MgFlasher.ViewModels;

public class FeedbackPageViewModel : PageViewModel
{
    private readonly IFeedbackCollector _feedbackCollector;
    private readonly ILogger<FeedbackPageViewModel> _logger;
    private readonly IAppContext _appContext;
    private readonly IConnectivityStatusAccessor _connectivityStatusAccessor;
    private readonly IUserDialogs _userDialogs;
    private readonly INavigationService _navigationService;
    private FeedbackPageNavigationArgs _args;
    private string _subject, _customerEmail, _message;
    private bool _isCustomerEmailValid;
    private PageType _navigatedFrom = PageType.Feedback;

    public bool IsCustomerEmailValid
    {
        get => _isCustomerEmailValid;
        set
        {
            SetProperty(ref _isCustomerEmailValid, value);
            OnPropertyChanged(nameof(CanSubmit));
        }
    }

    public string Subject
    {
        get => _subject;
        set
        {
            SetProperty(ref _subject, value);
            OnPropertyChanged(nameof(CanSubmit));
        }
    }

    public string CustomerEmail
    {
        get => _customerEmail;
        set
        {
            SetProperty(ref _customerEmail, value);
            OnPropertyChanged(nameof(CanSubmit));
        }
    }

    public string Message
    {
        get => _message;
        set
        {
            SetProperty(ref _message, value);
            OnPropertyChanged(nameof(CanSubmit));
        }
    }

    public bool CanSubmit =>
        !string.IsNullOrEmpty(_customerEmail) && _isCustomerEmailValid &&
        !string.IsNullOrEmpty(_subject) &&
        !string.IsNullOrEmpty(_message) &&
        !IsBusy;

    public ICommand SubmitCommand { get; }

    public FeedbackPageViewModel(
        IFeedbackCollector feedbackCollector,
        ILogger<FeedbackPageViewModel> logger,
        IDirectoryPathFactory directoryPathFactory,
        IBackendService backendService,
        IDCANService dCANService,
        IAppContext appContext,
        IConnectivityStatusAccessor connectivityStatusAccessor,
        ICarRepository carsService,
        IUserDialogs userDialogs,
        INavigationService navigationService)
    {
        _feedbackCollector = feedbackCollector;
        _logger = logger;
        _appContext = appContext;
        _userDialogs = userDialogs;
        _navigationService = navigationService;
        _connectivityStatusAccessor = connectivityStatusAccessor;
        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (previousPage != PageType.Pending)
        {
            _navigatedFrom = previousPage;
        }

        SetFeedbackViewModelArgs(arg);

        Subject = _args.Subject;
        Message = _args.Message;

        CustomerEmail = _appContext.CurrentUser?.Context?.Credentials?.Email;

        IsBusy = false;

        OnPropertyChanged(nameof(CanSubmit));
    }

    private async void Submit()
    {
        try
        {
            SetIsBusyWithCommandExecutionNotification(true);

            if (!await _connectivityStatusAccessor.IsConnected())
            {
                await _userDialogs.AlertAsync(AppResources.Data_YoureOffline, okText: AppResources.Ok);
                return;
            }

            bool success;
            using (_userDialogs.Loading(AppResources.Feedback_InProgress))
            {
                success = await _feedbackCollector.SendFeedback(CustomerEmail, _args.SystemSubject, Subject, Message, _args.AdditionalProperties);
            }
            if (success)
            {
                using (_userDialogs.Loading(AppResources.Feedback_Diagnostics_InProgress))
                {
                    await _feedbackCollector.CollectApplicationLogs(CustomerEmail);
                }
            }

            if (success)
            {
                await _userDialogs.AlertAsync(AppResources.Send_Feedback_Success, okText: AppResources.Ok);
                await _navigationService.NavigateToPreviousAsync();
            }
            else
            {
                await _userDialogs.AlertAsync(AppResources.Error_Occured, okText: AppResources.Ok);
            }
        }
        finally
        {
            SetIsBusyWithCommandExecutionNotification(false);
        }
    }

    private void SetIsBusyWithCommandExecutionNotification(bool value)
    {
        IsBusy = value;
        OnPropertyChanged(nameof(CanSubmit));
    }

    private void SetFeedbackViewModelArgs(object arg)
    {
        if (arg is FeedbackPageNavigationArgs feedbackPageNavigationArgs)
        {
            _args = feedbackPageNavigationArgs;
        }
        else
        {
            _args = new() { SystemSubject = SystemFeedbackSubject.RegularFeedback };
        }
    }
}