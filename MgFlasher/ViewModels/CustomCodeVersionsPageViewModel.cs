﻿using CommunityToolkit.Mvvm.Input;
using MgFlasher.CustomCode;
using MgFlasher.CustomCode.Models;
using MgFlasher.CustomCode.Services;
using MgFlasher.CustomCode.Storage.Changesets;
using MgFlasher.Flasher.Commands;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.Cars.FlashHistory;
using MgFlasher.Flasher.Services.CustomCode;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services.Navigation;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels;

public class CustomCodeVersionsPageViewModel : PageViewModel
{
    private readonly IFlashHistoryRepository _flashHistoryService;
    private readonly IDCANService _dCANService;
    private readonly INavigationService _navigationService;
    private readonly IFileDecryptor _fileDecryptor;
    private readonly IReferenceFileVersionExtractor _referenceFileVersionExtractor;
    private readonly ILogger<CustomCodeVersionsPageViewModel> _logger;
    private readonly IUserDialogs _userDialogs;

    private ObservableCollection<CustomCodeVersionInfoViewModel> _customCodeVersionInfoViewModels;
    private CustomCodeVersionsPageNavigationArgs _args;

    private Car _connectedCar;
    private bool _areAllCustomCodeVersionsShown;

    public bool LoadMoreCustomCodesButtonVisible
    {
        get => _areAllCustomCodeVersionsShown;
        private set => SetProperty(ref _areAllCustomCodeVersionsShown, value);
    }

    public ObservableCollection<CustomCodeVersionInfoViewModel> CustomCodeVersionInfoViewModels
    {
        get => _customCodeVersionInfoViewModels;
        private set => SetProperty(ref _customCodeVersionInfoViewModels, value);
    }

    public ICommand SubmitCommand { get; }

    public IRelayCommand ShowAllCCVersionsCommand { get; }

    public bool CanProceed => !IsBusy && _customCodeVersionInfoViewModels.Any(vn => vn.IsChecked);

    public CustomCodeVersionsPageViewModel(
        IFlashHistoryRepository flashHistoryService,
        IDCANService dCANService,
        INavigationService navigationService,
        IFileDecryptor fileDecryptor,
        IReferenceFileVersionExtractor referenceFileVersionExtractor,
        ILogger<CustomCodeVersionsPageViewModel> logger,
        IUserDialogs userDialogs)
    {
        _flashHistoryService = flashHistoryService;
        _dCANService = dCANService;
        _navigationService = navigationService;
        _fileDecryptor = fileDecryptor;
        _referenceFileVersionExtractor = referenceFileVersionExtractor;
        _logger = logger;
        _userDialogs = userDialogs;

        CustomCodeVersionInfoViewModels = new ObservableCollection<CustomCodeVersionInfoViewModel>();
        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));
        ShowAllCCVersionsCommand = new AsyncRelayCommand<object>(ShowAllCustomCodeVersions);
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _connectedCar = await _dCANService.GetConnectedCar();

        // here args are decoded as a list of CC versions, stage type and flasher command
        _args = (arg as CustomCodeVersionsPageNavigationArgs) ?? _args;

        var vms = await CreateViewModels(false);
        CustomCodeVersionInfoViewModels = new ObservableCollection<CustomCodeVersionInfoViewModel>(vms);

        IsBusy = false;
        OnPropertyChanged(nameof(CanProceed));
    }

    private async Task<IReadOnlyList<CustomCodeVersionInfoViewModel>> CreateViewModels(bool showAll)
    {
        LoadMoreCustomCodesButtonVisible = !showAll;
        var result = new List<CustomCodeVersionInfoViewModel>();
        // Here we have the custom code id installed.
        var currentCustomCodeId = new CustomCodeId(_connectedCar.EcuMaster?.CustomCodeVersionInstalled ?? "NotFound");

        // Gets current version, so i.e. 7.1.1?
        var ccVersionOfFile = await GetFileCCVersionAsync();

        // Gets args but they are passed from earlier page, so here we already should get it deprecated.
        var availableCustomCodeVersionList = _args.CustomCodeVersions
            .OrderByDescending(x => x.CanSelect)
            .ThenByDescending(x => x.Version?.Version)
            .ToList();

        if (ccVersionOfFile != null && !availableCustomCodeVersionList.Any(x => ccVersionOfFile == x.Version.ToVersion()))
        {
            var ccOverviewOfFile = GetCustomCodeOverviewFromFile(ccVersionOfFile);
            availableCustomCodeVersionList.Add(ccOverviewOfFile);
            availableCustomCodeVersionList = availableCustomCodeVersionList
                .OrderByDescending(x => x.CanSelect)
                .ThenByDescending(x => x.Version?.Version)
                .ToList();
        }

        if (!showAll)
        {
            availableCustomCodeVersionList = availableCustomCodeVersionList
                .Where(x => x.CanSelect)
                .Take(1)
                .ToList();
        }

        _logger.LogInformation($"There are [{availableCustomCodeVersionList.Count}] custom code versions to display:");

        var resultDetails = new List<string>();
        foreach (var ccVersion in availableCustomCodeVersionList)
        {
            var isNewest = ccVersion.Version.Version is string ccVer &&
                           _args.CustomCodeVersions.IndexOf(ccVersion) == 0 &&
                           !ccVer.Equals(_connectedCar.EcuMaster?.CustomCodeApplied ?? "", StringComparison.OrdinalIgnoreCase) &&
                           !ccVer.Equals(_connectedCar.EcuMaster?.CustomCodeVersionInstalled ?? "", StringComparison.OrdinalIgnoreCase) &&
                           _args.StageType != Flasher.Services.Models.StageType.Custom;

            var releasedAt = AcquireReleasedDate(ccVersion.ReleasedAtUtc);
            var isCurrentVersion = currentCustomCodeId.ToVersion() == ccVersion.Version.ToVersion();
            var isFileVersion = ccVersionOfFile != null && ccVersionOfFile == ccVersion.Version.ToVersion();
            var unavailableReason = GetUnavailableReasonString(_connectedCar.AnyEcuReqiresWave3Unlock ? CustomCodeUnavailableReasonEnum.Wave3Locked : ccVersion.UnavailableReason);

            var vm = new CustomCodeVersionInfoViewModel(ccVersion.Version, isNewest,
                ccVersion.ReleaseNotes, isCurrentVersion, isFileVersion, ccVersion.CanSelect, unavailableReason, releasedAt, OnItemChecked);
            resultDetails.Add(vm.ToString());
            result.Add(vm);
        }

        _logger.LogInformation($"Custom Code versions to display details:\n\t{string.Join("\n\t", resultDetails)}");

#if DEBUG
        await TryToCheckLastFlashedItem(result);
#else
        MarkFirstOption(result);
#endif
        return result;
    }

    private DateTime? AcquireReleasedDate(string releasedAtUtc)
    {
        try
        {
            if (!string.IsNullOrEmpty(releasedAtUtc))
            {
                var example = "19.02.2025 00:26:04"; // releasedAtUtc

                var formats = new string[] {
                    "dd.MM.yyyy HH:mm:ss",  // German/European format
                    "yyyy-MM-dd HH:mm:ss",  // ISO 8601
                    "MM/dd/yyyy HH:mm:ss",  // US format
                    "dd/MM/yyyy HH:mm:ss",  // UK/Canadian
                    "yyyy/MM/dd HH:mm:ss",  // Alternative format
                    "dd-MM-yyyy HH:mm:ss",  // European with hyphen
                    "yyyyMMdd HHmmss",      // Compact format
                    "dd.MM.yyyy"            // Date-only format
                };

                if (DateTime.TryParseExact(example, formats, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out DateTime parsedDate) ||
                    DateTime.TryParse(example, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out parsedDate) ||
                    DateTime.TryParse(example, CultureInfo.CurrentCulture, DateTimeStyles.AssumeUniversal, out parsedDate))
                {
                    var utcDate = DateTime.SpecifyKind(parsedDate, DateTimeKind.Utc);
                    return utcDate.ToLocalTime();
                }
                _logger.LogError($"Failed to parse date: [{releasedAtUtc}]");
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Failed to parse date: [{releasedAtUtc}]", ex);
            return null;
        }
    }

    private CustomCodeOverview GetCustomCodeOverviewFromFile(Version ccVersionOfFile)
    {
        var id = new CustomCodeId($"v{ccVersionOfFile.ToString()}", "File");
        var releaseNote = AppResources.CustomCodeReleaseNoteFromFile;
        var releasedAtUtc = DateTime.Now.ToString();
        return new CustomCodeOverview(id, releaseNote, releasedAtUtc, true, null);
    }

    private async Task<Version> GetFileCCVersionAsync()
    {
        try
        {
            if (_args.StageType != Flasher.Services.Models.StageType.Custom || _args.FlasherCommand is not ICustomMapFlashCommand)
            {
                return null;
            }

            var customMapFlashCommand = _args.FlasherCommand as ICustomMapFlashCommand;
            if (customMapFlashCommand == null)
            {
                return null;
            }

            var data = await _fileDecryptor.Decrypt(_connectedCar.VIN, customMapFlashCommand.FileDescriptor);
            if (data == null)
            {
                return null;
            }

            var referenceFileVersion = _referenceFileVersionExtractor.Extract(data, CarCustomCodeExtensions.GetCustomCodeProcessorArchitectureType(_connectedCar.EcuMaster?.ProcessorArchitectureType));
            if (referenceFileVersion == null || referenceFileVersion.CustomCodeId == null)
            {
                return null;
            }

            var result = referenceFileVersion.CustomCodeId.ToVersion();
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract File CC version!");
            return null;
        }
    }

    private string GetUnavailableReasonString(CustomCodeUnavailableReasonEnum? reason)
    {
        return reason switch
        {
            CustomCodeUnavailableReasonEnum.RockerComboUnsupported => AppResources.CustomCodeUnavailableReason_RockerComboUnsupported,
            CustomCodeUnavailableReasonEnum.Obsolete => AppResources.CustomCodeUnavailableReason_Obsolete,
            CustomCodeUnavailableReasonEnum.Wave3Locked => AppResources.CustomCodeUnavailableReason_Wave3Locked,
            _ => AppResources.CustomCodeUnavailableReason_Unknown
        };
    }

    private async Task TryToCheckLastFlashedItem(List<CustomCodeVersionInfoViewModel> result)
    {
        var lastFlashHistory = await _flashHistoryService.GetLastSucceddedFlashHistoryItem(_dCANService.ConnectedCarFingerprint);
        if (result is null || result.Count < 1 || string.IsNullOrWhiteSpace(lastFlashHistory?.CustomCodeVersion))
        {
            MarkFirstOption(result);
            return;
        }

        var versionNameGroup = result.FirstOrDefault(r => r.CustomCodeId.ToString() == lastFlashHistory.CustomCodeVersion);
        if (versionNameGroup is null)
        {
            MarkFirstOption(result);
            return;
        }

        versionNameGroup.IsChecked = true;
    }

    private void MarkFirstOption(List<CustomCodeVersionInfoViewModel> result)
    {
        if (result is null || !result.Any())
        {
            return;
        }

        result.FirstOrDefault().IsChecked = true;
    }

    private void OnItemChecked(IRadioButtonViewModel vm)
    {
        if (!vm.IsChecked)
        {
            return;
        }

        foreach (var customCodeVersionInfoVm in CustomCodeVersionInfoViewModels)
        {
            customCodeVersionInfoVm.PropagateChecked(vm);
        }

        OnPropertyChanged(nameof(CanProceed));
    }

    /// <summary>
    /// Submits the selection of Custom Code version user wants to flash into the car.
    /// </summary>
    private async void Submit()
    {
        var @checked = CustomCodeVersionInfoViewModels.First(vm => vm.IsChecked);
        var isDeprecated = @checked.UnavailableReason == AppResources.CustomCodeUnavailableReason_Obsolete;
        if (isDeprecated)
        {
            string message = string.Format(AppResources.Warning_DeprecatedCCC_Long, @checked.CustomCodeVersion);
            string title = AppResources.Warning;
            bool isWarningAccepted = await _userDialogs.ConfirmAsync(message, title, AppResources.Button_FlashAnyway, AppResources.Cancel);

            if (!isWarningAccepted)
            {
                return;
            }
        }

        if (_args.StageType == Flasher.Services.Models.StageType.Custom && _args.FlasherCommand is ICustomMapFlashCommand customMapCommand)
        {
            customMapCommand.TargetCustomCodeVersionString = @checked.CustomCodeId.ToString();
            await _navigationService.NavigateAsync(PageType.FlashingCarOptions, _args.FlasherCommand);
        }
        else if (_args.StageType == Flasher.Services.Models.StageType.Activated && _args.FlasherCommand is IActivateCarFlashCommand activationCommand)
        {
            activationCommand.TargetCustomCodeVersionString = @checked.CustomCodeId.ToString();
            var flashingCarPageNavigationArguments = new FlashingCarPageNavigationArgs()
            {
                ContinousCommand = activationCommand,
                WarningInfos = FlashingCarWarningInfo.GetWarningInfos(Flasher.Services.Models.StageType.Activated)
            };
            await _navigationService.NavigateAsync(PageType.FlashingCarWarningInfo, flashingCarPageNavigationArguments);
        }
        else
        {
            var args = new StageVersionsPageNavigationArgs(_args.StageType, _args.FlasherCommand, @checked.CustomCodeId, false);
            await _navigationService.NavigateAsync(PageType.StageVersions, args);
        }
    }

    private async Task ShowAllCustomCodeVersions(object obj)
    {
        var vms = await CreateViewModels(true);
        CustomCodeVersionInfoViewModels = new ObservableCollection<CustomCodeVersionInfoViewModel>(vms);
        OnPropertyChanged(nameof(CanProceed));
    }
}