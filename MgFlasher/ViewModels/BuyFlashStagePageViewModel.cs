﻿using MgFlasher.CustomCode.Storage.Changesets;
using MgFlasher.Flasher.Commands;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.FlashCarAllowance;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Flasher.Services.Purchases;
using MgFlasher.Flasher.Services.Purchases.Validator;
using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Commands.Flash;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Dispatching;
using MgFlasher.Services.Navigation;
using Polly;
using MgFlasher.Flasher.Services.User.Feedback;

namespace MgFlasher.ViewModels;

public class BuyFlashStagePageViewModel : PageViewModel
{
    private readonly IDCANService _dCANService;
    private readonly IRedeemPurchaseValidator _redeemPurchaseValidator;
    private readonly IAppContext _appContext;
    private readonly IFilesService _filesService;
    private readonly INavigationService _navigationService;
    private readonly IPurchaseItemService _purchaseItemService;
    private readonly ICarCompatabilityService _carCompatabilityService;
    private readonly IUserDialogs _userDialogs;
    private readonly IRestoreBackupCommand _restoreBackupCommand;
    private readonly IStageDescriptionFactory _stageDescriptionFactory;
    private readonly IPurchaseService _purchaseService;
    private readonly ILogger<BuyFlashStagePageViewModel> _logger;
    private Car _connectedCar;

    private FlashAllowanceItem _flashAllowanceItem;
    private bool _commandVisible;
    private ObservableCollection<ProductLinkViewModel> _includedInProducts;

    public StageDescription StageDescription { get; set; }

    public ICommand OpenProductPageCommand { get; }
    public ICommand SubmitCommand { get; }
    public ICommand DownloadAndShareStockMapCommand { get; }
    public bool CommandVisible { get => _commandVisible; private set => SetProperty(ref _commandVisible, value); }
    public bool IsBought => _flashAllowanceItem.IsBought;
    public bool CanOpenProductPage => !_flashAllowanceItem.IsBought && _appContext.RetailMode != RetailModeEnum.China;
    public bool CanDownloadStage => _flashAllowanceItem.Stage == StageType.Stock;

    public ObservableCollection<ProductLinkViewModel> IncludedInProducts
    {
        get => _includedInProducts;
        private set => SetProperty(ref _includedInProducts, value);
    }

    public BuyFlashStagePageViewModel(INavigationService navigationService,
        IPurchaseItemService purchaseItemService,
        IDCANService dCANService,
        IRedeemPurchaseValidator redeemPurchaseValidator,
        IAppContext appContext,
        IFilesService filesService,
        ICarCompatabilityService carCompatabilityService,
        IUserDialogs userDialogs,
        IRestoreBackupCommand restoreBackupCommand,
        IStageDescriptionFactory stageDescriptionFactory,
        IPurchaseService purchaseService,
        ShareStockMapCommand shareStockMapCommand,
        ILogger<BuyFlashStagePageViewModel> logger)
    {
        _includedInProducts = new ObservableCollection<ProductLinkViewModel>();
        _flashAllowanceItem = new FlashAllowanceItem();
        _navigationService = navigationService;
        _purchaseItemService = purchaseItemService;
        _dCANService = dCANService;
        _redeemPurchaseValidator = redeemPurchaseValidator;
        _appContext = appContext;
        _filesService = filesService;
        _carCompatabilityService = carCompatabilityService;
        _userDialogs = userDialogs;
        _restoreBackupCommand = restoreBackupCommand;
        _stageDescriptionFactory = stageDescriptionFactory;
        _purchaseService = purchaseService;
        _logger = logger;

        SubmitCommand = new TraceableCommand(async () => await Submit(), nameof(Submit));
        OpenProductPageCommand = new TraceableCommand(async (x) => await OpenProductPage(x), nameof(OpenProductPage));
        DownloadAndShareStockMapCommand = shareStockMapCommand;
    }

    private async Task OpenProductPage(object link)
    {
        if (link is string value)
        {
            await Launcher.OpenAsync(value);
        }
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (arg is FlashAllowanceItem flashAllowanceItem)
        {
            _flashAllowanceItem = flashAllowanceItem;
            SetCommandVisible();

            _connectedCar = await _dCANService.GetConnectedCar();

            await SetStageDescription(_connectedCar);
            SetIncludedInProduct(_connectedCar);
            OnPropertyChanged(null);
        }

        await base.OnNavigatedToAsync(previousPage, arg);
    }

    private void SetCommandVisible()
    {
        CommandVisible = !_flashAllowanceItem.IsBought || _flashAllowanceItem.Flashable;
    }

    private void SetIncludedInProduct(Car car)
    {
        var vms = _flashAllowanceItem.ProductSkus
            .Select(x => car.Products?.Items?.FirstOrDefault(d => d.ProductSku == x))
            .Where(x => x != null)
            .Select(x => new ProductLinkViewModel(x));

        _includedInProducts = new ObservableCollection<ProductLinkViewModel>(vms);
    }

    private async Task SetStageDescription(Car car)
    {
        StageDescription = await _stageDescriptionFactory.CreateAsync(car, _flashAllowanceItem);
    }

    public async Task Submit()
    {
        if (!_flashAllowanceItem.IsBought && _flashAllowanceItem.CanRedeem)
        {
            await RedeemItem();
            return;
        }

        if (!_flashAllowanceItem.IsBought)
        {
            await _userDialogs.AlertAsync(AppResources.Message_BuyStage_BuyFlasherLicenseFirst, okText: AppResources.Ok);
            return;
        }

        if (_flashAllowanceItem.IsBought && _flashAllowanceItem.Flashable)
        {
            var purchases = await _purchaseService.GetPurchases(_connectedCar.Fingerprint);
            if (!purchases.HasFlasherLicense())
            {
                await _userDialogs.AlertAsync(AppResources.Message_BuyStage_BuyFlasherLicenseFirst, okText: AppResources.Ok);
                return;
            }
        }

        if (!_flashAllowanceItem.Flashable)
        {
            await _userDialogs.AlertAsync(AppResources.Message_Buy_AlreadyPurchased, okText: AppResources.Ok);
            return;
        }

        if (_flashAllowanceItem.IsBought)
        {
            await Flash();
        }
    }

    private async Task RedeemItem()
    {
        IsBusy = true;

        if (!await _userDialogs.ConfirmAsync(AppResources.Redeem_Confirmation, okText: AppResources.Ok, cancelText: AppResources.Cancel))
        {
            IsBusy = false;
            return;
        }

        _ = Task.Run(async () =>
        {
            try
            {
                using (var loading = _userDialogs.Loading(AppResources.Loading))
                {
                    _connectedCar = await _dCANService.GetConnectedCar();
                    await _redeemPurchaseValidator.PrepareValidation(_connectedCar.Fingerprint);

                    await _redeemPurchaseValidator.Validate(_connectedCar.Fingerprint, _connectedCar.VIN, _flashAllowanceItem);
                    await _purchaseItemService.RedeemItem(_connectedCar, _flashAllowanceItem);
                }

                await Application.Current.Dispatcher.DispatchAsync(async () => await InformUserAboutSuccessRedeemAsync());
            }
            catch (CannotRedeemItemException ex)
            {
                _logger.LogInformation(ex, "Flash allowance item cannot be redeemed");

                await Application.Current.Dispatcher.DispatchAsync(async () => await HandleCannotRedeemItemException(ex));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cannot buy item");
                await Application.Current.Dispatcher.DispatchAsync(async () => await _userDialogs.AlertAsync(ex.Message, AppResources.Error_Occured, okText: AppResources.Ok));
            }
            finally
            {
                IsBusy = false;
            }
        });
    }

    private async Task InformUserAboutSuccessRedeemAsync()
    {
        var missingStages = await GetMissingStagesAsync();
        if (!missingStages.Any())
        {
            await _userDialogs.AlertAsync(AppResources.Redeem_Completed, okText: AppResources.Ok);
            await _navigationService.NavigateAsync(PageType.MyCar);
            return;
        }

        if (await NavigateToFeedbackDueToMissingStages(AppResources.Redeem_Completed_WithMissingStages, missingStages))
        {
            return;
        }

        await _navigationService.NavigateAsync(PageType.MyCar);
    }

    private async Task HandleCannotRedeemItemException(CannotRedeemItemException ex)
    {
        await _userDialogs.AlertAsync(ex.Message, okText: AppResources.Ok);

        if (ex.Reason == CannotRedeemItemReason.NotAuthenticated)
        {
            await _navigationService.NavigateAsync(PageType.Login, new LoginPageNavigationArgs()
            {
                NextPage = PageType.BuyFlashStage,
                NextPageArguments = _flashAllowanceItem
            });
        }
        else if (ex.Reason == CannotRedeemItemReason.PurchaseToRedeemDoesntExists && _appContext.RetailMode == RetailModeEnum.MG_Flasher)
        {
            var userConfirmation = await _userDialogs.ConfirmAsync(AppResources.Redemption_OpenShopWebsite,
                okText: AppResources.Yes,
                cancelText: AppResources.No);
            if (userConfirmation)
            {
                var productLinkViewModel = _includedInProducts.FirstOrDefault();
                productLinkViewModel?.OpenLinkCommand.Execute(null);
            }
        }
    }

    private async Task Flash()
    {
        _connectedCar = await _dCANService.GetConnectedCar();
        if (!_connectedCar.Activated && !_connectedCar.AnyEcuInBootMode)
        {
            await _userDialogs.AlertAsync(AppResources.Flash_PreCheck_EcuNotUnlocked_Text, okText: AppResources.Ok);
            return;
        }

        var flashCommand = ResolveFlashCommand();

        if (flashCommand is ICustomMapFlashCommand custom)
        {
            var customMapFilesArgs = new CustomMapFilesPageNavigationArgs(custom);
            await _navigationService.NavigateAsync(PageType.CustomMapFiles, customMapFilesArgs);
            return;
        }

        if (!await _filesService.Exists(_connectedCar.VIN, _connectedCar.Fingerprint, _connectedCar.StockMap))
        {
            var syncRequest = await _userDialogs.ConfirmAsync(AppResources.StockFile_DoesntExist_PleaseSync, okText: AppResources.Ok, cancelText: AppResources.Cancel);
            if(syncRequest)
                await _navigationService.NavigateAsync(PageType.SyncPage);
            return;
        }

        if (_flashAllowanceItem.Stage == StageType.Stock || _flashAllowanceItem.Stage == StageType.Activated)
        {
            var lockEcu = await GetStockChoiceFromUser(_userDialogs);
            if (lockEcu)
            {
                if (!await _filesService.Exists(_connectedCar.VIN, _connectedCar.Fingerprint, _connectedCar.StockMap))
                {
                    await _userDialogs.AlertAsync(AppResources.MissingStockFiles, okText: AppResources.Ok);
                    await _navigationService.NavigateAsync(PageType.Feedback, CreateFeedbackNavigationArgs());
                    return;
                }

                var flashingCarPageArguments = new FlashingCarPageNavigationArgs()
                {
                    ContinousCommand = _restoreBackupCommand,
                    WarningInfos = FlashingCarWarningInfo.GetWarningInfosForRestore()
                };
                await _navigationService.NavigateAsync(PageType.FlashingCarWarningInfo, flashingCarPageArguments);
            }
            else
            {
                SetMapIdToCommand(flashCommand, _connectedCar.StockMap.Id);

                var flashingCarPageNavigationArguments = new FlashingCarPageNavigationArgs()
                {
                    ContinousCommand = flashCommand,
                    WarningInfos = FlashingCarWarningInfo.GetWarningInfos(_flashAllowanceItem.Stage)
                };
                await _navigationService.NavigateAsync(PageType.FlashingCarWarningInfo, flashingCarPageNavigationArguments);
            }
            return;
        }

        var versions = _connectedCar.Changesets.GetChangesetsByType(_flashAllowanceItem.Stage);
        if (versions.Count == 0)
        {
            await _userDialogs.AlertAsync(string.Format(AppResources.Flash_Discarded_DueToMissingStages, _flashAllowanceItem.ProductType.GetLocalizedString()), okText: AppResources.Ok);
            return;
        }

        // here we get all available CC?
        var customCodeVersions = await _carCompatabilityService.GetCustomCodeOverviewByStockMap(_connectedCar);
        // here we filter cc by their compatiblity with OTS Map selected/installed?
        var ccVersions = customCodeVersions
            .Where(ccv => versions.Any(v => _carCompatabilityService.IsOtsMapCompatibleWithCustomCodeVersion(_connectedCar, Convert.ToDouble(v.VersionNumber, CultureInfo.InvariantCulture), ccv.Version)))
            .ToList();

        if (_connectedCar.AnyEcuReqiresWave3Unlock)
        {
            // With Wave3 unlocked ECU, one CC is hardcoded into the ECU. User can flash car with only this
            // specific CC selected. If it is missing, user cannot flash any other version, so redirect them
            // to feedback.
            var installedCC = CustomCodeId.FromString(_connectedCar.EcuMaster.CustomCodeVersionInstalled);
            if (!ccVersions.Where(x => x.CanSelect).Any(x => x.Version.ToVersion().Equals(installedCC.ToVersion())))
            {
                await NavigateToFeedbackDueToMissingCustomCodeVersion(AppResources.Flash_Discarded_DueToMissingCustomCode, _connectedCar.EcuMaster.CustomCodeVersionInstalled);
                return;
            }
        }

        if (ccVersions.Count == 0)
        {
            await _userDialogs.AlertAsync(string.Format(AppResources.Flash_Warning_MissingCustomCodeVersions, _flashAllowanceItem.ProductType.GetLocalizedString()), okText: AppResources.Ok);
            await _navigationService.NavigateAsync(PageType.StageVersions, new StageVersionsPageNavigationArgs(_flashAllowanceItem.Stage, flashCommand, null, true));
            return;
        }

        var args = new CustomCodeVersionsPageNavigationArgs(ccVersions, _flashAllowanceItem.Stage, flashCommand);
        await _navigationService.NavigateAsync(PageType.CustomCodeVersions, args);
    }

    private async Task<bool> GetStockChoiceFromUser(IUserDialogs _userDialogs)
    {
        // Prompt user for selection between full stock and just stock calibration
        var lockEcu = false;
        var result = await _userDialogs.ActionSheetAsync(AppResources.StockFlash_SelectSoftware, AppResources.Cancel, null, null, new string[] {
            AppResources.StockFlash_KeepUnlockedECU_Option,
            AppResources.StockFlash_LockECU_Option,
        });

        if (result.Contains(AppResources.StockFlash_KeepUnlockedECU_Option))
        {
            lockEcu = false;
        }
        else if (result.Contains(AppResources.StockFlash_LockECU_Option))
        {
            var confirmEcuLock = await _userDialogs.ConfirmAsync(AppResources.StockFlash_ConfirmLockECU,
                okText: AppResources.StockFlash_LockECU,
                cancelText: AppResources.StockFlash_DoNotLockECU,
                title: AppResources.StockFlash_ConfirmLockECU_Title);
            if (!confirmEcuLock)
            {
                _logger.LogInformation("User chose to not lock the ecu, asking again");
                lockEcu = await GetStockChoiceFromUser(_userDialogs);
            }
            else
            {
                lockEcu = true;
                _logger.LogInformation("User awknowledged they want to lock the ECU");
            }
        }
        else if (result.Contains(AppResources.Cancel))
        {
            _logger.LogInformation("User chose to cancel");
        }

        var informUserString = AppResources.Selected + (lockEcu ? AppResources.StockFlash_LockECU : AppResources.StockFlash_DoNotLockECU);
        await _userDialogs.AlertAsync(informUserString);
        return lockEcu;
    }

    private static void SetMapIdToCommand(IContinousFlasherCommand flashCommand, int mapId)
    {
        if (flashCommand is UniFlashStageFlashCommand)
        {
            ((UniFlashStageFlashCommand)flashCommand).MapId = mapId;
        }
    }

    private IContinousFlasherCommand ResolveFlashCommand() => _flashAllowanceItem.Stage switch
    {
        StageType.Activated => DependencyResolver.Resolve<IActivateCarFlashCommand>(),
        StageType.Custom => DependencyResolver.Resolve<ICustomMapFlashCommand>(),
        StageType.Stage1 => DependencyResolver.Resolve<IStageFirstFlashCommand>(),
        StageType.Stage2 => DependencyResolver.Resolve<IStageSecondFlashCommand>(),
        StageType.Stage2_5 => DependencyResolver.Resolve<IStageSecondPointFiveFlashCommand>(),
        StageType.Stock => DependencyResolver.Resolve<IStockFlashCommand>(),
        _ => throw new InvalidOperationException($"There is no assigned flash command to {_flashAllowanceItem.Stage} stage"),
    };

    private FeedbackPageNavigationArgs CreateFeedbackNavigationArgs() => new FeedbackPageNavigationArgs
    {
        SystemSubject = SystemFeedbackSubject.StockMapSupportRequest,
        Subject = "Stock files are missing",
        Message = "I would like to flash my ECU to full stock, but files are missing."
    };

    private async Task<List<StageType>> GetMissingStagesAsync()
    {
        var purchases = await _purchaseService.GetPurchases(_connectedCar.Fingerprint);
        var missingStages = new List<StageType>();
        if (_flashAllowanceItem.ProductType.In(FlashAllowanceProductType.OtsStage1) &&
            !_connectedCar.Changesets.HasChangesets(StageType.Stage1) &&
            purchases.HasStage1())
        {
            missingStages.Add(StageType.Stage1);
        }

        if (_flashAllowanceItem.ProductType.In(FlashAllowanceProductType.OtsStage2) &&
            !_connectedCar.Changesets.HasChangesets(StageType.Stage2) &&
            purchases.HasStage2())
        {
            missingStages.Add(StageType.Stage2);
        }

        if (_flashAllowanceItem.ProductType.In(FlashAllowanceProductType.OtsBundle, FlashAllowanceProductType.Ultimate) &&
            !_connectedCar.Changesets.Any() &&
            purchases.HasAnyStage())
        {
            missingStages.Add(StageType.Stage1);
        }

        return missingStages;
    }

    private async Task<bool> NavigateToFeedbackDueToMissingCustomCodeVersion(string msgResource, string missingCustomCodeVersion)
    {
        var message = string.Format(msgResource, missingCustomCodeVersion);
        var feedbackConfirmation = await _userDialogs.ConfirmAsync(
            message: message,
            okText: AppResources.CustomFlash_WithMissingCustomCodeVersions_NotifyUs);

        if (feedbackConfirmation)
        {
            var feedbackMsg = CreateCustomCodeMissingFeedbackNavigationArgs(missingCustomCodeVersion);
            await _navigationService.NavigateAsync(PageType.Feedback, feedbackMsg);
            return true;
        }

        return false;
    }

    private async Task<bool> NavigateToFeedbackDueToMissingStages(string msgResource, List<StageType> missingStages)
    {
        var message = string.Format(msgResource, _flashAllowanceItem.ProductType.GetLocalizedString());
        var feedbackConfirmation = await _userDialogs.ConfirmAsync(
            message: message,
            okText: AppResources.Redeem_Completed_WithMissingStages_NotifyUs);

        if (feedbackConfirmation)
        {
            var fuelPreference = await _userDialogs.ActionSheetAsync(
                AppResources.Redeem_Completed_WithMissingStages_FuelSelection, AppResources.BuyFlashStagePageViewModel_FuelType_Any, null, null, new string[] {
                    "ACN 91",
                    "91 OCT (95 RON)",
                    "93 OCT (98 RON)",
                    "95 OCT (100 RON)",
                    "E30",
                    "E50"
                });
            var feedbackMsg = CreateOtsMapsMissingFeedbackNavigationArgs(_flashAllowanceItem.ProductType, missingStages, fuelPreference);
            await _navigationService.NavigateAsync(PageType.Feedback, feedbackMsg);
            return true;
        }

        return false;
    }

    private FeedbackPageNavigationArgs CreateCustomCodeMissingFeedbackNavigationArgs(string missingCustomCodeVersion) => new FeedbackPageNavigationArgs
    {
        SystemSubject = SystemFeedbackSubject.CustomCodeSupportRequest,
        Subject = "Custom Code is missing",
        AdditionalProperties = new Dictionary<string, string>()
        {
            ["MissingCustomCode"] = missingCustomCodeVersion,
        },
        Message = $"My Wave3 unlocked ECU cannot be flashed due to a missing Custom Code version. Please prepare {missingCustomCodeVersion} for me.",
    };

    private FeedbackPageNavigationArgs CreateOtsMapsMissingFeedbackNavigationArgs(
        FlashAllowanceProductType type, List<StageType> stages, string fuelPreference) => new FeedbackPageNavigationArgs
        {
            SystemSubject = SystemFeedbackSubject.OtsMapsSupportRequest,
            Subject = "OTS Maps are missing",
            AdditionalProperties = new Dictionary<string, string>()
            {
                ["MissingStages"] = string.Join(", ", stages),
                ["FuelPreference"] = fuelPreference
            },
            Message =
            $"I purchased the {type.GetLocalizedString()} license but I can't access the maps. " +
            $"Please prepare {string.Join(", ", stages)} for me. " +
            (!string.IsNullOrEmpty(fuelPreference) ? $"OTS Map should be initially optimized for {fuelPreference} fuel type." : "")
        };
}