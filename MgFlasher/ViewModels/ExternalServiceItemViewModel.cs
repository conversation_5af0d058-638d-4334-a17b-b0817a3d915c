﻿using MgFlasher.Flasher.Services.CarLogger.Exporters.Models;
using MgFlasher.Localization;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Input;
using MgFlasher.Models;
using Microsoft.Maui.Controls;
using Microsoft.Maui;
using Microsoft.Maui.ApplicationModel;

namespace MgFlasher.ViewModels;

public class ExternalServiceItemViewModel : BaseViewModel
{
    private bool _modified;
    private ObservableCollection<ExternalServiceItemAuthTypeViewModel> _authTypes;
    private readonly ExporterProviderApiSettings _model;

    public ICommand RetrieveApiKeyCommand { get; }

    public string ServiceName => TranslateHelper.GetString($"ExternalService_{_model.Type}");

    public ObservableCollection<ExternalServiceItemAuthTypeViewModel> AuthTypes
    {
        get => _authTypes;
        set => SetProperty(ref _authTypes, value);
    }

    public ExternalServiceItemAuthTypeViewModel AuthenticationType
    {
        get => AuthTypes.FirstOrDefault(vm => vm.Type == _model.AuthenticationType) ?? AuthTypes.FirstOrDefault();
        set
        {
            _model.AuthenticationType = value?.Type ?? _model.AllowedAuthenticationTypes?.Select(s => (ExporterProviderAuthenticationType?)s).FirstOrDefault() ?? ExporterProviderAuthenticationType.User;
            OnPropertyChanged(nameof(AuthenticationType));
            OnPropertyChanged(nameof(ShouldPresentApiKeyInput));

            if (_model.AuthenticationType == ExporterProviderAuthenticationType.Anonymous)
            {
                ApiKey = null;
            }
        }
    }

    public string ApiKey
    {
        get => _model.UserApiKey;
        set
        {
            if (_model.UserApiKey != value)
            {
                _model.UserApiKey = value;
                Modified = true;
                OnPropertyChanged(nameof(ApiKey));
            }
        }
    }

    public bool ShouldPresentApiKeyInput => _model.AuthenticationType == ExporterProviderAuthenticationType.User;

    public bool Modified
    {
        get => _modified;
        set => SetProperty(ref _modified, value);
    }

    public ExternalServiceItemViewModel(ExporterProviderApiSettings model)
    {
        _model = model;
        var authTypes = model.AllowedAuthenticationTypes?.Select(s => new ExternalServiceItemAuthTypeViewModel(s)) ?? Enumerable.Empty<ExternalServiceItemAuthTypeViewModel>();
        AuthTypes = new ObservableCollection<ExternalServiceItemAuthTypeViewModel>(authTypes);
        RetrieveApiKeyCommand = new TraceableCommand(RetrieveApiKey, nameof(RetrieveApiKey));
    }

    public ExporterProviderApiSettings GetModel() => _model;

    private async void RetrieveApiKey()
    {
        await Launcher.OpenAsync(new Uri(_model.UserApiKeyRetrievalLink));
    }
}