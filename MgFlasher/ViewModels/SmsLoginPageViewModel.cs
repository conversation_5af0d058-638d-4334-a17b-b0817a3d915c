﻿using MgFlasher.Files;
using MgFlasher.Flasher.Client.Backend.Client.Contract;
using MgFlasher.Flasher.Client.Common;
using MgFlasher.Flasher.Client.Shop.Contract;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Localization;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services;
using System;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Maui.Controls;
using Microsoft.Maui;
using MgFlasher.Services.Navigation;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Bootstrap;
using System.Collections.Generic;

namespace MgFlasher.ViewModels;

public class SmsLoginPageViewModel : PageViewModel, IContextOptionsViewModel
{
    private readonly ShellTitleViewModel _shellTitleViewModel;
    private readonly IUserService _userService;
    private readonly INavigationService _navigationService;
    private readonly IConnectivityStatusAccessor _connectivityStatusAccessor;
    private readonly IUserDialogs _userDialogs;
    private readonly IAppContextService _appContextService;
    private readonly IApplicationBootstrapService _applicationBootstrapService;
    private string _phoneNumber;
    private string _errorMessage;
    private string _verificationCode;
    private string _loginButtonText;
    private bool _verificationCodeVisible;
    private bool _loginErrorOccured;
    private bool _isPhoneNumberValid;
    private LoginPageNavigationArgs _args;
    private Dictionary<string, Action> _contextOptions;

    public bool IsPhoneNumberValid
    {
        get => _isPhoneNumberValid;
        set
        {
            SetProperty(ref _isPhoneNumberValid, value);
            OnPropertyChanged(nameof(CanLogin));
        }
    }

    public string PhoneNumber
    {
        get => _phoneNumber;
        set
        {
            SetProperty(ref _phoneNumber, value);
            OnPropertyChanged(nameof(CanLogin));
        }
    }

    public string Message
    {
        get => _errorMessage;
        private set
        {
            SetProperty(ref _errorMessage, value);
        }
    }

    public string LoginButtonText
    {
        get => _loginButtonText;
        private set
        {
            SetProperty(ref _loginButtonText, value);
        }
    }

    public bool HasMessage
    {
        get => _loginErrorOccured;
        private set
        {
            SetProperty(ref _loginErrorOccured, value);
        }
    }

    public bool VerificationCodeVisible
    {
        get => _verificationCodeVisible;
        private set
        {
            SetProperty(ref _verificationCodeVisible, value);
            OnPropertyChanged(nameof(CanLogin));
        }
    }

    public string VerificationCode
    {
        get => _verificationCode;
        set
        {
            SetProperty(ref _verificationCode, value);
            OnPropertyChanged(nameof(CanLogin));
        }
    }

    public bool CanLogin =>
        !string.IsNullOrEmpty(_phoneNumber) && _isPhoneNumberValid &&
        (!VerificationCodeVisible ||
         (VerificationCodeVisible && !string.IsNullOrEmpty(_verificationCode))) &&
        !IsBusy;

    public Dictionary<string, Action> ContextOptions
    {
        get => _contextOptions;
        private set => SetProperty(ref _contextOptions, value);
    }

    public ICommand LoginCommand { get; }
    public ICommand ResendCommand { get; }
    public ICommand MiscCommand { get; }

    public SmsLoginPageViewModel(
        ShellTitleViewModel shellTitleViewModel,
        IUserService userService,
        INavigationService navigationService,
        IConnectivityStatusAccessor connectivityStatusAccessor,
        IUserDialogs userDialogs,
        IAppContextService appContextService,
        IApplicationBootstrapService applicationBootstrapService)
    {
        _shellTitleViewModel = shellTitleViewModel;
        _userService = userService;
        _navigationService = navigationService;
        _connectivityStatusAccessor = connectivityStatusAccessor;
        _userDialogs = userDialogs;
        _appContextService = appContextService;
        _applicationBootstrapService = applicationBootstrapService;
        LoginCommand = new TraceableCommand(Login, nameof(Login));
        ResendCommand = new TraceableCommand(Resend, nameof(Resend));
        MiscCommand = new TraceableCommand(Misc, nameof(Misc));

        InitContextOptions();
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        SetArgs(arg);

        IsBusy = false;

        HideVerificationCode();
        SetMessage(null);

        return base.OnNavigatedToAsync(previousPage, arg);
    }

    private void SetArgs(object arg)
    {
        _args = (arg as LoginPageNavigationArgs) ?? _args ?? new LoginPageNavigationArgs()
        {
            NextPage = PageType.MyCars
        };
    }

    private void InitContextOptions()
    {
        var options = new Dictionary<string, Action>
        {
            [AppResources.Login_SwitchToEmail] = SwitchToEmailLoginPage
        };

        ContextOptions = options;
    }

    private async void Login()
    {
        try
        {
            SetMessage(AppResources.Login_InProgress);
            SetIsBusyWithCommandExecutionNotification(true);

            if (!await _connectivityStatusAccessor.IsConnected())
            {
                SetMessage(null);
                await _userDialogs.AlertAsync(message: AppResources.InternetConnectionRequired, okText: AppResources.Ok);

                return;
            }

            if (VerificationCodeVisible)
            {
                await SignIn();
            }
            else
            {
                await AuthenticateBySms();
            }
        }
        catch (UserAuthenticationException ex) when (ex.LoginResult == CustomerLoginResults.CustomerNotExist)
        {
            SetMessage(AppResources.Login_InvalidVerificationCode);
        }
        catch (UserAuthenticationException ex)
        {
            SetMessage(ex.Message);
        }
        catch (Exception)
        {
            SetMessage(AppResources.Error_Occured);
        }
        finally
        {
            SetIsBusyWithCommandExecutionNotification(false);
        }
    }

    private async void Resend()
    {
        try
        {
            SetMessage(AppResources.Login_Resend_InProgress);
            SetIsBusyWithCommandExecutionNotification(true);

            if (!await _connectivityStatusAccessor.IsConnected())
            {
                await _userDialogs.AlertAsync(message: AppResources.InternetConnectionRequired, okText: AppResources.Ok);

                return;
            }

            await AuthenticateBySms();
        }
        catch (Exception)
        {
            SetMessage(AppResources.Error_Occured);
        }
        finally
        {
            SetIsBusyWithCommandExecutionNotification(false);
        }
    }

    private void Misc(object obj)
    {
        _shellTitleViewModel.ContextOptionsCommand.Execute(null);
    }

    private void SwitchToEmailLoginPage()
    {
        _appContextService.SetRetailMode(RetailModeEnum.MG_Flasher);
        Application.Current.MainPage = _applicationBootstrapService.GetStartupPage();
        Shell.Current.FlyoutBehavior = FlyoutBehavior.Disabled;
    }

    private async Task AuthenticateBySms()
    {
        var result = await _userService.AuthenticateBySms(PhoneNumber);

        if (result == SmsAuthenticationStatus.VerificationPending)
        {
            ShowVerificationCode();
        }
        else
        {
            HideVerificationCode();
        }

        SetMessage(TranslateHelper.GetString($"SMS_Login_AuthStatus_{result}"));
    }

    private async Task SignIn()
    {
        await _userService.SignInBySmsProvider(PhoneNumber, VerificationCode);
        await _navigationService.NavigateAsync(_args.NextPage, _args.NextPageArguments);
        SetMessage(null);
    }

    private void ShowVerificationCode()
    {
        VerificationCode = null;
        VerificationCodeVisible = true;
        LoginButtonText = AppResources.Login_Button;
    }

    private void HideVerificationCode()
    {
        VerificationCode = null;
        VerificationCodeVisible = false;
        LoginButtonText = AppResources.Login_Button_SendVerificationCode;
    }

    private void SetMessage(string error)
    {
        HasMessage = !string.IsNullOrEmpty(error);
        Message = error;
    }

    private void SetIsBusyWithCommandExecutionNotification(bool value)
    {
        IsBusy = value;
        OnPropertyChanged(nameof(CanLogin));
    }
}