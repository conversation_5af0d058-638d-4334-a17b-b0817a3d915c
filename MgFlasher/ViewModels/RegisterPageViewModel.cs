﻿using MgFlasher.Flasher.Client.Common;
using MgFlasher.Flasher.Client.Shop.Contract;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Services.Navigation;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels;

public class RegisterPageViewModel : PageViewModel
{
    private readonly IUserService _userService;
    private readonly INavigationService _navigationService;
    private readonly IConnectivityStatusAccessor _connectivityStatusAccessor;
    private readonly IUserDialogs _userDialogs;
    private readonly ILogger<RegisterPageViewModel> _logger;
    private string _email;
    private string _password;
    private string _firstName;
    private string _lastName;
    private string _phone;
    private bool _isEmailValid;
    private LoginPageNavigationArgs _args;

    public bool IsEmailValid
    {
        get => _isEmailValid;
        set
        {
            SetProperty(ref _isEmailValid, value);
            OnPropertyChanged(nameof(CanRegister));
        }
    }

    public string Email
    {
        get => _email;
        set
        {
            SetProperty(ref _email, value);
            OnPropertyChanged(nameof(CanRegister));
        }
    }

    public string Password
    {
        get => _password;
        set
        {
            SetProperty(ref _password, value);
            OnPropertyChanged(nameof(CanRegister));
        }
    }

    public string FirstName
    {
        get => _firstName;
        set
        {
            SetProperty(ref _firstName, value);
            OnPropertyChanged(nameof(CanRegister));
        }
    }

    public string LastName
    {
        get => _lastName;
        set
        {
            SetProperty(ref _lastName, value);
            OnPropertyChanged(nameof(CanRegister));
        }
    }

    public string Phone
    {
        get => _phone;
        set
        {
            SetProperty(ref _phone, value);
            OnPropertyChanged(nameof(CanRegister));
        }
    }

    public bool CanRegister =>
        !string.IsNullOrEmpty(_email) && _isEmailValid &&
        !string.IsNullOrEmpty(_firstName) &&
        !string.IsNullOrEmpty(_lastName) &&
        !string.IsNullOrEmpty(_phone) &&
        !string.IsNullOrEmpty(_password) &&
        !IsBusy;

    public ICommand RegisterCommand { get; }

    public RegisterPageViewModel(
        IUserService userService,
        INavigationService navigationService,
        IConnectivityStatusAccessor connectivityStatusAccessor,
        IUserDialogs userDialogs,
        ILogger<RegisterPageViewModel> logger)
    {
        _userService = userService;
        _navigationService = navigationService;
        _connectivityStatusAccessor = connectivityStatusAccessor;
        _userDialogs = userDialogs;
        _logger = logger;
        RegisterCommand = new TraceableCommand(Register, nameof(Register));
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _args = (LoginPageNavigationArgs)arg;

        return base.OnNavigatedToAsync(previousPage, arg);
    }

    private async void Register()
    {
        try
        {
            SetIsBusyWithCommandExecutionNotification(true);

            if (!await _connectivityStatusAccessor.IsConnected())
            {
                await _userDialogs.AlertAsync(message: AppResources.InternetConnectionRequired, okText: AppResources.Ok);

                return;
            }

            var userRegistrationType = await RegisterUser();

            await HandleRegistrationSuccess(userRegistrationType);
        }
        catch (UserRegistrationException ex)
        {
            await _userDialogs.AlertAsync(string.Format(AppResources.Error_RegistrationError, string.Join("\r\n", ex.Errors)), AppResources.Error, AppResources.Ok);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error!");
            await _userDialogs.AlertAsync(AppResources.Error_Occured, AppResources.Error, AppResources.Ok);
        }
        finally
        {
            SetIsBusyWithCommandExecutionNotification(false);
        }
    }

    private async Task HandleRegistrationSuccess(UserRegistrationType userRegistrationType)
    {
        if (userRegistrationType == UserRegistrationType.Standard)
        {
            await _userDialogs.AlertAsync(message: AppResources.Register_Succesful, okText: AppResources.Ok);

            await _navigationService.NavigateAsync(_args.NextPage, _args.NextPageArguments);
        }
        else if (userRegistrationType == UserRegistrationType.AdminApproval)
        {
            await _userDialogs.AlertAsync(message: AppResources.Register_AdminApproval, okText: AppResources.Ok);
        }
        else if (userRegistrationType == UserRegistrationType.EmailValidation)
        {
            await _userDialogs.AlertAsync(message: AppResources.Register_EmailVerification, okText: AppResources.Ok);
        }
        else
        {
            throw new InvalidOperationException("Not supported registration type");
        }

        _args.Email = Email;
        _args.Password = Password;

        await _navigationService.NavigateAsync(PageType.Login, _args);
    }

    private async Task<UserRegistrationType> RegisterUser()
    {
        using var loading = _userDialogs.Loading(AppResources.Register_InProgress);
        var registrationRequest = CreateRegistrationRequest();

        return await _userService.Register(registrationRequest);
    }

    private void SetIsBusyWithCommandExecutionNotification(bool value)
    {
        IsBusy = value;
        OnPropertyChanged(nameof(CanRegister));
    }

    private RegisterCustomerRequest CreateRegistrationRequest()
    {
        return new RegisterCustomerRequest()
        {
            FirstName = FirstName,
            LastName = LastName,
            Phone = Phone,
            Credentials = new UserCredentials()
            {
                Email = Email,
                Password = Password
            }
        };
    }
}