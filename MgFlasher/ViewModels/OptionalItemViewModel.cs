﻿using MgFlasher.Models;
using System;
using System.Windows.Input;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.ViewModels;

public class OptionalItemViewModel : BaseViewModel, IFilterableViewModel
{
    private bool _isChecked;
    private string _option;
    private readonly Action<OptionalItemViewModel> _selected;

    public ICommand CheckCommand { get; }

    public string Option
    {
        get => _option;
        set
        {
            // This is a bad hack, because both CollectionView and SfListView set the options to null, when an item on the list
            // is scrolled outside of the view. I think its because it is reusing cells when scrolling, can't find a proper fix.
            // Happens only on IOS.
            if (value is not null)
            {
                SetProperty(ref _option, value);
            }
        }
    }

    public bool IsChecked
    {
        get => _isChecked;
        set
        {
            SetProperty(ref _isChecked, value);
            if (value)
                _selected?.Invoke(this);
        }
    }

    string IFilterableViewModel.DisplayName => Option;

    public OptionalItemViewModel(string option, Action<OptionalItemViewModel> selected = null)
    {
        _selected = selected;

        Option = option;
        IsChecked = false;
        CheckCommand = new TraceableCommand(() => IsChecked = !IsChecked, "Check");
    }
}