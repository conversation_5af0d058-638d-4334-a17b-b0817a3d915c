﻿using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using Microsoft.Maui.Controls;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels;

public class KnowledgeBasePageViewModel : PageViewModel, IContextOptionsViewModel
{
    private readonly INavigationService _navigationService;
    private readonly IUserDialogs _userDialogs;

    private IProgressDialog _loader;
    private bool _allowToPresentLoader;

    public Dictionary<string, Action> ContextOptions { get; }

    public string Url => "https://backend.net.mgflasher.com/knowledge-base-webview.html";

    public ICommand OpenLoaderCommand { get; }
    public ICommand CloseLoaderCommand { get; }
    public ICommand ReloadCommand { get; }

    public KnowledgeBasePageViewModel(INavigationService navigationService, IUserDialogs userDialogs)
    {
        _navigationService = navigationService;
        _userDialogs = userDialogs;
        ContextOptions = new Dictionary<string, Action>()
        {
            [AppResources.KnowledgeBasePage_Misc_Reload] = () => Reload(null),
            [AppResources.KnowledgeBasePage_Misc_GoToFeedbackPage] = () => _navigationService.NavigateAsync(PageType.Feedback)
        };
        OpenLoaderCommand = new TraceableCommand(OpenLoader, nameof(OpenLoader));
        CloseLoaderCommand = new TraceableCommand(CloseLoader, nameof(CloseLoader));
        ReloadCommand = new TraceableCommand(Reload, nameof(Reload));
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _allowToPresentLoader = true;
        _loader?.Dispose();
        return base.OnNavigatedToAsync(previousPage, arg);
    }

    private async void CloseLoader(object obj)
    {
        var args = obj as WebNavigatedEventArgs;
        _loader?.Dispose();
        if (_allowToPresentLoader && args?.Result != Microsoft.Maui.WebNavigationResult.Success)
        {
            await _userDialogs.AlertAsync(AppResources.KnowledgeBasePage_ErrorLoadingPage);
        }
        _allowToPresentLoader = false;
    }

    private void OpenLoader(object obj)
    {
        if (!_allowToPresentLoader)
            return;
        _loader = _userDialogs.Loading(AppResources.Loading);
    }

    private void Reload(object obj)
    {
        _allowToPresentLoader = true;
        OnPropertyChanged(nameof(Url));
    }
}