﻿using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Localization.Resources;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using MgFlasher.Flasher.Commands.Diagnostics.Generic;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Helpers;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels;

public class ResetAdaptationPageViewModel : BaseOptionsPageViewModel
{
    private IResetAdaptationCommand _resetAdaptationCommand;
    private readonly IUserDialogs _userDialogs;

    public ResetAdaptationPageViewModel(
        INavigationService navigationService,
        IUserDialogs userDialogs) : base(navigationService)
    {
        _userDialogs = userDialogs;
    }

    protected override async void Submit(IEnumerable<OptionalItemViewModel> selectedOptions, INavigationService navigationService)
    {
        _resetAdaptationCommand.Init(selectedOptions?.Select(so => so.Option));

        using (var loading = _userDialogs.Loading(AppResources.Adaptions_Reset_InProgress))
        {
            await _resetAdaptationCommand.ExecuteAsync();
        }

        var result = _resetAdaptationCommand.GetResult();

        await _userDialogs.AlertAsync(result);
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (arg is IResetAdaptationCommand resetAdaptationCommand)
        {
            _resetAdaptationCommand = resetAdaptationCommand;
            SetOptions(_resetAdaptationCommand.GetCustomOptions());
        }

        return base.OnNavigatedToAsync(previousPage, arg);
    }
}