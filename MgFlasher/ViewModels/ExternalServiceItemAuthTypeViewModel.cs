﻿using MgFlasher.Flasher.Services.CarLogger.Exporters.Models;
using MgFlasher.Localization;

namespace MgFlasher.ViewModels;

public class ExternalServiceItemAuthTypeViewModel
{
    public ExporterProviderAuthenticationType Type { get; }
    public string Translation { get; }

    public ExternalServiceItemAuthTypeViewModel(ExporterProviderAuthenticationType type)
    {
        Type = type;
        Translation = TranslateHelper.GetString($"ExternalServiceAuthType_{type}");
    }
}