﻿using MgFlasher.Flasher.Commands.Models;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.Services.Navigation;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MgFlasher.ViewModels;

public class ProcessCompletedPageViewModel : PageViewModel
{
    private readonly INavigationService _navigationService;
    private CommandResultType _result;
    private string _message;
    private bool _success;

    public CommandResultType Result
    {
        get => _result;
        private set => SetProperty(ref _result, value);
    }

    public string Message
    {
        get => _message;
        private set => SetProperty(ref _message, value);
    }

    public bool Success
    {
        get => _success;
        private set => SetProperty(ref _success, value);
    }

    public ICommand BackToMainMenuCommand { get; }

    public ProcessCompletedPageViewModel(INavigationService navigationService)
    {
        _navigationService = navigationService;
        BackToMainMenuCommand = new TraceableCommand(BackToMainMenu, nameof(BackToMainMenu));
    }

    public override async Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        if (arg is CommandResult commandResult)
        {
            Result = commandResult.Result;
            Message = commandResult.Message;
        }
        Success = Result == CommandResultType.Completed;
        await base.OnNavigatedToAsync(previousPage, arg);
    }

    private async void BackToMainMenu()
    {
        await _navigationService.NavigateAsync(PageType.MyCar);
    }
}