﻿using MgFlasher.Services;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Input;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels;

public abstract class BaseOptionsPageViewModel : PageViewModel
{
    private readonly INavigationService _navigationService;

    private ObservableCollection<OptionalItemViewModel> _optionalItemViewModels;

    public ObservableCollection<OptionalItemViewModel> OptionalItemViewModels
    {
        get => _optionalItemViewModels;
        protected set => SetProperty(ref _optionalItemViewModels, value);
    }

    public ICommand SubmitCommand { get; }
    public ICommand MarkAllCommand { get; }

    public BaseOptionsPageViewModel(INavigationService navigationService)
    {
        _navigationService = navigationService;
        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));
        MarkAllCommand = new TraceableCommand(MarkAll, nameof(MarkAll));
    }

    protected void SetOptions(IEnumerable<string> customOptions)
    {
        var vms = customOptions.Select(s => new OptionalItemViewModel(s));
        OptionalItemViewModels = new ObservableCollection<OptionalItemViewModel>(vms);
    }

    protected abstract void Submit(IEnumerable<OptionalItemViewModel> selectedOptions, INavigationService navigationService);

    private void Submit()
    {
        try
        {
            IsBusy = true;

            var selectedOptions = OptionalItemViewModels.Where(s => s.IsChecked);
            Submit(selectedOptions, _navigationService);
        }
        finally
        {
            IsBusy = false;
        }
    }

    private void MarkAll()
    {
        foreach (var item in OptionalItemViewModels)
        {
            item.IsChecked = true;
        }
    }
}