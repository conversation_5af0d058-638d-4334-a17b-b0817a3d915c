﻿using MgFlasher.Flasher.Client.Common;
using MgFlasher.Flasher.Client.Shop.Contract;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using System;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.ViewModels;

public class ForgotPasswordPageViewModel : PageViewModel
{
    private readonly IUserService _userService;
    private readonly IConnectivityStatusAccessor _connectivityStatusAccessor;
    private readonly IUserDialogs _userDialogs;
    private string _email;
    private string _message;
    private bool _isEmailValid;
    private bool _hasMessage;
    private bool _completed;

    public bool IsEmailValid
    {
        get => _isEmailValid;
        set
        {
            SetProperty(ref _isEmailValid, value);
            OnPropertyChanged(nameof(CanRecover));
        }
    }

    public bool Completed
    {
        get => _completed;
        set
        {
            SetProperty(ref _completed, value);
            OnPropertyChanged(nameof(CanRecover));
        }
    }

    public string Email
    {
        get => _email;
        set
        {
            SetProperty(ref _email, value);
            OnPropertyChanged(nameof(CanRecover));
        }
    }

    public bool HasMessage
    {
        get => _hasMessage;
        private set
        {
            SetProperty(ref _hasMessage, value);
        }
    }

    public string Message
    {
        get => _message;
        private set
        {
            SetProperty(ref _message, value);
        }
    }

    public bool CanRecover =>
        !string.IsNullOrEmpty(_email) &&
        _isEmailValid &&
        !_completed &&
        !IsBusy;

    public ICommand RecoverCommand { get; }

    public ForgotPasswordPageViewModel(
        IUserService userService,
        IConnectivityStatusAccessor connectivityStatusAccessor,
        IUserDialogs userDialogs)
    {
        _userService = userService;
        _connectivityStatusAccessor = connectivityStatusAccessor;
        _userDialogs = userDialogs;
        RecoverCommand = new TraceableCommand(Recover, nameof(Recover));
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        IsBusy = false;
        Completed = false;
        Email = (arg as LoginPageNavigationArgs)?.Email ?? Email;
        SetMessage(null);

        return base.OnNavigatedToAsync(previousPage, arg);
    }

    private async void Recover()
    {
        try
        {
            SetMessage(null);
            SetIsBusyWithCommandExecutionNotification(true);

            if (!await _connectivityStatusAccessor.IsConnected())
            {
                await _userDialogs.AlertAsync(message: AppResources.InternetConnectionRequired, okText: AppResources.Ok);

                return;
            }

            RecoverPasswordStatus result;

            using (var loading = _userDialogs.Loading(AppResources.Loading))
            {
                result = await _userService.RecoverPassword(Email);
            }

            SetMessage(result == RecoverPasswordStatus.Success ? AppResources.PasswordRecoveryInitialized : AppResources.EmailNotFound);

            Completed = result == RecoverPasswordStatus.Success;
        }
        catch (Exception)
        {
            SetMessage(AppResources.Error_Occured);
        }
        finally
        {
            SetIsBusyWithCommandExecutionNotification(false);
        }
    }

    private void SetMessage(string message)
    {
        HasMessage = !string.IsNullOrEmpty(message);
        Message = message;
    }

    private void SetIsBusyWithCommandExecutionNotification(bool value)
    {
        IsBusy = value;
        OnPropertyChanged(nameof(CanRecover));
    }
}