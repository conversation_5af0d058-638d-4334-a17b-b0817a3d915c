﻿using MgFlasher.Localization.Resources;
using MgFlasher.Services;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.Models;
using MgFlasher.Flasher.Services.Cars.Sync;
using CommunityToolkit.Mvvm.Messaging;
using MgFlasher.Helpers;
using MgFlasher.Flasher.Commands.Models;
using MgFlasher.Ecu.Common.Extensions;
using MgFlasher.Flasher.Services.Cars.Sync.Chains;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Services.Navigation;

namespace MgFlasher.ViewModels;

public class SyncPageViewModel : PageViewModel
{
    private readonly FullSyncChainFactory _fullSyncChainFactory;
    private readonly INavigationService _navigationService;
    private readonly ISyncChainExecutor _syncChain;
    private readonly IUserDialogs _userDialogs;
    private readonly ILogger<SyncPageViewModel> _logger;
    private PageType _previousPage;
    private SyncPageNavigationArgs _args;
    private bool _syncing;

    public ObservableCollection<string> SyncAdvantages => new ObservableCollection<string>()
    {
        AppResources.SyncPage_Advantage_1,
        AppResources.SyncPage_Advantage_2,
        AppResources.SyncPage_Advantage_3,
        AppResources.SyncPage_Advantage_4
    };

    public ICommand SubmitCommand { get; }

    public SyncPageViewModel(
        FullSyncChainFactory fullSyncChainFactory,
        INavigationService navigationService,
        ISyncChainExecutor syncChain,
        IUserDialogs userDialogs,
        ILogger<SyncPageViewModel> logger)
    {
        _fullSyncChainFactory = fullSyncChainFactory;
        _navigationService = navigationService;
        _syncChain = syncChain;
        _userDialogs = userDialogs;
        _logger = logger;
        SubmitCommand = new TraceableCommand(Submit, nameof(Submit));
    }

    public override Task OnNavigatedToAsync(PageType previousPage, object arg)
    {
        _args = (arg as SyncPageNavigationArgs) ?? _args ?? new SyncPageNavigationArgs(_fullSyncChainFactory);
        if (!previousPage.In(PageType.SyncPage, PageType.Pending))
        {
            _previousPage = previousPage;
        }
        return Task.CompletedTask;
    }

    private async void Submit()
    {
        if (_syncing)
        {
            return;
        }

        _syncing = true;

        var pendingPageArgs = PendingPageNavigationArgs.InProgress(_previousPage);
        await _navigationService.NavigateAsync(PageType.Pending, pendingPageArgs);

        void OnProgressChanged(object sender, SyncChainProgressEventArgs args)
        {
            WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageUpdateText, new PendingPageUpdateArgs(args.CurrentLinkTitle, args.Progress)));
        }

        try
        {
            _syncChain.ProgressChanged += OnProgressChanged;
            var result = await _syncChain.SyncAsync(_args.SyncChainFactory, _args.Context);
            if (result.Success)
            {
                await _userDialogs.AlertAsync(AppResources.Car_Sync_Success, okText: AppResources.Ok);
                WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageCloseNavigation));
            }
            else
            {
                await ShowErrorUserMessage(result.Error);
                WeakReferenceMessenger.Default.Send(new MessagingCenterIdentifiers.PendingPageMessage(MessagingCenterIdentifiers.PendingPageBackNavigation));
            }
        }
        finally
        {
            _syncChain.ProgressChanged -= OnProgressChanged;
            _syncing = false;
        }
    }

    private async Task ShowErrorUserMessage(string message)
    {
        _logger.LogWarning("Sync Failed with msg: {msg}", message);

        var commandResult = new CommandResult()
        {
            Message = message,
            Result = CommandResultType.Error
        };

        await _userDialogs.AlertAsync(commandResult);
    }
}