﻿using CommunityToolkit.Mvvm.Messaging.Messages;
using MgFlasher.Models;

namespace MgFlasher.Helpers;

public static class MessagingCenterIdentifiers
{
    public const string PendingPageCloseNavigation = "PendingPageCloseNavigationMessage";
    public const string PendingPageBackNavigation = "PendingPageBackNavigationMessage";
    public const string PendingPageUpdateText = "PendingPageUpdateNavigation";

    public class PendingPageMessage : ValueChangedMessage<object>
    {
        public string Key { get; }

        public PendingPageMessage(string key, object value = null) : base(value)
        {
            Key = key;
        }
    }

    public class FlashingCarLogMessage : ValueChangedMessage<EventMessageModel>
    {
        public FlashingCarLogMessage(EventMessageModel value) : base(value)
        {
        }
    }
}