﻿using System.Threading.Tasks;
using MgFlasher.Flasher.Commands.Models;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;

namespace MgFlasher.Helpers;

public static class IUserDialogsExtensions
{
    public static async Task AlertAsync(this IUserDialogs userDialogs, CommandResult result)
    {
        result ??= new CommandResult(AppResources.Error_Occured, CommandResultType.Error);
        await userDialogs.AlertAsync(result.Message, title: GetAlertTitle(result.Result), AppResources.Ok);
    }

    private static string GetAlertTitle(CommandResultType result) => result switch
    {
        CommandResultType.Warning => AppResources.Warning,
        CommandResultType.Error => AppResources.Error,
        CommandResultType.Completed => AppResources.Success,
        _ => null
    };
}