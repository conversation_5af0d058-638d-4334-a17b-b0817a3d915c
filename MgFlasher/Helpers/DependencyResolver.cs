﻿using MgFlasher.Flasher.Services;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Services;
using MgFlasher.ViewModels;
using MgFlasher.Files;
using MgFlasher.Flasher.Client.Backend;
using MgFlasher.Logging;
using MgFlasher.Bootstrap;
using MgFlasher.Flasher.Client.Shop;
using MgFlasher.Flasher.Client.Common;
using MgFlasher.Flasher.Client.Logs;
using MgFlasher.Flasher.Client.Spoolstreet;
using MgFlasher.ViewModels.Logger;
using MgFlasher.ViewModels.Logger.Services;
using System;
using MgFlasher.ViewModels.FlashingCarOptions;
using Microsoft.Extensions.DependencyInjection;
using MgFlasher.Flasher.Commands;
using MgFlasher.Flasher.Services.CarLogger.Alerts;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Services.Notifications;
using Plugin.Maui.Audio;
using MgFlasher.ViewModels.CustomCodeDiagnostics;
using MgFlasher.ViewModels.MyCar;
using MgFlasher.Services.Navigation;
using MgFlasher.Flasher.Client.BackendNet;

namespace MgFlasher.Helpers;

public static class DependencyResolver
{
    private static IServiceProvider _container;

    public static bool Initialized { get; private set; }

    public static void Init(IServiceProvider services)
    {
        _container = services;
        Initialized = true;
    }

    public static void Register(IServiceCollection services)
    {
        services.AddFilesModule();
        services.AddLoggingModule();
        services.AddServicesModule();
        services.AddCommandsModule();

        RegisterClients(services);
        RegisterUIServices(services);
        RegisterUICommands(services);
        RegisterPageViewModels(services);
    }

    public static object TryResolve(Type @type)
    {
        try
        {
            return _container.GetService(@type);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public static T Resolve<T>()
    {
        try
        {
            return _container.GetRequiredService<T>();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    private static void RegisterPageViewModels(IServiceCollection builder)
    {
        builder.AddSingleton<PageLoaderViewModel>();
        builder.AddSingleton<AntilagDiagnosticsPageViewModel>();
        builder.AddSingleton<BurbleDiagnosticsPageViewModel>();
        builder.AddSingleton<EthanolOverrideDiagnosticsPageViewModel>();
        builder.AddSingleton<ExhaustFlapDiagnosticsPageViewModel>();
        builder.AddSingleton<RadiatorFlapDiagnosticsPageViewModel>();
        builder.AddSingleton<MaxCoolinDiagnosticsPageViewModel>();
        builder.AddSingleton<ValetModeDiagnosticsPageViewModel>();
        builder.AddSingleton<SwitchableMapDiagnosticsPageViewModel>();
        builder.AddSingleton<UserDtcRemovalListPageViewModel>();
        builder.AddSingleton<FlashingCarOptionsPageViewModel>();
        builder.AddSingleton<FlashingCarOptionsItemPageViewModel>();
        builder.AddSingleton<FlashingCarPageViewModel>();
        builder.AddSingleton<FlashingCarWarningInfoPageViewModel>();
        builder.AddSingleton<ILoggerSleeper>(x => x.GetRequiredService<MyCarLoggerViewModel>());
        builder.AddSingleton<ProcessCompletedPageViewModel>();
        builder.AddSingleton<ReadDtcPageViewModel>();
        builder.AddSingleton<ResetAdaptationPageViewModel>();
        builder.AddSingleton<SideMenuViewModel>();
        builder.AddSingleton<BuyFlashStagePageViewModel>();
        builder.AddSingleton<PendingPageViewModel>();
        builder.AddSingleton<ReadModulePageViewModel>();
        builder.AddSingleton<AboutViewModel>();
        builder.AddSingleton<CustomMapFilesPageViewModel>();
        builder.AddSingleton<LoginPageViewModel>();
        builder.AddSingleton<SmsLoginPageViewModel>();
        builder.AddSingleton<ForgotPasswordPageViewModel>();
        builder.AddSingleton<RegisterPageViewModel>();
        builder.AddSingleton<UserPageViewModel>();
        builder.AddSingleton<FeedbackPageViewModel>();
        builder.AddSingleton<KnowledgeBasePageViewModel>();
        builder.AddSingleton<StageVersionsPageViewModel>();
        builder.AddSingleton<SyncPageViewModel>();
        builder.AddSingleton<SettingsPageViewModel>();
        builder.AddSingleton<ShellTitleViewModel>();
        builder.AddSingleton<CustomCodeVersionsPageViewModel>();
        builder.AddSingleton<LoggerParametersPageViewModel>();
        builder.AddSingleton<LoggerUnitsPageViewModel>();
        builder.AddSingleton<LoggerParametersPreferencesViewModel>();
        builder.AddSingleton<LoggerDisplayPageViewModel>();
        builder.AddSingleton<LoggerDisplayPreferencesViewModel>();
        builder.AddSingleton<LoggerDisplayGaugesViewModel>();
        builder.AddSingleton<LoggerDisplayGaugeItemPageViewModel>();
        builder.AddSingleton<LoggerGaugesDashboardViewModel>();
        builder.AddSingleton<LoggerParametersModulesViewModel>();
        builder.AddSingleton<LoggerReplaceGaugeItemPageViewModel>();
        builder.AddSingleton<LoggerAlertsPageViewModel>();
        builder.AddSingleton<LoggerAlertsModulesViewModel>();
        builder.AddSingleton<LoggerAlertsPreferencesViewModel>();
        builder.AddSingleton<LoggerAlertItemPageViewModel>();
        builder.AddSingleton<MyCarPageViewModel>();
        builder.AddSingleton<MyCarsPageViewModel>();
        builder.AddSingleton<MyCarHomeViewModel>();
        builder.AddSingleton<MyCarHomeOverviewViewModel>();
        builder.AddSingleton<MyCarHomeDetailsViewModel>();
        builder.AddSingleton<MyCarFlashViewModel>();
        builder.AddSingleton<MyCarHistoryViewModel>();
        builder.AddSingleton<MyCarFlashHistoryViewModel>();
        builder.AddSingleton<MyCarFlashHistoryItemDetailPageViewModel>();
        builder.AddSingleton<MyCarLoggerFilesViewModel>();
        builder.AddSingleton<MyCarLoggerViewModel>();
        builder.AddSingleton<MyCarDiagnosticsViewModel>();
        builder.AddSingleton<MyCarDiagnosticsGeneralViewModel>();
        builder.AddSingleton<MyCarDiagnosticsCustomViewModel>();

        builder.AddSingleton<LoggerFilePageViewModel>();
        builder.AddSingleton<LoggerFileGeneralViewModel>();
        builder.AddSingleton<LoggerFileChartViewModel>();
        builder.AddSingleton<LoggerFileMapViewModel>();
    }

    private static void RegisterUICommands(IServiceCollection builder)
    {
        builder.AddTransient<ShareStockMapCommand>();
    }

    private static void RegisterUIServices(IServiceCollection builder)
    {
        builder.AddPushNotifications();
        builder.AddSingleton<IApplicationBootstrapService, ApplicationBootstrapService>();
        builder.AddSingleton<IMapToFlashResolver, MapToFlashResolver>();
        builder.AddSingleton<IPicturePicker, PicturePicker>();
        builder.AddSingleton<INavigationService, NavigationService>();
        builder.AddSingleton<ITabComponentNavigationService, TabComponentNavigationService>();
        builder.AddSingleton<IPagesService, PagesService>();
        builder.AddSingleton<ICarMapPicker, CarMapPicker>();
        builder.AddSingleton<MauiUserDialogs>();
        builder.AddSingleton<IUserDialogs, TracableUserDialogs>();
        builder.AddSingleton<IStageDescriptionFactory, StageDescriptionFactory>();
        builder.AddSingleton<IGaugeItemViewModelsFactory, GaugeItemViewModelsFactory>();
        builder.AddSingleton<IChangesetVersionNameViewModelsFactory, ChangesetVersionNameViewModelsFactory>();
        builder.AddSingleton<IAudioManager>(AudioManager.Current);
        builder.AddSingleton<ISoundPlayer, SoundPlayer>();
        builder.AddSingleton<IWebAuthenticatorService, WebAuthenticatorService>();
        builder.AddSingleton<IAppVersionBlockService, AppVersionBlockService>();
        builder.AddSingleton<IAppUpdateService, AppUpdateService>();
    }

    private static void RegisterClients(IServiceCollection builder)
    {
        builder.AddBackendModule();
        builder.AddBackendNetModule();
        builder.AddShopModule();
        builder.AddClientCommonModule();
        builder.AddLogsCloudModule();
        builder.AddSpoolstreetModule();
    }
}