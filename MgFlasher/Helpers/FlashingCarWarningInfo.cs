﻿using MgFlasher.Flasher.Services.Models;
using MgFlasher.Localization.Resources;
using System.Collections.Generic;

namespace MgFlasher.Helpers;

public static class FlashingCarWarningInfo
{
    public static IEnumerable<string> GetWarningInfos(StageType stageType)
    {
        switch (stageType)
        {
            case StageType.Activated:
                foreach (var warning in AppResources.FlashingCarWarning_Unlock.Split('\n'))
                {
                    yield return warning.Trim();
                }
                break;
        }
        foreach (var warning in AppResources.FlashingCarWarning_General.Split('\n'))
        {
            yield return warning.Trim();
        }
    }

    public static IEnumerable<string> GetWarningInfosForRestore()
    {
        foreach (var warning in AppResources.FlashingCarWarning_RestoreBackup.Split('\n'))
        {
            yield return warning.Trim();
        }
        foreach (var warning in AppResources.FlashingCarWarning_General.Split('\n'))
        {
            yield return warning.Trim();
        }
    }
}