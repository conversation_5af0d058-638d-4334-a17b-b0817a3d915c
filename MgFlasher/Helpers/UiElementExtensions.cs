﻿using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using Syncfusion.Maui.TabView;
using System;
using System.Linq;

namespace MgFlasher.Helpers;

public static class UiElementExtensions
{
    public static T GetTabContext<T>(this SfTabView tabView, double index)
    {
        if (tabView.Items.ElementAtOrDefault((int)index) is not SfTabItem tabItem)
            return default;
        if (tabItem.Content.BindingContext is not T myCarPart)
            return default;
        return myCarPart;
    }

    public static TValue GetCustomNavigationAttributeValue<TValue>(this PageType enumVal, Func<CustomNavigationAttribute, TValue> valueSelector)
    {
        var type = enumVal.GetType();
        var memInfo = type.GetMember(enumVal.ToString());
        var att = memInfo[0]
            .GetCustomAttributes(typeof(CustomNavigationAttribute), false)
            .OfType<CustomNavigationAttribute>()
            .FirstOrDefault();

        if (att != null)
        {
            return valueSelector(att);
        }

        return default;
    }

    public static string GetLocalizedString(this FlashAllowanceProductType productType)
    {
        switch (productType)
        {
            case FlashAllowanceProductType.Flasher:
                return AppResources.ProductType_Flasher.ToUpper();

            case FlashAllowanceProductType.OtsBundle:
                return AppResources.ProductType_OtsMapBundle.ToUpper();

            case FlashAllowanceProductType.OtsStage1:
                return AppResources.Stage_Stage1.ToUpper();

            case FlashAllowanceProductType.OtsStage2:
                return AppResources.Stage_Stage2.ToUpper();

            case FlashAllowanceProductType.OtsStage2_5:
                return AppResources.Stage_Stage2_5.ToUpper();

            case FlashAllowanceProductType.Stock:
                return AppResources.Stage_Stock.ToUpper();

            case FlashAllowanceProductType.CustomMap:
                return AppResources.Stage_CustomMap.ToUpper();

            case FlashAllowanceProductType.Logger:
                return AppResources.Stage_Logger.ToUpper();

            case FlashAllowanceProductType.Ultimate:
                return AppResources.ProductType_Ultimate.ToUpper();

            default:
                throw new InvalidOperationException($"Cannot convert {productType} product type to text");
        }
    }

    public static string ToModuleName(this ModuleId moduleId) => moduleId switch
    {
        ModuleId.DME_MASTER => AppResources.GaugeModuleName_Master,
        ModuleId.DME_SLAVE => AppResources.GaugeModuleName_Slave,
        _ => throw new NotSupportedException($"Invalid module id {moduleId}")
    };
}