﻿using MgFlasher.Localization;
using MgFlasher.Localization.Resources;
using System;
using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Helpers;

[ContentProperty("Text")]
[AcceptEmptyServiceProvider]
public class TranslateExtension : IMarkupExtension
{
    public string Text { get; set; }

    public object ProvideValue(IServiceProvider serviceProvider)
    {
        if (Text == null)
            return "";

        return TranslateHelper.GetString(Text);
    }
}