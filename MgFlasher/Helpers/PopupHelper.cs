﻿using System;
using Microsoft.Maui.Controls;
using Microsoft.Maui;
using Syncfusion.Maui.Popup;
using Microsoft.Maui.Graphics;
using MgFlasher.Localization.Resources;

namespace MgFlasher.Helpers;

public static class PopupHelper
{
    public static SfPopup GenerateGuidedModePrompt(Action<string> handleResponse)
    {
        var headerBgColor = GetResourceColor("ItemSelectedHoverColor", Colors.DarkGrey);
        var popupBgColor = GetResourceColor("ListItemSeparatorColor", Colors.LightGray);

        var popup = new SfPopup
        {
            HeaderTitle = AppResources.WizardMode_Popup_PromptTitle,
            WidthRequest = 400,
            HeightRequest = 250,
            StaysOpen = false,
            ShowCloseButton = true,
            PopupStyle = new PopupStyle
            {
                BlurIntensity = PopupBlurIntensity.ExtraDark,
                HeaderBackground = headerBgColor,
                PopupBackground = popupBgColor,
                CornerRadius = new CornerRadius(5),
                HeaderTextColor = Colors.White
            },
            ContentTemplate = new DataTemplate(() =>
            {
                var layout = new VerticalStackLayout { Spacing = 10, Padding = 15 };
                var label = new Label
                {
                    Text = AppResources.WizardMode_Popup_Prompt,
                    HorizontalOptions = LayoutOptions.Center,
                    FontSize = 18,
                    TextColor = Colors.White
                };

                var buttonLayout = new HorizontalStackLayout { Spacing = 10, HorizontalOptions = LayoutOptions.Center };

                var yesButton = new Button
                {
                    Text = AppResources.Yes,
                    BackgroundColor = headerBgColor,
                    TextColor = Colors.White,
                    CornerRadius = 5
                };
                yesButton.Clicked += (s, e) => handleResponse(AppResources.Yes);

                var noButton = new Button
                {
                    Text = AppResources.No,
                    BackgroundColor = headerBgColor,
                    TextColor = Colors.White,
                    CornerRadius = 5
                };
                noButton.Clicked += (s, e) => handleResponse(AppResources.No);

                var dontShowAgainButton = new Button
                {
                    Text = AppResources.PopupTutorials_DontShowAgain,
                    BackgroundColor = headerBgColor,
                    TextColor = Colors.White,
                    CornerRadius = 5
                };
                dontShowAgainButton.Clicked += (s, e) => handleResponse(AppResources.PopupTutorials_DontShowAgain);

                buttonLayout.Children.Add(yesButton);
                buttonLayout.Children.Add(noButton);
                buttonLayout.Children.Add(dontShowAgainButton);

                layout.Children.Add(label);
                layout.Children.Add(buttonLayout);

                return layout;
            })
        };

        return popup;
    }

    private static Color GetResourceColor(string key, Color fallback)
    {
        try
        {
            if (Application.Current.Resources.TryGetValue(key, out var value) && value is Color color)
            {
                return color;
            }
        }
        catch (Exception)
        {
        }

        return fallback;
    }
}