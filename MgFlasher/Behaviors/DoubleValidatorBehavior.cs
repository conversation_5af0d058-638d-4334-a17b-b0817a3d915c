using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace MgFlasher.Behaviors;

public class DoubleValidatorBehavior : BehaviorBase<Entry>
{
    public static readonly BindableProperty MinProperty = BindableProperty.Create(nameof(Min), typeof(double), typeof(IntegerValidatorBehavior), 1d);

    public double Min
    {
        get { return (double)GetValue(MinProperty); }
        private set { SetValue(MinProperty, value); }
    }

    public static BindableProperty MaxProperty = BindableProperty.Create(nameof(Max), typeof(double), typeof(IntegerValidatorBehavior), 20d);

    public double Max
    {
        get { return (double)GetValue(MaxProperty); }
        private set { SetValue(MaxProperty, value); }
    }

    protected override void OnAttachedTo(Entry bindable)
    {
        base.OnAttachedTo(bindable);
        bindable.TextChanged += HandleTextChanged;
    }

    protected override void OnDetachingFrom(Entry bindable)
    {
        bindable.TextChanged -= HandleTextChanged;
        base.OnDetachingFrom(bindable);
    }

    private void HandleTextChanged(object sender, TextChangedEventArgs e)
    {
        var entry = sender as Entry;

        var valid = true;

        if (string.IsNullOrEmpty(e.NewTextValue))
        {
            valid = false;
        }
        else if (!double.TryParse(e.NewTextValue, out double value))
        {
            entry.Text = e.OldTextValue;
            valid = false;
        }
        else if (value < Min || value > Max)
        {
            entry.Text = e.OldTextValue;
            valid = false;
        }

        AssociatedObject.TextColor = valid ?
            (Color)Application.Current.Resources["PrimaryTextColor"] :
            (Color)Application.Current.Resources["PrimaryImportantTextColor"];
    }
}