﻿using MgFlasher.Localization.Resources;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace MgFlasher.Behaviors;

public class IntegerValidatorBehavior : BehaviorBase<Entry>
{
    public static readonly BindableProperty MinProperty = BindableProperty.Create(nameof(Min), typeof(int), typeof(IntegerValidatorBehavior), 1);

    public int Min
    {
        get { return (int)GetValue(MinProperty); }
        private set { SetValue(MinProperty, value); }
    }

    public static BindableProperty MaxProperty = BindableProperty.Create(nameof(Max), typeof(int), typeof(IntegerValidatorBehavior), 20);

    public int Max
    {
        get { return (int)GetValue(MaxProperty); }
        private set { SetValue(MaxProperty, value); }
    }

    protected override void OnAttachedTo(Entry bindable)
    {
        base.OnAttachedTo(bindable);
        bindable.TextChanged += HandleTextChanged;
    }

    protected override void OnDetachingFrom(Entry bindable)
    {
        bindable.TextChanged -= HandleTextChanged;
        base.OnDetachingFrom(bindable);
    }

    private void HandleTextChanged(object sender, TextChangedEventArgs e)
    {
        var entry = sender as Entry;

        var valid = true;

        if (string.IsNullOrEmpty(e.NewTextValue))
        {
            valid = false;
        }
        else if (!int.TryParse(e.NewTextValue, out int value))
        {
            entry.Text = e.OldTextValue;
            valid = false;
        }
        else if (value < Min || value > Max)
        {
            entry.Text = e.OldTextValue;
            valid = false;
        }

        entry.Placeholder = valid ?
            string.Empty :
            string.Format(AppResources.IntegerValidatorBehavior_Text, Min, Max);

        AssociatedObject.TextColor = valid ?
            (Color)Application.Current.Resources["PrimaryTextColor"] :
            (Color)Application.Current.Resources["PrimaryImportantTextColor"];
    }
}