using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using MgFlasher.Ecu.Common.Extensions;
using MgFlasher.Models;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Devices;
using Syncfusion.Maui.ListView;

namespace MgFlasher.Behaviors;

public class SfListViewAccordionBehavior : BehaviorBase<SfListView>
{
    public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameof(ItemsSource), typeof(object), typeof(SfListViewAccordionBehavior), propertyChanged: OnItemsSourceChanged);

    public object ItemsSource
    {
        get => GetValue(ItemsSourceProperty);
        set => SetValue(ItemsSourceProperty, value);
    }

    private static void OnItemsSourceChanged(BindableObject bindable, object oldvalue, object newvalue)
    {
        var behavior = (SfListViewAccordionBehavior)bindable;

        foreach (IExpandableViewModel vm in oldvalue as IEnumerable<IExpandableViewModel> ??
                                            Enumerable.Empty<IExpandableViewModel>())
        {
            vm.PropertyChanged -= behavior.OnPropertyChanged;
        }

        foreach (IExpandableViewModel vm in newvalue as IEnumerable<IExpandableViewModel> ??
                                            Enumerable.Empty<IExpandableViewModel>())
        {
            vm.PropertyChanged += behavior.OnPropertyChanged;
        }
    }

    private void OnPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (sender is not IExpandableViewModel vm || e.PropertyName != nameof(IExpandableViewModel.IsExpanded))
        {
            return;
        }

        if (vm.IsExpanded)
        {
            var previousIndex = AssociatedObject.DataSource.DisplayItems.IndexOf(vm);
            AssociatedObject.RefreshItem(previousIndex, previousIndex, CanReload());
        }
        else
        {
            var currentIndex = AssociatedObject.DataSource.DisplayItems.IndexOf(vm);
            AssociatedObject.RefreshItem(currentIndex, currentIndex, CanReload());
        }
    }

    private bool CanReload() => DeviceInfo.Platform.In(DevicePlatform.iOS);
}