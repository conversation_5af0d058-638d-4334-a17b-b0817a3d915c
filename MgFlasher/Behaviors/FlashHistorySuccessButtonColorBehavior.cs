﻿using MgFlasher.Localization.Resources;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace MgFlasher.Behaviors;

public class FlashHistorySuccessButtonColorBehavior : BehaviorBase<Button>
{
    public static readonly BindableProperty ValueProperty = BindableProperty.Create(nameof(Value), typeof(object), typeof(FlashHistorySuccessButtonColorBehavior), null, propertyChanged: OnPropertyChanged);

    public object Value
    {
        get { return GetValue(ValueProperty); }
        set { SetValue(ValueProperty, value); }
    }

    private static void OnPropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var behavior = bindable as FlashHistorySuccessButtonColorBehavior;
        if (newValue is not bool val || behavior is null)
        {
            return;
        }

        behavior.AssociatedObject.Text = val ?
            AppResources.FlashingHistoryPage_Item_Success :
            AppResources.FlashingHistoryPage_Item_Error;

        behavior.AssociatedObject.BackgroundColor = val ?
            (Color)App.Current.Resources["PrimarySuccessTextColor"] :
            (Color)App.Current.Resources["PrimaryImportantTextColor"];
    }
}