﻿using System.Windows.Input;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Behaviors;

public class PressedEffect : RoutingEffect
{
    public static readonly BindableProperty LongPressedCommandProperty = BindableProperty.CreateAttached("LongPressedCommand", typeof(ICommand), typeof(PressedEffect), null);
    public static readonly BindableProperty PressedCommandProperty = BindableProperty.CreateAttached("PressedCommand", typeof(ICommand), typeof(PressedEffect), null);
    public static readonly BindableProperty DoublePressedCommandProperty = BindableProperty.CreateAttached("DoublePressedCommand", typeof(ICommand), typeof(PressedEffect), null);

    public static ICommand GetLongPressedCommand(BindableObject view)
    {
        return (ICommand)view.GetValue(LongPressedCommandProperty);
    }

    public static ICommand GetPressedCommand(BindableObject view)
    {
        return (ICommand)view.GetValue(PressedCommandProperty);
    }

    public static ICommand GetDoublePressedCommand(BindableObject view)
    {
        return (ICommand)view.GetValue(DoublePressedCommandProperty);
    }

    public static void SetLongPressedCommand(BindableObject view, ICommand value)
    {
        view.SetValue(LongPressedCommandProperty, value);
    }

    public static void SetPressedCommand(BindableObject view, ICommand value)
    {
        view.SetValue(PressedCommandProperty, value);
    }

    public static void SetDoublePressedCommand(BindableObject view, ICommand value)
    {
        view.SetValue(DoublePressedCommandProperty, value);
    }
}