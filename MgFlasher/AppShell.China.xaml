<?xml version="1.0" encoding="UTF-8" ?>
<r:AppShell
    BackgroundColor="{StaticResource TitleViewBackgroundColor}"
    FlyoutBackgroundColor="{StaticResource PrimaryBackgroundColor}"
    FlyoutHeaderBehavior="CollapseOnScroll"
    x:Class="MgFlasher.AppShellChina" x:Name="shell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:models="clr-namespace:MgFlasher.Models"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:r="clr-namespace:MgFlasher"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:SideMenuViewModel">

    <Shell.Resources>
        <ResourceDictionary>
            <Style Class="FlyoutItemLabelStyle" TargetType="Label">
                <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                <Setter Property="FontSize" Value="{StaticResource PrimaryFontSize}" />
                <Setter Property="Margin" Value="-5,0,0,0" />
                <Setter Property="VisualStateManager.VisualStateGroups">
                    <VisualStateGroupList>
                        <VisualStateGroup x:Name="CommonStates">
                            <VisualState x:Name="Normal">
                                <VisualState.Setters>
                                    <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                </VisualState.Setters>
                            </VisualState>
                        </VisualStateGroup>
                    </VisualStateGroupList>
                </Setter>
            </Style>
        </ResourceDictionary>
    </Shell.Resources>

    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsVisible="False" />
    </Shell.BackButtonBehavior>

    <Shell.FlyoutHeader>
        <VerticalStackLayout Margin="0" Padding="{OnPlatform iOS='20,20,20,76', Default='20'}">
            <Image
                Aspect="AspectFit" HorizontalOptions="FillAndExpand"
                Source="logo_276.png" VerticalOptions="FillAndExpand"
                MaximumHeightRequest="90" />
        </VerticalStackLayout>
    </Shell.FlyoutHeader>

    <MenuItem Command="{Binding NavigateCommand}" CommandParameter="{x:Static models:PageType.User}" IconImageSource="menuusersmall.png" Text="{i18n:Translate Menu_User}" />
    <MenuItem Command="{Binding NavigateCommand}" CommandParameter="{x:Static models:PageType.MyCars}" IconImageSource="menumycarsmall.png" Text="{i18n:Translate Menu_MyCars}" />
    <ShellContent ContentTemplate="{DataTemplate views:AboutPage}" Icon="menudemosmall.png" Title="{i18n:Translate Menu_About}" />
    <MenuItem Command="{Binding NavigateCommand}" CommandParameter="{x:Static models:PageType.KnowledgeBase}" IconImageSource="icon_faq.png" Text="{i18n:Translate Menu_Support}" />
    <MenuItem Command="{Binding NavigateCommand}" CommandParameter="{x:Static models:PageType.Settings}" IconImageSource="menuusersmall.png" Text="{i18n:Translate Menu_Settings}" />
    <MenuItem Command="{Binding NavigateCommand}" CommandParameter="{x:Static models:ActionType.Exit}" IconImageSource="iconexit.png" Text="{i18n:Translate Menu_Exit}" />

    <Shell.FlyoutFooter>
        <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" HeightRequest="80">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <HorizontalStackLayout Grid.Row="0" Spacing="0" HorizontalOptions="Center" VerticalOptions="EndAndExpand">
                <Label FontAttributes="Bold" Text="{i18n:Translate SideMenu_WeChatContactReference}" />
            </HorizontalStackLayout>
        </Grid>
    </Shell.FlyoutFooter>
</r:AppShell>