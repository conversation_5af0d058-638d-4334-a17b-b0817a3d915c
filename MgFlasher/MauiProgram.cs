﻿using DiagProtocolUtils;
using MgFlasher.Behaviors;
using MgFlasher.Flasher.Services.CarLogger.Alerts;
using MgFlasher.Flasher.Services.Cars;
using Microsoft.Maui.Controls.Compatibility.Hosting;
using Microsoft.Maui.Controls.Hosting;
using Microsoft.Maui.Hosting;
using MgFlasher.Helpers;
using MgFlasher.Views.Controls;
using MgFlasher.Flasher.Client.Common;
using MgFlasher.Logging;
using Microsoft.Extensions.DependencyInjection;
using MgFlasher.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.LifecycleEvents;
using Syncfusion.Maui.Core.Hosting;
using NLog.Extensions.Logging;
using CommunityToolkit.Maui;
using FFImageLoading.Maui;
using MgFlasher.Services.Notifications;
#if ANDROID
using Microsoft.Maui.Controls;
using Microsoft.Maui.ApplicationModel;
using MgFlasher.Platforms.Android.Renderers;
using MgFlasher.Platforms.Android.Services;
using MgFlasher.Platforms.Android.Helpers;
#endif
#if IOS
using MgFlasher.Platforms.iOS.Renderers;
using MgFlasher.Platforms.iOS.Services;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Compatibility.Platform.iOS;
#endif
#if WINDOWS
using MgFlasher.Platforms.Windows.Services;
using MgFlasher.Platforms.Windows.Renderers;
using InputKit.Handlers;
#endif

namespace MgFlasher;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        ConfigureFonts(builder);
        builder.UseMauiApp<App>();
        builder.UseMauiCompatibility();
        ConfigureLogging(builder);
#if RELEASE
        builder.UseSentryLoggingModule(DependencyResolver.TryResolve);
#endif
        builder.UsePushNotifications();
        builder.UseFFImageLoading();
        builder.UseMauiCommunityToolkit();
        builder.ConfigureSyncfusionCore();
        ConfigureMaps(builder);
        ConfigureForPlatform(builder);

        DependencyResolver.Register(builder.Services);
        var app = builder.Build();
        DependencyResolver.Init(app.Services);

        return app;
    }

    private static void ConfigureMaps(MauiAppBuilder builder)
    {
#if ANDROID || IOS
        builder.UseMauiMaps();
#elif WINDOWS
        CommunityToolkit.Maui.Maps.AppHostBuilderExtensions.UseMauiCommunityToolkitMaps(builder, "to-do");
#endif
    }

    private static void ConfigureLogging(MauiAppBuilder builder)
    {
        builder.Logging.ClearProviders();
        builder.Logging.AddNLog(
            new() { RemoveLoggerFactoryFilter = true, IncludeScopes = true },
            x => x.GetRequiredService<ILoggingConfigFactory>().Create().LogFactory);
    }

#if ANDROID
    private static void ConfigureForPlatform(MauiAppBuilder builder)
    {
        builder.ConfigureEffects(x =>
        {
            x.Add<PressedEffect, AndroidPressedEffect>();
            x.Add<TouchEffect, AndroidTouchEffect>();
        });
        builder.ConfigureMauiHandlers(x =>
        {
            x.AddHandler(typeof(Shell), typeof(CustomShellRenderer));
            x.AddHandler(typeof(ListView), typeof(CustomListViewRenderer));
            x.AddHandler(typeof(ClassicProgressBar), typeof(ClassicProgressBarRenderer));
            x.AddHandler<CustomViewCell, AndroidCustomViewCellHandler>();
        });
        builder.ConfigureLifecycleEvents(events =>
        {
            events.AddAndroid(android => android
                .OnApplicationLowMemory(_ => LogLifecycleEvent(nameof(AndroidLifecycle.OnApplicationLowMemory)))
                .OnActivityResult((_, requestCode, _, _) => LogLifecycleEvent(nameof(AndroidLifecycle.OnActivityResult), requestCode.ToString()))
                .OnStart(_ => LogLifecycleEvent(nameof(AndroidLifecycle.OnStart)))
                .OnCreate((_, _) => LogLifecycleEvent(nameof(AndroidLifecycle.OnCreate)))
                .OnBackPressed(_ => LogLifecycleEvent(nameof(AndroidLifecycle.OnBackPressed)) && false)
                .OnStop(_ => LogLifecycleEvent(nameof(AndroidLifecycle.OnStop))));
        });
        builder.Services.AddSingleton<IPlatformHttpClientFactory, PlatformHttpClientFactory>();
        builder.Services.AddSingleton<IPowerManager, AndroidPowerManager>();
        builder.Services.AddSingleton<ILatestVersionChecker, AndroidLatestVersionChecker>();
        builder.Services.AddSingleton<ICloseAppService, CloseAppService>();
    }
#endif
#if IOS

    private static void ConfigureForPlatform(MauiAppBuilder builder)
    {
        builder.ConfigureEffects(x =>
        {
            x.Add<PressedEffect, IOSPressedEffect>();
            x.Add<TouchEffect, IOSTouchEffect>();
        });
        builder.ConfigureMauiHandlers(x =>
        {
            x.AddHandler(typeof(Shell), typeof(CustomShellRenderer));
            x.AddHandler(typeof(ClassicProgressBar), typeof(ClassicProgressBarRenderer));
            x.AddHandler<CustomViewCell, iOSCustomViewCellHandler>();
        });
        builder.ConfigureLifecycleEvents(events =>
        {
            events.AddiOS(ios => ios
                .OnActivated((app) => LogLifecycleEvent(nameof(iOSLifecycle.OnActivated)))
                .OnResignActivation((app) => LogLifecycleEvent(nameof(iOSLifecycle.OnResignActivation)))
                .DidEnterBackground((app) => LogLifecycleEvent(nameof(iOSLifecycle.DidEnterBackground)))
                .WillTerminate((app) => LogLifecycleEvent(nameof(iOSLifecycle.WillTerminate))));
        });
        builder.Services.AddSingleton<IPlatformHttpClientFactory, PlatformHttpClientFactory>();
        builder.Services.AddSingleton<IPowerManager, IosPowerManager>();
        builder.Services.AddSingleton<ILatestVersionChecker, IosLatestVersionChecker>();
        builder.Services.AddSingleton<ICloseAppService, CloseAppService>();
    }

#endif
#if WINDOWS
    private static void ConfigureForPlatform(MauiAppBuilder builder)
    {
        builder.ConfigureEffects(x =>
        {
            x.Add<PressedEffect, WindowsPressedEffect>();
            x.Add<TouchEffect, WindowsTouchEffect>();
        });
        builder.ConfigureMauiHandlers(x =>
        {
            x.AddInputKitHandlers();
            x.AddHandler<CustomViewCell, WindowsCustomViewCellHandler>();
        });
        builder.ConfigureLifecycleEvents(events =>
        {
            events.AddWindows(win => win
                .OnActivated((win, args) => LogLifecycleEvent(nameof(WindowsLifecycle.OnActivated)))
                .OnClosed((win, args) => LogLifecycleEvent(nameof(WindowsLifecycle.OnClosed)))
                .OnLaunched((win, args) => LogLifecycleEvent(nameof(WindowsLifecycle.OnLaunched)))
                .OnLaunching((win, args) => LogLifecycleEvent(nameof(WindowsLifecycle.OnLaunching)))
                .OnVisibilityChanged((win, args) => LogLifecycleEvent(nameof(WindowsLifecycle.OnVisibilityChanged))));
        });
        builder.Services.AddSingleton<ICloseAppService, CloseAppService>();
        builder.Services.AddSingleton<IPowerManager, WindowsPowerManager>();
        builder.Services.AddSingleton<IPlatformHttpClientFactory, PlatformHttpClientFactory>();
        builder.Services.AddSingleton<ILatestVersionChecker, WindowsLatestVersionChecker>();
    }
#endif

    private static bool LogLifecycleEvent(string eventName, string type = null)
    {
        if (!DependencyResolver.Initialized)
        {
            return true;
        }

        var logger = DependencyResolver.Resolve<ILoggerFactory>().CreateLogger(nameof(MauiProgram));
        logger.LogInformation("Lifecycle event {EventName} occured", $"{eventName}{(type == null ? string.Empty : $" ({type})")}");
        return true;
    }

    private static void ConfigureFonts(MauiAppBuilder builder)
    {
        builder.ConfigureFonts(fonts =>
        {
            fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
            fonts.AddFont("OpenSans-SemiBold.ttf", "OpenSansSemiBold");
            fonts.AddFont("OpenSans-Medium.ttf", "sans-serif-medium");
        });
    }
}