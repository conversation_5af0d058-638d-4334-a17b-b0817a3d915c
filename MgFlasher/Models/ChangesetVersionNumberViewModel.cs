﻿using MgFlasher.Localization.Resources;
using MgFlasher.ViewModels;
using System;

namespace MgFlasher.Models;

public class ChangesetVersionNumberViewModel : BaseViewModel, IRadioButtonViewModel
{
    private bool _isChecked;
    private readonly Action<IRadioButtonViewModel> _checkedChanged;

    public int ChangesetId { get; }
    public string VersionNumber { get; }
    public bool IsNewestVersion { get; }
    public string ReleaseNotes { get; }

    public bool IsChecked
    {
        get => _isChecked;
        set
        {
            var different = _isChecked != value;
            _isChecked = value;

            if (different)
            {
                OnPropertyChanged(nameof(IsChecked));
                _checkedChanged?.Invoke(this);
            }
        }
    }

    public ChangesetVersionNumberViewModel(
        string versionNumber, bool isNewestVersion,
        string releaseNotes, int changesetId,
        Action<IRadioButtonViewModel> checkedChanged)
    {
        _checkedChanged = checkedChanged;

        VersionNumber = versionNumber;
        IsNewestVersion = isNewestVersion;
        ReleaseNotes = !string.IsNullOrEmpty(releaseNotes) ? releaseNotes : AppResources.AvailableStageVersion_EmptyReleaseNotes;
        ChangesetId = changesetId;
    }

    public void PropagateChecked(object vm)
    {
        if (vm == this) return;

        if (IsChecked)
        {
            IsChecked = false;
        }
    }
}