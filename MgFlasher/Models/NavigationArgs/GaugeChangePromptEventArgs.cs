﻿using MgFlasher.Flasher.Services.CarLogger.Gauges;
using System;

namespace MgFlasher.Models.NavigationArgs;

public class GaugeChangePromptEventArgs : EventArgs
{
    public int CurrentDisplayIndex { get; }
    public GaugeLogDefinition Definition { get; }

    public GaugeChangePromptEventArgs(int currentDisplayIndex, GaugeLogDefinition definition)
    {
        CurrentDisplayIndex = currentDisplayIndex;
        Definition = definition;
    }
}