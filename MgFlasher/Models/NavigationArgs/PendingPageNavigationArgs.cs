﻿using MgFlasher.Localization.Resources;
using System;

namespace MgFlasher.Models.NavigationArgs;

public class PendingPageNavigationArgs
{
    public PageType NextPage { get; set; }
    public PageType BackPage { get; set; }
    public string Text { get; set; }
    public object NextPageArguments { get; set; }
    public object BackPageArguments { get; set; }
    public Type NextPageTabbedComponent { get; internal set; }

    public bool EnableProgressBar { get; set; }

    public static PendingPageNavigationArgs InProgress(PageType withinPage, string text = null) => new()
    {
        Text = text ?? AppResources.SyncPage_Syncing,
        NextPage = withinPage,
        BackPage = withinPage,
        EnableProgressBar = true
    };
}