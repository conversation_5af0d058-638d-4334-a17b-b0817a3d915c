﻿using MgFlasher.CustomCode.Options.Configuration.User;
using MgFlasher.Flasher.Commands;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Helpers;
using System.Collections.Generic;

namespace MgFlasher.Models.NavigationArgs;

public class FlashingCarPageNavigationArgs
{
    public IContinousFlasherCommand ContinousCommand { get; set; }
    public IEnumerable<string> WarningInfos { get; set; }
    public CustomOptionsUserConfiguration Options { get; set; }

    public FlashingCarPageNavigationArgs()
    {
        WarningInfos = FlashingCarWarningInfo.GetWarningInfos(StageType.Stock);
    }
}