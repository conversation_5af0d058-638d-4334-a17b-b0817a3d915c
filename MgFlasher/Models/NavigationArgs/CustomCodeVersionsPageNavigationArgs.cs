﻿using MgFlasher.CustomCode;
using MgFlasher.Flasher.Commands;
using MgFlasher.Flasher.Services.Models;
using System.Collections.Generic;

namespace MgFlasher.Models.NavigationArgs;

public class CustomCodeVersionsPageNavigationArgs
{
    public List<CustomCodeOverview> CustomCodeVersions { get; }
    public StageType StageType { get; }
    public IContinousFlasherCommand FlasherCommand { get; }

    public CustomCodeVersionsPageNavigationArgs(List<CustomCodeOverview> customCodeVersions, StageType stageType, IContinousFlasherCommand flasherCommand)
    {
        CustomCodeVersions = customCodeVersions;
        StageType = stageType;
        FlasherCommand = flasherCommand;
    }
}