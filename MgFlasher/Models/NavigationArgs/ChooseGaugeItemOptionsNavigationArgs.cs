﻿using MgFlasher.Flasher.Services.CarLogger.Gauges;
using System.Collections.Generic;

namespace MgFlasher.Models.NavigationArgs;

public class ChooseGaugeItemOptionsNavigationArgs
{
    public GaugeChangePromptEventArgs Source { get; }
    public SortedList<int, GaugeLogDefinition> Available { get; }

    public ChooseGaugeItemOptionsNavigationArgs(GaugeChangePromptEventArgs source, SortedList<int, GaugeLogDefinition> available)
    {
        Source = source;
        Available = available;
    }
}