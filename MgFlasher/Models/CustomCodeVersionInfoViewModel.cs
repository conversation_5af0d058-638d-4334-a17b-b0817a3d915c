﻿using MgFlasher.CustomCode.Models;
using MgFlasher.CustomCode.Storage.Changesets;
using MgFlasher.Localization.Resources;
using MgFlasher.ViewModels;
using System;
using System.Globalization;
using System.Windows.Input;

namespace MgFlasher.Models;

public class CustomCodeVersionInfoViewModel : BaseViewModel, IRadioButtonViewModel, IExpandableViewModel
{
    private string _releaseNotes;
    private string _unavailableReason;
    private bool _isChecked;
    private bool _isExpanded;
    private bool _isCurrentVersion;
    private bool _isFileVersion;
    private bool _isObsoleteAndCurrentVersion;
    private bool _canSelect;
    private readonly Action<IRadioButtonViewModel> _checkedChanged;

    public string CustomCodeVersion { get; }
    public CustomCodeId CustomCodeId { get; }
    public string CustomCodeAccessibility { get; }
    public string ReleasedAt { get; }
    public bool IsNewVersion { get; }
    public ICommand ExpandCommand { get; }

    public string ReleaseNotes
    {
        get => _releaseNotes;
        private set => SetProperty(ref _releaseNotes, value);
    }

    public string UnavailableReason
    {
        get => _unavailableReason;
        private set => SetProperty(ref _unavailableReason, value);
    }

    public bool IsCurrentVersion
    {
        get => _isCurrentVersion;
        private set => SetProperty(ref _isCurrentVersion, value);
    }

    public bool IsFileVersion
    {
        get => _isFileVersion;
        private set => SetProperty(ref _isFileVersion, value);
    }

    public bool IsObsoleteAndCurrentVersion
    {
        get => _isObsoleteAndCurrentVersion;
        private set => SetProperty(ref _isObsoleteAndCurrentVersion, value);
    }

    public bool CanSelect
    {
        get => _canSelect;
        private set => SetProperty(ref _canSelect, value);
    }

    public bool IsExpanded
    {
        get => _isExpanded;
        set => SetProperty(ref _isExpanded, value);
    }

    public bool IsChecked
    {
        get => _isChecked;
        set
        {
            var different = _isChecked != value;
            _isChecked = value;

            if (different)
            {
                OnPropertyChanged(nameof(IsChecked));
                _checkedChanged?.Invoke(this);
            }
        }
    }

    public CustomCodeVersionInfoViewModel(
        CustomCodeId customCodeId, bool isNewVersion,
        string releaseNotes, bool isCurrentVersion, bool isFileVersion, bool canSelect, string unavailableReason,
        DateTime? releasedAt, Action<IRadioButtonViewModel> checkedAction)
    {
        _checkedChanged = checkedAction;
        ReleaseNotes = !string.IsNullOrEmpty(releaseNotes) ? releaseNotes : AppResources.CustomCodeVersions_EmptyReleaseNotes;

        CustomCodeId = customCodeId;
        CustomCodeVersion = customCodeId.Version;
        CustomCodeAccessibility = !string.IsNullOrEmpty(customCodeId.UserAvailabilityLevel) ?
            customCodeId.UserAvailabilityLevel : AppResources.CustomCodeAccessibilityLevel_Public;
        IsNewVersion = isNewVersion;
        IsCurrentVersion = isCurrentVersion;
        IsFileVersion = isFileVersion;
        IsObsoleteAndCurrentVersion = isCurrentVersion && canSelect && unavailableReason.Equals(CustomCodeUnavailableReasonEnum.Obsolete.ToString(), StringComparison.OrdinalIgnoreCase);
        CanSelect = canSelect;
        UnavailableReason = unavailableReason;
        ReleasedAt = releasedAt?.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) ?? "-";

        ExpandCommand = new TraceableCommand(Expand, nameof(Expand));
    }

    public override string ToString()
    {
        var message = $"{CustomCodeVersion} | {CustomCodeAccessibility} | {nameof(IsCurrentVersion)}: {IsCurrentVersion} | {nameof(IsFileVersion)}: {IsFileVersion}  | {nameof(CanSelect)}: {CanSelect} | {UnavailableReason} | {nameof(IsNewVersion)}: {IsNewVersion} | Release notes are{(ReleaseNotes.Length > 3 ? "" : " not")} available";
        return message;
    }

    private void Expand()
    {
        IsExpanded = !IsExpanded;
    }

    internal void PropagateChecked(object vm)
    {
        if (vm != this && IsChecked)
        {
            IsChecked = false;
        }
    }
}