﻿using MgFlasher.Flasher.Services.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Input;

namespace MgFlasher.Models;

public class ChangesetVersionNameViewModel : ObservableCollection<ChangesetVersionNumberViewModel>, IRadioButtonViewModel, IExpandableViewModel
{
    private List<ChangesetVersionNumberViewModel> _data;
    private bool _isChecked, _isExpanded;

    private readonly Action<IRadioButtonViewModel> _checkedChanged;

    public StageType StageType { get; }
    public string VersionName { get; }
    public bool IsNewVersion { get; }
    public DateTime NewestVersionNumberReleasedAt { get; }
    public int ChangesetId { get; }
    public string NewestVersionNumber { get; }

    public ICommand ExpandCommand { get; }

    public bool IsExpanded
    {
        get => _isExpanded;
        set
        {
            _isExpanded = value;
            OnPropertyChanged(new PropertyChangedEventArgs(nameof(IsExpanded)));
        }
    }

    public bool IsChecked
    {
        get => _isChecked;
        set
        {
            var different = _isChecked != value;
            _isChecked = value;

            if (different)
            {
                OnPropertyChanged(new PropertyChangedEventArgs(nameof(IsChecked)));
                _checkedChanged?.Invoke(this);
            }
        }
    }

    public bool AnyChecked => IsChecked || this.Any(x => x.IsChecked);

    public ChangesetVersionNameViewModel(StageType stageType, string versionName, bool isNewVersion,
        DateTime newestVersionNumberReleasedAt, int changesetId, string newestVersionNumber,
        IEnumerable<ChangesetVersionNumberViewModel> versionNumberViewModels,
        Action<IRadioButtonViewModel> checkedAction)
    {
        StageType = stageType;
        VersionName = versionName;
        IsNewVersion = isNewVersion;
        NewestVersionNumberReleasedAt = newestVersionNumberReleasedAt;
        ChangesetId = changesetId;
        NewestVersionNumber = newestVersionNumber;
        ExpandCommand = new TraceableCommand(Expand, nameof(Expand));

        _data = versionNumberViewModels.ToList();
        _checkedChanged = checkedAction;
    }

    public int GetCheckedChangesetId()
    {
        if (IsChecked)
        {
            return ChangesetId;
        }
        else
        {
            return this.FirstOrDefault(x => x.IsChecked).ChangesetId;
        }
    }

    private void Expand()
    {
        if (Count > 0)
        {
            IsExpanded = false;
            Clear();
        }
        else
        {
            foreach (var item in _data)
            {
                Add(item);
            }
            IsExpanded = true;
        }
    }

    internal void PropagateChecked(object vm)
    {
        if (vm != this && IsChecked)
        {
            IsChecked = false;
        }

        foreach (var versionNumberVm in _data)
        {
            versionNumberVm.PropagateChecked(vm);
        }
    }
}