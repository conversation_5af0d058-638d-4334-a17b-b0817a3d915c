﻿using MgFlasher.Flasher.Services.AppContext;

namespace MgFlasher.Models;

public enum PageType
{
    [CustomNavigation(forceBackNavigationTo: MyCar)]
    BuyFlashStage,
    [CustomNavigation(userAuthorizationRequired: true)]
    User,
    [CustomNavigation(forceBackNavigationTo: MyCars)]
    MyCar,
    ReadDtc,
    SwitchableMapDiagnostics,
    BurbleDiagnostics,
    MaxCoolinDiagnostics,
    AntilagDiagnostics,
    EthanolOverrideDiagnostics,
    ExhaustFlapDiagnostics,
    RadiatorFlapDiagnostics,
    ValetModeDiagnostics,
    FlashingCar,
    [CustomNavigation(forceBackNavigationTo: MyCar)]
    ProcessCompleted,
    FlashingCarOptions,
    FlashingCarWarningInfo,
    ResetAdaptation,
    LoggerParameters,
    Pending,
    ReadModule,
    About,
    CustomMapFiles,
    [CustomNavigation(forceBackNavigationTo: Login)]
    ForgotPassword,
    Login,
    [CustomNavigation(forceBackNavigationTo: Login)]
    Register,
    [CustomNavigation(userAuthorizationRequired: true)]
    MyCars,
    LoggerUnits,
    [CustomNavigation(disabledForMode: RetailModeEnum.China)]
    Feedback,
    StageVersions,
    SyncPage,
    LoggerFile,
    [CustomNavigation(userAuthorizationRequired: true)]
    Settings,
    CustomCodeVersions,
    LoggerDisplay,
    LoggerDisplayGaugeItem,
    LoggerReplaceGaugeItem,
    LoggerAlerts,
    LoggerAlertItem,
    [CustomNavigation(forceBackNavigationTo: FlashingCarOptions)]
    UserDtcRemovalList,
    [CustomNavigation(forceBackNavigationTo: FlashingCarOptions)]
    FlashingCarOptionsItem,
    MyCarFlashHistoryItemDetail,
    KnowledgeBase,
}