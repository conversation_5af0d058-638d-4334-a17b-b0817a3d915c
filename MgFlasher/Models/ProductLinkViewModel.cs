﻿using System.Windows.Input;
using MgFlasher.Flasher.Services.Products;
using MgFlasher.ViewModels;

namespace MgFlasher.Models;

public class ProductLinkViewModel
{
    private readonly Product _product;

    public string Name => _product.Name;
    public ICommand OpenLinkCommand { get; }

    public ProductLinkViewModel(Product product)
    {
        _product = product;
        OpenLinkCommand = new LinkCommand(product.PurchaseLink);
    }
}