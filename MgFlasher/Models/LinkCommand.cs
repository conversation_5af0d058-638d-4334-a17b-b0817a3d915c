﻿using System;
using System.Windows.Input;
using Microsoft.Maui.ApplicationModel;

namespace MgFlasher.Models;

public class LinkCommand : ICommand
{
    private readonly string _link;

    event EventHandler ICommand.CanExecuteChanged
    {
        add { }
        remove { }
    }

    public string Link => _link.Replace("mailto:", string.Empty);

    public LinkCommand(string link)
    {
        _link = link;
    }

    public bool CanExecute(object parameter) => true;

    public async void Execute(object parameter)
    {
        await Launcher.OpenAsync(_link);
    }
}