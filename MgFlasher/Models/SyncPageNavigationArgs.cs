﻿using MgFlasher.Flasher.Services.Cars.Sync;

namespace MgFlasher.Models;

public class SyncPageNavigationArgs
{
    public SyncChainLinkContext Context { get; }

    public ISyncChainFactory SyncChainFactory { get; }

    public SyncPageNavigationArgs(ISyncChainFactory syncChainFactory, SyncChainLinkContext context = null)
    {
        Context = context ?? new SyncChainLinkContext();
        SyncChainFactory = syncChainFactory;
    }
}