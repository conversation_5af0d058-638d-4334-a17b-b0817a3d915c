﻿using System.Collections.Generic;
using System.Linq;

namespace MgFlasher.Models;

public class StageDescription
{
    public string Headline1 { get; set; }
    public string Headline2 { get; set; }
    public string Headline3 { get; set; }
    public string Headline4 { get; set; }
    public string Headline5 { get; set; }
    public string Subline1 { get; set; }
    public string Subline2 { get; set; }
    public string Subline3 { get; set; }
    public string Subline4 { get; set; }
    public string Subline5 { get; set; }
    public string Subline6 { get; set; }
    public string Block1 { get; set; }
    public string Block2 { get; set; }
    public IEnumerable<string> List { get; set; }
    public IEnumerable<string> List2 { get; set; }
    public IEnumerable<string> List3 { get; set; }
    public IEnumerable<string> List4 { get; set; }
    public string List2Headline { get; set; }
    public int ItemHeight1 => List?.Any(x => x.Length > 40) ?? false ? 60 : 35;
    public int ItemHeight2 => List2?.Any(x => x.Length > 40) ?? false ? 60 : 35;
    public int ItemHeight3 => List3?.Any(x => x.Length > 40) ?? false ? 60 : 35;
    public int ItemHeight4 => List4?.Any(x => x.Length > 40) ?? false ? 60 : 35;
    public int ListHeight => (List?.Count() ?? 0) * ItemHeight1;
    public int List2Height => (List2?.Count() ?? 0) * ItemHeight2;
    public int List3Height => (List3?.Count() ?? 0) * ItemHeight3;
    public int List4Height => (List4?.Count() ?? 0) * ItemHeight4;
}