﻿using System;
using System.Windows.Input;
using MgFlasher.Helpers;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;

namespace MgFlasher.Models;

public class TraceableCommand : ICommand
{
    private readonly Command _command;
    private readonly string _callerName;

    private readonly ILogger<ICommand> _logger = DependencyResolver.Resolve<ILogger<ICommand>>();

    public event EventHandler CanExecuteChanged
    {
        add => _command.CanExecuteChanged += value;
        remove => _command.CanExecuteChanged -= value;
    }

    public TraceableCommand(Action<object> execute, string callerName)
    {
        _command = new Command(execute);
        _callerName = callerName;
    }

    public TraceableCommand(Action execute, string callerName)
    {
        _command = new Command(execute);
        _callerName = callerName;
    }

    public TraceableCommand(Action<object> execute, Func<object, bool> canExecute, string callerName)
    {
        _command = new Command(execute, canExecute);
        _callerName = callerName;
    }

    public TraceableCommand(Action execute, Func<bool> canExecute, string callerName)
    {
        _command = new Command(execute, canExecute);
        _callerName = callerName;
    }

    public bool CanExecute(object parameter) => _command.CanExecute(parameter);

    public void Execute(object parameter)
    {
        _logger.LogInformation("Execute {CallerName} command", _callerName);
        _command.Execute(parameter);
    }

    public void ChangeCanExecute() => _command.ChangeCanExecute();
}