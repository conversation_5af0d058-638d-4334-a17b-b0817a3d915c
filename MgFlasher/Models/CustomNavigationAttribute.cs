﻿using System;
using MgFlasher.Flasher.Services.AppContext;

namespace MgFlasher.Models;

public class CustomNavigationAttribute : Attribute
{
    public RetailModeEnum? DisabledForMode { get; }
    public PageType? ForceBackNavigationTo { get; }
    public bool UserAuthorizationRequired { get; }

    public CustomNavigationAttribute(PageType forceBackNavigationTo, bool userAuthorizationRequired = false)
    {
        ForceBackNavigationTo = forceBackNavigationTo;
        UserAuthorizationRequired = userAuthorizationRequired;
    }

    public CustomNavigationAttribute(bool userAuthorizationRequired)
    {
        UserAuthorizationRequired = userAuthorizationRequired;
    }

    public CustomNavigationAttribute(RetailModeEnum disabledForMode)
    {
        DisabledForMode = disabledForMode;
    }
}