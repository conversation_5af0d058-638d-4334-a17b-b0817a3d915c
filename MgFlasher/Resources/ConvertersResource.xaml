<?xml version="1.0" encoding="UTF-8" ?>
<ResourceDictionary
    x:Class="MgFlasher.Resources.ConvertersResource"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:MgFlasher.Resources"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    xmlns:ms="clr-namespace:MgFlasher.Views.Controls"
    xmlns:converters="clr-namespace:MgFlasher.Converters"
    xmlns:logConverters="clr-namespace:MgFlasher.Views.Logger.Converters">
    <converters:InvertedBoolConverter x:Key="InvertedBoolConverter" />
    <converters:InvertedBoolToOpacityConverter x:Key="InvertedBoolToOpacityConverter" />
    <converters:StringToVisibilityConverter x:Key="StringToVisibility" />
    <converters:CollectionToVisibilityConverter x:Key="CollectionToVisibility" />
    <converters:DecimalToInvariantCultureStringConverter x:Key="DecimalToInvariantCultureStringConverter" />
    <converters:TcuLimiterTorqueToTextConverter x:Key="TcuLimiterTorqueConverter" />
    <converters:IntToHexConverter x:Key="IntToHexConverter" />
    <converters:PowerGauge_kW_ToTextConverter x:Key="PowerGauge_kW_ToTextConverter" />
    <converters:PowerGauge_hp_ToTextConverter x:Key="PowerGauge_hp_ToTextConverter" />
    <converters:TorqueGauge_Nm_ToTextConverter x:Key="TorqueGauge_Nm_ToTextConverter" />
    <converters:TorqueGauge_lbft_ToTextConverter x:Key="TorqueGauge_lbft_ToTextConverter" />
    <converters:TorqueGauge_kgm_ToTextConverter x:Key="TorqueGauge_kgm_ToTextConverter" />
    <converters:BurbleDurationToTextConverter x:Key="BurbleDurationConverter" />
    <converters:BurbleStyle1AggressivenessToTextConverter x:Key="BurbleStyle1AggressivenessConverter" />
    <converters:BurbleStyle2AggressivenessToTextConverter x:Key="BurbleStyle2AggressivenessConverter" />
    <converters:BurbleStyle3AggressivenessToTextConverter x:Key="BurbleStyle3AggressivenessConverter" />
    <converters:BurbleRpmMinToTextConverter x:Key="BurbleRpmMinConverter" />
    <converters:BurbleRpmMaxToTextConverter x:Key="BurbleRpmMaxConverter" />
    <converters:BurbleSpeedMinToTextConverter x:Key="BurbleSpeedMinConverter" />
    <converters:BurbleSpeedMaxToTextConverter x:Key="BurbleSpeedMaxConverter" />
    <converters:BurbleAggressionStyleOffInvertedConverter x:Key="BurbleAggressionStyleOffInvertedConverter" />
    <converters:BurbleAggressionStyleToVisibilityConverter x:Key="BurbleAggressionStyleToVisibilityConverter" />
    <converters:MaxCoolingTargetEngineTemperatureToTextConverter x:Key="MaxCoolingTargetEngineTemperatureConverter" />
    <converters:MaxCoolingTargetIntercoolerCoolantVolumeFlowToTextConverter x:Key="MaxCoolingTargetIntercoolerCoolantVolumeFlowConverter" />
    <converters:BurbleFlameTimingToTextConverter x:Key="BurbleFlameTimingConverter" />
    <converters:BurbleFlameFuelToTextConverter x:Key="BurbleFlameFuelConverter" />
    <converters:StartupRoarAggressionToTextConverter x:Key="StartupRoarAggressionConverter" />
    <converters:StartupRoarDurationToTextConverter x:Key="StartupRoarDurationConverter" />
    <converters:TorqueByGearToTextConverter x:Key="TorqueByGearConverter" />
    <converters:TorqueByGearNoLimitVisibilityConverter x:Key="TorqueByGearNoLimitVisibilityConverter" />
    <converters:TorqueByGearNoLimitInverseVisibilityConverter x:Key="TorqueByGearNoLimitInverseVisibilityConverter" />
    <converters:AntilagRpmConverter x:Key="AntilagRpmConverter" />
    <converters:AntilagRpmHystConverter x:Key="AntilagRpmHystConverter" />
    <converters:AntilagRollingSpeedThresholdConverter x:Key="AntilagRollingSpeedThresholdConverter" />
    <converters:AntilagTimeConverter x:Key="AntilagTimeConverter" />
    <converters:AntilagPedalConverter x:Key="AntilagPedalConverter" />
    <converters:AntilagTargetNumberOfCylindersToSuppressConverter x:Key="AntilagTargetNumberOfCylindersToSuppressConverter" />
    <converters:AntilagTempConverter x:Key="AntilagTempConverter" />
    <converters:AntilagEgtConverter x:Key="AntilagEgtConverter" />
    <converters:AntilagBoostConverter x:Key="AntilagBoostConverter" />
    <converters:AntilagOverboostConverter x:Key="AntilagOverboostConverter" />
    <converters:AntilagPercentageConverter x:Key="AntilagPercentageConverter" />
    <converters:AntilagFlatSafetyTorqueCapConverter x:Key="AntilagSafetyTorqueCapConverter" />
    <converters:AntilagFlatIgnConverter x:Key="AntilagFlatIgnConverter" />
    <converters:AntilagFlatLambdaConverter x:Key="AntilagFlatLambdaConverter" />
    <converters:StringNullabilityToVisibilityConverter x:Key="StringNullabilityToVisibilityConverter" />
    <logConverters:MinMaxPercentileConverter x:Key="MinMaxPercentileConverter" />
    <logConverters:ValueWithDisplayPrecisionConverter x:Key="ValueWithDisplayPrecisionConverter" />
    <logConverters:CircularValueConverter x:Key="CircularValueConverter" />
    <logConverters:PixelSizesScaleConverter x:Key="PixelSizesScaleConverter" />
    <logConverters:GaugeColorConverter x:Key="GaugeColorConverter" />
    <logConverters:CircularValuePrimaryLineRangeStartConverter x:Key="CircularValuePrimaryLineRangeStartConverter" />
    <converters:IdleRpmTargetClutchConverter x:Key="IdleRpmTargetClutchConverter" />
    <converters:IdleRpmTargetClutchDelayConverter x:Key="IdleRpmTargetClutchDelayConverter" />
    <converters:IdleRpmTargetConverter x:Key="IdleRpmTargetConverter" />
    <converters:FlexFuelTimeConverter x:Key="FlexFuelTimeConverter" />
    <converters:CustomCodeMenuTime1Converter x:Key="CustomCodeMenuTime1Converter" />
    <converters:CustomCodeMenuTime10Converter x:Key="CustomCodeMenuTime10Converter" />
    <converters:ExhaustFlapOnTheFlyLoadConverter x:Key="ExhaustFlapOnTheFlyLoadConverter" />
    <converters:ValetMaxPedalConverter x:Key="ValetMaxPedalConverter" />
    <converters:ValetMaxEngineSpeedConverter x:Key="ValetMaxEngineSpeedConverter" />
    <converters:ValetMaxVehicleSpeedConverter x:Key="ValetMaxVehicleSpeedConverter" />
    <converters:ValetTorqueLevelConverter x:Key="ValetTorqueLevelConverter" />
    <converters:AllTrueMultiConverter x:Key="AllTrueMultiConverter" />
    <converters:BoolToVisualOpacityConverter x:Key="BoolToVisualOpacityConverter" />
    <converters:CarConnectivityIconConverter x:Key="CarConnectivityIconConverter" />
    <converters:CarConnectivityTextConverter x:Key="CarConnectivityTextConverter" />
    <converters:VersionNameToTextConverter x:Key="VersionNameToTextConverter" />
    <converters:StageTypeToTextConverter x:Key="StageTypeToTextConverter" />
    <converters:BooleanToLoadMoreOptionConverter x:Key="BooleanToLoadMoreOptionConverter" />
    <converters:ScreenWidthToPopupWidthConverter x:Key="ScreenWidthToPopupWidthConverter" />
    <converters:BackgroundColorConverter x:Key="BackgroundColorConverter" />
    <converters:BoolToRowHeightOnMobileConverter x:Key="BoolToRowHeightOnMobileConverter" />
    <converters:BoolToColumnHeightOnDesktopConverter x:Key="BoolToColumnHeightOnDesktopConverter" />
    <converters:AnyTrueMultiConverter x:Key="AnyTrueMultiConverter" />
    <converters:EqualToConverter x:Key="EqualToConverter" />
    <converters:ProportionalPaddingConverter x:Key="ProportionalPaddingConverter" />
</ResourceDictionary>