<?xml version="1.0" encoding="UTF-8" ?>
<ResourceDictionary
    x:Class="MgFlasher.Resources.TemplatesResource"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:MgFlasher.Resources"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    xmlns:ms="clr-namespace:MgFlasher.Views.Controls">
    <ResourceDictionary.MergedDictionaries>
        <local:MgFlasherThemeResource />
        <local:FontsResource />
        <local:ControlsResource />
    </ResourceDictionary.MergedDictionaries>
    <DataTemplate x:Key="OptionalItemTemplate" x:DataType="vms:OptionalItemViewModel">
        <ViewCell>
            <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                <Grid.RowDefinitions>
                    <RowDefinition Height="0.03*" />
                    <RowDefinition Height="0.15*" />
                    <RowDefinition Height="1.4*" />
                    <RowDefinition Height="0.15*" />
                    <RowDefinition Height="0.03*" />
                </Grid.RowDefinitions>
                <Grid Grid.Row="1" Grid.RowSpan="3">
                    <Grid.Resources>
                        <ResourceDictionary>
                            <Style TargetType="Grid" BasedOn="{StaticResource GridMgBaseStyle}">
                                <Setter Property="BackgroundColor" Value="Transparent" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsChecked, Mode=OneWay}" TargetType="Grid" Value="True" x:DataType="vms:OptionalItemViewModel">
                                        <Setter Property="BackgroundColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ResourceDictionary>
                    </Grid.Resources>
                </Grid>
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="6.2*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <Label
                        Grid.Column="0" Margin="5,0,0,0"
                        HorizontalTextAlignment="Start"
                        Text="{Binding Option, Mode=OneWay}"
                        LineBreakMode="WordWrap"
                        FontSize="{StaticResource SmallFontSize}"
                        VerticalTextAlignment="Center" />
                    <ms:Checkbox Grid.Column="1" IsChecked="{Binding IsChecked, Mode=TwoWay}" />
                </Grid>
                <Grid
                    Grid.Row="4"
                    BackgroundColor="{StaticResource ListItemSeparatorColor}"
                    HorizontalOptions="FillAndExpand" HeightRequest="1"
                    VerticalOptions="FillAndExpand" />
            </Grid>
        </ViewCell>
    </DataTemplate>
</ResourceDictionary>