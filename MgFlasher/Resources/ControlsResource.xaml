<?xml version="1.0" encoding="UTF-8" ?>
<ResourceDictionary
    x:Class="MgFlasher.Resources.ControlsResource"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:input="clr-namespace:InputKit.Shared.Controls;assembly=InputKit.Maui"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:local="clr-namespace:MgFlasher.Resources"
    xmlns:tabView="clr-namespace:Syncfusion.Maui.TabView;assembly=Syncfusion.Maui.TabView"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">
    <ResourceDictionary.MergedDictionaries>
        <local:MgFlasherThemeResource />
        <local:FontsResource />
    </ResourceDictionary.MergedDictionaries>
    <Style TargetType="Grid" x:Key="GridMgBaseStyle">
        <Setter Property="RowSpacing" Value="5" />
        <Setter Property="ColumnSpacing" Value="5" />
    </Style>
    <Style TargetType="Grid" BasedOn="{StaticResource GridMgBaseStyle}" />
    <Style TargetType="StackLayout" x:Key="StackLayoutMgBaseStyle">
        <Setter Property="Spacing" Value="5" />
    </Style>
    <Style TargetType="StackLayout" BasedOn="{StaticResource StackLayoutMgBaseStyle}" />
    <Style TargetType="HorizontalStackLayout" x:Key="HorizontalStackLayoutMgBaseStyle">
        <Setter Property="Spacing" Value="5" />
    </Style>
    <Style TargetType="HorizontalStackLayout" BasedOn="{StaticResource HorizontalStackLayoutMgBaseStyle}" />
    <Style TargetType="VerticalStackLayout" x:Key="VerticalStackLayoutMgBaseStyle">
        <Setter Property="Spacing" Value="5" />
    </Style>
    <Style TargetType="VerticalStackLayout" BasedOn="{StaticResource VerticalStackLayoutMgBaseStyle}" />
    <Style TargetType="tabView:SfTabView" x:Key="SfTabViewMgBaseStyle">
        <Setter Property="IndicatorBackground" Value="{StaticResource PrimaryColor}" />
    </Style>
    <Style TargetType="tabView:SfTabView" BasedOn="{StaticResource SfTabViewMgBaseStyle}" />
    <Style TargetType="tabView:SfTabItem" x:Key="SfTabItemMgBaseStyle">
        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
    </Style>
    <Style TargetType="tabView:SfTabItem" BasedOn="{StaticResource SfTabItemMgBaseStyle}" />
    <Style TargetType="controls:ClassicProgressBar" x:Key="ClassicProgressBarMgBaseStyle">
        <Setter Property="ForegroundProgressColor" Value="{StaticResource PrimaryImportantTextColor}" />
        <Setter Property="BaseColor" Value="{StaticResource PrimaryAccentColor}" />
    </Style>
    <Style TargetType="controls:ClassicProgressBar" BasedOn="{StaticResource ClassicProgressBarMgBaseStyle}" />
    <Style TargetType="controls:Checkbox" x:Key="CheckboxMgBaseStyle">
        <Setter Property="CheckedColor" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="UncheckedColor" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="TickColor" Value="{StaticResource ItemSelectedHoverColor}" />
        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
    </Style>
    <Style TargetType="controls:Checkbox" BasedOn="{StaticResource CheckboxMgBaseStyle}" />
    <Style TargetType="Button" x:Key="ButtonMgBaseStyle">
        <Setter Property="FontSize" Value="{StaticResource PrimaryFontSize}" />
        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
    </Style>
    <Style TargetType="controls:SmallButton" BasedOn="{StaticResource ButtonMgBaseStyle}" />
    <Style TargetType="controls:BigButton" BasedOn="{StaticResource ButtonMgBaseStyle}" />
    <Style TargetType="Button" BasedOn="{StaticResource ButtonMgBaseStyle}" />
    <Style TargetType="Button" x:Key="RoundedButtonMgStyle">
        <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
        <Setter Property="CornerRadius" Value="{OnPlatform iOS=10, Default=25}" />
    </Style>
    <Style TargetType="Label" x:Key="LabelMgBaseStyle">
        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="FontSize" Value="{StaticResource PrimaryFontSize}" />
    </Style>
    <Style TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}" />
    <Style TargetType="Picker" x:Key="PickerMgBaseStyle">
        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="BackgroundColor" Value="{StaticResource PrimaryAccentColor}" />
        <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
    </Style>
    <Style TargetType="Picker" BasedOn="{StaticResource PickerMgBaseStyle}" />
    <Style TargetType="Entry" x:Key="EntryMgBaseStyle">
        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="PlaceholderColor" Value="{StaticResource AccentTextColor}" />
        <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
        <Setter Property="BackgroundColor" Value="Transparent" />
    </Style>
    <Style TargetType="Entry" BasedOn="{StaticResource EntryMgBaseStyle}" />
    <Style TargetType="ViewCell" x:Key="ViewCellMgBaseStyle">
        <Setter Property="ios:Cell.DefaultBackgroundColor" Value="{StaticResource PrimaryBackgroundColor}" />
    </Style>
    <Style TargetType="ViewCell" BasedOn="{StaticResource ViewCellMgBaseStyle}" />
    <Style TargetType="controls:CustomViewCell" x:Key="CustomViewCellMgBaseStyle">
        <Setter Property="ios:Cell.DefaultBackgroundColor" Value="{StaticResource PrimaryBackgroundColor}" />
        <Setter Property="SelectedBackgroundColor" Value="Transparent" />
    </Style>
    <Style TargetType="controls:CustomViewCell" BasedOn="{StaticResource CustomViewCellMgBaseStyle}" />
    <Style TargetType="input:RadioButton" x:Key="InputKitRadioButtonMgBaseStyle">
        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="CircleColor" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="Color" Value="{StaticResource PrimaryTextColor}" />
    </Style>
    <Style TargetType="input:RadioButton" BasedOn="{StaticResource InputKitRadioButtonMgBaseStyle}" />
    <Style TargetType="RadioButton" x:Key="RadioButtonMgBaseStyle">
        <Setter Property="TextColor" Value="{StaticResource RadioButtonTextColor}" />
        <Setter Property="BackgroundColor" Value="Transparent" />
    </Style>
    <Style TargetType="RadioButton" BasedOn="{StaticResource RadioButtonMgBaseStyle}" />
    <Style TargetType="Span" x:Key="SpanMgBaseStyle">
        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
    </Style>
    <Style TargetType="Span" BasedOn="{StaticResource SpanMgBaseStyle}" />
    <Style TargetType="Editor" x:Key="EditorMgBaseStyle">
        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="PlaceholderColor" Value="{StaticResource AccentTextColor}" />
        <Setter Property="BackgroundColor" Value="Transparent" />
    </Style>
    <Style TargetType="Editor" BasedOn="{StaticResource EditorMgBaseStyle}" />
    <Style BasedOn="{StaticResource LabelMgBaseStyle}" TargetType="Label" x:Key="LabelPageTitleStyle">
        <Setter Property="FontSize" Value="{StaticResource LargeFontSize}" />
        <Setter Property="LineBreakMode" Value="NoWrap" />
        <Setter Property="HorizontalOptions" Value="CenterAndExpand" />
        <Setter Property="HorizontalTextAlignment" Value="Center" />
    </Style>
    <Style TargetType="Grid" x:Key="MainGridPageStyle" BasedOn="{StaticResource GridMgBaseStyle}">
        <Setter Property="Padding" Value="{OnPlatform iOS='20', Default='20,20,20,10'}" />
        <Setter Property="HorizontalOptions" Value="FillAndExpand" />
        <Setter Property="VerticalOptions" Value="FillAndExpand" />
    </Style>
    <Style TargetType="Grid" x:Key="FixedScrollMainGridPageStyle" BasedOn="{StaticResource MainGridPageStyle}">
        <Setter Property="Padding" Value="{OnPlatform iOS='20,20,0,20', Default='20,20,20,10'}" />
    </Style>
    <Style TargetType="BoxView" x:Key="Separator">
        <Setter Property="HeightRequest" Value="3" />
        <Setter Property="HorizontalOptions" Value="FillAndExpand" />
        <Setter Property="Color" Value="{StaticResource ListItemSeparatorColor}" />
        <Setter Property="Margin" Value="0, 5, 0, 5" />
    </Style>
    <Style TargetType="ScrollView" x:Key="ScrollViewMgBaseStyle">
        <Setter Property="Margin" Value="0" />
        <Setter Property="Padding" Value="0" />
    </Style>
    <Style TargetType="ScrollView" BasedOn="{StaticResource ScrollViewMgBaseStyle}" />
</ResourceDictionary>