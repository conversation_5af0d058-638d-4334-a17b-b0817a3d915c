<ResourceDictionary x:Class="MgFlasher.Resources.MgFlasherThemeResource" xmlns="http://schemas.microsoft.com/dotnet/2021/maui" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" xmlns:controls="clr-namespace:MgFlasher.Views.Controls">
    <Color x:Key="ItemSelectedHoverColor">#3D3D3D</Color>
    <Color x:Key="PrimaryBackgroundColor">#0D0D0D</Color>
    <Color x:Key="SemiTransparentPrimaryBackgroundColor">#600D0D0D</Color>
    <Color x:Key="AccentBackgroundColor">#000000</Color>
    <Color x:Key="PrimaryAccentColor">#262626</Color>
    <Color x:Key="PrimaryColor">#E3242B</Color>
    <Color x:Key="PrimarySuccessColor">#009B5F</Color>
    <Color x:Key="PrimaryTextColor">#FFFFFF</Color>
    <Color x:Key="PrimaryImportantTextColor">#D60000</Color>
    <Color x:Key="PrimaryWarningTextColor">#F4C458</Color>
    <Color x:Key="PrimaryErrorTextBackgroundColor">#820000</Color>
    <Color x:Key="PrimarySuccessTextColor">#009B5F</Color>
    <Color x:Key="AccentSuccessTextColor">#0078ff</Color>
    <Color x:Key="AccentTextColor">#69696B</Color>
    <Color x:Key="ListItemSeparatorColor">#1F1F1F</Color>
    <Color x:Key="TitleViewBackgroundColor">#040404</Color>
    <Color x:Key="RadioButtonTextColor">#FFFFFF</Color>
    <Color x:Key="GaugeValuePrimaryColor">#cb440c</Color>
    <Color x:Key="GaugeValueAccentColor">#31150a</Color>
    <Color x:Key="GaugeScalePrimaryColor">#465363</Color>
    <Color x:Key="GaugeScaleAccentColor">#222a34</Color>
    <Color x:Key="GaugeValueLinePrimaryColor">#e6a272</Color>
    <Color x:Key="GaugeValueLineAccentColor">#cd7e47</Color>
    <Color x:Key="GaugeValueLineStartColor">#955528</Color>
    <Color x:Key="GaugeAxisLineColor">#1a1a1a</Color>
    <Color x:Key="GaugeRimInternalRing">#FFFFFF</Color>
    <Color x:Key="GaugeRimExternalRing">#D3D3D3</Color>
    <Color x:Key="GaugeBackgroundColor">#1a1a1a</Color>
    <Color x:Key="GaugeRangePointerMainColor_Grey">#FFFFFF</Color>
    <Color x:Key="GaugeRangePointerShadingColor_Grey">#969da5</Color>
    <Color x:Key="GaugeRangeShapePointerColor_Grey">#E3242B</Color>
    <Color x:Key="GaugeRangePointerMainColor_Blue">#81c4ff</Color>
    <Color x:Key="GaugeRangePointerShadingColor_Blue">#16588e</Color>
    <Color x:Key="GaugeRangeShapePointerColor_Blue">#FFFFFF</Color>
    <Color x:Key="GaugeRangePointerMainColor_Red">#E7222e</Color>
    <Color x:Key="GaugeRangePointerShadingColor_Red">#8C1C1C</Color>
    <Color x:Key="GaugeRangeShapePointerColor_Red">#FFFFFF</Color>
</ResourceDictionary>