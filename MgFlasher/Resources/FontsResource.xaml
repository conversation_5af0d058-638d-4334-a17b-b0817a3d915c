<ResourceDictionary x:Class="MgFlasher.Resources.FontsResource" xmlns="http://schemas.microsoft.com/dotnet/2021/maui" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" xmlns:controls="clr-namespace:MgFlasher.Views.Controls">
    <OnIdiom x:Key="ExtraLargeFontSize" x:TypeArguments="x:Double">
        <OnIdiom.Tablet>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="54" />
                <On Platform="Default" Value="64" />
            </OnPlatform>
        </OnIdiom.Tablet>
        <OnIdiom.Phone>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="49" />
                <On Platform="Default" Value="54" />
            </OnPlatform>
        </OnIdiom.Phone>
        <OnIdiom.Desktop>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="Default" Value="49" />
            </OnPlatform>
        </OnIdiom.Desktop>
    </OnIdiom>
    <!--  This can be reverted to Platform="Default" when this issue gets fixed: https://github.com/dotnet/maui/issues/22105  -->
    <OnIdiom x:Key="ExtraSemiLargeFontSize" x:TypeArguments="x:Double">
        <OnIdiom.Tablet>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="32" />
                <On Platform="Android" Value="34" />
                <On Platform="MacCatalyst" Value="34" />
                <On Platform="Tizen" Value="34" />
                <On Platform="WinUI" Value="34" />
            </OnPlatform>
        </OnIdiom.Tablet>
        <OnIdiom.Phone>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="25" />
                <On Platform="Android" Value="27" />
                <On Platform="MacCatalyst" Value="27" />
                <On Platform="Tizen" Value="27" />
                <On Platform="WinUI" Value="27" />
            </OnPlatform>
        </OnIdiom.Phone>
        <OnIdiom.Desktop>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="27" />
                <On Platform="Android" Value="27" />
                <On Platform="MacCatalyst" Value="27" />
                <On Platform="Tizen" Value="27" />
                <On Platform="WinUI" Value="27" />
            </OnPlatform>
        </OnIdiom.Desktop>
    </OnIdiom>
    <OnIdiom x:Key="LargeFontSize" x:TypeArguments="x:Double">
        <OnIdiom.Tablet>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="30" />
                <On Platform="Default" Value="40" />
            </OnPlatform>
        </OnIdiom.Tablet>
        <OnIdiom.Phone>
            <OnPlatform x:TypeArguments="x:Double" Default="30" />
        </OnIdiom.Phone>
        <OnIdiom.Desktop>
            <OnPlatform x:TypeArguments="x:Double" Default="30" />
        </OnIdiom.Desktop>
    </OnIdiom>
    <OnIdiom x:Key="PrimaryFontSize" x:TypeArguments="x:Double">
        <OnIdiom.Tablet>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="22" />
                <On Platform="Default" Value="24" />
            </OnPlatform>
        </OnIdiom.Tablet>
        <OnIdiom.Phone>
            <OnPlatform x:TypeArguments="x:Double" Default="20" />
        </OnIdiom.Phone>
        <OnIdiom.Desktop>
            <OnPlatform x:TypeArguments="x:Double" Default="20" />
        </OnIdiom.Desktop>
    </OnIdiom>
    <OnIdiom x:Key="SecondaryFontSize" x:TypeArguments="x:Double">
        <OnIdiom.Tablet>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="22" />
                <On Platform="Default" Value="23" />
            </OnPlatform>
        </OnIdiom.Tablet>
        <OnIdiom.Phone>
            <OnPlatform x:TypeArguments="x:Double" Default="17" />
        </OnIdiom.Phone>
        <OnIdiom.Desktop>
            <OnPlatform x:TypeArguments="x:Double" Default="17" />
        </OnIdiom.Desktop>
    </OnIdiom>
    <OnIdiom x:Key="SmallFontSize" x:TypeArguments="x:Double">
        <OnIdiom.Tablet>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="19" />
                <On Platform="Default" Value="21" />
            </OnPlatform>
        </OnIdiom.Tablet>
        <OnIdiom.Phone>
            <OnPlatform x:TypeArguments="x:Double" Default="15" />
        </OnIdiom.Phone>
        <OnIdiom.Desktop>
            <OnPlatform x:TypeArguments="x:Double" Default="15" />
        </OnIdiom.Desktop>
    </OnIdiom>
    <OnIdiom x:Key="ExtraSmallFontSize" x:TypeArguments="x:Double">
        <OnIdiom.Tablet>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="18" />
                <On Platform="Default" Value="18" />
            </OnPlatform>
        </OnIdiom.Tablet>
        <OnIdiom.Phone>
            <OnPlatform x:TypeArguments="x:Double" Default="12" />
        </OnIdiom.Phone>
        <OnIdiom.Desktop>
            <OnPlatform x:TypeArguments="x:Double" Default="12" />
        </OnIdiom.Desktop>
    </OnIdiom>
    <OnIdiom x:Key="BigGaugeFontSize" x:TypeArguments="x:Double">
        <OnIdiom.Tablet>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="25" />
                <On Platform="Default" Value="25" />
            </OnPlatform>
        </OnIdiom.Tablet>
        <OnIdiom.Phone>
            <OnPlatform x:TypeArguments="x:Double" Default="15" />
        </OnIdiom.Phone>
        <OnIdiom.Desktop>
            <OnPlatform x:TypeArguments="x:Double" Default="15" />
        </OnIdiom.Desktop>
    </OnIdiom>
    <OnIdiom x:Key="GaugeFontSize" x:TypeArguments="x:Double">
        <OnIdiom.Tablet>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="20" />
                <On Platform="Default" Value="20" />
            </OnPlatform>
        </OnIdiom.Tablet>
        <OnIdiom.Phone>
            <OnPlatform x:TypeArguments="x:Double" Default="12" />
        </OnIdiom.Phone>
        <OnIdiom.Desktop>
            <OnPlatform x:TypeArguments="x:Double" Default="12" />
        </OnIdiom.Desktop>
    </OnIdiom>
    <OnIdiom x:Key="SmallGaugeFontSize" x:TypeArguments="x:Double">
        <OnIdiom.Tablet>
            <OnPlatform x:TypeArguments="x:Double">
                <On Platform="iOS" Value="18" />
                <On Platform="Default" Value="18" />
            </OnPlatform>
        </OnIdiom.Tablet>
        <OnIdiom.Phone>
            <OnPlatform x:TypeArguments="x:Double" Default="8" />
        </OnIdiom.Phone>
        <OnIdiom.Desktop>
            <OnPlatform x:TypeArguments="x:Double" Default="8" />
        </OnIdiom.Desktop>
    </OnIdiom>
</ResourceDictionary>