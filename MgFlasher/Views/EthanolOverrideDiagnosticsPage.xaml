<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.EthanolOverrideDiagnosticsPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:models="clr-namespace:MgFlasher.Models"
    controls:ShellTitleView.HasBarBackButton="{Binding HasBarBackButton, Mode=OneWay}"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.CustomCodeDiagnostics"
    x:DataType="vms:EthanolOverrideDiagnosticsPageViewModel"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate Ethanol_Override_Diagnostics_Headline}" />
            <sf:SfListView
                Grid.Row="1" Margin="0,20,0,0"
                BackgroundColor="Transparent"
                ItemsSource="{Binding DiagnosticCommands}"
                SelectionMode="None" ItemSize="70">
                <sf:SfListView.ItemTemplate>
                    <DataTemplate x:DataType="models:DiagnosticCommandItemModel">
                        <ViewCell>
                            <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="0.03*" />
                                    <RowDefinition Height="0.15*" />
                                    <RowDefinition Height="1.4*" />
                                    <RowDefinition Height="0.15*" />
                                    <RowDefinition Height="0.03*" />
                                </Grid.RowDefinitions>
                                <Grid Grid.Row="2">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="0.3*" />
                                        <ColumnDefinition Width="2*" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Image
                                        HeightRequest="29" WidthRequest="29" Grid.Column="0"
                                        Aspect="AspectFit"
                                        Source="{Binding Icon, Mode=OneWay}" />
                                    <Label
                                        Grid.Column="1" Margin="5,0,0,0"
                                        FontSize="{StaticResource SecondaryFontSize}"
                                        HorizontalTextAlignment="Start"
                                        Text="{Binding CommandName, Mode=OneWay}"
                                        VerticalTextAlignment="Center" />
                                    <controls:SmallButton
                                        Grid.Column="2"
                                        BackgroundColor="{StaticResource PrimarySuccessColor}"
                                        Command="{Binding Command, Mode=OneWay}"
                                        FontSize="{StaticResource SecondaryFontSize}"
                                        Text="{Binding ActionName, Mode=OneWay}" />
                                </Grid>
                                <Grid
                                    Grid.Row="4"
                                    BackgroundColor="{StaticResource ListItemSeparatorColor}"
                                    HorizontalOptions="FillAndExpand" HeightRequest="1"
                                    VerticalOptions="FillAndExpand" />
                            </Grid>
                        </ViewCell>
                    </DataTemplate>
                </sf:SfListView.ItemTemplate>
            </sf:SfListView>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>