<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.ReadDtcPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views"
    controls:ShellTitleView.HasBarBackButton="true"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:ReadDtcPageViewModel"
    xmlns:cm="clr-namespace:MgFlasher.Flasher.Commands.Models;assembly=MgFlasher.Flasher.Commands">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="4.2*" />
                <RowDefinition Height="0.6*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate ReadDtc_Headline}" />
            <sf:SfListView Grid.Row="1" Padding="0,20,0,0" ItemSize="70" ItemsSource="{Binding Codes, Mode=OneWay}">
                <sf:SfListView.ItemTemplate>
                    <DataTemplate x:DataType="cm:DtcCode">
                        <Grid ColumnDefinitions="0.3*, *" Background="{StaticResource PrimaryAccentColor}" HorizontalOptions="FillAndExpand">
                            <Label
                                Grid.Column="0" FontAttributes="Bold"
                                FontSize="{StaticResource SmallFontSize}"
                                TextColor="{StaticResource PrimaryImportantTextColor}"
                                Text="{Binding Code}"
                                HorizontalTextAlignment="Center"
                                VerticalTextAlignment="Center" />
                            <Label
                                Grid.Column="1"
                                Text="{Binding Description}"
                                FontSize="{StaticResource SmallFontSize}"
                                HorizontalTextAlignment="Center"
                                VerticalTextAlignment="Center" />
                        </Grid>
                    </DataTemplate>
                </sf:SfListView.ItemTemplate>
            </sf:SfListView>
            <controls:BigButton Grid.Row="2" BackgroundColor="{StaticResource PrimaryAccentColor}" Command="{Binding ClearDtcCommand}" Text="{i18n:Translate ReadDtc_Clear}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>