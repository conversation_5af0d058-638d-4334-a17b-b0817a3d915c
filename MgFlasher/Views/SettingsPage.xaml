<views:BasePage
    x:Class="MgFlasher.Views.SettingsPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    x:DataType="vms:SettingsPageViewModel"
    xmlns:views="clr-namespace:MgFlasher.Views"
    controls:ShellTitleView.HasBarBackButton="True">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="3.32*" />
                <RowDefinition Height="0.02*" />
                <RowDefinition Height="0.52*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate SettingsPage_Title}" />
            <ScrollView Grid.Row="1" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
                <StackLayout Orientation="Vertical">
                    <BoxView Style="{StaticResource Separator}" />
                    <Label FontAttributes="Bold" Text="{i18n:Translate ExternalServicesPage_Title}" />
                    <ListView
                        Margin="0,10,0,0" HasUnevenRows="True"
                        BackgroundColor="Transparent"
                        ItemsSource="{Binding ExternalServiceItems}"
                        CachingStrategy="RecycleElement"
                        SeparatorVisibility="None" SelectionMode="None">
                        <ListView.ItemTemplate>
                            <DataTemplate x:DataType="vms:ExternalServiceItemViewModel">
                                <ViewCell Height="200">
                                    <StackLayout Orientation="Vertical" HorizontalOptions="FillAndExpand" VerticalOptions="StartAndExpand" Spacing="15">
                                        <StackLayout.Margin>
                                            <OnPlatform x:TypeArguments="Thickness" Default="0,0,0,20">
                                                <On Platform="iOS" Value="0,0,0,5" />
                                            </OnPlatform>
                                        </StackLayout.Margin>
                                        <Label Text="{Binding ServiceName, Mode=OneTime}" />
                                        <StackLayout Orientation="Horizontal" Spacing="10">
                                            <Label VerticalOptions="Center" Text="{i18n:Translate ExternalServicesPage_AuthenticationType}" FontSize="{StaticResource SecondaryFontSize}" />
                                            <Picker
                                                ItemsSource="{Binding AuthTypes, Mode=OneWay}"
                                                SelectedItem="{Binding AuthenticationType}"
                                                ItemDisplayBinding="{Binding Translation}"
                                                VerticalOptions="CenterAndExpand"
                                                HorizontalOptions="FillAndExpand" />
                                        </StackLayout>
                                        <StackLayout Orientation="Horizontal" IsVisible="{Binding ShouldPresentApiKeyInput}" Spacing="10">
                                            <Label FontSize="{StaticResource SecondaryFontSize}" VerticalOptions="Center" Text="{i18n:Translate ExternalServicePage_ApiKey}" />
                                            <Entry
                                                BackgroundColor="{StaticResource PrimaryAccentColor}"
                                                VerticalOptions="CenterAndExpand"
                                                HorizontalOptions="FillAndExpand" Placeholder="xyz..."
                                                Text="{Binding ApiKey, Mode=TwoWay}" />
                                        </StackLayout>
                                        <controls:SmallButton
                                            IsVisible="{Binding ShouldPresentApiKeyInput}"
                                            HorizontalOptions="FillAndExpand"
                                            BackgroundColor="{StaticResource PrimaryAccentColor}"
                                            Text="{i18n:Translate ExternalServiecPage_RetrieveApiKey}"
                                            Command="{Binding RetrieveApiKeyCommand}" />
                                        <BoxView Style="{StaticResource Separator}" />
                                    </StackLayout>
                                </ViewCell>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </StackLayout>
            </ScrollView>
            <controls:BigButton
                Grid.Row="3"
                BackgroundColor="{StaticResource PrimaryAccentColor}"
                Command="{Binding SubmitCommand}"
                IsEnabled="{Binding CanSubmit, Mode=OneWay}"
                Text="{i18n:Translate ExternalServicesPage_Submit}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>