<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.CustomMapFilesPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    controls:ShellTitleView.HasBarBackButton="True"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:CustomMapFilesPageViewModel"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="4.5*" />
                <RowDefinition Height="{OnPlatform Default=1.04*, WinUI=0.52*}" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate CustomMapFilesPage_Title}" />
            <sf:SfListView
                Grid.Row="1" Margin="0,10,0,0"
                BackgroundColor="Transparent"
                ItemsSource="{Binding OptionalItemViewModels}"
                SelectionMode="None" ItemSize="58">
                <sf:SfListView.ItemTemplate>
                    <DataTemplate x:DataType="vms:MapFileItemViewModel">
                        <ViewCell ios:Cell.DefaultBackgroundColor="{StaticResource PrimaryBackgroundColor}">
                            <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1.1*" />
                                    <ColumnDefinition Width="4.94*" />
                                    <ColumnDefinition Width="0.08*" />
                                    <ColumnDefinition Width="0.88*" />
                                </Grid.ColumnDefinitions>
                                <Grid Grid.Column="0" Grid.ColumnSpan="4" Margin="0,3,0,3">
                                    <Grid.Resources>
                                        <ResourceDictionary>
                                            <Style TargetType="Grid" BasedOn="{StaticResource GridMgBaseStyle}">
                                                <Setter Property="BackgroundColor" Value="{StaticResource PrimaryAccentColor}" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsChecked, Mode=OneWay}" TargetType="Grid" Value="True" x:DataType="vms:MapFileItemViewModel">
                                                        <Setter Property="BackgroundColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ResourceDictionary>
                                    </Grid.Resources>
                                </Grid>
                                <Grid Grid.Column="0" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="0.03*" />
                                        <RowDefinition Height="0.15*" />
                                        <RowDefinition Height="1.4*" />
                                        <RowDefinition Height="0.15*" />
                                        <RowDefinition Height="0.03*" />
                                    </Grid.RowDefinitions>
                                    <controls:Checkbox Grid.Row="2" IsChecked="{Binding IsChecked, Mode=TwoWay}" HorizontalOptions="Center" />
                                </Grid>
                                <Label Grid.Column="1" Text="{Binding Option, Mode=OneWay}" VerticalTextAlignment="Center" />
                                <Grid
                                    Grid.Column="2" Margin="0,3,0,3"
                                    BackgroundColor="{StaticResource ListItemSeparatorColor}"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="FillAndExpand" />
                                <Image
                                    Grid.Column="3" Margin="-6,0,0,0" Aspect="AspectFit"
                                    HeightRequest="29" HorizontalOptions="Center"
                                    Source="menudiagnosticssmall.png"
                                    VerticalOptions="Center" WidthRequest="25">
                                    <Image.GestureRecognizers>
                                        <TapGestureRecognizer Command="{Binding MiscCommand}" />
                                    </Image.GestureRecognizers>
                                </Image>
                            </Grid>
                        </ViewCell>
                    </DataTemplate>
                </sf:SfListView.ItemTemplate>
            </sf:SfListView>
            <Grid Grid.Row="3" VerticalOptions="End">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="{OnPlatform Default=Auto, WinUI=0}" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="{OnPlatform Default=0, WinUI=*}" />
                </Grid.ColumnDefinitions>
                <controls:BigButton
                    Grid.Row="0" Grid.Column="0"
                    AutomationId="CustomMapFilesPage_MiscButton"
                    BackgroundColor="{StaticResource PrimaryAccentColor}"
                    Command="{Binding MiscCommand}"
                    Text="{i18n:Translate Misc_Title}" />
                <controls:BigButton
                    Grid.Row="0" Grid.Column="1"
                    AutomationId="CustomMapFilesPage_UploadButton"
                    BackgroundColor="{StaticResource PrimaryAccentColor}"
                    Command="{Binding UploadCommand}"
                    Text="{i18n:Translate Upload_Title}" />
                <controls:BigButton
                    Grid.Row="{OnPlatform Default=1, WinUI=0}"
                    Grid.Column="{OnPlatform Default=0, WinUI=2}"
                    Grid.ColumnSpan="{OnPlatform Default=2, WinUI=1}"
                    AutomationId="CustomMapFilesPage_SubmitButton"
                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                    Command="{Binding SubmitCommand}"
                    Text="{i18n:Translate CustomOptions_Flash}" />
            </Grid>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>