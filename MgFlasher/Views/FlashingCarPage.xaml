<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.FlashingCarPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView"
    xmlns:models="clr-namespace:MgFlasher.Models"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:FlashingCarPageViewModel"
    Shell.NavBarIsVisible="False">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="2*" />
                <RowDefinition Height="1.05*" />
                <RowDefinition Height="0.15*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Margin="0,20,0,0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate FlashingCar_Headline}" />
            <Grid Grid.Row="1" Margin="20" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <controls:GifImage
                    x:Name="Gif" Grid.Row="0" Grid.Column="0" ZIndex="1000"
                    Source="flashinganimation.gif">
                    <controls:GifImage.Margin>
                        <OnPlatform x:TypeArguments="Thickness">
                            <On Platform="WinUI" Value="-10,-5,-5,0" />
                            <On Platform="Default" Value="-6,0" />
                        </OnPlatform>
                    </controls:GifImage.Margin>
                </controls:GifImage>
                <controls:ProgressRing
                    Grid.Row="0" Grid.Column="0"
                    BackgroundColor="Transparent" ZIndex="999"
                    BorderColor="{StaticResource ItemSelectedHoverColor}"
                    RingBaseColor="Transparent"
                    RingProgressColor="{StaticResource PrimaryColor}"
                    Value="{Binding Progress, Mode=OneWay}" />
                <VerticalStackLayout Grid.Row="0" Grid.Column="0" HorizontalOptions="Center" VerticalOptions="Center">
                    <Label
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{i18n:Translate FlashingCar_Progress}"
                        TextColor="{StaticResource AccentTextColor}"
                        VerticalTextAlignment="Center" />
                    <Label
                        FontSize="{StaticResource LargeFontSize}"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Progress, Mode=OneWay, StringFormat='{}{0}%'}"
                        VerticalTextAlignment="Center" />
                </VerticalStackLayout>
            </Grid>
            <Grid Grid.Row="2" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand" RowSpacing="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Label Grid.Row="0" FontSize="{StaticResource PrimaryFontSize}" HorizontalOptions="Center" Text="{i18n:Translate FlashingCar_Details}" />
                <Frame
                    Grid.Row="1" Margin="0,10,0,0"
                    BackgroundColor="{StaticResource PrimaryBackgroundColor}"
                    BorderColor="{StaticResource ItemSelectedHoverColor}"
                    HasShadow="False">
                    <sf:SfListView
                        x:Name="MessagesListView" Margin="0"
                        BackgroundColor="Transparent"
                        ItemsSource="{Binding Messages}"
                        ItemSize="15" ItemSpacing="0"
                        VerticalOptions="FillAndExpand"
                        HorizontalOptions="FillAndExpand" SelectionMode="None">
                        <sf:SfListView.ItemTemplate>
                            <DataTemplate x:DataType="models:EventMessageModel">
                                <Label
                                    LineBreakMode="NoWrap" HorizontalTextAlignment="Start"
                                    VerticalTextAlignment="Center"
                                    VerticalOptions="FillAndExpand"
                                    FontSize="{StaticResource ExtraSmallFontSize}"
                                    HorizontalOptions="FillAndExpand"
                                    Text="{Binding Text}" />
                            </DataTemplate>
                        </sf:SfListView.ItemTemplate>
                    </sf:SfListView>
                </Frame>
            </Grid>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>