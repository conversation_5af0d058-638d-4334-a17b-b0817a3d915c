﻿using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.ViewModels;
using MgFlasher.ViewModels.CustomCodeDiagnostics;
using Microsoft.Maui.Controls.Xaml;

namespace MgFlasher.Views;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class RadiatorFlapDiagnosticsPage : BasePage
{
    public RadiatorFlapDiagnosticsPage() : base(DependencyResolver.Resolve<RadiatorFlapDiagnosticsPageViewModel>(), PageType.RadiatorFlapDiagnostics)
    {
        InitializeComponent();
    }
}