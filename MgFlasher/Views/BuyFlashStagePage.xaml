<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.BuyFlashStagePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:models="clr-namespace:MgFlasher.Models"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:views="clr-namespace:MgFlasher.Views"
    controls:ShellTitleView.HasBarBackButton="True"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:BuyFlashStagePageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource BuyFlashStagePageMainGridPageStyle}">
            <Grid.Resources>
                <ResourceDictionary>
                    <Style TargetType="Image">
                        <Setter Property="Aspect" Value="AspectFit" />
                        <Setter Property="WidthRequest" Value="21" />
                        <Setter Property="HeightRequest" Value="17" />
                    </Style>
                    <Style x:Key="BuyFlashStagePageMainGridPageStyle" TargetType="Grid" BasedOn="{StaticResource MainGridPageStyle}">
                        <Setter Property="Grid.RowDefinitions">
                            <Setter.Value>
                                <RowDefinitionCollection>
                                    <RowDefinition Height="10*" />
                                    <RowDefinition Height="0*" />
                                    <RowDefinition Height="*" />
                                </RowDefinitionCollection>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <DataTrigger TargetType="Grid" Binding="{Binding CanDownloadStage, Mode=OneWay}" Value="True" x:DataType="vms:BuyFlashStagePageViewModel">
                                <Setter Property="Grid.RowDefinitions">
                                    <Setter.Value>
                                        <RowDefinitionCollection>
                                            <RowDefinition Height="9*" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="*" />
                                        </RowDefinitionCollection>
                                    </Setter.Value>
                                </Setter>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ResourceDictionary>
            </Grid.Resources>
            <ScrollView Grid.Row="0">
                <StackLayout Orientation="Vertical" Spacing="15" AutomationId="BuyFlashStagePage_StackLayout">
                    <Label FontSize="{StaticResource PrimaryFontSize}" IsVisible="{Binding StageDescription.Headline1, Converter={StaticResource StringToVisibility}}" Text="{Binding StageDescription.Headline1}" />
                    <Label FontSize="{StaticResource PrimaryFontSize}" IsVisible="{Binding StageDescription.Headline2, Converter={StaticResource StringToVisibility}}" Text="{Binding StageDescription.Headline2}" />
                    <StackLayout Orientation="Vertical">
                        <Label
                            FontSize="{StaticResource SecondaryFontSize}"
                            FontAttributes="Bold" HorizontalTextAlignment="Start"
                            IsVisible="{Binding StageDescription.Subline1, Converter={StaticResource StringToVisibility}}"
                            Text="{Binding StageDescription.Subline1}" />
                        <Label FontSize="{StaticResource SecondaryFontSize}" HorizontalTextAlignment="Start" IsVisible="{Binding StageDescription.Subline2, Converter={StaticResource StringToVisibility}}" Text="{Binding StageDescription.Subline2}" />
                    </StackLayout>
                    <Label FontSize="{StaticResource SecondaryFontSize}" IsVisible="{Binding StageDescription.Block1, Converter={StaticResource StringToVisibility}}" Text="{Binding StageDescription.Block1}" />
                    <Label FontSize="{StaticResource SecondaryFontSize}" IsVisible="{Binding StageDescription.Subline3, Converter={StaticResource StringToVisibility}}" Text="{Binding StageDescription.Subline3}" />
                    <Label FontSize="{StaticResource SecondaryFontSize}" IsVisible="{Binding StageDescription.Subline4, Converter={StaticResource StringToVisibility}}" Text="{Binding StageDescription.Subline4}" />
                    <Label FontSize="{StaticResource PrimaryFontSize}" IsVisible="{Binding StageDescription.List3, Converter={StaticResource CollectionToVisibility}}" Text="{Binding StageDescription.Headline4}" />
                    <controls:RepeaterView x:TypeArguments="sys:String" ItemsSource="{Binding StageDescription.List3}" Spacing="15">
                        <controls:RepeaterView.ItemTemplate>
                            <DataTemplate x:DataType="x:String">
                                <Grid ColumnDefinitions="Auto, *">
                                    <Image Grid.Column="0" Source="oksmall.png" />
                                    <Label
                                        Grid.Column="1" HorizontalOptions="FillAndExpand"
                                        VerticalOptions="FillAndExpand"
                                        LineBreakMode="WordWrap"
                                        FontSize="{StaticResource SecondaryFontSize}"
                                        Text="{Binding ., Converter={StaticResource VersionNameToTextConverter}}" />
                                </Grid>
                            </DataTemplate>
                        </controls:RepeaterView.ItemTemplate>
                    </controls:RepeaterView>
                    <Label FontSize="{StaticResource PrimaryFontSize}" IsVisible="{Binding StageDescription.Headline3, Converter={StaticResource StringToVisibility}}" Text="{Binding StageDescription.Headline3}" />
                    <controls:RepeaterView x:TypeArguments="sys:String" ItemsSource="{Binding StageDescription.List}" Spacing="15">
                        <controls:RepeaterView.ItemTemplate>
                            <DataTemplate x:DataType="x:String">
                                <Grid ColumnDefinitions="Auto, *">
                                    <Image Grid.Column="0" Source="oksmall.png" />
                                    <Label
                                        Grid.Column="1" HorizontalOptions="FillAndExpand"
                                        VerticalOptions="FillAndExpand"
                                        LineBreakMode="WordWrap"
                                        FontSize="{StaticResource SecondaryFontSize}"
                                        Text="{Binding .}" />
                                </Grid>
                            </DataTemplate>
                        </controls:RepeaterView.ItemTemplate>
                    </controls:RepeaterView>
                    <Label FontSize="{StaticResource PrimaryFontSize}" IsVisible="{Binding StageDescription.List2Headline, Converter={StaticResource StringToVisibility}}" Text="{Binding StageDescription.List2Headline}" />
                    <controls:RepeaterView x:TypeArguments="sys:String" ItemsSource="{Binding StageDescription.List2}" Spacing="15">
                        <controls:RepeaterView.ItemTemplate>
                            <DataTemplate x:DataType="x:String">
                                <Grid ColumnDefinitions="Auto, *">
                                    <Image Grid.Column="0" Source="oksmall.png" />
                                    <Label
                                        Grid.Column="1" HorizontalOptions="FillAndExpand"
                                        VerticalOptions="FillAndExpand"
                                        LineBreakMode="WordWrap"
                                        FontSize="{StaticResource SecondaryFontSize}"
                                        Text="{Binding .}" />
                                </Grid>
                            </DataTemplate>
                        </controls:RepeaterView.ItemTemplate>
                    </controls:RepeaterView>
                    <Label FontSize="{StaticResource PrimaryFontSize}" IsVisible="{Binding StageDescription.List4, Converter={StaticResource CollectionToVisibility}}" Text="{Binding StageDescription.Headline5}" />
                    <StackLayout Orientation="Horizontal" IsVisible="{Binding StageDescription.List4, Converter={StaticResource CollectionToVisibility}}">
                        <Label FontSize="{StaticResource SmallFontSize}" Text="{Binding StageDescription.Subline5}" FontAttributes="Italic" />
                        <Label FontSize="{StaticResource SmallFontSize}" Text="{Binding StageDescription.Subline6, Converter={StaticResource VersionNameToTextConverter}}" FontAttributes="Italic" />
                    </StackLayout>
                    <controls:RepeaterView x:TypeArguments="sys:String" ItemsSource="{Binding StageDescription.List4}" Spacing="15">
                        <controls:RepeaterView.ItemTemplate>
                            <DataTemplate x:DataType="x:String">
                                <Grid ColumnDefinitions="Auto, *">
                                    <Image Grid.Column="0" Source="resetecusmall.png" />
                                    <Label
                                        Grid.Column="1" HorizontalOptions="FillAndExpand"
                                        VerticalOptions="FillAndExpand"
                                        LineBreakMode="WordWrap"
                                        FontSize="{StaticResource SecondaryFontSize}"
                                        Text="{Binding .}" />
                                </Grid>
                            </DataTemplate>
                        </controls:RepeaterView.ItemTemplate>
                    </controls:RepeaterView>

                    <Label FontSize="{StaticResource SecondaryFontSize}" IsVisible="{Binding StageDescription.Block2, Converter={StaticResource StringToVisibility}}" Text="{Binding StageDescription.Block2}" />

                    <StackLayout Orientation="Vertical" IsVisible="{Binding CanOpenProductPage, Mode=OneWay}" Spacing="5" VerticalOptions="Start">
                        <Label FontAttributes="Bold" Text="{i18n:Translate BuyFlashStagePage_IncludedInProducts}" />
                        <controls:RepeaterView x:TypeArguments="models:ProductLinkViewModel" ItemsSource="{Binding IncludedInProducts, Mode=OneWay}" Spacing="10">
                            <controls:RepeaterView.ItemTemplate>
                                <DataTemplate x:DataType="models:ProductLinkViewModel">
                                    <Grid ColumnDefinitions="Auto, *">
                                        <Image Grid.Column="0" Source="oksmall.png" />
                                        <Label
                                            Grid.Column="1" HorizontalOptions="FillAndExpand"
                                            VerticalOptions="FillAndExpand"
                                            LineBreakMode="WordWrap"
                                            FontSize="{StaticResource SecondaryFontSize}"
                                            TextColor="Blue"
                                            Text="{Binding Name, Mode=OneWay}"
                                            TextDecorations="Underline">
                                            <Label.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding OpenLinkCommand}" />
                                            </Label.GestureRecognizers>
                                        </Label>
                                    </Grid>
                                </DataTemplate>
                            </controls:RepeaterView.ItemTemplate>
                        </controls:RepeaterView>
                    </StackLayout>
                </StackLayout>
            </ScrollView>
            <controls:BigButton
                Grid.Row="1"
                IsVisible="{Binding CanDownloadStage}"
                Command="{Binding DownloadAndShareStockMapCommand}"
                BackgroundColor="{StaticResource PrimaryAccentColor}"
                IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                Text="{i18n:Translate BuyFlashStagePage_DownloadAndShareStockMap}" />
            <controls:BigButton
                Grid.Row="2"
                AutomationId="BuyFlashStagePage_SubmitButton"
                BackgroundColor="{StaticResource PrimaryColor}"
                Command="{Binding SubmitCommand}"
                IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                IsVisible="{Binding CommandVisible}">
                <controls:BigButton.Style>
                    <Style BasedOn="{StaticResource ButtonMgBaseStyle}" TargetType="controls:BigButton">
                        <Setter Property="Text" Value="{i18n:Translate BuyFlashStagePage_Redeem}" />
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsBought}" TargetType="controls:BigButton" Value="True" x:DataType="vms:BuyFlashStagePageViewModel">
                                <Setter Property="Text" Value="{i18n:Translate BuyFlashStagePage_Flash}" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </controls:BigButton.Style>
            </controls:BigButton>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>