<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.UserPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:ms="clr-namespace:MgFlasher.Views.Controls"
    xmlns:views="clr-namespace:MgFlasher.Views"
    mc:Ignorable="d"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    controls:ShellTitleView.HasContextOptionsButton="True"
    x:DataType="vms:UserPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="0.5*" />
                <RowDefinition Height="{OnIdiom Phone={OnPlatform Default=*}, Default={OnPlatform Android=Auto, Default=*}}" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="2.1*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Text="{i18n:Translate UserPage_Title}" Style="{StaticResource LabelPageTitleStyle}" />
            <HorizontalStackLayout Grid.Row="1" HorizontalOptions="CenterAndExpand" Spacing="10">
                <Image Source="user60.png" Aspect="AspectFit" HeightRequest="60" WidthRequest="60" />
                <Label Text="{Binding Email, Mode=OneWay}" VerticalOptions="Center" />
            </HorizontalStackLayout>
            <ListView
                Grid.Row="2" Margin="60,10,60,0"
                BackgroundColor="{StaticResource PrimaryAccentColor}"
                HorizontalOptions="CenterAndExpand"
                ItemsSource="{Binding Roles, Mode=OneWay}"
                CachingStrategy="RecycleElement"
                SeparatorColor="{StaticResource ListItemSeparatorColor}"
                VerticalScrollBarVisibility="Always"
                SeparatorVisibility="Default"
                RowHeight="{OnIdiom Phone={OnPlatform Default=20}, Desktop={OnPlatform Default=20}, Default={OnPlatform Android=40, Default=20}}"
                SelectionMode="None" VerticalOptions="CenterAndExpand">
                <ListView.ItemTemplate>
                    <DataTemplate x:DataType="x:String">
                        <ViewCell>
                            <Label
                                Margin="0"
                                FontSize="{StaticResource SmallFontSize}"
                                HorizontalTextAlignment="Center"
                                Text="{Binding}"
                                TextColor="{StaticResource PrimaryImportantTextColor}" />
                        </ViewCell>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>
            <Label
                Grid.Row="3" Margin="0,20,0,10"
                FontSize="{StaticResource SecondaryFontSize}"
                HorizontalOptions="CenterAndExpand"
                HorizontalTextAlignment="Center"
                Text="{i18n:Translate UserPage_LicensesText}"
                IsVisible="{Binding HasRedeemableProductItems, Mode=OneWay}"
                VerticalOptions="CenterAndExpand" />
            <ListView
                Grid.Row="4" BackgroundColor="Transparent"
                HorizontalOptions="Center"
                IsVisible="{Binding HasRedeemableProductItems, Mode=OneWay}"
                ItemsSource="{Binding RedeemableProductItems}"
                SelectionMode="None" RowHeight="70"
                SeparatorVisibility="None"
                VerticalOptions="FillAndExpand">
                <ListView.ItemTemplate>
                    <DataTemplate x:DataType="vms:RedeemableProductItemViewModel">
                        <ViewCell>
                            <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1.1*" />
                                    <ColumnDefinition Width="4.94*" />
                                    <ColumnDefinition Width="0.08*" />
                                    <ColumnDefinition Width="0.88*" />
                                </Grid.ColumnDefinitions>
                                <Grid Grid.Column="0" Grid.ColumnSpan="4" Margin="0,3,0,3" BackgroundColor="{StaticResource ItemSelectedHoverColor}" />
                                <Label
                                    Grid.Column="0" Grid.ColumnSpan="2"
                                    HorizontalTextAlignment="Center"
                                    Text="{Binding ProductTypeText, Mode=OneWay}"
                                    VerticalTextAlignment="Center" />
                                <Grid
                                    Grid.Column="2" Margin="0,3,0,3"
                                    BackgroundColor="{StaticResource ListItemSeparatorColor}"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="FillAndExpand" />
                                <Label
                                    Grid.Column="3" Margin="0"
                                    HorizontalTextAlignment="Center"
                                    Text="{Binding Quantity, Mode=OneWay}"
                                    VerticalTextAlignment="Center" />
                            </Grid>
                        </ViewCell>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>
            <StackLayout Grid.Row="4" Orientation="Vertical" VerticalOptions="End" IsVisible="{Binding HasRedeemableProductItems, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}">
                <controls:SmallButton
                    IsEnabled="{Binding CanGoToMyCarsPage, Mode=OneWay}"
                    Opacity="{Binding CanGoToMyCarsPage, Mode=OneWay, Converter={StaticResource BoolToVisualOpacityConverter}}"
                    BackgroundColor="{StaticResource PrimaryAccentColor}"
                    Command="{Binding GoToMyCarsPageCommand}"
                    Text="{i18n:Translate UserPage_MyCars}" />
                <controls:SmallButton BackgroundColor="{StaticResource PrimaryAccentColor}" Command="{Binding GoToSyncPageCommand}" Text="{i18n:Translate UserPage_Misc_SyncPageButtonText}" />
                <controls:SmallButton BackgroundColor="{StaticResource PrimaryAccentColor}" Command="{Binding RegisterToBetaTestingCommand}" IsVisible="{Binding CanJoinBetaTesting, Mode=OneWay}" Text="{i18n:Translate UserPage_Misc_RegisterToBetaTesting}" />
                <controls:SmallButton BackgroundColor="{StaticResource PrimaryAccentColor}" Command="{Binding ResetPopupTutorialsCommand}" Text="{i18n:Translate UserPage_Misc_ResetTutorialPopups}" />
                <controls:SmallButton BackgroundColor="{StaticResource PrimaryAccentColor}" Command="{Binding LogoutCommand}" Text="{i18n:Translate UserPage_Misc_Logout}" />
                <controls:SmallButton BackgroundColor="{StaticResource PrimaryColor}" Command="{Binding DeleteCommand}" Text="{i18n:Translate UserPage_Misc_Delete}" />
            </StackLayout>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>