<views:BasePage
    x:Class="MgFlasher.Views.KnowledgeBasePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:behaviors="clr-namespace:MgFlasher.Behaviors"
    mc:Ignorable="d"
    controls:ShellTitleView.HasContextOptionsButton="True"
    x:Name="root"
    x:DataType="vms:KnowledgeBasePageViewModel">
    <views:BasePage.Body>
        <Grid Padding="0" Margin="0">
            <WebView
                x:Name="webView" BackgroundColor="Transparent"
                VerticalOptions="FillAndExpand"
                HorizontalOptions="FillAndExpand"
                Source="{Binding Url}">
                <WebView.Behaviors>
                    <behaviors:EventToCommandBehavior Command="{Binding OpenLoaderCommand}" EventName="Navigating" />
                    <behaviors:EventToCommandBehavior Command="{Binding CloseLoaderCommand}" EventName="Navigated" />
                </WebView.Behaviors>
            </WebView>
            <controls:BigButton
                Text="&#10227;" HorizontalOptions="Start"
                Margin="10"
                BackgroundColor="{StaticResource PrimaryAccentColor}"
                TextColor="{StaticResource PrimaryTextColor}"
                Command="{Binding ReloadCommand}"
                CommandParameter="{x:Reference webView}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>