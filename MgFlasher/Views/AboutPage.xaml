<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    controls:ShellTitleView.HasBarBackButton="False"
    x:Class="MgFlasher.Views.AboutPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:AboutViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <ScrollView>
                <StackLayout Orientation="Vertical" Spacing="25">
                    <Image HorizontalOptions="Center" Source="logo_2.png" MaximumHeightRequest="90">
                        <Image.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding ToggleDevModeCommand}" />
                        </Image.GestureRecognizers>
                    </Image>
                    <Label FontAttributes="Bold" Text="{i18n:Translate About_Block1}" />
                    <StackLayout Orientation="Vertical">
                        <Label FontSize="{StaticResource SecondaryFontSize}" Text="{Binding AppVersion}" />
                        <Label FontSize="{StaticResource SecondaryFontSize}" Text="{Binding OsVersion}" />
                        <Label FontSize="{StaticResource SecondaryFontSize}" Text="{Binding DeviceName}" />
                        <Label IsVisible="{Binding IsDevMode, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" Text="{Binding ENETIP}" />
                        <Label IsVisible="{Binding IsDevMode, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" Text="{Binding BackendURL}" />
                    </StackLayout>
                    <StackLayout Orientation="Vertical" Spacing="5">
                        <Label Text="{i18n:Translate About_Diagnostics}" />
                        <controls:SmallButton BackgroundColor="{StaticResource PrimaryAccentColor}" Command="{Binding ShareAppLogsCommand}" IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" Text="{i18n:Translate Diagnostics_ShareAppLogs}" />
                        <controls:SmallButton
                            BackgroundColor="{StaticResource PrimaryAccentColor}"
                            Command="{Binding ReadPartialEcuCommand}"
                            IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                            IsVisible="{Binding IsDevMode, Mode=OneWay}"
                            Text="{i18n:Translate Misc_ReadPartialEcu}" />
                        <controls:SmallButton
                            BackgroundColor="{StaticResource PrimaryAccentColor}"
                            Command="{Binding OverrideEnetIpCommand}"
                            IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                            IsVisible="{Binding IsDevMode, Mode=OneWay}"
                            Text="{i18n:Translate Misc_OverrideEnetIp}" />
                        <controls:SmallButton
                            BackgroundColor="{StaticResource PrimaryAccentColor}"
                            Command="{Binding OverrideBackendUrlCommand}"
                            IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                            IsVisible="{Binding IsDevMode, Mode=OneWay}"
                            Text="{i18n:Translate Misc_OverrideBackendUrl}" />
                        <controls:SmallButton
                            BackgroundColor="{StaticResource PrimaryAccentColor}"
                            Command="{Binding DeleteDebugBackupCommand}"
                            IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                            IsVisible="{Binding IsAbleToDeleteBackups, Mode=OneWay}"
                            Text="{i18n:Translate Misc_DeleteDebugBackup}" />
                    </StackLayout>
                </StackLayout>
            </ScrollView>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>