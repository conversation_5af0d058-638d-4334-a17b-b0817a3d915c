<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.CustomCodeVersionsPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:input="clr-namespace:InputKit.Shared.Controls;assembly=InputKit.Maui"
    xmlns:models="clr-namespace:MgFlasher.Models"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    controls:ShellTitleView.HasBarBackButton="True"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView"
    xmlns:behaviors="clr-namespace:MgFlasher.Behaviors"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:CustomCodeVersionsPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.Resources>
                <ResourceDictionary>
                    <Style TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}">
                        <Setter Property="VerticalTextAlignment" Value="Center" />
                        <Setter Property="FontSize" Value="{StaticResource SecondaryFontSize}" />
                    </Style>
                </ResourceDictionary>
            </Grid.Resources>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="9.5*" />
                <RowDefinition Height="0.5*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Label
                Grid.Row="0"
                Style="{StaticResource LabelPageTitleStyle}"
                FontSize="{StaticResource PrimaryFontSize}"
                HorizontalOptions="Center"
                Text="{i18n:Translate CustomCodeVersion_Title}" />
            <sf:SfListView
                x:Name="listView" Grid.Row="1" Margin="0,10,0,0"
                BackgroundColor="Transparent" SelectionMode="None"
                AutoFitMode="DynamicHeight"
                ItemsSource="{Binding CustomCodeVersionInfoViewModels}"
                LoadMoreOption="{Binding LoadMoreCustomCodesButtonVisible, Converter={StaticResource BooleanToLoadMoreOptionConverter}, Mode=OneWay}"
                LoadMoreCommand="{Binding ShowAllCCVersionsCommand}"
                CanMaintainScrollPosition="False"
                EmptyView="{i18n:Translate NoItems_PleaseSync}">
                <sf:SfListView.Behaviors>
                    <behaviors:SfListViewAccordionBehavior ItemsSource="{Binding CustomCodeVersionInfoViewModels}" />
                </sf:SfListView.Behaviors>
                <sf:SfListView.ItemTemplate>
                    <DataTemplate x:DataType="models:CustomCodeVersionInfoViewModel">
                        <Grid
                            Padding="1" Margin="1" VerticalOptions="FillAndExpand"
                            HorizontalOptions="FillAndExpand" RowSpacing="5">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="190" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Grid Grid.Row="0" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                                <Grid.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding ExpandCommand}" />
                                </Grid.GestureRecognizers>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="0.03*" />
                                    <RowDefinition Height="0.15*" />
                                    <RowDefinition Height="8*" />
                                    <RowDefinition Height="0.15*" />
                                    <RowDefinition Height="0.03*" />
                                </Grid.RowDefinitions>
                                <Grid Grid.Row="1" Grid.RowSpan="3">
                                    <Grid.Resources>
                                        <ResourceDictionary>
                                            <Style TargetType="Grid" BasedOn="{StaticResource GridMgBaseStyle}">
                                                <Setter Property="BackgroundColor" Value="Transparent" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsChecked, Mode=OneWay}" TargetType="Grid" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                        <Setter Property="BackgroundColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ResourceDictionary>
                                    </Grid.Resources>
                                </Grid>
                                <Grid Grid.Row="2">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="6.2*" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid Grid.Column="0" Margin="5,0,0,0" HorizontalOptions="FillAndExpand" VerticalOptions="Center">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="{OnPlatform iOS=*, WinUI=*, Default=Auto}" />
                                        </Grid.RowDefinitions>
                                        <StackLayout Orientation="Horizontal" Grid.Row="0">
                                            <Label
                                                Padding="{OnIdiom Desktop='0', Default='5,-2,5,0'}"
                                                BackgroundColor="{StaticResource ItemSelectedHoverColor}"
                                                VerticalTextAlignment="Center"
                                                HorizontalTextAlignment="Center"
                                                Text="{i18n:Translate CustomCodeVersion_ItemTitle}">
                                                <Label.Resources>
                                                    <ResourceDictionary>
                                                        <Style TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}">
                                                            <Setter Property="BackgroundColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                            <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                            <Style.Triggers>
                                                                <DataTrigger TargetType="Label" Binding="{Binding IsChecked, Mode=OneWay}" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                                    <Setter Property="BackgroundColor" Value="{StaticResource PrimaryBackgroundColor}" />
                                                                </DataTrigger>
                                                                <DataTrigger TargetType="Label" Binding="{Binding CanSelect, Converter={StaticResource InvertedBoolConverter}}" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                                    <Setter Property="TextColor" Value="{StaticResource PrimaryBackgroundColor}" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ResourceDictionary>
                                                </Label.Resources>
                                            </Label>
                                            <Label
                                                Padding="5,0,5,0" LineBreakMode="NoWrap"
                                                BackgroundColor="{StaticResource PrimaryErrorTextBackgroundColor}"
                                                IsVisible="{Binding CanSelect, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}"
                                                Text="X" />
                                            <Label
                                                Padding="5,0,5,0" LineBreakMode="NoWrap"
                                                BackgroundColor="{StaticResource PrimaryColor}"
                                                IsVisible="{Binding IsNewVersion, Mode=OneWay}"
                                                Text="{i18n:Translate CustomCodeVersion_NewVersion}" />
                                            <Frame
                                                BorderColor="Gold" BackgroundColor="Transparent"
                                                CornerRadius="5" Padding="1"
                                                IsVisible="{Binding IsCurrentVersion, Mode=OneWay}">
                                                <Label Padding="5,0,5,0" LineBreakMode="NoWrap" TextColor="Gold" Text="{i18n:Translate CustomCodeVersion_CurrentVersion}" />
                                            </Frame>
                                            <Frame
                                                BorderColor="{StaticResource PrimaryColor}"
                                                BackgroundColor="Transparent" CornerRadius="5"
                                                Padding="1"
                                                IsVisible="{Binding IsObsoleteAndCurrentVersion, Mode=OneWay}">
                                                <Label Padding="5,0,5,0" LineBreakMode="NoWrap" TextColor="{StaticResource PrimaryColor}" Text="{i18n:Translate Warning}" />
                                            </Frame>
                                            <Frame
                                                BorderColor="{StaticResource PrimarySuccessTextColor}"
                                                BackgroundColor="Transparent" CornerRadius="5"
                                                Padding="1"
                                                IsVisible="{Binding IsFileVersion, Mode=OneWay}">
                                                <Label Padding="5,0,5,0" LineBreakMode="NoWrap" TextColor="{StaticResource PrimarySuccessTextColor}" Text="{i18n:Translate CustomCodeVersion_FileVersion}" />
                                            </Frame>
                                        </StackLayout>
                                        <StackLayout Orientation="Horizontal" Grid.Row="1">
                                            <Label Text="{i18n:Translate CustomCodeVersion_VersionNumber}">
                                                <Label.Resources>
                                                    <ResourceDictionary>
                                                        <Style TargetType="Label">
                                                            <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                            <Style.Triggers>
                                                                <DataTrigger TargetType="Label" Binding="{Binding CanSelect, Converter={StaticResource InvertedBoolConverter}}" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                                    <Setter Property="TextColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ResourceDictionary>
                                                </Label.Resources>
                                            </Label>
                                            <Label FontAttributes="Bold" Text="{Binding CustomCodeVersion, Mode=OneWay}">
                                                <Label.Resources>
                                                    <ResourceDictionary>
                                                        <Style TargetType="Label">
                                                            <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                            <Style.Triggers>
                                                                <DataTrigger TargetType="Label" Binding="{Binding CanSelect, Converter={StaticResource InvertedBoolConverter}}" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                                    <Setter Property="TextColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ResourceDictionary>
                                                </Label.Resources>
                                            </Label>
                                        </StackLayout>
                                        <StackLayout Orientation="Horizontal" Grid.Row="2">
                                            <Label Text="{i18n:Translate CustomCodeVersion_ReleasedAt}">
                                                <Label.Resources>
                                                    <ResourceDictionary>
                                                        <Style TargetType="Label">
                                                            <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                            <Style.Triggers>
                                                                <DataTrigger TargetType="Label" Binding="{Binding CanSelect, Converter={StaticResource InvertedBoolConverter}}" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                                    <Setter Property="TextColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ResourceDictionary>
                                                </Label.Resources>
                                            </Label>
                                            <Label FontAttributes="Bold" Text="{Binding ReleasedAt, Mode=OneWay}">
                                                <Label.Resources>
                                                    <ResourceDictionary>
                                                        <Style TargetType="Label">
                                                            <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                            <Style.Triggers>
                                                                <DataTrigger TargetType="Label" Binding="{Binding CanSelect, Converter={StaticResource InvertedBoolConverter}}" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                                    <Setter Property="TextColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ResourceDictionary>
                                                </Label.Resources>
                                            </Label>
                                        </StackLayout>
                                        <StackLayout Orientation="Horizontal" Grid.Row="3">
                                            <Label Text="{i18n:Translate CustomCodeVersion_AccessLevel}">
                                                <Label.Resources>
                                                    <ResourceDictionary>
                                                        <Style TargetType="Label">
                                                            <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                            <Style.Triggers>
                                                                <DataTrigger TargetType="Label" Binding="{Binding CanSelect, Converter={StaticResource InvertedBoolConverter}}" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                                    <Setter Property="TextColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ResourceDictionary>
                                                </Label.Resources>
                                            </Label>
                                            <Label FontAttributes="Bold" Text="{Binding CustomCodeAccessibility, Mode=OneWay}">
                                                <Label.Resources>
                                                    <ResourceDictionary>
                                                        <Style TargetType="Label">
                                                            <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                            <Style.Triggers>
                                                                <DataTrigger TargetType="Label" Binding="{Binding CanSelect, Converter={StaticResource InvertedBoolConverter}}" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                                    <Setter Property="TextColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ResourceDictionary>
                                                </Label.Resources>
                                            </Label>
                                        </StackLayout>
                                        <StackLayout Orientation="Horizontal" Grid.Row="4" IsVisible="{Binding CanSelect, Converter={StaticResource InvertedBoolConverter}}">
                                            <Label TextColor="{StaticResource PrimaryColor}" BackgroundColor="Transparent" Text="{i18n:Translate CustomCodeVersion_BlockedDueTo}" />
                                            <Label TextColor="{StaticResource PrimaryColor}" FontAttributes="Bold" Text="{Binding UnavailableReason}" />
                                        </StackLayout>

                                        <Label
                                            Grid.Row="5"
                                            FontSize="{StaticResource SmallFontSize}"
                                            FontAttributes="Italic"
                                            IsVisible="{Binding IsExpanded, Converter={StaticResource InvertedBoolConverter}}"
                                            Text="{i18n:Translate CustomCodeVersion_ShowMore}">
                                            <Label.Resources>
                                                <ResourceDictionary>
                                                    <Style TargetType="Label">
                                                        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                        <Style.Triggers>
                                                            <DataTrigger TargetType="Label" Binding="{Binding CanSelect, Converter={StaticResource InvertedBoolConverter}}" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                                <Setter Property="TextColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </ResourceDictionary>
                                            </Label.Resources>
                                        </Label>
                                        <Label
                                            Grid.Row="5"
                                            FontSize="{StaticResource SmallFontSize}"
                                            FontAttributes="Italic"
                                            IsVisible="{Binding IsExpanded}"
                                            Text="{i18n:Translate CustomCodeVersion_ShowLess}">
                                            <Label.Resources>
                                                <ResourceDictionary>
                                                    <Style TargetType="Label">
                                                        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                        <Style.Triggers>
                                                            <DataTrigger TargetType="Label" Binding="{Binding CanSelect, Converter={StaticResource InvertedBoolConverter}}" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                                <Setter Property="TextColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </ResourceDictionary>
                                            </Label.Resources>
                                        </Label>
                                    </Grid>
                                    <input:RadioButton Grid.Column="1" IsChecked="{Binding IsChecked, Mode=TwoWay}" IsVisible="{Binding CanSelect, Mode=OneWay}" />
                                </Grid>
                                <Grid
                                    Grid.Row="4"
                                    BackgroundColor="{StaticResource ListItemSeparatorColor}"
                                    HorizontalOptions="FillAndExpand" HeightRequest="1"
                                    VerticalOptions="FillAndExpand" />
                            </Grid>
                            <Grid
                                Grid.Row="1"
                                IsVisible="{Binding IsExpanded, Mode=OneWay}"
                                Margin="15,10,0,0" HorizontalOptions="FillAndExpand"
                                VerticalOptions="FillAndExpand">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="6*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Label Grid.Column="0" Text="{Binding ReleaseNotes, Mode=OneWay}" FontSize="{StaticResource SmallFontSize}">
                                    <Label.Resources>
                                        <ResourceDictionary>
                                            <Style TargetType="Label">
                                                <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                <Style.Triggers>
                                                    <DataTrigger TargetType="Label" Binding="{Binding CanSelect, Converter={StaticResource InvertedBoolConverter}}" Value="True" x:DataType="models:CustomCodeVersionInfoViewModel">
                                                        <Setter Property="TextColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ResourceDictionary>
                                    </Label.Resources>
                                </Label>
                            </Grid>
                        </Grid>
                    </DataTemplate>
                </sf:SfListView.ItemTemplate>
                <sf:SfListView.LoadMoreTemplate>
                    <DataTemplate>
                        <Grid>
                            <Border StrokeShape="RoundRectangle 6" BackgroundColor="{StaticResource ItemSelectedHoverColor}" Padding="10" HorizontalOptions="Start">
                                <Label Text="{i18n:Translate ShowDeprecatedCC_Button}" TextColor="{StaticResource PrimaryTextColor}" HorizontalTextAlignment="Center" VerticalTextAlignment="Center" />
                            </Border>
                        </Grid>
                    </DataTemplate>
                </sf:SfListView.LoadMoreTemplate>
            </sf:SfListView>
            <controls:BigButton
                Grid.Row="3"
                AutomationId="CustomCodeVersionsPage_SubmitButton"
                BackgroundColor="{StaticResource PrimaryColor}"
                Command="{Binding SubmitCommand}"
                IsEnabled="{Binding CanProceed}"
                Text="{i18n:Translate CustomCodeVersion_Button}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>