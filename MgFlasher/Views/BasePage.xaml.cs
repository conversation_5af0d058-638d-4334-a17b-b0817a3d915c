﻿using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Services;
using MgFlasher.ViewModels;
using System;
using System.Linq;
using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using MgFlasher.Views.Controls;
using MgFlasher.Services.Navigation;

namespace MgFlasher.Views;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class BasePage : ContentPage, IDisposable
{
    private readonly WeakReference<INavigationService> _navigationService;

    public PageViewModel ViewModel => (PageViewModel)BindingContext;
    public PageType PageId { get; }

    public static readonly BindableProperty BodyProperty =
        BindableProperty.Create(nameof(Body), typeof(View), typeof(BasePage), propertyChanged: OnBodyPropertyChanged);

    public View Body
    {
        get => (View)GetValue(BodyProperty);
        set => SetValue(BodyProperty, value);
    }

#if ANDROID
    public static new readonly BindableProperty IsBusyProperty = BindableProperty.Create(nameof(IsBusy), typeof(bool), typeof(BasePage), false);

    public new bool IsBusy
    {
        get { return (bool)GetValue(IsBusyProperty); }
        set { SetValue(IsBusyProperty, value); }
    }
#endif

    public BasePage(PageViewModel viewModel, PageType pageType)
    {
        BindingContext = viewModel;
        InitializeComponent();

        PageId = pageType;
        _navigationService = new WeakReference<INavigationService>(DependencyResolver.Resolve<INavigationService>());
        viewModel.CurrentPageType = pageType;
        viewModel.TutorialPopup = TutorialPopup.Popup;
    }

    public void Dispose()
    {
        foreach (var child in VisualTreeHelper.GetChildren<ContentView>(this).OfType<IDisposable>())
        {
            child.Dispose();
        }
    }

    protected override bool OnBackButtonPressed()
    {
        if (!IsBusy && ShellTitleView.GetHasBarBackButton(this) && ShellTitleView.GetBarBackButtonVisible(this) && _navigationService.TryGetTarget(out INavigationService navigationService))
        {
            navigationService.NavigateToPreviousAsync();
        }

        return true;
    }

    private static void OnBodyPropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var basePage = (BasePage)bindable;
        var body = newValue as View;

        if (oldValue != null && basePage.BodyLayout.Children.Contains(oldValue))
        {
            basePage.BodyLayout.Content = null;
        }

        basePage.BodyLayout.Content = body;
    }
}