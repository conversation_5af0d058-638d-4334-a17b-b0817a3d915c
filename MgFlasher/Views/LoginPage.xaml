<views:BasePage
    x:Class="MgFlasher.Views.LoginPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:behaviors="clr-namespace:MgFlasher.Behaviors"
    xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:LoginPageViewModel"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    controls:ShellTitleView.HasContextOptionsButton="True"
    HideSoftInputOnTapped="True" mc:Ignorable="d">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="{OnPlatform Default=2.76*, WinUI=3.26*}" />
                <RowDefinition Height="0.1*" />
                <RowDefinition Height="{OnPlatform Default=1*, WinUI=0.5*}" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate LoginPage_Title}" />
            <VerticalStackLayout Grid.Row="1" HorizontalOptions="FillAndExpand" Spacing="14" VerticalOptions="CenterAndExpand">
                <VerticalStackLayout.Resources>
                    <Style TargetType="ImageButton">
                        <Setter Property="BackgroundColor" Value="Transparent" />
                        <Setter Property="Command" Value="{Binding LoginCommand}" />
                        <Setter Property="IsEnabled" Value="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" />
                        <Setter Property="VerticalOptions" Value="Center" />
                    </Style>
                </VerticalStackLayout.Resources>
                <VerticalStackLayout>
                    <Label Text="{i18n:Translate Login_Email}" />
                    <Entry AutomationId="LoginPage_Email" IsReadOnly="{Binding IsBusy, Mode=OneWay}" Placeholder="{i18n:Translate Login_PleaseEnterEmail}" Text="{Binding Email, Mode=TwoWay}">
                        <Entry.Behaviors>
                            <behaviors:EmailValidatorBehavior IsValid="{Binding IsEmailValid, Mode=OneWayToSource}" />
                        </Entry.Behaviors>
                    </Entry>
                </VerticalStackLayout>
                <VerticalStackLayout>
                    <Label Text="{i18n:Translate Login_Password}" />
                    <Entry
                        AutomationId="LoginPage_Password" IsPassword="True"
                        IsReadOnly="{Binding IsBusy, Mode=OneWay}"
                        Placeholder="{i18n:Translate Login_PleaseEnterPassword}"
                        Text="{Binding Password, Mode=TwoWay}" />
                </VerticalStackLayout>
                <Label
                    FontSize="{StaticResource SmallFontSize}"
                    HorizontalOptions="Start"
                    Text="{i18n:Translate Login_ForgotPassword}"
                    TextColor="{StaticResource AccentSuccessTextColor}"
                    TextDecorations="Underline">
                    <Label.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding ForgotPasswordCommand}" />
                    </Label.GestureRecognizers>
                </Label>
                <Label FontSize="{StaticResource ExtraSmallFontSize}" HorizontalOptions="Center" HorizontalTextAlignment="Center" Text="{i18n:Translate Login_or}">
                    <Label.IsVisible>
                        <OnPlatform x:TypeArguments="x:Boolean" Default="True">
                            <On Platform="WinUI" Value="False" />
                        </OnPlatform>
                    </Label.IsVisible>
                </Label>
                <HorizontalStackLayout HorizontalOptions="Center">
                    <HorizontalStackLayout.IsVisible>
                        <OnPlatform x:TypeArguments="x:Boolean" Default="True">
                            <On Platform="WinUI" Value="False" />
                        </OnPlatform>
                    </HorizontalStackLayout.IsVisible>
                    <ImageButton CommandParameter="Google" Source="google.png" />
                    <ImageButton CommandParameter="Facebook" Source="facebook.png" />
                </HorizontalStackLayout>
                <Label FontSize="{StaticResource SmallFontSize}" HorizontalOptions="Center" HorizontalTextAlignment="Center" Text="ENET Wi-Fi/Cable Adapter is required to use the app">
                    <Label.IsVisible>
                        <OnPlatform x:TypeArguments="x:Boolean" Default="False">
                            <On Platform="WinUI" Value="True" />
                        </OnPlatform>
                    </Label.IsVisible>
                </Label>
                <Frame
                    Padding="0,5,0,5" BackgroundColor="White"
                    CornerRadius="5" HorizontalOptions="FillAndExpand"
                    IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                    BorderColor="{StaticResource PrimaryAccentColor}">
                    <Frame.Resources>
                        <ResourceDictionary>
                            <Style TargetType="Frame">
                                <Setter Property="Opacity" Value="1" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsBusy}" TargetType="Frame" Value="True" x:DataType="vms:LoginPageViewModel">
                                        <Setter Property="Opacity" Value="0.5" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ResourceDictionary>
                    </Frame.Resources>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding LoginCommand}" CommandParameter="Apple" />
                    </Frame.GestureRecognizers>
                    <Frame.IsVisible>
                        <OnPlatform x:TypeArguments="x:Boolean" Default="False">
                            <On Platform="iOS" Value="True" />
                        </OnPlatform>
                    </Frame.IsVisible>
                    <HorizontalStackLayout HorizontalOptions="CenterAndExpand" Spacing="2">
                        <Image Aspect="AspectFit" Scale="0.7" Source="apple_black.png" />
                        <Label Text="{i18n:Translate LoginPage_SignInWithApple}" TextColor="{StaticResource PrimaryAccentColor}" VerticalTextAlignment="Center" />
                    </HorizontalStackLayout>
                </Frame>
                <Label
                    FontSize="{StaticResource SmallFontSize}"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    IsVisible="{Binding HasMessage}"
                    Text="{Binding Message}"
                    TextColor="{StaticResource PrimaryImportantTextColor}" />
            </VerticalStackLayout>
            <Grid Grid.Row="3" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="{OnPlatform Default=Auto, WinUI=0}" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="{OnPlatform Default=0.2*, WinUI=1*}" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="{OnPlatform Default=0, WinUI=0.1*}" />
                </Grid.ColumnDefinitions>
                <controls:BigButton
                    Grid.Row="0" Grid.Column="0"
                    Grid.ColumnSpan="{OnPlatform Default=2, WinUI=1}"
                    BackgroundColor="{StaticResource PrimaryAccentColor}"
                    Command="{Binding RegisterCommand}"
                    AutomationId="LoginPage_Register"
                    IsEnabled="{Binding IsBusy, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}"
                    Text="{i18n:Translate Login_Register}" />
                <controls:BigButton
                    Grid.Column="{OnPlatform Default=1, WinUI=1}"
                    Grid.Row="{OnPlatform Default=1, WinUI=0}"
                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                    Command="{Binding LoginCommand}"
                    AutomationId="LoginPage_Login"
                    IsEnabled="{Binding CanLogin, Mode=OneWay}"
                    Opacity="{Binding CanLogin, Mode=OneWay, Converter={StaticResource BoolToVisualOpacityConverter}}"
                    Text="{i18n:Translate Login_Button}" />
                <controls:BigButton
                    Grid.Column="{OnPlatform Default=0, WinUI=2}"
                    Grid.Row="{OnPlatform Default=1, WinUI=0}"
                    BackgroundColor="{StaticResource PrimaryAccentColor}"
                    Command="{Binding MiscCommand}"
                    Text="&#x22EE;" />
            </Grid>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>