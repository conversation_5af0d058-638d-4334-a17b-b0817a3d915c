<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.StageVersionsPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:input="clr-namespace:InputKit.Shared.Controls;assembly=InputKit.Maui"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:ms="clr-namespace:MgFlasher.Views.Controls"
    xmlns:views="clr-namespace:MgFlasher.Views"
    controls:ShellTitleView.HasBarBackButton="True"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    xmlns:models="clr-namespace:MgFlasher.Models"
    xmlns:behaviors="clr-namespace:MgFlasher.Behaviors"
    x:DataType="vms:StageVersionsPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.Resources>
                <ResourceDictionary>
                    <Style TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}">
                        <Setter Property="VerticalTextAlignment" Value="Center" />
                        <Setter Property="FontSize" Value="{StaticResource SecondaryFontSize}" />
                    </Style>
                </ResourceDictionary>
            </Grid.Resources>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="9.5*" />
                <RowDefinition Height="0.5*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Label
                Grid.Row="0"
                Style="{StaticResource LabelPageTitleStyle}"
                FontSize="{StaticResource PrimaryFontSize}"
                HorizontalOptions="Center"
                Text="{i18n:Translate AvailableStageVersion_Title}" />
            <sf:SfListView
                x:Name="listView" Grid.Row="1" Margin="0,10,0,0"
                BackgroundColor="Transparent" SelectionMode="None"
                AutoFitMode="DynamicHeight"
                ItemsSource="{Binding VersionNameViewModels}">
                <sf:SfListView.Behaviors>
                    <behaviors:SfListViewAccordionBehavior ItemsSource="{Binding VersionNameViewModels}" />
                </sf:SfListView.Behaviors>
                <sf:SfListView.ItemTemplate>
                    <DataTemplate x:DataType="models:ChangesetVersionNameViewModel">
                        <Grid
                            Padding="1" Margin="1" VerticalOptions="FillAndExpand"
                            HorizontalOptions="FillAndExpand" RowSpacing="0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="150" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Grid Grid.Row="0" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                                <Grid.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding ExpandCommand}" />
                                </Grid.GestureRecognizers>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="0.03*" />
                                    <RowDefinition Height="0.15*" />
                                    <RowDefinition Height="8*" />
                                    <RowDefinition Height="0.15*" />
                                    <RowDefinition Height="0.03*" />
                                </Grid.RowDefinitions>
                                <Grid Grid.Row="1" Grid.RowSpan="3">
                                    <Grid.Resources>
                                        <ResourceDictionary>
                                            <Style TargetType="Grid" BasedOn="{StaticResource GridMgBaseStyle}">
                                                <Setter Property="BackgroundColor" Value="Transparent" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsChecked, Mode=OneWay}" TargetType="Grid" Value="True" x:DataType="models:ChangesetVersionNameViewModel">
                                                        <Setter Property="BackgroundColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ResourceDictionary>
                                    </Grid.Resources>
                                </Grid>
                                <Grid Grid.Row="2">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="6.2*" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid Grid.Column="0" Margin="5,0,0,0" HorizontalOptions="FillAndExpand" VerticalOptions="Center">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="{OnIdiom Phone={OnPlatform iOS=*, Default=Auto}, Desktop={OnPlatform Default=*}, Default={OnPlatform iOS=*, Default=Auto}}" />
                                        </Grid.RowDefinitions>
                                        <StackLayout Orientation="Horizontal" Grid.Row="0">
                                            <Label Padding="5,0,5,0" BackgroundColor="{StaticResource ItemSelectedHoverColor}" HorizontalTextAlignment="Center" Text="{Binding StageType, Mode=OneWay, Converter={StaticResource StageTypeToTextConverter}}">
                                                <Label.Resources>
                                                    <ResourceDictionary>
                                                        <Style TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}">
                                                            <Setter Property="BackgroundColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                                            <Style.Triggers>
                                                                <DataTrigger TargetType="Label" Binding="{Binding IsChecked, Mode=OneWay}" Value="True" x:DataType="models:ChangesetVersionNameViewModel">
                                                                    <Setter Property="BackgroundColor" Value="{StaticResource PrimaryBackgroundColor}" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ResourceDictionary>
                                                </Label.Resources>
                                            </Label>
                                            <Label FontSize="{StaticResource SmallFontSize}" LineBreakMode="TailTruncation" Text="{Binding VersionName, Mode=OneWay, Converter={StaticResource VersionNameToTextConverter}}" />
                                            <Label
                                                Padding="5,0,5,0" LineBreakMode="NoWrap"
                                                BackgroundColor="{StaticResource PrimaryColor}"
                                                IsVisible="{Binding IsNewVersion, Mode=OneWay}"
                                                Text="{i18n:Translate AvailableStageVersion_NewVersion}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Horizontal" Grid.Row="1">
                                            <Label Text="{i18n:Translate AvailableStageVersion_ReleasedAt}" />
                                            <Label FontAttributes="Bold" Text="{Binding NewestVersionNumberReleasedAt, Mode=OneWay, StringFormat='{0:yyyy-MM-dd}'}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Horizontal" Grid.Row="2">
                                            <Label Text="{i18n:Translate AvailableStageVersion_VersionNumber}" />
                                            <Label FontAttributes="Bold" Text="{Binding NewestVersionNumber, Mode=OneWay}" />
                                        </StackLayout>
                                        <Label
                                            Grid.Row="3"
                                            FontSize="{StaticResource SmallFontSize}"
                                            FontAttributes="Italic"
                                            IsVisible="{Binding IsExpanded, Converter={StaticResource InvertedBoolConverter}}"
                                            Text="{i18n:Translate AvailableStageVersion_ShowMore}" />
                                        <Label
                                            Grid.Row="3"
                                            FontSize="{StaticResource SmallFontSize}"
                                            FontAttributes="Italic"
                                            IsVisible="{Binding IsExpanded}"
                                            Text="{i18n:Translate AvailableStageVersion_ShowLess}" />
                                    </Grid>
                                    <input:RadioButton Grid.Column="1" IsVisible="{Binding IsExpanded, Converter={StaticResource InvertedBoolConverter}}" IsChecked="{Binding IsChecked, Mode=TwoWay}" />
                                </Grid>
                                <Grid
                                    Grid.Row="4"
                                    BackgroundColor="{StaticResource ListItemSeparatorColor}"
                                    HorizontalOptions="FillAndExpand" HeightRequest="1"
                                    VerticalOptions="FillAndExpand" />
                            </Grid>
                            <sf:SfListView
                                Grid.Row="1" SelectionMode="None"
                                ItemsSource="{Binding}"
                                AutoFitMode="DynamicHeight" MinimumHeightRequest="300"
                                IsVisible="{Binding IsExpanded, Mode=OneWay}"
                                Margin="15,10,0,0" HorizontalOptions="FillAndExpand"
                                VerticalOptions="FillAndExpand">
                                <sf:SfListView.ItemTemplate>
                                    <DataTemplate x:DataType="models:ChangesetVersionNumberViewModel">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="6*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <StackLayout Orientation="Vertical" Grid.Column="0">
                                                <HorizontalStackLayout>
                                                    <Label FontAttributes="Bold" LineBreakMode="NoWrap" Text="{Binding VersionNumber, Mode=OneWay}" TextColor="{StaticResource PrimaryImportantTextColor}" />
                                                    <Label
                                                        Padding="5,0,5,0" LineBreakMode="NoWrap"
                                                        BackgroundColor="{StaticResource PrimaryColor}"
                                                        IsVisible="{Binding IsNewestVersion, Mode=OneWay}"
                                                        Text="{i18n:Translate AvailableStageVersion_NewestVersion}" />
                                                </HorizontalStackLayout>
                                                <Label Text="{Binding ReleaseNotes, Mode=OneWay}" />
                                            </StackLayout>
                                            <input:RadioButton Grid.Column="1" IsChecked="{Binding IsChecked, Mode=TwoWay}" />
                                        </Grid>
                                    </DataTemplate>
                                </sf:SfListView.ItemTemplate>
                            </sf:SfListView>
                        </Grid>
                    </DataTemplate>
                </sf:SfListView.ItemTemplate>
            </sf:SfListView>
            <controls:BigButton
                Grid.Row="3"
                AutomationId="StageVersionsPage_SubmitButton"
                BackgroundColor="{StaticResource PrimaryColor}"
                Command="{Binding SubmitCommand}"
                IsEnabled="{Binding CanFlash}"
                Text="{i18n:Translate AvailableStageVersion_Button}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>