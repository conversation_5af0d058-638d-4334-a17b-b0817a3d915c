<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.SyncPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:SyncPageViewModel"
    controls:ShellTitleView.HasBarBackButton="True">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="4*" />
                <RowDefinition Height="0.1*" />
                <RowDefinition Height="0.52*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate SyncPage_Title}" />
            <Grid Grid.Row="1" Margin="0,10,0,0" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                <ScrollView>
                    <VerticalStackLayout>
                        <Label Margin="0,10,0,0" FontSize="{StaticResource SecondaryFontSize}" HorizontalOptions="StartAndExpand" Text="{i18n:Translate SyncPage_GeneralInformation}" />
                        <Label
                            Margin="0,10,0,0"
                            FontSize="{StaticResource SecondaryFontSize}"
                            HorizontalOptions="StartAndExpand"
                            FontAttributes="Bold"
                            TextColor="{StaticResource PrimaryWarningTextColor}"
                            Text="{i18n:Translate SyncPage_GeneralInformation_EnsureGoodInternetAccess}" />
                        <VerticalStackLayout Margin="0,10,0,0" Spacing="0" VerticalOptions="Start">
                            <controls:RepeaterView x:TypeArguments="sys:String" ItemsSource="{Binding SyncAdvantages}" Spacing="15">
                                <controls:RepeaterView.ItemTemplate>
                                    <DataTemplate x:DataType="x:String">
                                        <ViewCell>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="0.1*" />
                                                    <ColumnDefinition Width="0.7*" />
                                                </Grid.ColumnDefinitions>
                                                <Image
                                                    Grid.Column="0" Aspect="AspectFit" HeightRequest="60"
                                                    Scale="0.6" Source="oksmall.png" />
                                                <Label Grid.Column="1" FontSize="{StaticResource SecondaryFontSize}" Text="{Binding .}" VerticalTextAlignment="Center" />
                                            </Grid>
                                        </ViewCell>
                                    </DataTemplate>
                                </controls:RepeaterView.ItemTemplate>
                            </controls:RepeaterView>
                        </VerticalStackLayout>
                    </VerticalStackLayout>
                </ScrollView>
            </Grid>
            <controls:BigButton
                Grid.Row="3" AutomationId="SyncPage_SubmitButton"
                BackgroundColor="{StaticResource PrimaryColor}"
                Command="{Binding SubmitCommand}"
                IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                Text="{i18n:Translate SyncPage_Submit}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>