<views:BasePage
    x:Class="MgFlasher.Views.RegisterPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:behaviors="clr-namespace:MgFlasher.Behaviors"
    xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:RegisterPageViewModel"
    controls:ShellTitleView.BarBackButtonVisible="True"
    HideSoftInputOnTapped="True" mc:Ignorable="d">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="2.7*" />
                <RowDefinition Height="0.1*" />
                <RowDefinition Height="0.52*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate RegisterPage_Title}" />
            <ScrollView Grid.Row="1">
                <VerticalStackLayout Grid.Row="1" HorizontalOptions="FillAndExpand" Spacing="15" VerticalOptions="CenterAndExpand">
                    <VerticalStackLayout>
                        <Label Text="{i18n:Translate Register_FirstName}" />
                        <Entry IsReadOnly="{Binding IsBusy, Mode=OneWay}" Placeholder="Please enter first name" Text="{Binding FirstName, Mode=TwoWay}" />
                    </VerticalStackLayout>
                    <VerticalStackLayout>
                        <Label Text="{i18n:Translate Register_LastName}" />
                        <Entry IsReadOnly="{Binding IsBusy, Mode=OneWay}" Placeholder="Please enter last name" Text="{Binding LastName, Mode=TwoWay}" />
                    </VerticalStackLayout>
                    <VerticalStackLayout>
                        <Label Text="{i18n:Translate Register_Phone}" />
                        <Entry IsReadOnly="{Binding IsBusy, Mode=OneWay}" Placeholder="Please enter phone number" Text="{Binding Phone, Mode=TwoWay}" />
                    </VerticalStackLayout>
                    <VerticalStackLayout>
                        <Label Text="{i18n:Translate Register_Email}" />
                        <Entry IsReadOnly="{Binding IsBusy, Mode=OneWay}" Placeholder="Please enter your email" Text="{Binding Email, Mode=TwoWay}">
                            <Entry.Behaviors>
                                <behaviors:EmailValidatorBehavior IsValid="{Binding IsEmailValid, Mode=OneWayToSource}" />
                            </Entry.Behaviors>
                        </Entry>
                    </VerticalStackLayout>
                    <VerticalStackLayout>
                        <Label Text="{i18n:Translate Register_Password}" />
                        <Entry IsPassword="True" IsReadOnly="{Binding IsBusy, Mode=OneWay}" Placeholder="Please enter password" Text="{Binding Password, Mode=TwoWay}" />
                    </VerticalStackLayout>
                </VerticalStackLayout>
            </ScrollView>
            <controls:BigButton
                Grid.Row="3" AutomationId="RegisterPage_Register"
                BackgroundColor="{StaticResource PrimaryAccentColor}"
                Command="{Binding RegisterCommand}"
                IsEnabled="{Binding CanRegister, Mode=OneWay}"
                Text="{i18n:Translate Login_Register}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>