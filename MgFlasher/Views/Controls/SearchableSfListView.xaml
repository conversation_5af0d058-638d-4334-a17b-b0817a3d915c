<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:syncfusion="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    x:Class="MgFlasher.Views.Controls.SearchableSfListView"
    x:Name="root">
    <ContentView.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Entry
                TextChanged="SearchBar_TextChanged" Grid.Row="0"
                x:Name="searchBar" ClearButtonVisibility="WhileEditing"
                Placeholder="{i18n:Translate SearchableSfListView_SearchToFilter}"
                HorizontalOptions="FillAndExpand" />
            <syncfusion:SfListView
                ScrollStateChanged="SfListView_ScrollStateChanged"
                Focused="SfListView_Focused" Grid.Row="1"
                x:Name="sfListView" AllowSwiping="False"
                ItemTemplate="{Binding ItemTemplate, Source={x:Reference root}, Mode=OneWay}"
                Margin="0,0,0,5" BackgroundColor="Transparent"
                ItemsSource="{Binding ItemsSource, Source={x:Reference root}, Mode=OneWay}"
                VerticalOptions="FillAndExpand" SelectionMode="None"
                ItemSize="{Binding ItemSize, Source={x:Reference root}}" />
        </Grid>
    </ContentView.Content>
</ContentView>