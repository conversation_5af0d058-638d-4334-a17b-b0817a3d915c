<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MgFlasher.Views.Controls.TutorialPopup"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:sfListView="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView"
    xmlns:sfp="clr-namespace:Syncfusion.Maui.Popup;assembly=Syncfusion.Maui.Popup"
    xmlns:tut="clr-namespace:MgFlasher.Services.Tutorials">
    <ContentView.Content>
        <sfp:SfPopup
            x:Name="SfPopupTutorial"
            IsOpen="{Binding IsPopupOpen, Mode=OneWay}"
            VerticalOptions="CenterAndExpand" OverlayMode="Blur"
            WidthRequest="{Binding IsPopupOpen, Converter={StaticResource ScreenWidthToPopupWidthConverter}}"
            ShowOverlayAlways="{OnPlatform Default=True, WinUI=False}"
            HorizontalOptions="Center" ShowCloseButton="True">
            <sfp:SfPopup.HeaderTemplate>
                <DataTemplate>
                    <Label
                        Text="{Binding TutorialPageTitle}"
                        FontAttributes="Bold" Padding="15"
                        FontSize="{StaticResource PrimaryFontSize}"
                        HorizontalTextAlignment="Start"
                        VerticalTextAlignment="Center" />
                </DataTemplate>
            </sfp:SfPopup.HeaderTemplate>
            <sfp:SfPopup.PopupStyle>
                <sfp:PopupStyle
                    BlurIntensity="ExtraDark"
                    HeaderBackground="{StaticResource ItemSelectedHoverColor}"
                    PopupBackground="{StaticResource ListItemSeparatorColor}"
                    CornerRadius="5" HeaderTextColor="White" />
            </sfp:SfPopup.PopupStyle>
            <sfp:SfPopup.ContentTemplate>
                <DataTemplate>
                    <StackLayout>
                        <sfListView:SfListView
                            BackgroundColor="{StaticResource ItemSelectedHoverColor}"
                            x:Name="listView" Padding="25" SelectionMode="None"
                            AutoFitMode="DynamicHeight"
                            SizeChanged="ListView_SizeChanged"
                            ItemsSource="{Binding TutorialSteps}">
                            <sfListView:SfListView.ItemTemplate>
                                <DataTemplate x:DataType="tut:PopupTutorial">
                                    <Grid Padding="10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <Label
                                            Grid.Column="1"
                                            Text="{Binding Step}"
                                            FontSize="{StaticResource SmallFontSize}"
                                            TextColor="White" VerticalOptions="Center"
                                            LineBreakMode="WordWrap" MaxLines="0" />
                                    </Grid>
                                </DataTemplate>
                            </sfListView:SfListView.ItemTemplate>
                        </sfListView:SfListView>
                        <Button
                            Text="{i18n:Translate PopupTutorials_DontShowAgain}"
                            Margin="15"
                            BackgroundColor="{StaticResource ItemSelectedHoverColor}"
                            TextColor="White" BorderWidth="0" Padding="10"
                            FontSize="{StaticResource SecondaryFontSize}"
                            Command="{Binding DontShowPopupTutorialAgainCommand}"
                            HorizontalOptions="Center" VerticalOptions="Center"
                            CornerRadius="5" />
                    </StackLayout>
                </DataTemplate>
            </sfp:SfPopup.ContentTemplate>
        </sfp:SfPopup>
    </ContentView.Content>
</ContentView>