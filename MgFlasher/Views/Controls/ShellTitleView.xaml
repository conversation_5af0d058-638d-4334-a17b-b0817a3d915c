<?xml version="1.0" encoding="UTF-8" ?>
<Grid
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MgFlasher.Views.Controls.ShellTitleView"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    x:DataType="controls:ShellTitleView"
    IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
    VerticalOptions="{OnPlatform iOS=Start, Default=FillAndExpand}"
    HeightRequest="{OnPlatform iOS=44, WinUI=40, Default=-1}"
    WidthRequest="{Binding Source={RelativeSource Self}, Path=Window.Width}"
    BackgroundColor="{StaticResource TitleViewBackgroundColor}"
    Margin="{OnPlatform Android='-5, 0, 0, 0', iOS='0, 0, 0, 0', WinUI='0, 0, 0, 0'}"
    Padding="0">
    <Grid.RowDefinitions>
        <RowDefinition Height="*" />
    </Grid.RowDefinitions>
    <Grid.ColumnDefinitions>
        <ColumnDefinition Width="*" />
        <ColumnDefinition Width="2*" />
        <ColumnDefinition Width="*" />
    </Grid.ColumnDefinitions>
    <ImageButton
        Margin="{OnPlatform Android='10, 0, 0, 0', iOS='20, 0, 0, 0', WinUI='5, 0, 0, 0'}"
        Grid.Row="0" Grid.Column="0" HorizontalOptions="Start"
        VerticalOptions="Center" Source="menu.png"
        AutomationId="ShellTitleView_MenuButton"
        IsVisible="{Binding HasBarBackButton, Converter={StaticResource InvertedBoolConverter}}"
        Command="{Binding ShellViewModel.MenuCommand}"
        IsEnabled="{Binding IsBusy, Mode=OneTime, Converter={StaticResource InvertedBoolConverter}}" />
    <ImageButton
        Margin="{OnPlatform Android='10, 0, 0, 0', iOS='20, 0, 0, 0', WinUI='5, 0, 0, 0'}"
        Grid.Row="0" Grid.Column="0" HorizontalOptions="Start"
        VerticalOptions="Center" Source="backicon_1icon.png"
        AutomationId="ShellTitleView_BackButton"
        Command="{Binding ShellViewModel.BackCommand}"
        IsEnabled="{Binding IsBusy, Mode=OneTime, Converter={StaticResource InvertedBoolConverter}}">
        <ImageButton.IsVisible>
            <MultiBinding Converter="{StaticResource AllTrueMultiConverter}">
                <Binding Path="HasBarBackButton" Mode="OneWay" />
                <Binding Path="BarBackButtonVisible" Mode="OneWay" />
            </MultiBinding>
        </ImageButton.IsVisible>
    </ImageButton>
    <StackLayout
        Orientation="Horizontal" Grid.Row="0" Grid.Column="1"
        Spacing="5" MaximumWidthRequest="175"
        HorizontalOptions="Center"
        IsEnabled="{Binding IsBusy, Mode=OneTime, Converter={StaticResource InvertedBoolConverter}}">
        <ImageButton AutomationId="ShellTitleView_CarConnectivityButton" Source="{Binding ShellViewModel.CarConnectivity, Converter={StaticResource CarConnectivityIconConverter}}" Command="{Binding ShellViewModel.ConnectivityCommand}" />
        <Label
            AutomationId="ShellTitleView_CarConnectivityLabel"
            VerticalOptions="Center" VerticalTextAlignment="Center"
            Text="{Binding ShellViewModel.CarConnectivity, Converter={StaticResource CarConnectivityTextConverter}}"
            FontSize="{StaticResource SecondaryFontSize}">
            <Label.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding ShellViewModel.ConnectivityCommand}" />
            </Label.GestureRecognizers>
        </Label>
    </StackLayout>
    <ImageButton
        Margin="{OnPlatform Android='0, 0, 20, 0', iOS='0, 0, 20, 0', WinUI='0, 0, 20, 0'}"
        Grid.Row="0" Grid.Column="2" HorizontalOptions="End"
        VerticalOptions="Center" Source="contextoptions.png"
        AutomationId="ShellTitleView_ContextOptionsButton"
        Command="{Binding ShellViewModel.ContextOptionsCommand}"
        IsEnabled="{Binding IsBusy, Mode=OneTime, Converter={StaticResource InvertedBoolConverter}}"
        IsVisible="{Binding HasContextOptionsButton, Mode=OneWay}" />
</Grid>