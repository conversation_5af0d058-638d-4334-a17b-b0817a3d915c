﻿using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using System;
using MgFlasher.Helpers;
using System.Linq;
using MgFlasher.ViewModels;

namespace MgFlasher.Views.Controls;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class ShellTitleView : Grid, IDisposable
{
    public static readonly BindableProperty IsBusyProperty = BindableProperty.CreateAttached(nameof(IsBusy), typeof(bool), typeof(ShellTitleView), false, propertyChanged: OnIsBusyPropertyChanged);

    public bool IsBusy
    {
        get => (bool)GetValue(IsBusyProperty);
        set => SetValue(IsBusyProperty, value);
    }

    public static bool GetIsBusy(BindableObject view)
    {
        return (bool)view.GetValue(IsBusyProperty);
    }

    public static void SetIsBusy(BindableObject view, bool value)
    {
        view.SetValue(IsBusyProperty, value);
    }

    private static void OnIsBusyPropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is ShellTitleView)
            return;
        if (Shell.Current is null)
            return;
        if (Shell.GetTitleView(Shell.Current) is not ShellTitleView obj)
            return;
        SetIsBusy(obj, (bool)newValue);
    }

    public static readonly BindableProperty HasBarBackButtonProperty = BindableProperty.CreateAttached(nameof(HasBarBackButton), typeof(bool), typeof(ShellTitleView), false, propertyChanged: OnHasBarBackButtonPropertyChanged);

    public bool HasBarBackButton
    {
        get => (bool)GetValue(HasBarBackButtonProperty);
        set => SetValue(HasBarBackButtonProperty, value);
    }

    public static bool GetHasBarBackButton(BindableObject view)
    {
        return (bool)view.GetValue(HasBarBackButtonProperty);
    }

    public static void SetHasBarBackButton(BindableObject view, bool value)
    {
        view.SetValue(HasBarBackButtonProperty, value);
    }

    private static void OnHasBarBackButtonPropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is ShellTitleView || Shell.GetTitleView(Shell.Current) is not ShellTitleView obj)
            return;
        SetHasBarBackButton(obj, (bool)newValue);
    }

    public static readonly BindableProperty BarBackButtonVisibleProperty = BindableProperty.CreateAttached(nameof(BarBackButtonVisible), typeof(bool), typeof(ShellTitleView), true, propertyChanged: OnBarBackButtonVisiblePropertyChanged);

    public bool BarBackButtonVisible
    {
        get => (bool)GetValue(BarBackButtonVisibleProperty);
        set => SetValue(BarBackButtonVisibleProperty, value);
    }

    public static bool GetBarBackButtonVisible(BindableObject view)
    {
        return (bool)view.GetValue(BarBackButtonVisibleProperty);
    }

    public static void SetBarBackButtonVisible(BindableObject view, bool value)
    {
        view.SetValue(BarBackButtonVisibleProperty, value);
    }

    private static void OnBarBackButtonVisiblePropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is ShellTitleView || Shell.GetTitleView(Shell.Current) is not ShellTitleView obj)
            return;
        SetBarBackButtonVisible(obj, (bool)newValue);
    }

    public static readonly BindableProperty HasContextOptionsButtonProperty = BindableProperty.CreateAttached(nameof(HasContextOptionsButton), typeof(bool), typeof(ShellTitleView), false, propertyChanged: OnHasContextOptionsButtonPropertyChanged);

    public bool HasContextOptionsButton
    {
        get => (bool)GetValue(HasContextOptionsButtonProperty);
        set => SetValue(HasContextOptionsButtonProperty, value);
    }

    public static bool GetHasContextOptionsButton(BindableObject view)
    {
        return (bool)view.GetValue(HasContextOptionsButtonProperty);
    }

    public static void SetHasContextOptionsButton(BindableObject view, bool value)
    {
        view.SetValue(HasContextOptionsButtonProperty, value);
    }

    private static void OnHasContextOptionsButtonPropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is ShellTitleView || Shell.GetTitleView(Shell.Current) is not ShellTitleView obj)
            return;
        SetHasContextOptionsButton(obj, (bool)newValue);
    }

    public static readonly BindableProperty BackwardNavigationParameterProperty = BindableProperty.CreateAttached(nameof(BackwardNavigationParameter), typeof(object), typeof(BasePage), null);

    public object BackwardNavigationParameter
    {
        get => GetValue(BackwardNavigationParameterProperty);
        set => SetValue(BackwardNavigationParameterProperty, value);
    }

    public static object GetBackwardNavigationParameter(BindableObject view)
    {
        return view.GetValue(BackwardNavigationParameterProperty);
    }

    public static void SetBackwardNavigationParameter(BindableObject view, object value)
    {
        view.SetValue(BackwardNavigationParameterProperty, value);
    }

    private bool _initialized;

    public ShellTitleViewModel ShellViewModel { get; }

    public ShellTitleView()
    {
        BindingContext = this;
        ShellViewModel = DependencyResolver.Resolve<ShellTitleViewModel>();
        InitializeComponent();
    }

    protected override void OnParentSet()
    {
        base.OnParentSet();
        if (Parent is AppShell shell && !_initialized)
        {
            shell.Navigated += OnShellNavigated;
            _initialized = true;
        }
    }

    private void OnShellNavigated(object sender, ShellNavigatedEventArgs e)
    {
        if (Shell.Current?.CurrentPage is not BasePage page) return;
        IsBusy = GetIsBusy(page);
        HasBarBackButton = GetHasBarBackButton(page);
        BarBackButtonVisible = GetBarBackButtonVisible(page);
        HasContextOptionsButton = GetHasContextOptionsButton(page);
    }

    public void Dispose()
    {
        if (Parent is AppShell shell)
        {
            shell.Navigated -= OnShellNavigated;
        }

        foreach (var child in VisualTreeHelper.GetChildren<ContentView>(this).OfType<IDisposable>())
        {
            child.Dispose();
        }
    }
}