<?xml version="1.0" encoding="UTF-8" ?>
<Grid
    x:Class="MgFlasher.Views.Controls.ImageHeader"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    x:Name="ImageHeaderAbsoluteLayout" mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="0.13*" />
            <RowDefinition Height="1.44*" />
            <RowDefinition Height="0.13*" />
        </Grid.RowDefinitions>
        <AbsoluteLayout Grid.Row="1" HorizontalOptions="FillAndExpand">
            <Frame
                Margin="50,0,0,0" Padding="1"
                AbsoluteLayout.LayoutBounds="0.5,0.5,1,0.95"
                AbsoluteLayout.LayoutFlags="SizeProportional"
                BackgroundColor="{StaticResource PrimaryAccentColor}"
                BorderColor="{StaticResource ItemSelectedHoverColor}"
                HasShadow="False">
                <Grid Margin="0" Padding="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="11.5*" />
                    </Grid.ColumnDefinitions>
                    <Label
                        x:Name="lbText" Grid.Column="1"
                        FontSize="{StaticResource SecondaryFontSize}"
                        HorizontalOptions="Start"
                        HorizontalTextAlignment="Start"
                        VerticalOptions="CenterAndExpand"
                        VerticalTextAlignment="Center" />
                </Grid>
            </Frame>
            <Frame
                Padding="0,0,0,0"
                AbsoluteLayout.LayoutBounds="0,0.5,70,70"
                AbsoluteLayout.LayoutFlags="PositionProportional"
                BackgroundColor="Transparent" BorderColor="Transparent"
                HorizontalOptions="Fill" VerticalOptions="Fill">
                <Image x:Name="img" Aspect="AspectFit" />
            </Frame>
        </AbsoluteLayout>
    </Grid>
</Grid>