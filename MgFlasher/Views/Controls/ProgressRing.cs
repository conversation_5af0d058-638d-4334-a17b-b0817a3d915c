﻿using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;
using Syncfusion.Maui.ProgressBar;

namespace MgFlasher.Views.Controls;

public class ProgressRing : SfCircularProgressBar
{
    public static readonly BindableProperty RingProgressColorProperty = BindableProperty.Create(nameof(RingProgressColor), typeof(Color), typeof(ProgressRing), Color.FromRgb(234, 105, 92),
        propertyChanged: RingProgressColorChanged);

    public Color RingProgressColor
    {
        get { return (Color)base.GetValue(RingProgressColorProperty); }
        set { SetValue(RingProgressColorProperty, value); }
    }

    private static void RingProgressColorChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var progress = (ProgressRing)bindable;
        progress.ProgressFill = (Color)newValue;
    }

    public static readonly BindableProperty RingBaseColorProperty = BindableProperty.Create(nameof(RingBaseColor), typeof(Color), typeof(ProgressRing), Color.FromRgb(46, 60, 76),
        propertyChanged: RingBaseColorChanged);

    public Color RingBaseColor
    {
        get { return (Color)base.GetValue(RingBaseColorProperty); }
        set { SetValue(RingBaseColorProperty, value); }
    }

    private static void RingBaseColorChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var progress = (ProgressRing)bindable;
        progress.BackgroundColor = (Color)newValue;
    }

    public static readonly BindableProperty BorderColorProperty = BindableProperty.Create(nameof(BorderColor), typeof(Color),
        typeof(ProgressRing), Color.FromRgb(46, 60, 76),
        propertyChanged: RingBorderColorChanged);

    public Color BorderColor
    {
        get { return (Color)base.GetValue(BorderColorProperty); }
        set { SetValue(BorderColorProperty, value); }
    }

    private static void RingBorderColorChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var progress = (ProgressRing)bindable;
        progress.TrackFill = (Color)newValue;
    }

    public static readonly BindableProperty ValueProperty = BindableProperty.Create(nameof(Value), typeof(int),
        typeof(ProgressRing), 0,
        propertyChanged: HandleValueChanged);

    public int Value
    {
        get { return (int)base.GetValue(ValueProperty); }
        set { SetValue(ValueProperty, value); }
    }

    private static void HandleValueChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var progress = (ProgressRing)bindable;
        progress.Progress = (int)newValue;
    }

    public ProgressRing()
    {
        SegmentCount = 120;
        SegmentGapWidth = 1;
        ProgressThickness = 10.0;
        TrackThickness = 10.0;
    }
}