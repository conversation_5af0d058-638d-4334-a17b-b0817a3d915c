using Microsoft.Maui;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Layouts;
using Syncfusion.Maui.ListView;
using Syncfusion.Maui.Popup;

namespace MgFlasher.Views.Controls;

public partial class TutorialPopup : ContentView
{
    public TutorialPopup()
    {
        InitializeComponent();
    }

    public SfPopup Popup { get => SfPopupTutorial; }

    private void ListView_SizeChanged(object sender, System.EventArgs e)
    {
        SfPopupTutorial.HeightRequest = (sender as SfListView).Height + 155;
    }
}