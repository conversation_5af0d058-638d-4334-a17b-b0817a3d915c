﻿using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace MgFlasher.Views.Controls;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class ClassicProgressBar : ProgressBar
{
    public static readonly BindableProperty ForegroundProgressColorProperty = BindableProperty.Create(nameof(ForegroundProgressColor), typeof(Color),
        typeof(ProgressRing), Color.FromRgb(234, 105, 92));

    public Color ForegroundProgressColor
    {
        get { return (Color)base.GetValue(ForegroundProgressColorProperty); }
        set { SetValue(ForegroundProgressColorProperty, value); }
    }

    public static readonly BindableProperty BaseColorProperty = BindableProperty.Create(nameof(BaseColor), typeof(Color),
        typeof(ProgressRing), Color.FromRgb(46, 60, 76));

    public Color BaseColor
    {
        get { return (Color)base.GetValue(BaseColorProperty); }
        set { SetValue(BaseColorProperty, value); }
    }

    public static readonly BindableProperty HalfSizeModeProperty = BindableProperty.Create(nameof(HalfSizeMode), typeof(bool),
        typeof(ProgressRing), true);

    public bool HalfSizeMode
    {
        get { return (bool)base.GetValue(HalfSizeModeProperty); }
        set { SetValue(HalfSizeModeProperty, value); }
    }
}