﻿using Microsoft.Maui.Controls;

namespace MgFlasher.Views.Controls;

public class BigButton : Button
{
    public BigButton()
    {
        HorizontalOptions = LayoutOptions.FillAndExpand;
#if WINDOWS
        HeightRequest = 60;
        VerticalOptions = LayoutOptions.End;
#else
        VerticalOptions = LayoutOptions.FillAndExpand;
#endif
    }

    protected override void OnPropertyChanged(string propertyName = null)
    {
        base.OnPropertyChanged(propertyName);
        if (propertyName == nameof(Text) && !string.IsNullOrEmpty(Text))
        {
            if (Text != Text.ToUpperInvariant())
            {
                Text = Text.ToUpperInvariant();
            }
        }
    }
}