﻿using MgFlasher.Models;
using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using CommunityToolkit.Maui.Core.Platform;

namespace MgFlasher.Views.Controls;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class SearchableSfListView : ContentView
{
    public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameof(ItemsSource),
        typeof(object), typeof(SearchableSfListView), null, BindingMode.Default, null, null);

    public object ItemsSource
    {
        get => GetValue(ItemsSourceProperty);
        set => SetValue(ItemsSourceProperty, value);
    }

    public static readonly BindableProperty ItemTemplateProperty = BindableProperty.Create(nameof(ItemTemplate),
        typeof(DataTemplate), typeof(SearchableSfListView), null, BindingMode.Default, null, null);

    public DataTemplate ItemTemplate
    {
        get => (DataTemplate)GetValue(ItemTemplateProperty);
        set => SetValue(ItemTemplateProperty, value);
    }

    public static readonly BindableProperty ItemSizeProperty = BindableProperty.Create(nameof(ItemSize),
        typeof(double), typeof(SearchableSfListView), 40.0, BindingMode.Default, null, null);

    public double ItemSize
    {
        get => (double)GetValue(ItemSizeProperty);
        set => SetValue(ItemSizeProperty, value);
    }

    public SearchableSfListView()
    {
        InitializeComponent();
    }

    private void SearchBar_TextChanged(object sender, TextChangedEventArgs e)
    {
        if (sfListView.DataSource != null)
        {
            sfListView.DataSource.Filter = FilterItems;
            sfListView.DataSource.RefreshFilter();
        }

        sfListView.RefreshView();
    }

    private bool FilterItems(object obj)
    {
        if (string.IsNullOrEmpty(searchBar?.Text))
        {
            return true;
        }

        if (obj is not IFilterableViewModel filterable)
        {
            return true;
        }

        return filterable.Match(searchBar.Text);
    }

    private void SfListView_ScrollStateChanged(object sender, Syncfusion.Maui.ListView.ScrollStateChangedEventArgs e)
    {
        if (e.ScrollState == Syncfusion.Maui.ListView.ListViewScrollState.Dragging)
        {
            searchBar.HideKeyboardAsync();
            searchBar.Unfocus();
        }
    }

    private void SfListView_Focused(object sender, FocusEventArgs e)
    {
        searchBar.HideKeyboardAsync();
        searchBar.Unfocus();
    }
}