﻿using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Views.Controls;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class ImageHeader : Grid
{
    public static readonly BindableProperty TextProperty = BindableProperty.Create(
        propertyName: nameof(Text),
        returnType: typeof(string),
        declaringType: typeof(ImageHeader),
        defaultValue: "-",
        defaultBindingMode: BindingMode.OneWay,
        propertyChanged: OnTextPropertyChanged);

    public string Text
    {
        get { return (string)GetValue(TextProperty); }
        set { SetValue(TextProperty, value); }
    }

    public static readonly BindableProperty SourceProperty = BindableProperty.Create(
        propertyName: nameof(Source),
        returnType: typeof(ImageSource),
        declaringType: typeof(ImageHeader),
        defaultValue: null,
        defaultBindingMode: BindingMode.OneWay,
        propertyChanged: OnSourcePropertyChanged);

    public ImageSource Source
    {
        get { return (string)GetValue(SourceProperty); }
        set { SetValue(SourceProperty, value); }
    }

    public ImageHeader()
    {
        InitializeComponent();
    }

    private static void OnTextPropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var control = (ImageHeader)bindable;
        control.lbText.Text = newValue?.ToString();
    }

    private static void OnSourcePropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var control = (ImageHeader)bindable;
        control.img.Source = newValue as ImageSource;
    }
}