using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace MgFlasher.Views.Controls;

public class CustomViewCell : ViewCell
{
    public static readonly BindableProperty SelectedBackgroundColorProperty = BindableProperty.Create(
        nameof(SelectedBackgroundColor), typeof(Color), typeof(CustomViewCell), Colors.Transparent);

    public Color SelectedBackgroundColor
    {
        get => (Color)GetValue(SelectedBackgroundColorProperty);
        set => SetValue(SelectedBackgroundColorProperty, value);
    }
}