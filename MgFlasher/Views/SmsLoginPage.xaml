<views:BasePage
    x:Class="MgFlasher.Views.SmsLoginPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:behaviors="clr-namespace:MgFlasher.Behaviors"
    xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:SmsLoginPageViewModel"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    controls:ShellTitleView.HasContextOptionsButton="True"
    HideSoftInputOnTapped="True" mc:Ignorable="d">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="2.76*" />
                <RowDefinition Height="0.61*" />
                <RowDefinition Height="0.49*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Text="{i18n:Translate LoginPage_Title}" Style="{StaticResource LabelPageTitleStyle}" />
            <VerticalStackLayout Grid.Row="1" HorizontalOptions="FillAndExpand" Spacing="14" VerticalOptions="CenterAndExpand">
                <VerticalStackLayout>
                    <Label Text="{i18n:Translate Login_PhoneNumber}" />
                    <Entry IsReadOnly="{Binding IsBusy, Mode=OneWay}" Placeholder="{i18n:Translate Login_PleaseEnterPhoneNumber}" Text="{Binding PhoneNumber, Mode=TwoWay}">
                        <Entry.Behaviors>
                            <behaviors:ChinesePhoneNumberValidatorBehavior IsValid="{Binding IsPhoneNumberValid, Mode=OneWayToSource}" />
                        </Entry.Behaviors>
                    </Entry>
                </VerticalStackLayout>
                <VerticalStackLayout IsVisible="{Binding VerificationCodeVisible, Mode=OneWay}" HorizontalOptions="FillAndExpand">
                    <Label Text="{i18n:Translate Login_SmsVerificationCode}" />
                    <Grid HorizontalOptions="FillAndExpand" ColumnSpacing="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Entry Grid.Column="0" IsReadOnly="{Binding IsBusy, Mode=OneWay}" Placeholder="{i18n:Translate Login_PleaseEnterVerificationCode}" Text="{Binding VerificationCode, Mode=TwoWay}" />
                        <controls:SmallButton
                            Grid.Column="1"
                            BackgroundColor="{StaticResource PrimaryErrorTextBackgroundColor}"
                            Command="{Binding ResendCommand}"
                            IsEnabled="{Binding IsBusy, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}"
                            IsVisible="{Binding VerificationCodeVisible, Mode=OneWay}"
                            Text="{i18n:Translate Login_Resend}" />
                    </Grid>
                </VerticalStackLayout>
                <Label
                    TextColor="{StaticResource PrimaryWarningTextColor}"
                    Text="{i18n:Translate SmsLoginPage_OnlyForChineseMainlandUsers}"
                    FontSize="{StaticResource SmallFontSize}"
                    HorizontalOptions="StartAndExpand"
                    HorizontalTextAlignment="Start"
                    VerticalTextAlignment="Center" />
                <Label
                    FontSize="{StaticResource SmallFontSize}"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    IsVisible="{Binding HasMessage}"
                    Text="{Binding Message}"
                    TextColor="{StaticResource PrimaryImportantTextColor}" />
            </VerticalStackLayout>
            <Grid ColumnSpacing="5" Grid.Row="3" HorizontalOptions="FillAndExpand" IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="0.1*" />
                </Grid.ColumnDefinitions>
                <controls:BigButton
                    Grid.Column="0"
                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                    Command="{Binding LoginCommand}"
                    IsEnabled="{Binding CanLogin, Mode=OneWay}"
                    Text="{Binding LoginButtonText, Mode=OneWay}" />
                <controls:BigButton BackgroundColor="{StaticResource PrimaryAccentColor}" Command="{Binding MiscCommand}" Grid.Column="1" Text="&#x22EE;" />
            </Grid>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>