<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="MgFlasher.Views.MyCar.MyCarHomeView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:MgFlasher.Converters"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.MyCar"
    xmlns:tabView="clr-namespace:Syncfusion.Maui.TabView;assembly=Syncfusion.Maui.TabView"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    x:Name="root" x:DataType="vms:MyCarHomeViewModel">
    <tabView:SfTabView
        Margin="0" TabBarPlacement="Top"
        IndicatorPlacement="Top"
        SelectionChanged="SfTabView_SelectionChanged"
        SelectedIndex="{Binding SelectedTabIndex}">
        <tabView:SfTabItem Header="{i18n:Translate MyCarDetailPage_Tab_Overview}">
            <tabView:SfTabItem.Content>
                <Grid
                    VerticalOptions="CenterAndExpand" RowSpacing="15"
                    Margin="0"
                    BindingContext="{Binding BindingContext.Overview, Source={x:Reference root}}"
                    x:DataType="vms:MyCarHomeOverviewViewModel">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="1.0*" />
                        <RowDefinition Height="3.35*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{Binding UserCarName}">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding EnterUserCarNameCommand}" NumberOfTapsRequired="2" />
                        </Label.GestureRecognizers>
                    </Label>
                    <Grid Grid.Row="1" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Image
                            Grid.Row="0" Aspect="AspectFit" VerticalOptions="Fill"
                            HorizontalOptions="Fill"
                            Source="{Binding CarPicture}">
                            <Image.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding OpenGalleryCommand}" />
                            </Image.GestureRecognizers>
                        </Image>
                    </Grid>
                    <ScrollView
                        x:Name="MyCarScrollView" Grid.Row="2"
                        HorizontalOptions="CenterAndExpand"
                        Orientation="Vertical" VerticalOptions="StartAndExpand">
                        <VerticalStackLayout Spacing="5">
                            <VerticalStackLayout.Resources>
                                <ResourceDictionary>
                                    <converters:ConnectedCarStatusToTextConverter x:Key="ConnectedCarStatusToTextConverter" />
                                    <converters:ConnectedCarStatusToColorConverter x:Key="ConnectedCarStatusToColorConverter" />
                                    <converters:ConnectedCarStageToColorConverter x:Key="ConnectedCarStageToColorConverter" />
                                    <converters:ConnectedCarSecurityWaveToColorConverter x:Key="ConnectedCarSecurityWaveToColorConverter" />
                                    <converters:ConnectedCarUpdateAvailableToColorConverter x:Key="ConnectedCarUpdateAvailableToColorConverter" />
                                    <Style TargetType="VerticalStackLayout" BasedOn="{StaticResource VerticalStackLayoutMgBaseStyle}">
                                        <Setter Property="HorizontalOptions" Value="CenterAndExpand" />
                                        <Setter Property="Spacing" Value="0" />
                                    </Style>
                                    <Style TargetType="HorizontalStackLayout" BasedOn="{StaticResource HorizontalStackLayoutMgBaseStyle}">
                                        <Setter Property="HorizontalOptions" Value="CenterAndExpand" />
                                        <Setter Property="Spacing" Value="0" />
                                    </Style>
                                </ResourceDictionary>
                            </VerticalStackLayout.Resources>
                            <HorizontalStackLayout>
                                <Label Text="{i18n:Translate MyCar_Vin}" Padding="{OnIdiom 0, Desktop='0,0,3,0'}" />
                                <Label FontSize="{StaticResource PrimaryFontSize}" FontAttributes="Bold" Text="{Binding ConnectedCar.VIN}" />
                            </HorizontalStackLayout>
                            <HorizontalStackLayout IsVisible="{Binding ShowEngineStrings}">
                                <Label Text="{i18n:Translate MyCar_Engine}" Padding="{OnIdiom 0, Desktop='0,0,3,0'}" />
                                <Label FontSize="{StaticResource PrimaryFontSize}" FontAttributes="Bold" Text="{Binding EngineString}" />
                            </HorizontalStackLayout>
                            <HorizontalStackLayout IsVisible="{Binding ShowEngineDetailsStrings}">
                                <Label FontSize="{StaticResource PrimaryFontSize}" FontAttributes="Bold" Text="{Binding ConnectedCar.EngineConfiguration}" />
                            </HorizontalStackLayout>
                            <HorizontalStackLayout IsVisible="{Binding ConnectedCar.AnyEcuInBootMode}">
                                <Label FontSize="{StaticResource LargeFontSize}" FontAttributes="Bold" Text="{i18n:Translate MyCar_Bootmode}" TextColor="{StaticResource PrimaryImportantTextColor}" />
                            </HorizontalStackLayout>
                            <Label
                                FontSize="{StaticResource SecondaryFontSize}"
                                FontAttributes="Bold"
                                Text="{i18n:Translate MyCar_Bootmode_Message}"
                                HorizontalTextAlignment="Center"
                                VerticalTextAlignment="Center"
                                TextColor="{StaticResource PrimaryWarningTextColor}"
                                LineBreakMode="WordWrap"
                                IsVisible="{Binding ConnectedCar.AnyEcuInBootMode}" />
                            <HorizontalStackLayout IsVisible="{Binding EmergencyMode}">
                                <Label FontSize="{StaticResource PrimaryFontSize}" FontAttributes="Bold" Text="{i18n:Translate UnknownSoftware}" TextColor="{StaticResource PrimaryImportantTextColor}" />
                            </HorizontalStackLayout>
                            <HorizontalStackLayout IsVisible="{Binding ShowUnlockStatus}">
                                <Label Text="{i18n:Translate MyCar_Status}" Padding="{OnIdiom 0, Desktop='0,0,3,0'}" />
                                <Label FontSize="{StaticResource PrimaryFontSize}" FontAttributes="Bold" Text="{Binding IsActive, Converter={StaticResource ConnectedCarStatusToTextConverter}}" TextColor="{Binding IsActive, Converter={StaticResource ConnectedCarStatusToColorConverter}}" />
                            </HorizontalStackLayout>
                            <HorizontalStackLayout IsVisible="{Binding ShowUnlockStatus}">
                                <Label Text="{i18n:Translate MyCar_SecurityLevel}" Padding="{OnIdiom 0, Desktop='0,0,3,0'}">
                                    <Label.GestureRecognizers>
                                        <TapGestureRecognizer Command="{Binding ShowUnlockWaveTooltipCommand}" />
                                    </Label.GestureRecognizers>
                                </Label>
                                <Label FontSize="{StaticResource PrimaryFontSize}" FontAttributes="Bold" Text="{Binding SecurityWaveLevel}" TextColor="{Binding SecurityColour, Converter={StaticResource ConnectedCarSecurityWaveToColorConverter}}" />
                            </HorizontalStackLayout>
                            <HorizontalStackLayout IsVisible="{Binding ShowUnlockStatus}">
                                <Label Text="{i18n:Translate MyCar_CustomCodeVersion}" Padding="{OnIdiom 0, Desktop='0,0,3,0'}" />
                                <Label FontSize="{StaticResource PrimaryFontSize}" FontAttributes="Bold" Text="{Binding CustomCodeText}" TextColor="{Binding CustomCodeUpdateAvailable, Converter={StaticResource ConnectedCarUpdateAvailableToColorConverter}}" />
                            </HorizontalStackLayout>
                            <HorizontalStackLayout IsVisible="{Binding ShowUnlockStatus}">
                                <Label Text="{i18n:Translate MyCar_Calibration}" Padding="{OnIdiom 0, Desktop='0,0,3,0'}" />
                                <Label FontSize="{StaticResource PrimaryFontSize}" FontAttributes="Bold" Text="{Binding StageTypeText, Mode=OneWay}" TextColor="{Binding CurrentStage, Converter={StaticResource ConnectedCarStageToColorConverter}}" />
                            </HorizontalStackLayout>
                            <HorizontalStackLayout IsVisible="{Binding ShowLastFlashedOtsMap}">
                                <Label Text="{i18n:Translate MyCar_OtsMap}" Padding="{OnIdiom 0, Desktop='0,0,3,0'}" />
                                <Label FontSize="{StaticResource PrimaryFontSize}" FontAttributes="Bold" Text="{Binding OtsMapText, Mode=OneWay}" TextColor="{Binding ChangesetUpdateAvailable, Converter={StaticResource ConnectedCarUpdateAvailableToColorConverter}}" />
                            </HorizontalStackLayout>
                            <HorizontalStackLayout IsVisible="{Binding CustomBinaryVisible}">
                                <Label FontSize="{StaticResource ExtraSmallFontSize}" FontAttributes="Bold" Text="{Binding CustomBinaryText, Mode=OneWay}" TextColor="{Binding CurrentStage, Converter={StaticResource ConnectedCarStageToColorConverter}}" />
                            </HorizontalStackLayout>
                            <HorizontalStackLayout IsVisible="{Binding ShowSwitchableMapSlotInfo}">
                                <Label FontSize="{StaticResource PrimaryFontSize}" FontAttributes="Italic" Text="{i18n:Translate MyCar_SwitchableMapsSlotInfo}" />
                            </HorizontalStackLayout>
                            <VerticalStackLayout.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding DisplaySwitchableMapsSlotDescriptionListCommand}" />
                            </VerticalStackLayout.GestureRecognizers>
                        </VerticalStackLayout>
                    </ScrollView>
                    <controls:SmallButton
                        Grid.Row="3"
                        BackgroundColor="{StaticResource PrimarySuccessColor}"
                        AutomationId="MyCarPage_CheckSupportButton"
                        Command="{Binding CheckSupportCommand}"
                        HorizontalOptions="Center" Padding="20,15,20,15"
                        IsVisible="{Binding ShouldCheckSupport, Mode=OneWay}"
                        Text="{i18n:Translate MyCar_CheckSupport}" />
                </Grid>
            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>
        <tabView:SfTabItem Header="{i18n:Translate MyCarDetailPage_Tab_General}">
            <tabView:SfTabItem.Content>
                <ScrollView Margin="0" BindingContext="{Binding BindingContext.Details, Source={x:Reference root}}" x:DataType="vms:MyCarHomeDetailsViewModel">
                    <StackLayout Orientation="Vertical">
                        <BoxView IsVisible="{Binding UserCarName, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding UserCarName, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_UserCarName}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding UserCarName, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding UserCarName, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding VIN, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding VIN, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_VIN}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding VIN, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding VIN, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding SerialNo, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding SerialNo, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_SerialNo}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding SerialNo, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding SerialNo, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding Engine, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding Engine, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_Engine}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding Engine, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding Engine, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding ProcessorType, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding ProcessorType, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_ProcessorType}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding ProcessorType, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding ProcessorType, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding UnlockCompany, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding UnlockCompany, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_UnlockCompany}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding UnlockCompany, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding UnlockCompany, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding SecurityLevel, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding SecurityLevel, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_SecurityLevel}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding SecurityLevel, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding SecurityLevel, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding ManufacturingDate, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding ManufacturingDate, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_ManufacturingDate}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding ManufacturingDate, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding ManufacturingDate, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding Region, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding Region, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_Region}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding Region, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding Region, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding Calid, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding Calid, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_Calid}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding Calid, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding Calid, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding CVN, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding CVN, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_CVN}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding CVN, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding CVN, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding SgbdIndex, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding SgbdIndex, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_SgbdIndex}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding SgbdIndex, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding SgbdIndex, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    </StackLayout>
                </ScrollView>
            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>
        <tabView:SfTabItem Header="{i18n:Translate MyCarDetailPage_Tab_Software}">
            <tabView:SfTabItem.Content>
                <ScrollView Margin="0" BindingContext="{Binding BindingContext.Details, Source={x:Reference root}}" x:DataType="vms:MyCarHomeDetailsViewModel">
                    <StackLayout Orientation="Vertical">
                        <BoxView IsVisible="{Binding A2L, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding A2L, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_A2L}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding A2L, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding A2L, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding Logistikbereich, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding Logistikbereich, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_Logistikbereich}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding Logistikbereich, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding Logistikbereich, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding BootctrlVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding BootctrlVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_BootctrlVersion}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding BootctrlVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding BootctrlVersion, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding HWEL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding HWEL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_HWEL}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding HWEL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding HWEL, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding HWAP, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding HWAP, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_HWAP}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding HWAP, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding HWAP, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding BTLD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding BTLD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_BTLD}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding BTLD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding BTLD, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding SWFL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding SWFL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_SWFL}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding SWFL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding SWFL, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding SWFK, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding SWFK, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_SWFK}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding SWFK, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding SWFK, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding CAFD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding CAFD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_CAFD}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding CAFD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding CAFD, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding CustomCode, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding CustomCode, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_CustomCode}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding CustomCode, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding CustomCode, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    </StackLayout>
                </ScrollView>
            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>
        <tabView:SfTabItem Header="{i18n:Translate MyCarDetailPage_Tab_Connection}">
            <tabView:SfTabItem.Content>
                <ScrollView Margin="0" BindingContext="{Binding BindingContext.Details, Source={x:Reference root}}" x:DataType="vms:MyCarHomeDetailsViewModel">
                    <StackLayout Orientation="Vertical">
                        <BoxView IsVisible="{Binding ConnectionHost, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding ConnectionHost, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_ConnectionHost}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding ConnectionHost, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding ConnectionHost, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding ConnectionPort, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding ConnectionPort, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_ConnectionPort}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding ConnectionPort, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding ConnectionPort, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding LastConnectedOn, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding LastConnectedOn, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_LastConnectedOn}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding LastConnectedOn, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding LastConnectedOn, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <BoxView IsVisible="{Binding LastSyncedOn, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                        <Label IsVisible="{Binding LastSyncedOn, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate MyCarDetailPage_LastSyncedOn}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                        <Label IsVisible="{Binding LastSyncedOn, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding LastSyncedOn, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    </StackLayout>
                </ScrollView>
            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>
    </tabView:SfTabView>
</ContentView>