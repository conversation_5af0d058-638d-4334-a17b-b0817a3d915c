<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="MgFlasher.Views.MyCar.MyCarFlashHistoryView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:behaviors="clr-namespace:MgFlasher.Behaviors"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.MyCar"
    x:DataType="vms:MyCarFlashHistoryViewModel">
    <controls:SearchableSfListView Margin="0" ItemsSource="{Binding Items, Mode=OneWay}" ItemSize="{OnIdiom Phone={OnPlatform Default=70}, Desktop={OnPlatform Default=70}, Default={OnPlatform Android=75, Default=65}}">
        <controls:SearchableSfListView.ItemTemplate>
            <DataTemplate x:DataType="vms:MyCarFlashHistoryItemViewModel">
                <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="7.2*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <VerticalStackLayout Grid.Column="0" Spacing="7" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
                        <Label FontSize="{StaticResource SecondaryFontSize}" LineBreakMode="NoWrap" Text="{Binding Name, Mode=OneWay}" HorizontalOptions="FillAndExpand" />
                        <HorizontalStackLayout Spacing="5" HorizontalOptions="FillAndExpand">
                            <controls:SmallButton Style="{StaticResource RoundedButtonMgStyle}" Command="{Binding GoToDetailsCommand}" AutomationId="{Binding AutomationId}">
                                <controls:SmallButton.Behaviors>
                                    <behaviors:FlashHistorySuccessButtonColorBehavior Value="{Binding Success}" />
                                </controls:SmallButton.Behaviors>
                            </controls:SmallButton>
                            <controls:SmallButton
                                Command="{Binding GoToSyncCommand}"
                                Style="{StaticResource RoundedButtonMgStyle}"
                                Text="{i18n:Translate FlashingHistoryPage_Item_NotSynced}"
                                IsVisible="{Binding Synced, Converter={StaticResource InvertedBoolConverter}}"
                                BackgroundColor="{StaticResource PrimaryWarningTextColor}" />
                            <controls:SmallButton Text="{Binding CreatedAt}" Style="{StaticResource RoundedButtonMgStyle}" BackgroundColor="{StaticResource ItemSelectedHoverColor}" />
                        </HorizontalStackLayout>
                    </VerticalStackLayout>
                    <Image
                        Grid.Column="1" Aspect="AspectFit" HeightRequest="40"
                        HorizontalOptions="Center" Source="iconexit.png"
                        VerticalOptions="Center" WidthRequest="40">
                        <Image.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding GoToDetailsCommand}" />
                        </Image.GestureRecognizers>
                    </Image>
                </Grid>
            </DataTemplate>
        </controls:SearchableSfListView.ItemTemplate>
    </controls:SearchableSfListView>
</ContentView>