<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.MyCar.MyCarsPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.MyCar"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView"
    controls:ShellTitleView.HasBarBackButton="False"
    x:DataType="vms:MyCarsPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="3.2*" />
                <RowDefinition Height="0.1*" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="0.1*" />
                <RowDefinition Height="0.49*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Text="{i18n:Translate MyCars_Headline}" Style="{StaticResource LabelPageTitleStyle}" IsVisible="{Binding HasAnyCars}" />
            <sf:SfListView
                AllowSwiping="False" VerticalOptions="FillAndExpand"
                Grid.Row="1" BackgroundColor="Transparent"
                HorizontalOptions="Center"
                ItemsSource="{Binding Cars}"
                IsVisible="{Binding HasAnyCars}"
                SelectionMode="None"
                ItemSize="{OnIdiom Desktop=100, Default=90}">
                <sf:SfListView.ItemTemplate>
                    <DataTemplate x:DataType="vms:MyCarsOverviewViewModel">
                        <controls:CustomViewCell>
                            <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Spacing="10">
                                <Image
                                    Aspect="AspectFit"
                                    HeightRequest="{OnIdiom Desktop=60, Default=50}"
                                    WidthRequest="{OnIdiom Desktop=60, Default=50}"
                                    HorizontalOptions="Start"
                                    Source="{Binding Image, Mode=OneWay}"
                                    VerticalOptions="Center">
                                    <Image.GestureRecognizers>
                                        <TapGestureRecognizer Command="{Binding ViewCommand}" />
                                    </Image.GestureRecognizers>
                                </Image>
                                <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                    <StackLayout Orientation="Vertical" VerticalOptions="Center" Spacing="5">
                                        <StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding ViewCommand}" />
                                        </StackLayout.GestureRecognizers>
                                        <Label FontSize="{StaticResource SecondaryFontSize}" HorizontalTextAlignment="Start" VerticalTextAlignment="Center" Text="{Binding UserCarName, Mode=OneWay}" />
                                        <Label FontSize="{StaticResource SecondaryFontSize}" HorizontalTextAlignment="Start" VerticalTextAlignment="Center" Text="{Binding VIN, Mode=OneWay}" />
                                    </StackLayout>
                                    <StackLayout Orientation="Horizontal" HorizontalOptions="EndAndExpand" Spacing="{OnIdiom Desktop=25, Default=10}">
                                        <Image
                                            Aspect="AspectFit"
                                            HeightRequest="{OnIdiom Desktop=40, Default=30}"
                                            WidthRequest="{OnIdiom Desktop=40, Default=30}"
                                            HorizontalOptions="Center"
                                            Source="menudiagnosticssmall.png">
                                            <Image.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding MiscCommand}" />
                                            </Image.GestureRecognizers>
                                        </Image>
                                        <BoxView WidthRequest="2" HeightRequest="50" VerticalOptions="Center" BackgroundColor="{StaticResource ItemSelectedHoverColor}" />
                                        <Image
                                            Aspect="AspectFit"
                                            HeightRequest="{OnIdiom Desktop=50, Default=40}"
                                            WidthRequest="{OnIdiom Desktop=50, Default=40}"
                                            HorizontalOptions="Center" Source="iconexit.png">
                                            <Image.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding ViewCommand}" />
                                            </Image.GestureRecognizers>
                                        </Image>
                                    </StackLayout>
                                </StackLayout>
                            </StackLayout>
                        </controls:CustomViewCell>
                    </DataTemplate>
                </sf:SfListView.ItemTemplate>
            </sf:SfListView>
            <Label
                Grid.Row="3"
                IsVisible="{Binding HasAnyCars}"
                FontSize="{StaticResource ExtraSmallFontSize}"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                Text="{i18n:Translate Login_or}" />
            <controls:BigButton
                BackgroundColor="{StaticResource PrimarySuccessColor}"
                Command="{Binding AddCommand}"
                AutomationId="MyCars_Add"
                IsEnabled="{Binding IsBusy, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}"
                Text="{i18n:Translate MyCars_Add_Button}">
                <controls:BigButton.Style>
                    <Style TargetType="controls:BigButton" BasedOn="{StaticResource ButtonMgBaseStyle}">
                        <Setter Property="Grid.Row" Value="5" />
                        <Setter Property="Grid.RowSpan" Value="1" />
                        <Style.Triggers>
                            <DataTrigger TargetType="controls:BigButton" Binding="{Binding HasAnyCars}" Value="False" x:DataType="vms:MyCarsPageViewModel">
                                <Setter Property="Grid.Row" Value="0" />
                                <Setter Property="Grid.RowSpan" Value="6" />
                                <Setter Property="VerticalOptions" Value="Center" />
                                <Setter Property="HorizontalOptions" Value="Center" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </controls:BigButton.Style>
            </controls:BigButton>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>