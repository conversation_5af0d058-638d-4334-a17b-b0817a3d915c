﻿using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.MyCar;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Xaml;
using Syncfusion.Maui.TabView;
using System;

namespace MgFlasher.Views.MyCar;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class MyCarPage : BasePage
{
    private readonly ILogger<MyCarPage> _logger;
    private readonly ITabComponentNavigationService _tabComponentNavigationService;

    public new MyCarPageViewModel ViewModel => (MyCarPageViewModel)BindingContext;

    public MyCarPage() : base(DependencyResolver.Resolve<MyCarPageViewModel>(), PageType.MyCar)
    {
        _logger = DependencyResolver.Resolve<ILogger<MyCarPage>>();
        _tabComponentNavigationService = DependencyResolver.Resolve<ITabComponentNavigationService>();

        InitializeComponent();
        Loaded += OnLoaded;
    }

    private void OnLoaded(object sender, EventArgs e)
    {
#if !WINDOWS
        var navBarBinding = new Binding(nameof(MyCarPageViewModel.NavBarIsVisible), mode: BindingMode.OneWay);
        SetBinding(Shell.NavBarIsVisibleProperty, navBarBinding);
#endif
    }

    private async void SfTabView_SelectionChanged(object sender, TabSelectionChangedEventArgs e)
    {
        await _tabComponentNavigationService.HandleSelectionChanged(ViewModel, sender, e);
    }

    private void SfTabView_SelectionChanging(object sender, SelectionChangingEventArgs e)
    {
        _tabComponentNavigationService.HandleSelectionChanging(ViewModel, sender, e);
    }
}