<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.MyCar.MyCarPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:mycarviews="clr-namespace:MgFlasher.Views.MyCar"
    xmlns:logger="clr-namespace:MgFlasher.Views.Logger"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.MyCar"
    xmlns:tabView="clr-namespace:Syncfusion.Maui.TabView;assembly=Syncfusion.Maui.TabView"
    controls:ShellTitleView.HasBarBackButton="False"
    controls:ShellTitleView.HasContextOptionsButton="{Binding ContextOptions, Converter={StaticResource CollectionToVisibility}}"
    x:DataType="vms:MyCarPageViewModel" x:Name="root"
    HideSoftInputOnTapped="True">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}" Padding="10,20,10,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <tabView:SfTabView
                SelectionChanged="SfTabView_SelectionChanged"
                SelectionChanging="SfTabView_SelectionChanging"
                SelectedIndex="{Binding SelectedTabIndex}"
                TabBarPlacement="Bottom"
                TabBarHeight="{Binding TabBarHeight, Mode=OneWay}"
                Grid.Row="0"
                TabBarBackground="{StaticResource ItemSelectedHoverColor}"
                IsEnabled="{Binding BindingContext.IsBusy, Source={x:Reference root}, Converter={StaticResource InvertedBoolConverter}}">
                <tabView:SfTabItem ImageSource="tab_home.png">
                    <tabView:SfTabItem.Content>
                        <mycarviews:MyCarHomeView Margin="0,0,0,10" BindingContext="{Binding BindingContext.Home, Source={x:Reference root}}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
                <tabView:SfTabItem ImageSource="tab_logger.png">
                    <tabView:SfTabItem.Content>
                        <logger:MyCarLoggerView Margin="0,0,0,10" BindingContext="{Binding BindingContext.Logger, Source={x:Reference root}}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
                <tabView:SfTabItem ImageSource="tab_flash.png">
                    <tabView:SfTabItem.Content>
                        <mycarviews:MyCarFlashView Margin="0,0,0,10" BindingContext="{Binding BindingContext.Flash, Source={x:Reference root}}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
                <tabView:SfTabItem ImageSource="tab_history.png">
                    <tabView:SfTabItem.Content>
                        <mycarviews:MyCarHistoryView Margin="0,0,0,10" BindingContext="{Binding BindingContext.History, Source={x:Reference root}}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
                <tabView:SfTabItem ImageSource="tab_diag.png">
                    <tabView:SfTabItem.Content>
                        <mycarviews:MyCarDiagnosticsView Margin="0,0,0,10" BindingContext="{Binding BindingContext.Diagnostics, Source={x:Reference root}}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
            </tabView:SfTabView>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>