<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="MgFlasher.Views.MyCar.MyCarDiagnosticsView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views.MyCar"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.MyCar"
    xmlns:tabView="clr-namespace:Syncfusion.Maui.TabView;assembly=Syncfusion.Maui.TabView"
    x:Name="root"
    x:DataType="vms:MyCarDiagnosticsViewModel">
    <tabView:SfTabView
        Margin="0" TabBarPlacement="Top"
        IndicatorPlacement="Top"
        SelectionChanged="SfTabView_SelectionChanged"
        SelectedIndex="{Binding SelectedTabIndex}">
        <tabView:SfTabItem Header="{i18n:Translate MyCarDiagnostics_Tab_General}">
            <tabView:SfTabItem.Content>
                <views:MyCarDiagnosticsGeneralView Margin="0" BindingContext="{Binding BindingContext.General, Source={x:Reference root}}" />
            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>
        <tabView:SfTabItem Header="{i18n:Translate MyCarDiagnostics_Tab_Custom}">
            <tabView:SfTabItem.Content>
                <views:MyCarDiagnosticsCustomView Margin="0" BindingContext="{Binding BindingContext.Custom, Source={x:Reference root}}" />
            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>
    </tabView:SfTabView>
</ContentView>