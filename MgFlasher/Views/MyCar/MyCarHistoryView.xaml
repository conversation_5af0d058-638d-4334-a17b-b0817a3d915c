<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="MgFlasher.Views.MyCar.MyCarHistoryView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views.MyCar"
    xmlns:logger="clr-namespace:MgFlasher.Views.Logger"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.MyCar"
    xmlns:tabView="clr-namespace:Syncfusion.Maui.TabView;assembly=Syncfusion.Maui.TabView"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    x:Name="root" x:DataType="vms:MyCarHistoryViewModel">
    <Grid Margin="0">
        <Grid.RowDefinitions>
            <RowDefinition Height="13*" />
            <RowDefinition Height="1.5*" />
        </Grid.RowDefinitions>
        <tabView:SfTabView
            Grid.Row="0" Margin="0" TabBarPlacement="Top"
            IndicatorPlacement="Top"
            SelectionChanged="SfTabView_SelectionChanged"
            SelectedIndex="{Binding SelectedTabIndex}">
            <tabView:SfTabView.Resources>
                <Style TargetType="tabView:SfTabView" BasedOn="{StaticResource SfTabViewMgBaseStyle}">
                    <Setter Property="Grid.RowSpan" Value="1" />
                    <Style.Triggers>
                        <DataTrigger TargetType="tabView:SfTabView" Binding="{Binding IsLoggerButtonVisible}" Value="False" x:DataType="vms:MyCarHistoryViewModel">
                            <Setter Property="Grid.RowSpan" Value="2" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </tabView:SfTabView.Resources>
            <tabView:SfTabItem Header="{i18n:Translate MyCarHistory_Tab_Flash}">
                <tabView:SfTabItem.Content>
                    <views:MyCarFlashHistoryView Margin="0" BindingContext="{Binding BindingContext.FlashHistory, Source={x:Reference root}}" />
                </tabView:SfTabItem.Content>
            </tabView:SfTabItem>
            <tabView:SfTabItem Header="{i18n:Translate MyCarHistory_Tab_Logger}">
                <tabView:SfTabItem.Content>
                    <logger:MyCarLoggerFilesView Margin="0" BindingContext="{Binding BindingContext.LoggerFiles, Source={x:Reference root}}" />
                </tabView:SfTabItem.Content>
            </tabView:SfTabItem>
        </tabView:SfTabView>
        <StackLayout
            Orientation="Horizontal" Grid.Row="1"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand"
            IsVisible="{Binding IsLoggerButtonVisible}">
            <controls:BigButton BackgroundColor="{StaticResource PrimaryColor}" Command="{Binding LoggerFiles.GoToLoggerCommand}" Text="{i18n:Translate MyCarFlashView_Logger}" />
            <controls:BigButton HorizontalOptions="End" BackgroundColor="{StaticResource PrimaryAccentColor}" Command="{Binding LoggerFiles.ShareCommand}" Text="{i18n:Translate MyCarFlashView_Share}" />
        </StackLayout>
    </Grid>
</ContentView>