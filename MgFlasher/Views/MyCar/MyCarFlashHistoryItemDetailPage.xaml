<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.MyCar.MyCarFlashHistoryItemDetailPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:mycarviews="clr-namespace:MgFlasher.Views.MyCar"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.MyCar"
    controls:ShellTitleView.HasBarBackButton="True"
    x:DataType="vms:MyCarFlashHistoryItemDetailPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" AutomationId="FlashingHistoryDetailPage_TitleLabel" Text="{i18n:Translate FlashingHistoryDetailPage_Title}" Style="{StaticResource LabelPageTitleStyle}" />
            <ScrollView Grid.Row="1">
                <StackLayout Orientation="Vertical">
                    <BoxView IsVisible="{Binding StartedAt, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding StartedAt, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_StartedAt}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding StartedAt, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding StartedAt, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding FinishedAt, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding FinishedAt, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_FinishedAt}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding FinishedAt, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding FinishedAt, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView Style="{StaticResource Separator}" />
                    <StackLayout Orientation="Horizontal" Spacing="0" HorizontalOptions="FillAndExpand">
                        <Label FontAttributes="Bold" Text="{i18n:Translate FlashingHistoryDetailPage_Success}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                        <controls:Checkbox HorizontalOptions="End" VerticalOptions="FillAndExpand" IsChecked="{Binding Success, Mode=OneWay}" />
                    </StackLayout>
                    <BoxView IsVisible="{Binding Stage, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding Stage, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_StageType}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding Stage, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding Stage, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding VersionName, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding VersionName, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_VersionName}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding VersionName, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding VersionName, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding VersionNumber, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding VersionNumber, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_VersionNumber}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding VersionNumber, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding VersionNumber, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding CustomCodeVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding CustomCodeVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_CustomCodeVersion}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding CustomCodeVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding CustomCodeVersion, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding A2lString, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding A2lString, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_A2lString}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding A2lString, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding A2lString, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding HWEL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding HWEL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_HWEL}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding HWEL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding HWEL, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding HWAP, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding HWAP, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_HWAP}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding HWAP, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding HWAP, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding BTLD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding BTLD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_BTLD}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding BTLD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding BTLD, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding SWFL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding SWFL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_SWFL}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding SWFL, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding SWFL, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding SWFK, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding SWFK, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_SWFK}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding SWFK, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding SWFK, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding CAFD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding CAFD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_CAFD}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding CAFD, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding CAFD, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding Details, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding Details, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_Details}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding Details, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding Details, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding Id, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding Id, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_Id}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding Id, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding Id, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding UserEmail, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding UserEmail, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate FlashingHistoryDetailPage_UserEmail}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding UserEmail, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding UserEmail, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView Style="{StaticResource Separator}" />
                    <StackLayout Orientation="Horizontal" Spacing="0" HorizontalOptions="FillAndExpand">
                        <Label FontAttributes="Bold" Text="{i18n:Translate FlashingHistoryDetailPage_Synced}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                        <controls:Checkbox HorizontalOptions="End" VerticalOptions="FillAndExpand" IsChecked="{Binding Synced, Mode=OneWay}" />
                    </StackLayout>
                </StackLayout>
            </ScrollView>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>