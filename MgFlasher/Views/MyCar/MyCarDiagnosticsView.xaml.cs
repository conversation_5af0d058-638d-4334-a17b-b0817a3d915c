﻿using MgFlasher.Helpers;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.MyCar;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Xaml;
using Syncfusion.Maui.TabView;

namespace MgFlasher.Views.MyCar;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class MyCarDiagnosticsView : ContentView
{
    private readonly ITabComponentNavigationService _tabComponentNavigationService;

    public MyCarDiagnosticsViewModel ViewModel => (MyCarDiagnosticsViewModel)BindingContext;

    public MyCarDiagnosticsView()
    {
        _tabComponentNavigationService = DependencyResolver.Resolve<ITabComponentNavigationService>();
        InitializeComponent();
    }

    private async void SfTabView_SelectionChanged(object sender, TabSelectionChangedEventArgs e)
    {
        await _tabComponentNavigationService.HandleSelectionChanged(ViewModel, sender, e);
    }
}