<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="MgFlasher.Views.MyCar.MyCarDiagnosticsCustomView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:models="clr-namespace:MgFlasher.Models"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.MyCar"
    x:DataType="vms:MyCarDiagnosticsCustomViewModel"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Label
            Grid.Row="0"
            FontSize="{StaticResource PrimaryFontSize}"
            HorizontalTextAlignment="Center"
            VerticalTextAlignment="Center"
            IsVisible="{Binding HasOptionAvailable, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}"
            Text="{i18n:Translate CustomCode_Diagnostics_NoOptions}" />
        <Label
            Grid.Row="0"
            FontSize="{StaticResource PrimaryFontSize}"
            HorizontalTextAlignment="Center"
            VerticalTextAlignment="Center"
            IsVisible="{Binding HasOptionAvailable, Mode=OneWay}}"
            Text="{Binding CustomCodeVersionString}" />
        <sf:SfListView
            Grid.Row="2" BackgroundColor="Transparent"
            ItemsSource="{Binding DiagnosticCommands}"
            SelectionMode="None" ItemSize="70">
            <sf:SfListView.ItemTemplate>
                <DataTemplate x:DataType="models:DiagnosticCommandItemModel">
                    <ViewCell>
                        <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="0.03*" />
                                <RowDefinition Height="0.15*" />
                                <RowDefinition Height="1.4*" />
                                <RowDefinition Height="0.15*" />
                                <RowDefinition Height="0.03*" />
                            </Grid.RowDefinitions>
                            <Grid Grid.Row="2">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="0.3*" />
                                    <ColumnDefinition Width="2.2*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Image
                                    HeightRequest="29" WidthRequest="29" Grid.Column="0"
                                    Aspect="AspectFit"
                                    Source="{Binding Icon, Mode=OneWay}" />
                                <Label
                                    Grid.Column="1" Margin="5,0,0,0"
                                    FontSize="{StaticResource SecondaryFontSize}"
                                    HorizontalTextAlignment="Start"
                                    Text="{Binding CommandName, Mode=OneWay}"
                                    VerticalTextAlignment="Center" />
                                <controls:SmallButton
                                    Grid.Column="2"
                                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                                    Command="{Binding Command, Mode=OneWay}"
                                    FontSize="{StaticResource SmallFontSize}"
                                    Text="{Binding ActionName, Mode=OneWay}" />
                            </Grid>
                            <Grid
                                Grid.Row="4"
                                BackgroundColor="{StaticResource ListItemSeparatorColor}"
                                HorizontalOptions="FillAndExpand" HeightRequest="1"
                                VerticalOptions="FillAndExpand" />
                        </Grid>
                    </ViewCell>
                </DataTemplate>
            </sf:SfListView.ItemTemplate>
        </sf:SfListView>
    </Grid>
</ContentView>