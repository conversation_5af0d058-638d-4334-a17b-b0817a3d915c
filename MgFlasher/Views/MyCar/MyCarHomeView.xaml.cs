﻿using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using Syncfusion.Maui.TabView;
using MgFlasher.ViewModels.MyCar;
using MgFlasher.Helpers;
using MgFlasher.Services.Navigation;

namespace MgFlasher.Views.MyCar;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class MyCarHomeView : ContentView
{
    private readonly ITabComponentNavigationService _tabComponentNavigationService;

    public MyCarHomeViewModel ViewModel => (MyCarHomeViewModel)BindingContext;

    public MyCarHomeView()
    {
        _tabComponentNavigationService = DependencyResolver.Resolve<ITabComponentNavigationService>();
        InitializeComponent();
    }

    private async void SfTabView_SelectionChanged(object sender, TabSelectionChangedEventArgs e)
    {
        await _tabComponentNavigationService.HandleSelectionChanged(ViewModel, sender, e);
    }
}