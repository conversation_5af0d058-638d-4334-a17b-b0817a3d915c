<ContentView
    x:Class="MgFlasher.Views.MyCar.MyCarFlashView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.MyCar"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    x:DataType="vms:MyCarFlashViewModel">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="13*" />
            <RowDefinition Height="1.5*" />
        </Grid.RowDefinitions>
        <sf:SfListView
            AutomationId="FlashCarStepsManagerPage_FlashItems"
            Grid.Row="0" BackgroundColor="Transparent"
            HorizontalOptions="FillAndExpand"
            ItemsSource="{Binding FlashCarAllowanceItemViewModels}"
            SelectionMode="None"
            ItemSize="{OnIdiom Phone={OnPlatform Default=75}, Desktop={OnPlatform Default=75}, Default={OnPlatform Default=120}}">
            <sf:SfListView.ItemTemplate>
                <DataTemplate x:DataType="vms:MyCarFlashAllowanceItemViewModel">
                    <ViewCell>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="0.03*" />
                                <RowDefinition Height="0.17*" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="0.17*" />
                                <RowDefinition Height="0.03*" />
                            </Grid.RowDefinitions>
                            <Grid Grid.Row="2">
                                <Grid.Resources>
                                    <ResourceDictionary>
                                        <Style TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}">
                                            <Setter Property="VerticalOptions" Value="Center" />
                                            <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
                                        </Style>
                                    </ResourceDictionary>
                                </Grid.Resources>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1.2*" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="0.9*" />
                                </Grid.ColumnDefinitions>
                                <Label Grid.Column="0" Text="{Binding ProductTypeText, Mode=OneWay}" />
                                <VerticalStackLayout Grid.Column="1" HorizontalOptions="FillAndExpand" IsVisible="{Binding IsBought}" VerticalOptions="CenterAndExpand">
                                    <Image HeightRequest="15" HorizontalOptions="CenterAndExpand" Source="oksmall.png" />
                                </VerticalStackLayout>
                                <controls:SmallButton
                                    Grid.Column="2"
                                    AutomationId="{Binding AutomationId}"
                                    Command="{Binding ViewCommand}"
                                    FontSize="{StaticResource SecondaryFontSize}"
                                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                                    Text="{Binding CommandLabel}" />
                            </Grid>
                            <Grid
                                Grid.Row="4"
                                BackgroundColor="{StaticResource ListItemSeparatorColor}"
                                HorizontalOptions="FillAndExpand" HeightRequest="1"
                                VerticalOptions="FillAndExpand" />
                        </Grid>
                    </ViewCell>
                </DataTemplate>
            </sf:SfListView.ItemTemplate>
        </sf:SfListView>
        <controls:BigButton
            Grid.Row="1" HorizontalOptions="FillAndExpand"
            BackgroundColor="{StaticResource PrimarySuccessColor}"
            Command="{Binding ActivateCommand}"
            IsVisible="{Binding CanActivate}"
            Text="{i18n:Translate Install_Btn_Label}" />
        <controls:BigButton
            Grid.Row="1" HorizontalOptions="FillAndExpand"
            BackgroundColor="{StaticResource PrimaryColor}"
            Command="{Binding LoggerCommand}"
            IsVisible="{Binding CanActivate, Converter={StaticResource InvertedBoolConverter}}"
            Text="{i18n:Translate MyCarFlashView_Logger}" />
    </Grid>
</ContentView>