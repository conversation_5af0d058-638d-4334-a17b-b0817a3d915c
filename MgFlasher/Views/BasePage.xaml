<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    BackgroundColor="{StaticResource AccentBackgroundColor}"
    x:Class="MgFlasher.Views.BasePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:PageViewModel"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:sf="clr-namespace:Syncfusion.Maui.Core;assembly=Syncfusion.Maui.Core"
    controls:ShellTitleView.IsBusy="{Binding IsBusy}"
    Shell.BackgroundColor="{StaticResource TitleViewBackgroundColor}"
    IsBusy="{Binding IsBusy}">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsVisible="False" />
    </Shell.BackButtonBehavior>
    <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" x:Name="MainGrid">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Image Grid.Row="0" Aspect="AspectFill" Source="bg.png" />
        <ContentView Grid.Row="0" x:Name="BodyLayout" Padding="{Binding Source={x:Reference MainGrid}, Path=Width, Converter={StaticResource ProportionalPaddingConverter}}" />
        <controls:TutorialPopup x:Name="TutorialPopup" IsVisible="{OnPlatform Default=True, iOS=False}" />
        <sf:SfBusyIndicator
            Grid.Row="0"
            IsRunning="{Binding PageLoader.IsLoading}"
            IsVisible="{Binding PageLoader.IsLoading}"
            AnimationType="CircularMaterial"
            Title="{Binding PageLoader.LoadingTitle}"
            SizeFactor="0.7" MaximumHeightRequest="150"
            MaximumWidthRequest="200"
            TextColor="{StaticResource PrimaryTextColor}"
            IndicatorColor="{StaticResource PrimaryColor}"
            FontAttributes="Bold"
            OverlayFill="{StaticResource SemiTransparentPrimaryBackgroundColor}" />
    </Grid>
</ContentPage>