<?xml version="1.0" encoding="UTF-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.ReadModulePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views"
    controls:ShellTitleView.HasBarBackButton="True"
    x:Key="root"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:ReadModulePageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <VerticalStackLayout Grid.Row="0" HorizontalOptions="FillAndExpand" IsVisible="{Binding CarNotSupported, Mode=OneWay}" Spacing="10">
                <Label HorizontalOptions="Center" Text="{i18n:Translate MyCar_CheckSupport_Unsupported}">
                    <Label.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding SelectSweListCommand}" NumberOfTapsRequired="2" />
                    </Label.GestureRecognizers>
                </Label>
                <controls:SmallButton
                    IsVisible="{Binding CanSendFeedback, Mode=OneWay}"
                    Margin="20,0,20,0"
                    BackgroundColor="{StaticResource PrimaryAccentColor}"
                    Command="{Binding SendFeedbackComand}"
                    Text="{i18n:Translate ReadModuleInfo_SendFeedback}" />
            </VerticalStackLayout>
            <Label
                Grid.Row="0"
                FontSize="{StaticResource LargeFontSize}"
                HorizontalOptions="Center"
                IsVisible="{Binding CarNotSupported, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}"
                Text="{Binding EcuInfoHeadingString}">
                <Label.GestureRecognizers>
                    <TapGestureRecognizer Command="{Binding SelectSweListCommand}" NumberOfTapsRequired="2" />
                </Label.GestureRecognizers>
            </Label>
            <Label
                Grid.Row="1"
                BackgroundColor="{StaticResource PrimaryColor}"
                Margin="0,0,0,0"
                FontSize="{StaticResource PrimaryFontSize}"
                TextColor="{StaticResource PrimaryTextColor}"
                Text="{Binding SweListNameString}" />
            <sf:SfListView
                ItemSize="70" SelectionMode="None" Padding="0,10,0,0"
                Grid.Row="2"
                ItemsSource="{Binding EcuInformation, Mode=OneWay}">
                <sf:SfListView.ItemTemplate>
                    <DataTemplate x:DataType="vms:EcuInformationValue">
                        <Grid Grid.ColumnDefinitions="*" Background="{StaticResource PrimaryAccentColor}">
                            <Label
                                Grid.Row="0" Margin="5"
                                Text="{Binding Value}"
                                TextColor="{StaticResource PrimaryTextColor}"
                                FontSize="{StaticResource SmallFontSize}"
                                HorizontalTextAlignment="Center"
                                VerticalTextAlignment="Center" />
                        </Grid>
                    </DataTemplate>
                </sf:SfListView.ItemTemplate>
            </sf:SfListView>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>