﻿using System;
using MgFlasher.ViewModels.FlashingCarOptions;
using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using MgFlasher.Localization.Resources;

namespace MgFlasher.Views.FlashingCarOptions.Controls;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class CustomOptionCheckboxRow : Grid
{
    public static readonly BindableProperty ItemTemplateProperty = BindableProperty.Create(nameof(ItemTemplate),
        typeof(DataTemplate), typeof(CustomOptionCheckboxRow), null);

    public DataTemplate ItemTemplate
    {
        get { return (DataTemplate)GetValue(ItemTemplateProperty); }
        set { SetValue(ItemTemplateProperty, value); }
    }

    public static readonly BindableProperty CustomizeButtonTextProperty = BindableProperty.Create(
        nameof(CustomizeButtonText),
        typeof(string),
        typeof(CustomOptionCheckboxRow),
        default(string),
        propertyChanged: OnCustomizeButtonTextChanged);

    public string CustomizeButtonText
    {
        get => (string)GetValue(CustomizeButtonTextProperty);
        set => SetValue(CustomizeButtonTextProperty, value);
    }

    private static void OnCustomizeButtonTextChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is CustomOptionCheckboxRow control)
        {
            var newText = newValue as string;
            if (string.IsNullOrEmpty(newText))
            {
                newText = AppResources.CustomOptions_Previews_Edit;
            }

            control.CustomizeCommandLabel.Text = $"{newText} >";
        }
    }

    public CustomOptionCheckboxRow()
    {
        InitializeComponent();
        BindingContextChanged += OnBindingContextChanged;
    }

    private void OnBindingContextChanged(object sender, EventArgs e)
    {
        if (BindingContext is CustomizableCustomOptionViewModel vm)
        {
            vm.ItemTemplate = ItemTemplate;
            vm.ItemTemplateViewModel = Parent.BindingContext;
        }
    }
}