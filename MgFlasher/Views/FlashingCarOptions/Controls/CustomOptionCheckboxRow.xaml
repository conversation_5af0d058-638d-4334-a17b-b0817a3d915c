<?xml version="1.0" encoding="UTF-8" ?>
<Grid
    x:Class="MgFlasher.Views.FlashingCarOptions.Controls.CustomOptionCheckboxRow"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:ms="clr-namespace:MgFlasher.Views.Controls"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.FlashingCarOptions"
    x:DataType="vms:CustomOptionViewModel"
    HeightRequest="63"
    IsVisible="{Binding IsVisible}"
    HorizontalOptions="FillAndExpand">
    <Grid.RowDefinitions>
        <RowDefinition Height="0.02*" />
        <RowDefinition Height="0.06*" />
        <RowDefinition Height="Auto" />
        <!--<RowDefinition Height="{OnIdiom Phone={OnPlatform Default=1.6*}, Default={OnPlatform Android=5.6*, Default=*}}" />-->
        <RowDefinition Height="0.06*" />
        <RowDefinition Height="0.02*" />
    </Grid.RowDefinitions>
    <Grid
        Grid.Row="1" Grid.RowSpan="3" Margin="0"
        Padding="0,5,0,5" VerticalOptions="FillAndExpand"
        HorizontalOptions="FillAndExpand">
        <Grid.Resources>
            <ResourceDictionary>
                <Style TargetType="Grid" BasedOn="{StaticResource GridMgBaseStyle}">
                    <Setter Property="BackgroundColor" Value="Transparent" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsChecked}" TargetType="Grid" Value="True" x:DataType="vms:CustomOptionViewModel">
                            <Setter Property="BackgroundColor" Value="{StaticResource ItemSelectedHoverColor}" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </ResourceDictionary>
        </Grid.Resources>
    </Grid>
    <Grid Grid.Row="2" VerticalOptions="FillAndExpand">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="5.8*" />
            <ColumnDefinition Width="2.2*" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="1*" />
        </Grid.ColumnDefinitions>
        <VerticalStackLayout VerticalOptions="Center" Grid.Column="0" Spacing="0" Margin="5,5,5,5">
            <VerticalStackLayout.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding CheckCommand}" />
            </VerticalStackLayout.GestureRecognizers>
            <Label Margin="0" Padding="0,5,0,0" FontSize="{StaticResource SmallFontSize}" Text="{Binding Option}" />
            <Label
                Margin="0" Padding="0,0,0,5"
                FontSize="{StaticResource ExtraSmallFontSize}"
                IsVisible="{Binding Info, Converter={StaticResource StringToVisibility}}"
                Text="{Binding Info}" />
        </VerticalStackLayout>
        <Label
            Grid.Column="1" x:Name="CustomizeCommandLabel"
            Opacity="{Binding IsChecked, Converter={StaticResource BoolToVisualOpacityConverter}}"
            IsVisible="{Binding IsCustomizeCommandVisible, Mode=OneWay}"
            Text="Edit &gt;" Margin="0,0,0,5"
            HorizontalOptions="End" VerticalOptions="Center"
            AutomationId="{Binding CustomizeCommandAutomationId}">
            <Label.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding CustomizeCommand}" />
            </Label.GestureRecognizers>
        </Label>
        <Grid Grid.Column="2" BackgroundColor="{StaticResource ListItemSeparatorColor}" WidthRequest="1" />
        <ms:Checkbox
            Grid.Column="3" HorizontalOptions="Center"
            VerticalOptions="Center"
            IsChecked="{Binding IsChecked, Mode=TwoWay}"
            AutomationId="{Binding CheckButtonAutomationId}" />
    </Grid>
    <Grid
        Grid.Row="4"
        BackgroundColor="{StaticResource ListItemSeparatorColor}"
        HorizontalOptions="FillAndExpand" HeightRequest="1"
        VerticalOptions="FillAndExpand" />
</Grid>