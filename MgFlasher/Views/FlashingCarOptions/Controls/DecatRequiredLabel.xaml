<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" x:Class="MgFlasher.Views.FlashingCarOptions.Controls.DecatRequiredLabel" x:Name="Root">
    <HorizontalStackLayout Margin="20,0,0,10">
        <Border
            Stroke="Red" StrokeThickness="2"
            Background="Transparent"
            StrokeShape="RoundRectangle 20" Padding="10,5"
            HorizontalOptions="Start">
            <Label HorizontalOptions="Center">
                <Label.FormattedText>
                    <FormattedString>
                        <Span Text="Free-flow" FontAttributes="Bold" TextColor="White" FontSize="{StaticResource ExtraSmallFontSize}" />
                        <Span Text=" Exhaust Required!" TextColor="White" FontSize="{StaticResource ExtraSmallFontSize}" />
                    </FormattedString>
                </Label.FormattedText>
            </Label>
        </Border>
    </HorizontalStackLayout>
</ContentView>