﻿using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.ViewModels.FlashingCarOptions;
using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Views.FlashingCarOptions;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class FlashingCarOptionsItemPage : BasePage
{
    public FlashingCarOptionsItemPage() : base(DependencyResolver.Resolve<FlashingCarOptionsItemPageViewModel>(), PageType.FlashingCarOptionsItem)
    {
        InitializeComponent();
    }

    protected override void OnParentSet()
    {
        base.OnParentSet();
        if (BindingContext == null || Parent is null)
        {
            DisplayWrapper.Children.Clear();
        }
        else if (BindingContext is FlashingCarOptionsItemPageViewModel vm && vm.ViewModel?.ItemTemplate is not null)
        {
            var view = vm.ViewModel.ItemTemplate.CreateContent() as Layout;
            view.BindingContext = vm.ViewModel.ItemTemplateViewModel;
            DisplayWrapper.Children.Add(view);
            if (vm.ViewModel.DefaultsCommand is not null)
            {
                DefaultsButton.Command = vm.ViewModel.DefaultsCommand;
                DefaultsButton.IsVisible = true;
                DefaultsButton.IsEnabled = true;
            }
            else
            {
                DefaultsButton.Command = null;
                DefaultsButton.IsVisible = false;
                DefaultsButton.IsEnabled = false;
            }
        }
    }
}