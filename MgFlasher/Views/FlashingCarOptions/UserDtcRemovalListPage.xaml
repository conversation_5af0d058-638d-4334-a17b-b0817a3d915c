<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.FlashingCarOptions.UserDtcRemovalListPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.FlashingCarOptions"
    x:DataType="vms:UserDtcRemovalListPageViewModel"
    controls:ShellTitleView.HasBarBackButton="True">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="10*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate DtcRemovalListPage_Title}" />
            <controls:SearchableSfListView Grid.Row="1" ItemTemplate="{StaticResource OptionalItemTemplate}" ItemsSource="{Binding DtcItems, Mode=OneWay}" ItemSize="{OnIdiom Phone={OnPlatform Default=95}, Desktop={OnPlatform Default=95}, Default={OnPlatform Android=100, Default=87}}" />
            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="0.7*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <controls:BigButton Grid.Column="0" BackgroundColor="{StaticResource AccentTextColor}" Command="{Binding DefaultCommand}" Text="{i18n:Translate DtcRemovalListPage_Default}" />
                <controls:BigButton Grid.Column="1" BackgroundColor="{StaticResource PrimarySuccessColor}" Command="{Binding SubmitCommand}" Text="{i18n:Translate DtcRemovalListPage_Submit}" />
            </Grid>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>