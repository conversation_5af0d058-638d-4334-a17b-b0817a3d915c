<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    controls:ShellTitleView.HasBarBackButton="True"
    x:Class="MgFlasher.Views.FlashingCarOptions.FlashingCarOptionsPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:ms="clr-namespace:MgFlasher.Views.FlashingCarOptions.Controls"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:ik="clr-namespace:InputKit.Shared.Controls;assembly=InputKit.Maui"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.FlashingCarOptions"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    x:DataType="vms:FlashingCarOptionsPageViewModel"
    xmlns:stepProgressBar="clr-namespace:Syncfusion.Maui.ProgressBar;assembly=Syncfusion.Maui.ProgressBar"
    xmlns:behaviors="clr-namespace:MgFlasher.Behaviors">
    <views:BasePage.Body>
        <Grid Style="{StaticResource FixedScrollMainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="8*" />
                <RowDefinition Height="{Binding IsWizardMode, Converter={StaticResource BoolToRowHeightOnMobileConverter}, ConverterParameter={OnPlatform WinUI=false, Default=true}}" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate CustomOptions_Headline}" IsVisible="{Binding IsWizardMode, Converter={StaticResource InvertedBoolConverter}}" />
            <!--#region StepProgressBar-->
            <ScrollView
                IsVisible="{Binding IsWizardMode}"
                Orientation="Horizontal"
                HorizontalScrollBarVisibility="{OnIdiom Phone='Never', Default='Always'}"
                x:Name="wizardScroll"
                Margin="{OnPlatform iOS='0,0,20,0', Default='0,0,0,0'}">
                <StackLayout Margin="{OnIdiom Desktop='0,0,0,20', Default='0,0,0,0'}" Orientation="Horizontal" Spacing="10" HorizontalOptions="FillAndExpand">
                    <StackLayout x:Name="ProgressBarStep1" WidthRequest="150">
                        <Border
                            WidthRequest="50" HeightRequest="50"
                            HorizontalOptions="CenterAndExpand"
                            Stroke="Transparent" StrokeThickness="0">
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="1" />
                            </Border.GestureRecognizers>
                            <Image
                                Source="co_wizard_exhaust.png" Aspect="AspectFit"
                                WidthRequest="40" HeightRequest="40"
                                HorizontalOptions="Center" VerticalOptions="Center">
                                <Image.Behaviors>
                                    <toolkit:IconTintColorBehavior TintColor="{Binding Source={x:Reference LabelStep1}, Path=TextColor}" />
                                </Image.Behaviors>
                            </Image>
                        </Border>
                        <Label
                            Text="EXHAUST" HorizontalOptions="Center"
                            VerticalOptions="Center"
                            TextColor="{Binding CurrentWizardStep, Converter={StaticResource BackgroundColorConverter}, ConverterParameter='1'}"
                            x:Name="LabelStep1">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="1" />
                            </Label.GestureRecognizers>
                        </Label>
                    </StackLayout>
                    <BoxView
                        WidthRequest="100" HeightRequest="2"
                        Margin="{OnIdiom Phone='-75,-25,-75,0', Default='-125,-25,-125,0'}"
                        BackgroundColor="Gray" VerticalOptions="Center"
                        HorizontalOptions="CenterAndExpand" />
                    <StackLayout x:Name="ProgressBarStep2" WidthRequest="150">
                        <Border
                            WidthRequest="50" HeightRequest="50"
                            HorizontalOptions="CenterAndExpand"
                            Stroke="Transparent" StrokeThickness="0">
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="2" />
                            </Border.GestureRecognizers>
                            <Image
                                Source="co_wizard_rad.png" Aspect="AspectFit"
                                WidthRequest="40" HeightRequest="40"
                                HorizontalOptions="Center" VerticalOptions="Center">
                                <Image.Behaviors>
                                    <toolkit:IconTintColorBehavior TintColor="{Binding Source={x:Reference LabelStep2}, Path=TextColor}" />
                                </Image.Behaviors>
                            </Image>
                        </Border>
                        <Label
                            Text="COOLING" HorizontalOptions="Center"
                            VerticalOptions="Center"
                            TextColor="{Binding CurrentWizardStep, Converter={StaticResource BackgroundColorConverter}, ConverterParameter='2'}"
                            x:Name="LabelStep2">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="2" />
                            </Label.GestureRecognizers>
                        </Label>
                    </StackLayout>
                    <BoxView
                        WidthRequest="100" HeightRequest="2"
                        Margin="{OnIdiom Phone='-75,-25,-75,0', Default='-125,-25,-125,0'}"
                        BackgroundColor="Gray" VerticalOptions="Center"
                        HorizontalOptions="CenterAndExpand" />
                    <StackLayout x:Name="ProgressBarStep3" WidthRequest="150">
                        <Border
                            WidthRequest="50" HeightRequest="50"
                            HorizontalOptions="CenterAndExpand"
                            Stroke="Transparent" StrokeThickness="0">
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="3" />
                            </Border.GestureRecognizers>
                            <Image
                                Source="co_wizard_torque.png" Aspect="AspectFit"
                                WidthRequest="40" HeightRequest="40"
                                HorizontalOptions="Center" VerticalOptions="Center">
                                <Image.Behaviors>
                                    <toolkit:IconTintColorBehavior TintColor="{Binding Source={x:Reference LabelStep3}, Path=TextColor}" />
                                </Image.Behaviors>
                            </Image>
                        </Border>
                        <Label
                            Text="TORQUE" HorizontalOptions="Center"
                            VerticalOptions="Center"
                            TextColor="{Binding CurrentWizardStep, Converter={StaticResource BackgroundColorConverter}, ConverterParameter='3'}"
                            x:Name="LabelStep3">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="3" />
                            </Label.GestureRecognizers>
                        </Label>
                    </StackLayout>
                    <BoxView
                        WidthRequest="100" HeightRequest="2"
                        Margin="{OnIdiom Phone='-75,-25,-75,0', Default='-125,-25,-125,0'}"
                        BackgroundColor="Gray" VerticalOptions="Center"
                        HorizontalOptions="CenterAndExpand" />
                    <StackLayout x:Name="ProgressBarStep4" WidthRequest="150">
                        <Border
                            WidthRequest="50" HeightRequest="50"
                            HorizontalOptions="CenterAndExpand"
                            Stroke="Transparent" StrokeThickness="0">
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="4" />
                            </Border.GestureRecognizers>
                            <Image
                                Source="co_wizard_hardware.png" Aspect="AspectFit"
                                WidthRequest="40" HeightRequest="40"
                                HorizontalOptions="Center" VerticalOptions="Center">
                                <Image.Behaviors>
                                    <toolkit:IconTintColorBehavior TintColor="{Binding Source={x:Reference LabelStep4}, Path=TextColor}" />
                                </Image.Behaviors>
                            </Image>
                        </Border>
                        <Label
                            Text="HARDWARE" HorizontalOptions="Center"
                            VerticalOptions="Center"
                            TextColor="{Binding CurrentWizardStep, Converter={StaticResource BackgroundColorConverter}, ConverterParameter='4'}"
                            x:Name="LabelStep4">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="4" />
                            </Label.GestureRecognizers>
                        </Label>
                    </StackLayout>
                    <BoxView
                        WidthRequest="100" HeightRequest="2"
                        Margin="{OnIdiom Phone='-75,-25,-75,0', Default='-125,-25,-125,0'}"
                        BackgroundColor="Gray" VerticalOptions="Center"
                        HorizontalOptions="CenterAndExpand" />
                    <StackLayout x:Name="ProgressBarStep5" WidthRequest="150">
                        <Border
                            WidthRequest="50" HeightRequest="50"
                            HorizontalOptions="CenterAndExpand"
                            Stroke="Transparent" StrokeThickness="0">
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="5" />
                            </Border.GestureRecognizers>
                            <Image
                                Source="co_wizard_other.png" Aspect="AspectFit"
                                WidthRequest="40" HeightRequest="40"
                                HorizontalOptions="Center" VerticalOptions="Center">
                                <Image.Behaviors>
                                    <toolkit:IconTintColorBehavior TintColor="{Binding Source={x:Reference LabelStep5}, Path=TextColor}" />
                                </Image.Behaviors>
                            </Image>
                        </Border>
                        <Label
                            Text="OTHER" HorizontalOptions="Center"
                            VerticalOptions="Center"
                            TextColor="{Binding CurrentWizardStep, Converter={StaticResource BackgroundColorConverter}, ConverterParameter='5'}"
                            x:Name="LabelStep5">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="5" />
                            </Label.GestureRecognizers>
                        </Label>
                    </StackLayout>
                    <BoxView
                        WidthRequest="100" HeightRequest="2"
                        Margin="{OnIdiom Phone='-75,-25,-75,0', Default='-125,-25,-125,0'}"
                        BackgroundColor="Gray" VerticalOptions="Center"
                        HorizontalOptions="CenterAndExpand" />
                    <StackLayout x:Name="ProgressBarStep6" WidthRequest="150">
                        <Border
                            WidthRequest="50" HeightRequest="50"
                            HorizontalOptions="CenterAndExpand"
                            Stroke="Transparent" StrokeThickness="0">
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="6" />
                            </Border.GestureRecognizers>
                            <Image
                                Source="co_wizard_cc.png" Aspect="AspectFit"
                                WidthRequest="40" HeightRequest="40"
                                HorizontalOptions="Center" VerticalOptions="Center">
                                <Image.Behaviors>
                                    <toolkit:IconTintColorBehavior TintColor="{Binding Source={x:Reference LabelStep6}, Path=TextColor}" />
                                </Image.Behaviors>
                            </Image>
                        </Border>
                        <Label
                            Text="CUSTOM CODE" HorizontalOptions="Center"
                            VerticalOptions="Center"
                            TextColor="{Binding CurrentWizardStep, Converter={StaticResource BackgroundColorConverter}, ConverterParameter='6'}"
                            x:Name="LabelStep6">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="6" />
                            </Label.GestureRecognizers>
                        </Label>
                    </StackLayout>
                    <BoxView
                        WidthRequest="100" HeightRequest="2"
                        Margin="{OnIdiom Phone='-75,-25,-75,0', Default='-125,-25,-125,0'}"
                        BackgroundColor="Gray" VerticalOptions="Center"
                        HorizontalOptions="CenterAndExpand" />
                    <StackLayout x:Name="ProgressBarStep7" WidthRequest="150">
                        <Border
                            WidthRequest="50" HeightRequest="50"
                            HorizontalOptions="CenterAndExpand"
                            Stroke="Transparent" StrokeThickness="0">
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="7" />
                            </Border.GestureRecognizers>
                            <Image
                                Source="co_wizard_ccadvanced.png" Aspect="AspectFit"
                                WidthRequest="40" HeightRequest="40"
                                HorizontalOptions="Center" VerticalOptions="Center">
                                <Image.Behaviors>
                                    <toolkit:IconTintColorBehavior TintColor="{Binding Source={x:Reference LabelStep7}, Path=TextColor}" />
                                </Image.Behaviors>
                            </Image>
                        </Border>
                        <Label
                            Text="CC ADVANCED" HorizontalOptions="Center"
                            VerticalOptions="Center"
                            TextColor="{Binding CurrentWizardStep, Converter={StaticResource BackgroundColorConverter}, ConverterParameter='7'}"
                            x:Name="LabelStep7">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ProgressBarStepTappedCommand}" CommandParameter="7" />
                            </Label.GestureRecognizers>
                        </Label>
                    </StackLayout>
                </StackLayout>
            </ScrollView>
            <!--#endregion StepProgressBar-->
            <Label Grid.Row="1" x:Name="stickyHeaderLabel" IsVisible="{Binding IsWizardMode, Converter={StaticResource InvertedBoolConverter}}" Style="{StaticResource LabelPageTitleStyle}" />
            <ScrollView Grid.Row="2" x:Name="scrollView" Scrolled="ScrollView_Scrolled">
                <StackLayout Orientation="Vertical" x:Name="stackLayout">
                    <StackLayout.Resources>
                        <ResourceDictionary>
                            <Style BasedOn="{StaticResource LabelMgBaseStyle}" TargetType="Label">
                                <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
                            </Style>
                            <Style BasedOn="{StaticResource InputKitRadioButtonMgBaseStyle}" TargetType="ik:RadioButton">
                                <Setter Property="TextFontSize" Value="{StaticResource SmallFontSize}" />
                            </Style>
                        </ResourceDictionary>
                    </StackLayout.Resources>
                    <!--#region DEVELOPER ONLY-->
                    <StackLayout x:Name="Section_DeveloperOnly" IsVisible="{Binding IsDeveloperOnlyVisible, Mode=OneWay}">
                        <Label Text="Developer Only" Style="{StaticResource LabelPageTitleStyle}" />
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding RemoveGeneralDtcCodes}" />
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding SkipCrcCorrection}" />
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding DeleteBenchEcuDtcs}" />
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding PrepareFileWithoutFlashing}" />
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding FlashingSegmentOptions}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <ik:RadioButtonGroupView Orientation="Vertical" RadioButtonGroup.GroupName="PSTFlash" Spacing="10">
                                            <ik:RadioButton Text="{Binding PreventPSTFlash.Option}" IsChecked="{Binding PreventPSTFlash.IsChecked}" />
                                            <ik:RadioButton Text="{Binding ForceFlashPST.Option}" IsChecked="{Binding ForceFlashPST.IsChecked}" />
                                        </ik:RadioButtonGroupView>
                                        <ik:RadioButtonGroupView
                                            Orientation="Vertical"
                                            IsVisible="{Binding ForceFlashPST.IsChecked}"
                                            Margin="10,5,0,0"
                                            RadioButtonGroup.GroupName="WithCustomCode"
                                            Spacing="10">
                                            <ik:RadioButton Text="{Binding ForceFlashPST_WithCustomCode.Option}" IsChecked="{Binding ForceFlashPST_WithCustomCode.IsChecked}" />
                                            <ik:RadioButton Text="{Binding ForceFlashPST_WithoutCustomCode.Option}" IsChecked="{Binding ForceFlashPST_WithoutCustomCode.IsChecked}" />
                                        </ik:RadioButtonGroupView>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding TunerOverrideDst}" />
                    </StackLayout>
                    <!--#endregion DEVELOPER ONLY-->
                    <!--#region EXHAUST-->
                    <StackLayout x:Name="Section_Exhaust">
                        <StackLayout.IsVisible>
                            <MultiBinding Converter="{StaticResource AnyTrueMultiConverter}">
                                <Binding Converter="{StaticResource InvertedBoolConverter}" Mode="OneWay" Path="IsWizardMode" />
                                <Binding Mode="OneWay" Path="CurrentWizardStep" Converter="{StaticResource EqualToConverter}" ConverterParameter="1" />
                            </MultiBinding>
                        </StackLayout.IsVisible>
                        <Label Text="Exhaust" Style="{StaticResource LabelPageTitleStyle}" />
                        <Label
                            Text="Use options in this section to customize how your exhaust works - set burbles, cold start and roars to make your car sound the way you want."
                            HorizontalTextAlignment="Center"
                            FontSize="{StaticResource ExtraSmallFontSize}"
                            IsVisible="{Binding IsWizardMode}"
                            Margin="{OnPlatform iOS='0,0,20,0', Default='0,0,0,0'}" />
                        <!--#region Burbles-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding Burble}" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=BurblePreview}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <StackLayout Orientation="Horizontal">
                                            <Label HorizontalOptions="StartAndExpand" HorizontalTextAlignment="Center" Text="{i18n:Translate FlashingCarOptionsPage_BurbleStyle}" VerticalTextAlignment="Center" />
                                            <Picker
                                                BackgroundColor="#88000000" HorizontalOptions="End"
                                                HorizontalTextAlignment="Center"
                                                ItemDisplayBinding="{Binding DisplayName, Mode=OneWay}"
                                                ItemsSource="{Binding BurbleStyleList}"
                                                SelectedItem="{Binding BurbleAggressionStyle}"
                                                VerticalTextAlignment="Center" WidthRequest="100"
                                                x:Name="BurbleAggressionStyle_Picker" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="20,10,20,10">
                                            <Label HorizontalOptions="StartAndExpand" Text="{Binding BurbleAggressionStyle.Description, Mode=OneWay}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding Source={x:Reference BurbleAggressionStyle_Picker}, Path=SelectedIndex, Converter={StaticResource BurbleAggressionStyleOffInvertedConverter}}" Spacing="20">
                                            <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleHybridPatch}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding Source={x:Reference BurbleAggressionStyle_Picker}, Path=SelectedIndex, Converter={StaticResource BurbleAggressionStyleOffInvertedConverter}}" Spacing="20">
                                            <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleSpecifyDrivingModes}" />
                                            <StackLayout Orientation="Vertical" BackgroundColor="#44000000" Margin="0,-10,0,-10">
                                                <StackLayout Orientation="Vertical" IsVisible="{Binding BurbleSpecifyDrivingModes.IsChecked}" Margin="30,10,30,10" Spacing="-10">
                                                    <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleEnableEcoDrivingMode}" />
                                                    <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleEnableEcoPlusDrivingMode}" />
                                                    <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleEnableComfortDrivingMode}" />
                                                    <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleEnableComfortPlusDrivingMode}" />
                                                    <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleEnableSportDrivingMode}" />
                                                    <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleEnableSportPlusDrivingMode}" />
                                                </StackLayout>
                                            </StackLayout>
                                            <ms:CustomOptionCheckboxRow BindingContext="{Binding Burble_CW_SOUND}" />
                                            <StackLayout Orientation="Vertical" BackgroundColor="#44000000" IsVisible="{Binding Burble_CW_SOUND.IsVisible}">
                                                <StackLayout Orientation="Vertical" IsVisible="{Binding Burble_CW_SOUND.IsChecked}" Margin="20,10,20,10">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="CW_SOUND (Default = 0x2B)" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding Burble_CW_SOUND_Value, Converter={StaticResource IntToHexConverter}}" />
                                                    </StackLayout>
                                                    <StackLayout Orientation="Vertical">
                                                        <StackLayout Orientation="Horizontal" BackgroundColor="#55880000">
                                                            <Label HorizontalOptions="EndAndExpand" HorizontalTextAlignment="Center" Text="Enter value: " VerticalTextAlignment="Center" />
                                                            <Entry
                                                                HorizontalOptions="Center"
                                                                HorizontalTextAlignment="Center"
                                                                IsTextPredictionEnabled="false" Keyboard="Numeric"
                                                                MaxLength="3"
                                                                Text="{Binding Burble_CW_SOUND_Value}"
                                                                VerticalTextAlignment="Center" WidthRequest="75" />
                                                            <Label HorizontalOptions="StartAndExpand" HorizontalTextAlignment="Center" Text="[Decimal]" VerticalTextAlignment="Center" />
                                                        </StackLayout>
                                                        <Slider Maximum="255" Minimum="0" Value="{Binding Burble_CW_SOUND_Value}" />
                                                    </StackLayout>
                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding Source={x:Reference BurbleAggressionStyle_Picker}, Path=SelectedIndex, Converter={StaticResource BurbleAggressionStyleToVisibilityConverter}, ConverterParameter=1}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_Aggressiveness_Style1}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleStyle1Aggressiveness, Converter={StaticResource BurbleStyle1AggressivenessConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="10" Minimum="0" Value="{Binding BurbleStyle1Aggressiveness}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding Source={x:Reference BurbleAggressionStyle_Picker}, Path=SelectedIndex, Converter={StaticResource BurbleAggressionStyleToVisibilityConverter}, ConverterParameter=2}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_Aggressiveness_Style2}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleStyle2Aggressiveness, Converter={StaticResource BurbleStyle2AggressivenessConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="30" Minimum="0" Value="{Binding BurbleStyle2Aggressiveness}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding Source={x:Reference BurbleAggressionStyle_Picker}, Path=SelectedIndex, Converter={StaticResource BurbleAggressionStyleToVisibilityConverter}, ConverterParameter=3}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_Aggressiveness_Style3Normal}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleStyle3NormalAggressiveness, Converter={StaticResource BurbleStyle3AggressivenessConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="100" Minimum="0" Value="{Binding BurbleStyle3NormalAggressiveness}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding Source={x:Reference BurbleAggressionStyle_Picker}, Path=SelectedIndex, Converter={StaticResource BurbleAggressionStyleToVisibilityConverter}, ConverterParameter=3}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_Aggressiveness_Style3Sport}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleStyle3SportAggressiveness, Converter={StaticResource BurbleStyle3AggressivenessConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="100" Minimum="0" Value="{Binding BurbleStyle3SportAggressiveness}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_DurationSport}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleDurationSport, Converter={StaticResource BurbleDurationConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="41" Minimum="0" Value="{Binding BurbleDurationSport}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_DurationSportPlus}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleDurationSportPlus, Converter={StaticResource BurbleDurationConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="41" Minimum="0" Value="{Binding BurbleDurationSportPlus}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_RpmMin}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleRpmMin, Converter={StaticResource BurbleRpmMinConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="62" Minimum="0" Value="{Binding BurbleRpmMin}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_RpmMax}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleRpmMax, Converter={StaticResource BurbleRpmMaxConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="62" Minimum="0" Value="{Binding BurbleRpmMax}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_SpeedMin}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleSpeedMin, Converter={StaticResource BurbleSpeedMinConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="61" Minimum="0" Value="{Binding BurbleSpeedMin}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_SpeedMax}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleSpeedMax, Converter={StaticResource BurbleSpeedMaxConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="62" Minimum="0" Value="{Binding BurbleSpeedMax}" />
                                            </StackLayout>
                                            <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleTemperatureLimitIncrease}" />
                                            <ms:DecatRequiredLabel />
                                            <StackLayout Orientation="Vertical">
                                                <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleUpShift}" />
                                                <StackLayout Orientation="Vertical" IsVisible="{Binding BurbleUpShift.IsVisible}" Margin="20,0,20,0">
                                                    <StackLayout Orientation="Horizontal" IsVisible="{Binding BurbleUpShift.IsChecked}">
                                                        <Label HorizontalOptions="StartAndExpand" HorizontalTextAlignment="Center" Text="{i18n:Translate FlashingCarOptionsPage_BurbleUpShiftAggression}" VerticalTextAlignment="Center" />
                                                        <Picker
                                                            BackgroundColor="#88000000" HorizontalOptions="End"
                                                            HorizontalTextAlignment="Center"
                                                            ItemsSource="{Binding BurbleUpShiftAggressionList}"
                                                            SelectedItem="{Binding BurbleUpShiftAggression}"
                                                            VerticalTextAlignment="Center" WidthRequest="100"
                                                            x:Name="BurbleUpShiftAggression_Picker" />
                                                    </StackLayout>
                                                </StackLayout>
                                            </StackLayout>
                                            <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleForceMaxAggressionAndDurationFactor}" />
                                            <Label HorizontalOptions="StartAndExpand" IsVisible="{Binding Dcat.IsChecked, Converter={StaticResource InvertedBoolConverter}}" Margin="20" Text="{i18n:Translate FlashingCarOptionsPage_BurbleFlamesRequireDecat}" />
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding Dcat.IsChecked}">
                                                <ms:CustomOptionCheckboxRow BindingContext="{Binding BurbleFlame}" />
                                                <Label HorizontalOptions="StartAndExpand" IsVisible="{Binding BurbleFlame.IsVisible, Converter={StaticResource InvertedBoolConverter}}" Margin="20" Text="{i18n:Translate FlashingCarOptionsPage_BurbleFlamesUnsupportedSoftware}" />
                                                <StackLayout Orientation="Vertical" BackgroundColor="#22000000">
                                                    <StackLayout Orientation="Vertical" IsVisible="{Binding BurbleFlame.IsChecked}" Margin="20,10,20,10" Spacing="10">
                                                        <StackLayout Orientation="Horizontal">
                                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_BurbleFlameTiming}" />
                                                            <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleFlameTiming, Converter={StaticResource BurbleFlameTimingConverter}}" />
                                                        </StackLayout>
                                                        <Slider Maximum="101" Minimum="1" Value="{Binding BurbleFlameTiming}" />
                                                    </StackLayout>
                                                    <StackLayout Orientation="Vertical" IsVisible="{Binding BurbleFlame.IsChecked}" Margin="20,10,20,10" Spacing="10">
                                                        <StackLayout Orientation="Horizontal">
                                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_BurbleFlameFuel}" />
                                                            <Label HorizontalOptions="EndAndExpand" Text="{Binding BurbleFlameFuel, Converter={StaticResource BurbleFlameFuelConverter}}" />
                                                        </StackLayout>
                                                        <Slider Maximum="32" Minimum="1" Value="{Binding BurbleFlameFuel}" />
                                                    </StackLayout>
                                                </StackLayout>
                                            </StackLayout>
                                            <HorizontalStackLayout>
                                                <Label HorizontalOptions="Center" VerticalOptions="Center" IsVisible="{Binding Dcat.IsChecked, Converter={StaticResource InvertedBoolConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_BurbleFlamesRequireDecat}" />
                                                <ms:DecatRequiredLabel />
                                            </HorizontalStackLayout>
                                        </StackLayout>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding ColdStartDelete}" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=ColdStartDeletePreview}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <ik:RadioButtonGroupView Orientation="Vertical" RadioButtonGroup.GroupName="ColdStartDelete" Spacing="10">
                                        <ik:RadioButton Text="{Binding ColdStartDeleteAlways.Option}" IsChecked="{Binding ColdStartDeleteAlways.IsChecked}" />
                                        <ik:RadioButton Text="{Binding ColdStartDeleteIfWarm.Option}" IsChecked="{Binding ColdStartDeleteIfWarm.IsChecked}" />
                                    </ik:RadioButtonGroupView>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding ExhaustFlap}" />
                        <!--#region Startup Roar-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding StartupRoar}" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=StartupRoarPreview}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_StartupRoarDuration}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding StartupRoarDuration, Converter={StaticResource StartupRoarDurationConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="11" Minimum="1" Value="{Binding StartupRoarDuration}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_StartupRoarAggression}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding StartupRoarAggression, Converter={StaticResource StartupRoarAggressionConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="102" Minimum="1" Value="{Binding StartupRoarAggression}" />
                                        </StackLayout>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding OpfDelete}" />
                    </StackLayout>
                    <!--#endregion  EXHAUST-->
                    <!--#region ENGINE COOLING-->
                    <StackLayout x:Name="Section_EngineCooling">
                        <StackLayout.IsVisible>
                            <MultiBinding Converter="{StaticResource AnyTrueMultiConverter}">
                                <Binding Converter="{StaticResource InvertedBoolConverter}" Mode="OneWay" Path="IsWizardMode" />
                                <Binding Mode="OneWay" Path="CurrentWizardStep" Converter="{StaticResource EqualToConverter}" ConverterParameter="2" />
                            </MultiBinding>
                        </StackLayout.IsVisible>
                        <Label Text="Engine Cooling" Style="{StaticResource LabelPageTitleStyle}" />
                        <Label
                            Text="In this section you can improve the cooling of your engine."
                            HorizontalTextAlignment="Center"
                            FontSize="{StaticResource ExtraSmallFontSize}"
                            IsVisible="{Binding IsWizardMode}"
                            Margin="{OnPlatform iOS='0,0,20,0', Default='0,0,0,0'}" />
                        <!--#region Cooling-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding Cooling}" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=CoolingPreview}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <StackLayout Orientation="Vertical" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_RegularTargetEngineTemperature}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding MaxCoolingRegularTargetEngineTemperature, Converter={StaticResource MaxCoolingTargetEngineTemperatureConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="110" Minimum="80" Value="{Binding MaxCoolingRegularTargetEngineTemperature}" />
                                        </StackLayout>
                                        <ms:CustomOptionCheckboxRow BindingContext="{Binding MaxCooling}" />
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding Cooling.IsChecked}" Margin="20,0,20,0" Spacing="20">
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding MaxCooling.IsChecked}" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_MaxCoolingRegularTargetEngineTemperature}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding MaxCoolingTargetEngineTemperature, Converter={StaticResource MaxCoolingTargetEngineTemperatureConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="110" Minimum="80" Value="{Binding MaxCoolingTargetEngineTemperature}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding MaxCooling.IsChecked}" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_MaxCoolingTargetIntercoolerCoolantVolumeFlow}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding MaxCoolingTargetIntercoolerCoolantVolumeFlow, Converter={StaticResource MaxCoolingTargetIntercoolerCoolantVolumeFlowConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="1800" Minimum="1500" Value="{Binding MaxCoolingTargetIntercoolerCoolantVolumeFlow}" />
                                            </StackLayout>
                                        </StackLayout>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding RadiatorFlap}" />
                    </StackLayout>
                    <!--#endregion ENGINE COOLING-->
                    <!--#region TORQUE-->
                    <StackLayout x:Name="Section_Torque">
                        <StackLayout.IsVisible>
                            <MultiBinding Converter="{StaticResource AnyTrueMultiConverter}">
                                <Binding Converter="{StaticResource InvertedBoolConverter}" Mode="OneWay" Path="IsWizardMode" />
                                <Binding Mode="OneWay" Path="CurrentWizardStep" Converter="{StaticResource EqualToConverter}" ConverterParameter="3" />
                            </MultiBinding>
                        </StackLayout.IsVisible>
                        <Label Text="Torque settings" Style="{StaticResource LabelPageTitleStyle}" />
                        <Label
                            Text="You can configure the Torque Limiter here, as well as setting it for each gear. Below you will also find options to cusomize the Gauges that can be displayed on your infotainment unit."
                            HorizontalTextAlignment="Center"
                            FontSize="{StaticResource ExtraSmallFontSize}"
                            IsVisible="{Binding IsWizardMode}"
                            Margin="{OnPlatform iOS='0,0,20,0', Default='0,0,0,0'}" />
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding TcuLimiterDynamic}" />
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding TcuLimiterStatic}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical" Spacing="20">
                                        <StackLayout Orientation="Horizontal">
                                            <Label HorizontalOptions="StartAndExpand" Text="{Binding TcuLimiterStatic_Name}" VerticalOptions="Center" />
                                            <StackLayout Orientation="Horizontal">
                                                <Entry
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="End"
                                                    IsTextPredictionEnabled="false" Keyboard="Numeric"
                                                    MaxLength="4"
                                                    Text="{Binding TcuLimiterStaticTorque}"
                                                    VerticalOptions="Center" VerticalTextAlignment="Center"
                                                    WidthRequest="100" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalOptions="Center" VerticalTextAlignment="Center" />
                                            </StackLayout>
                                        </StackLayout>
                                        <Slider Maximum="1000" Minimum="250" Value="{Binding TcuLimiterStaticTorque}" />
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#region Torque By Gear-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding TorqueByGear}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <ik:RadioButtonGroupView Orientation="Vertical" RadioButtonGroup.GroupName="TorqueByGear_Gears" Spacing="10">
                                            <ik:RadioButton Text="{Binding TorqueByGear_AllGears.Option}" IsChecked="{Binding TorqueByGear_AllGears.IsChecked}" />
                                            <ik:RadioButton Text="{Binding TorqueByGear_IndividualGears.Option}" IsChecked="{Binding TorqueByGear_IndividualGears.IsChecked}" />
                                        </ik:RadioButtonGroupView>
                                        <!--#region Torque By All Gear-->
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding TorqueByGear_AllGears.IsChecked}" Margin="10">
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_GearAll_MaxTorque}" />
                                                    <StackLayout Orientation="Horizontal">
                                                        <Entry
                                                            HorizontalOptions="Center"
                                                            HorizontalTextAlignment="End"
                                                            IsTextPredictionEnabled="false"
                                                            IsVisible="{Binding TorqueByGear_GearAll_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}"
                                                            Keyboard="Numeric" MaxLength="4"
                                                            Text="{Binding TorqueByGear_GearAll_MaxTorque}"
                                                            VerticalTextAlignment="Center" WidthRequest="100" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_GearAll_MaxTorque, Converter={StaticResource TorqueByGearNoLimitVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NoLimit}" VerticalTextAlignment="Center" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_GearAll_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalTextAlignment="Center" />
                                                    </StackLayout>
                                                </StackLayout>
                                                <Slider Maximum="1001" Minimum="200" Value="{Binding TorqueByGear_GearAll_MaxTorque}" />
                                            </StackLayout>
                                        </StackLayout>
                                        <!--#endregion-->
                                        <!--#region Torque By Individual Gear-->
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding TorqueByGear_IndividualGears.IsChecked}" Margin="10">
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_Gear0_MaxTorque}" />
                                                    <StackLayout Orientation="Horizontal">
                                                        <Entry
                                                            HorizontalOptions="Center"
                                                            HorizontalTextAlignment="End"
                                                            IsTextPredictionEnabled="false"
                                                            IsVisible="{Binding TorqueByGear_Gear0_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}"
                                                            Keyboard="Numeric" MaxLength="4"
                                                            Text="{Binding TorqueByGear_Gear0_MaxTorque}"
                                                            VerticalTextAlignment="Center" WidthRequest="100" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear0_MaxTorque, Converter={StaticResource TorqueByGearNoLimitVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NoLimit}" VerticalTextAlignment="Center" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear0_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalTextAlignment="Center" />
                                                    </StackLayout>
                                                </StackLayout>
                                                <Slider Maximum="1001" Minimum="200" Value="{Binding TorqueByGear_Gear0_MaxTorque}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_Gear1_MaxTorque}" />
                                                    <StackLayout Orientation="Horizontal">
                                                        <Entry
                                                            HorizontalOptions="Center"
                                                            HorizontalTextAlignment="End"
                                                            IsTextPredictionEnabled="false"
                                                            IsVisible="{Binding TorqueByGear_Gear1_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}"
                                                            Keyboard="Numeric" MaxLength="4"
                                                            Text="{Binding TorqueByGear_Gear1_MaxTorque}"
                                                            VerticalTextAlignment="Center" WidthRequest="100" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear1_MaxTorque, Converter={StaticResource TorqueByGearNoLimitVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NoLimit}" VerticalTextAlignment="Center" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear1_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalTextAlignment="Center" />
                                                    </StackLayout>
                                                </StackLayout>
                                                <Slider Maximum="1001" Minimum="200" Value="{Binding TorqueByGear_Gear1_MaxTorque}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_Gear2_MaxTorque}" />
                                                    <StackLayout Orientation="Horizontal">
                                                        <Entry
                                                            HorizontalOptions="Center"
                                                            HorizontalTextAlignment="End"
                                                            IsTextPredictionEnabled="false"
                                                            IsVisible="{Binding TorqueByGear_Gear2_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}"
                                                            Keyboard="Numeric" MaxLength="4"
                                                            Text="{Binding TorqueByGear_Gear2_MaxTorque}"
                                                            VerticalTextAlignment="Center" WidthRequest="100" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear2_MaxTorque, Converter={StaticResource TorqueByGearNoLimitVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NoLimit}" VerticalTextAlignment="Center" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear2_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalTextAlignment="Center" />
                                                    </StackLayout>
                                                </StackLayout>
                                                <Slider Maximum="1001" Minimum="200" Value="{Binding TorqueByGear_Gear2_MaxTorque}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_Gear3_MaxTorque}" />
                                                    <StackLayout Orientation="Horizontal">
                                                        <Entry
                                                            HorizontalOptions="Center"
                                                            HorizontalTextAlignment="End"
                                                            IsTextPredictionEnabled="false"
                                                            IsVisible="{Binding TorqueByGear_Gear3_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}"
                                                            Keyboard="Numeric" MaxLength="4"
                                                            Text="{Binding TorqueByGear_Gear3_MaxTorque}"
                                                            VerticalTextAlignment="Center" WidthRequest="100" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear3_MaxTorque, Converter={StaticResource TorqueByGearNoLimitVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NoLimit}" VerticalTextAlignment="Center" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear3_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalTextAlignment="Center" />
                                                    </StackLayout>
                                                </StackLayout>
                                                <Slider Maximum="1001" Minimum="200" Value="{Binding TorqueByGear_Gear3_MaxTorque}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_Gear4_MaxTorque}" />
                                                    <StackLayout Orientation="Horizontal">
                                                        <Entry
                                                            HorizontalOptions="Center"
                                                            HorizontalTextAlignment="End"
                                                            IsTextPredictionEnabled="false"
                                                            IsVisible="{Binding TorqueByGear_Gear4_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}"
                                                            Keyboard="Numeric" MaxLength="4"
                                                            Text="{Binding TorqueByGear_Gear4_MaxTorque}"
                                                            VerticalTextAlignment="Center" WidthRequest="100" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear4_MaxTorque, Converter={StaticResource TorqueByGearNoLimitVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NoLimit}" VerticalTextAlignment="Center" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear4_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalTextAlignment="Center" />
                                                    </StackLayout>
                                                </StackLayout>
                                                <Slider Maximum="1001" Minimum="200" Value="{Binding TorqueByGear_Gear4_MaxTorque}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_Gear5_MaxTorque}" />
                                                    <StackLayout Orientation="Horizontal">
                                                        <Entry
                                                            HorizontalOptions="Center"
                                                            HorizontalTextAlignment="End"
                                                            IsTextPredictionEnabled="false"
                                                            IsVisible="{Binding TorqueByGear_Gear5_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}"
                                                            Keyboard="Numeric" MaxLength="4"
                                                            Text="{Binding TorqueByGear_Gear5_MaxTorque}"
                                                            VerticalTextAlignment="Center" WidthRequest="100" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear5_MaxTorque, Converter={StaticResource TorqueByGearNoLimitVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NoLimit}" VerticalTextAlignment="Center" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear5_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalTextAlignment="Center" />
                                                    </StackLayout>
                                                </StackLayout>
                                                <Slider Maximum="1001" Minimum="200" Value="{Binding TorqueByGear_Gear5_MaxTorque}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_Gear6_MaxTorque}" />
                                                    <StackLayout Orientation="Horizontal">
                                                        <Entry
                                                            HorizontalOptions="Center"
                                                            HorizontalTextAlignment="End"
                                                            IsTextPredictionEnabled="false"
                                                            IsVisible="{Binding TorqueByGear_Gear6_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}"
                                                            Keyboard="Numeric" MaxLength="4"
                                                            Text="{Binding TorqueByGear_Gear6_MaxTorque}"
                                                            VerticalTextAlignment="Center" WidthRequest="100" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear6_MaxTorque, Converter={StaticResource TorqueByGearNoLimitVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NoLimit}" VerticalTextAlignment="Center" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear6_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalTextAlignment="Center" />
                                                    </StackLayout>
                                                </StackLayout>
                                                <Slider Maximum="1001" Minimum="200" Value="{Binding TorqueByGear_Gear6_MaxTorque}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_Gear7_MaxTorque}" />
                                                    <StackLayout Orientation="Horizontal">
                                                        <Entry
                                                            HorizontalOptions="Center"
                                                            HorizontalTextAlignment="End"
                                                            IsTextPredictionEnabled="false"
                                                            IsVisible="{Binding TorqueByGear_Gear7_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}"
                                                            Keyboard="Numeric" MaxLength="4"
                                                            Text="{Binding TorqueByGear_Gear7_MaxTorque}"
                                                            VerticalTextAlignment="Center" WidthRequest="100" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear7_MaxTorque, Converter={StaticResource TorqueByGearNoLimitVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NoLimit}" VerticalTextAlignment="Center" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear7_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalTextAlignment="Center" />
                                                    </StackLayout>
                                                </StackLayout>
                                                <Slider Maximum="1001" Minimum="200" Value="{Binding TorqueByGear_Gear7_MaxTorque}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_Gear8_MaxTorque}" />
                                                    <StackLayout Orientation="Horizontal">
                                                        <Entry
                                                            HorizontalOptions="Center"
                                                            HorizontalTextAlignment="End"
                                                            IsTextPredictionEnabled="false"
                                                            IsVisible="{Binding TorqueByGear_Gear8_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}"
                                                            Keyboard="Numeric" MaxLength="4"
                                                            Text="{Binding TorqueByGear_Gear8_MaxTorque}"
                                                            VerticalTextAlignment="Center" WidthRequest="100" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear8_MaxTorque, Converter={StaticResource TorqueByGearNoLimitVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NoLimit}" VerticalTextAlignment="Center" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear8_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalTextAlignment="Center" />
                                                    </StackLayout>
                                                </StackLayout>
                                                <Slider Maximum="1001" Minimum="200" Value="{Binding TorqueByGear_Gear8_MaxTorque}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_Gear9_MaxTorque}" />
                                                    <StackLayout Orientation="Horizontal">
                                                        <Entry
                                                            HorizontalOptions="Center"
                                                            HorizontalTextAlignment="End"
                                                            IsTextPredictionEnabled="false"
                                                            IsVisible="{Binding TorqueByGear_Gear9_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}"
                                                            Keyboard="Numeric" MaxLength="4"
                                                            Text="{Binding TorqueByGear_Gear9_MaxTorque}"
                                                            VerticalTextAlignment="Center" WidthRequest="100" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear9_MaxTorque, Converter={StaticResource TorqueByGearNoLimitVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NoLimit}" VerticalTextAlignment="Center" />
                                                        <Label HorizontalOptions="EndAndExpand" IsVisible="{Binding TorqueByGear_Gear9_MaxTorque, Converter={StaticResource TorqueByGearNoLimitInverseVisibilityConverter}}" Text="{i18n:Translate FlashingCarOptionsPage_TorqueByGear_NmUnit}" VerticalTextAlignment="Center" />
                                                    </StackLayout>
                                                </StackLayout>
                                                <Slider Maximum="1001" Minimum="200" Value="{Binding TorqueByGear_Gear9_MaxTorque}" />
                                            </StackLayout>
                                        </StackLayout>
                                        <!--#endregion-->
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                        <!--#region Power Gauge Max-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding PowerGauge}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_PowerGauge_kW}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding PowerGaugeMax_kW, Converter={StaticResource PowerGauge_kW_ToTextConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="10" Minimum="1" Value="{Binding PowerGaugeMax_kW}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_PowerGauge_hp}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding PowerGaugeMax_hp, Converter={StaticResource PowerGauge_hp_ToTextConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="12" Minimum="1" Value="{Binding PowerGaugeMax_hp}" />
                                        </StackLayout>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion Power Gauge Max-->
                        <!--#region Torque Gauge Max-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding TorqueGauge}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueGauge_Nm}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding TorqueGaugeMax_Nm, Converter={StaticResource TorqueGauge_Nm_ToTextConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="12" Minimum="1" Value="{Binding TorqueGaugeMax_Nm}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueGauge_lbft}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding TorqueGaugeMax_lbft, Converter={StaticResource TorqueGauge_lbft_ToTextConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="10" Minimum="1" Value="{Binding TorqueGaugeMax_lbft}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_TorqueGauge_kgm}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding TorqueGaugeMax_kgm, Converter={StaticResource TorqueGauge_kgm_ToTextConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="4" Minimum="1" Value="{Binding TorqueGaugeMax_kgm}" />
                                        </StackLayout>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion Torque Gauge Max-->
                    </StackLayout>
                    <!--#endregion TORQUE-->
                    <!--#region HARDWARE-->
                    <StackLayout x:Name="Section_Hardware">
                        <StackLayout.IsVisible>
                            <MultiBinding Converter="{StaticResource AnyTrueMultiConverter}">
                                <Binding Converter="{StaticResource InvertedBoolConverter}" Mode="OneWay" Path="IsWizardMode" />
                                <Binding Mode="OneWay" Path="CurrentWizardStep" Converter="{StaticResource EqualToConverter}" ConverterParameter="4" />
                            </MultiBinding>
                        </StackLayout.IsVisible>
                        <Label Text="Hardware" Style="{StaticResource LabelPageTitleStyle}" />
                        <Label
                            Text="Please specify here your car's modifications to get the best out of MG Flasher's software."
                            HorizontalTextAlignment="Center"
                            FontSize="{StaticResource ExtraSmallFontSize}"
                            IsVisible="{Binding IsWizardMode}"
                            Margin="{OnPlatform iOS='0,0,20,0', Default='0,0,0,0'}" />
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding Dcat}" x:Name="DcatRow" />
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding HpfpInstalled}" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=HpfpSelectedPreview}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical" Spacing="20">
                                        <ms:CustomOptionCheckboxRow BindingContext="{Binding HpfpLambdaEnrichment}" />
                                        <ik:RadioButtonGroupView Orientation="Vertical" RadioButtonGroup.GroupName="HPFP" Spacing="10">
                                            <Label
                                                HorizontalOptions="StartAndExpand"
                                                IsVisible="{Binding HpfpAvailableFromOEM}"
                                                Margin="0,0,0,10"
                                                FontSize="{StaticResource PrimaryFontSize}"
                                                Text="{i18n:Translate FlashingCarOptionsPage_Manufacturer_Oem}"
                                                VerticalOptions="Center" VerticalTextAlignment="Center" />
                                            <ik:RadioButton IsVisible="{Binding B58TU_Installed.IsVisible}" Text="{Binding B58TU_Installed.Option}" IsChecked="{Binding B58TU_Installed.IsChecked}" />
                                            <Label
                                                HorizontalOptions="StartAndExpand"
                                                IsVisible="{Binding HpfpAvailableFromSpoolPerformance}"
                                                Margin="0,0,0,10"
                                                FontSize="{StaticResource PrimaryFontSize}"
                                                Text="{i18n:Translate FlashingCarOptionsPage_Manufacturer_SpoolPerformance}"
                                                VerticalOptions="Center" VerticalTextAlignment="Center" />
                                            <ik:RadioButton IsVisible="{Binding SpoolPerformance_Fx150_Installed.IsVisible}" Text="{Binding SpoolPerformance_Fx150_Installed.Option}" IsChecked="{Binding SpoolPerformance_Fx150_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding SpoolPerformance_Fx170_Installed.IsVisible}" Text="{Binding SpoolPerformance_Fx170_Installed.Option}" IsChecked="{Binding SpoolPerformance_Fx170_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding SpoolPerformance_Fx180_Installed.IsVisible}" Text="{Binding SpoolPerformance_Fx180_Installed.Option}" IsChecked="{Binding SpoolPerformance_Fx180_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding SpoolPerformance_Fx200_Installed.IsVisible}" Text="{Binding SpoolPerformance_Fx200_Installed.Option}" IsChecked="{Binding SpoolPerformance_Fx200_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding SpoolPerformance_Fx350_Installed.IsVisible}" Text="{Binding SpoolPerformance_Fx350_Installed.Option}" IsChecked="{Binding SpoolPerformance_Fx350_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding SpoolPerformance_Fx400_Installed.IsVisible}" Text="{Binding SpoolPerformance_Fx400_Installed.Option}" IsChecked="{Binding SpoolPerformance_Fx400_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding SpoolPerformance_Fx400X_Installed.IsVisible}" Text="{Binding SpoolPerformance_Fx400X_Installed.Option}" IsChecked="{Binding SpoolPerformance_Fx400X_Installed.IsChecked}" />
                                            <Label
                                                HorizontalOptions="StartAndExpand"
                                                IsVisible="{Binding HpfpAvailableFromDorchEngineering}"
                                                Margin="0,0,0,10"
                                                FontSize="{StaticResource PrimaryFontSize}"
                                                Text="{i18n:Translate FlashingCarOptionsPage_Manufacturer_DorchEngineering}"
                                                VerticalOptions="Center" VerticalTextAlignment="Center" />
                                            <ik:RadioButton IsVisible="{Binding DorchEngineering_Stage1_DS1_Installed.IsVisible}" Text="{Binding DorchEngineering_Stage1_DS1_Installed.Option}" IsChecked="{Binding DorchEngineering_Stage1_DS1_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding DorchEngineering_Stage15_DS15_Installed.IsVisible}" Text="{Binding DorchEngineering_Stage15_DS15_Installed.Option}" IsChecked="{Binding DorchEngineering_Stage15_DS15_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding DorchEngineering_Stage2_DS2_Installed.IsVisible}" Text="{Binding DorchEngineering_Stage2_DS2_Installed.Option}" IsChecked="{Binding DorchEngineering_Stage2_DS2_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding DorchEngineering_Stage25_DS25_250Bar_Installed.IsVisible}" Text="{Binding DorchEngineering_Stage25_DS25_250Bar_Installed.Option}" IsChecked="{Binding DorchEngineering_Stage25_DS25_250Bar_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding DorchEngineering_Stage25_DS25_350Bar_Installed.IsVisible}" Text="{Binding DorchEngineering_Stage25_DS25_350Bar_Installed.Option}" IsChecked="{Binding DorchEngineering_Stage25_DS25_350Bar_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding DorchEngineering_Stage3_DS3_Installed.IsVisible}" Text="{Binding DorchEngineering_Stage3_DS3_Installed.Option}" IsChecked="{Binding DorchEngineering_Stage3_DS3_Installed.IsChecked}" />
                                            <Label
                                                HorizontalOptions="StartAndExpand"
                                                IsVisible="{Binding HpfpAvailableFromXtremeDI}"
                                                Margin="0,0,0,10"
                                                FontSize="{StaticResource PrimaryFontSize}"
                                                Text="{i18n:Translate FlashingCarOptionsPage_Manufacturer_XtremeDI}"
                                                VerticalOptions="Center" VerticalTextAlignment="Center" />
                                            <ik:RadioButton IsVisible="{Binding XtremeDI_35_Installed.IsVisible}" Text="{Binding XtremeDI_35_Installed.Option}" IsChecked="{Binding XtremeDI_35_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding XtremeDI_60_Installed.IsVisible}" Text="{Binding XtremeDI_60_Installed.Option}" IsChecked="{Binding XtremeDI_60_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding XtremeDI_EVO_Installed.IsVisible}" Text="{Binding XtremeDI_EVO_Installed.Option}" IsChecked="{Binding XtremeDI_EVO_Installed.IsChecked}" />
                                            <Label
                                                HorizontalOptions="StartAndExpand"
                                                IsVisible="{Binding HpfpAvailableFromPrecisionRaceworks}"
                                                Margin="0,0,0,10"
                                                FontSize="{StaticResource PrimaryFontSize}"
                                                Text="{i18n:Translate FlashingCarOptionsPage_Manufacturer_PrecisionRaceworks}"
                                                VerticalOptions="Center" VerticalTextAlignment="Center" />
                                            <ik:RadioButton IsVisible="{Binding PrecisionRaceworks_HPFP_Installed.IsVisible}" Text="{Binding PrecisionRaceworks_HPFP_Installed.Option}" IsChecked="{Binding PrecisionRaceworks_HPFP_Installed.IsChecked}" />
                                        </ik:RadioButtonGroupView>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding InjectorsInstalled}" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=InjectorsSelectedPreview}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical" Spacing="20">
                                        <ik:RadioButtonGroupView Orientation="Vertical" RadioButtonGroup.GroupName="Injectors" Spacing="10">
                                            <Label
                                                HorizontalOptions="StartAndExpand"
                                                IsVisible="{Binding InjectorsAvailableFromSpoolPerformance}"
                                                Margin="0,0,0,10"
                                                FontSize="{StaticResource PrimaryFontSize}"
                                                Text="{i18n:Translate FlashingCarOptionsPage_Manufacturer_SpoolPerformance}"
                                                VerticalOptions="Center" VerticalTextAlignment="Center" />
                                            <ik:RadioButton IsVisible="{Binding SpoolPerformance_Ifx150_Installed.IsVisible}" Text="{Binding SpoolPerformance_Ifx150_Installed.Option}" IsChecked="{Binding SpoolPerformance_Ifx150_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding SpoolPerformance_Ifx350_Installed.IsVisible}" Text="{Binding SpoolPerformance_Ifx350_Installed.Option}" IsChecked="{Binding SpoolPerformance_Ifx350_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding SpoolPerformance_Ifx350X_Installed.IsVisible}" Text="{Binding SpoolPerformance_Ifx350X_Installed.Option}" IsChecked="{Binding SpoolPerformance_Ifx350X_Installed.IsChecked}" />
                                            <Label
                                                HorizontalOptions="StartAndExpand"
                                                IsVisible="{Binding InjectorsAvailableFromXtremeDI}"
                                                Margin="0,0,0,10"
                                                FontSize="{StaticResource PrimaryFontSize}"
                                                Text="{i18n:Translate FlashingCarOptionsPage_Manufacturer_XtremeDI}"
                                                VerticalOptions="Center" VerticalTextAlignment="Center" />
                                            <ik:RadioButton IsVisible="{Binding XtremeDI_40_Installed.IsVisible}" Text="{Binding XtremeDI_40_Installed.Option}" IsChecked="{Binding XtremeDI_40_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding XtremeDI_75_Installed.IsVisible}" Text="{Binding XtremeDI_75_Installed.Option}" IsChecked="{Binding XtremeDI_75_Installed.IsChecked}" />
                                            <Label
                                                HorizontalOptions="StartAndExpand"
                                                IsVisible="{Binding InjectorsAvailableFromNostrum}"
                                                Margin="0,0,0,10"
                                                FontSize="{StaticResource PrimaryFontSize}"
                                                Text="{i18n:Translate FlashingCarOptionsPage_Manufacturer_Nostrum}"
                                                VerticalOptions="Center" VerticalTextAlignment="Center" />
                                            <ik:RadioButton IsVisible="{Binding Nostrum_Stage1_Installed.IsVisible}" Text="{Binding Nostrum_Stage1_Installed.Option}" IsChecked="{Binding Nostrum_Stage1_Installed.IsChecked}" />
                                            <ik:RadioButton IsVisible="{Binding Nostrum_Stage2_Installed.IsVisible}" Text="{Binding Nostrum_Stage2_Installed.Option}" IsChecked="{Binding Nostrum_Stage2_Installed.IsChecked}" />
                                        </ik:RadioButtonGroupView>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding IntakeInstalled}" />
                        <!--#region LSD Installed-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding LsdInstalled}" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=AftermarketLSDPreview}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Horizontal">
                                        <StackLayout Orientation="Vertical" HorizontalOptions="Start">
                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_LsdInstalledFinalDrive_RearDifferential}" />
                                            <Label Text="{i18n:Translate FlashingCarOptionsPage_LsdInstalledFinalDrive_FinalDriveRatio}" />
                                        </StackLayout>
                                        <Entry
                                            VerticalOptions="Start"
                                            HorizontalOptions="EndAndExpand" Keyboard="Numeric"
                                            MaxLength="5"
                                            Text="{Binding LsdInstalledFinalDrive}">
                                            <Entry.Behaviors>
                                                <behaviors:DoubleValidatorBehavior Min="0" Max="32" />
                                            </Entry.Behaviors>
                                        </Entry>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                        <!--#region MotivReflex Installed-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding MotivReflexInstalledCustomizable}" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=MotivReflexPreview}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical" Spacing="20">
                                        <StackLayout Orientation="Horizontal" IsVisible="{Binding MotivReflexInstalledCustomizable.IsChecked}">
                                            <Label HorizontalOptions="StartAndExpand" HorizontalTextAlignment="Center" Text="{i18n:Translate FlashingCarOptionsPage_MotivReflexCanBusIdOutput}" VerticalTextAlignment="Center" />
                                            <Picker
                                                BackgroundColor="#88000000" HorizontalOptions="End"
                                                HorizontalTextAlignment="Center"
                                                ItemsSource="{Binding MotivReflexCanBusIdOutputList}"
                                                SelectedItem="{Binding MotivReflexCanBusIdOutput}"
                                                VerticalTextAlignment="Center" WidthRequest="100"
                                                x:Name="MotivReflexCanBusIdOutput_Picker" />
                                        </StackLayout>
                                        <ms:CustomOptionCheckboxRow BindingContext="{Binding MotivReflexDeepIntegration}" />
                                        <StackLayout Orientation="Horizontal" IsVisible="{Binding MotivReflexDeepIntegration.IsChecked}">
                                            <Label HorizontalOptions="StartAndExpand" HorizontalTextAlignment="Center" Text="{i18n:Translate FlashingCarOptionsPage_MotivReflexCanBusIdInput}" VerticalTextAlignment="Center" />
                                            <Picker
                                                BackgroundColor="#88000000" HorizontalOptions="End"
                                                HorizontalTextAlignment="Center"
                                                ItemsSource="{Binding MotivReflexCanBusIdInputList}"
                                                SelectedItem="{Binding MotivReflexCanBusIdInput}"
                                                VerticalTextAlignment="Center" WidthRequest="100"
                                                x:Name="MotivReflexCanBusIdInput_Picker" />
                                        </StackLayout>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding LowPressureFuelPressureSensorInstalled}" />
                    </StackLayout>
                    <!--#endregion HARDWARE-->
                    <!--#region OTHER SETTINGS-->
                    <StackLayout x:Name="Section_Other">
                        <StackLayout.IsVisible>
                            <MultiBinding Converter="{StaticResource AnyTrueMultiConverter}">
                                <Binding Converter="{StaticResource InvertedBoolConverter}" Mode="OneWay" Path="IsWizardMode" />
                                <Binding Mode="OneWay" Path="CurrentWizardStep" Converter="{StaticResource EqualToConverter}" ConverterParameter="5" />
                            </MultiBinding>
                        </StackLayout.IsVisible>
                        <Label Text="Other settings" Style="{StaticResource LabelPageTitleStyle}" />
                        <Label
                            Text="Here you can configure other settings for your engine."
                            HorizontalTextAlignment="Center"
                            FontSize="{StaticResource ExtraSmallFontSize}"
                            IsVisible="{Binding IsWizardMode}"
                            Margin="{OnPlatform iOS='0,0,20,0', Default='0,0,0,0'}" />
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding Vmax}" />
                        <!--#region Idle RPM-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding IdleRpm}" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=IdleRPMPreview}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <StackLayout Orientation="Horizontal">
                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_IdleRpmTargetBase}" />
                                            <Label HorizontalOptions="EndAndExpand" Text="{Binding IdleRpmTargetBase, Converter={StaticResource IdleRpmTargetConverter}}" />
                                        </StackLayout>
                                        <Slider Maximum="150" Minimum="60" Value="{Binding IdleRpmTargetBase}" />
                                        <ms:CustomOptionCheckboxRow BindingContext="{Binding IdleRpm6mt}" />
                                        <StackLayout Orientation="Vertical" BackgroundColor="#22000000">
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding IdleRpm6mt.IsChecked}" Margin="10">
                                                <StackLayout Orientation="Vertical" IsVisible="{Binding IdleRpm6mt.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_IdleRpmTargetClutch}" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding IdleRpmTargetClutch, Converter={StaticResource IdleRpmTargetClutchConverter}}" />
                                                    </StackLayout>
                                                    <Slider Maximum="150" Minimum="60" Value="{Binding IdleRpmTargetClutch}" />
                                                </StackLayout>
                                                <StackLayout Orientation="Vertical" IsVisible="{Binding IdleRpm6mt.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_IdleRpmTargetClutchDelay}" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding IdleRpmTargetClutchDelay, Converter={StaticResource IdleRpmTargetClutchDelayConverter}}" />
                                                    </StackLayout>
                                                    <Slider Maximum="30" Minimum="1" Value="{Binding IdleRpmTargetClutchDelay}" />
                                                </StackLayout>
                                                <ms:CustomOptionCheckboxRow BindingContext="{Binding IdleRpmTargetClutchFast}" />
                                            </StackLayout>
                                        </StackLayout>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding InductionNoise}" />
                    </StackLayout>
                    <!--#endregion OTHER SETTINGS-->
                    <!--#region CUSTOM CODE-->
                    <StackLayout x:Name="Section_CustomCode">
                        <StackLayout.IsVisible>
                            <MultiBinding Converter="{StaticResource AnyTrueMultiConverter}">
                                <Binding Converter="{StaticResource InvertedBoolConverter}" Mode="OneWay" Path="IsWizardMode" />
                                <Binding Mode="OneWay" Path="CurrentWizardStep" Converter="{StaticResource EqualToConverter}" ConverterParameter="6" />
                            </MultiBinding>
                        </StackLayout.IsVisible>
                        <Label Text="Custom Code" Style="{StaticResource LabelPageTitleStyle}" />
                        <Label
                            Text="If you want to take advantage of all the features of MG Flasher's software, enable special options here - for some of them you need to enable Custom Code Menu in the CC ADVANCED section."
                            HorizontalTextAlignment="Center"
                            FontSize="{StaticResource ExtraSmallFontSize}"
                            IsVisible="{Binding IsWizardMode}"
                            Margin="{OnPlatform iOS='0,0,20,0', Default='0,0,0,0'}" />
                        <!--#region Antilag-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding Antilag}" x:Name="AntilagRow">
                            <ms:CustomOptionCheckboxRow.IsVisible>
                                <MultiBinding Converter="{StaticResource AllTrueMultiConverter}">
                                    <Binding Mode="OneWay" Path="BindingContext.IsVisible" Source="{x:Reference AntilagRow}" />
                                    <Binding Mode="OneWay" Path="BindingContext.IsChecked" Source="{x:Reference DcatRow}" />
                                </MultiBinding>
                            </ms:CustomOptionCheckboxRow.IsVisible>
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <ms:CustomOptionCheckboxRow BindingContext="{Binding AntilagExpertOptions}" />
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagExpertOptions.IsChecked}">
                                            <ms:CustomOptionCheckboxRow BindingContext="{Binding AntilagDevOptions}" Margin="10,0,10,0" />
                                            <ms:CustomOptionCheckboxRow BindingContext="{Binding AntilagUseIgnitionCut}" Margin="10,0,10,0" />
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagUseIgnitionCut.IsChecked, Converter={StaticResource InvertedBoolConverter}}">
                                                <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10" IsVisible="{Binding CanAdjustAntilagTargetNumberOfCylindersToSuppress}">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagTargetNumberOfCylindersToSuppress}" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagTargetNumberOfCylindersToSuppress, Converter={StaticResource AntilagTargetNumberOfCylindersToSuppressConverter}}" />
                                                    </StackLayout>
                                                    <Slider Maximum="{Binding NumberOfCylindersInEngine}" Minimum="0" Value="{Binding AntilagTargetNumberOfCylindersToSuppress}" />
                                                </StackLayout>
                                            </StackLayout>
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagPedal}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagPedal, Converter={StaticResource AntilagPedalConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="95" Minimum="1" Value="{Binding AntilagPedal}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagRollingSpeedThreshold}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagRollingSpeedThreshold, Converter={StaticResource AntilagRollingSpeedThresholdConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="50" Minimum="0" Value="{Binding AntilagRollingSpeedThreshold}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagActivationDelay}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagActivationDelay, Converter={StaticResource AntilagTimeConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="20" Minimum="1" Value="{Binding AntilagActivationDelay}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagMaxDuration}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagMaxDuration, Converter={StaticResource AntilagTimeConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="70" Minimum="5" Value="{Binding AntilagMaxDuration}" />
                                        </StackLayout>
                                        <Label
                                            FontSize="{StaticResource PrimaryFontSize}"
                                            HorizontalOptions="CenterAndExpand"
                                            HorizontalTextAlignment="Center" LineBreakMode="NoWrap"
                                            Text="{i18n:Translate FlashingCarOptionsPage_AntilagRollingSettings}" />
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagExpertOptions.IsChecked}">
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagDevOptions.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Vertical">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagIgnRoll}" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagFlatIgnRoll, Converter={StaticResource AntilagFlatIgnConverter}}" />
                                                    </StackLayout>
                                                    <Slider Maximum="5.0" Minimum="-40.0" Value="{Binding AntilagFlatIgnRoll}" />
                                                </StackLayout>
                                                <StackLayout Orientation="Vertical">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagLambdaRoll}" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagFlatLambdaRoll, Converter={StaticResource AntilagFlatLambdaConverter}}" />
                                                    </StackLayout>
                                                    <Slider Maximum="1.1" Minimum="0.7" Value="{Binding AntilagFlatLambdaRoll}" />
                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagDevOptions.IsChecked, Converter={StaticResource InvertedBoolConverter}}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Vertical">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagIgnRoll}" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagCurveIgnRoll, Converter={StaticResource AntilagPercentageConverter}}" />
                                                    </StackLayout>
                                                    <Slider Maximum="100" Minimum="0" Value="{Binding AntilagCurveIgnRoll}" />
                                                </StackLayout>
                                                <StackLayout Orientation="Vertical">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagLambdaRoll}" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagCurveLambdaRoll, Converter={StaticResource AntilagPercentageConverter}}" />
                                                    </StackLayout>
                                                    <Slider Maximum="100" Minimum="0" Value="{Binding AntilagCurveLambdaRoll}" />
                                                </StackLayout>
                                            </StackLayout>
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagExpertOptions.IsChecked, Converter={StaticResource InvertedBoolConverter}}" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagAggressionRoll}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagAggressionRoll, Converter={StaticResource AntilagPercentageConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="100" Minimum="0" Value="{Binding AntilagAggressionRoll}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagTargetBoostRoll}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagTargetBoostRoll, Converter={StaticResource AntilagBoostConverter}}" />
                                            </StackLayout>
                                            <Slider IsVisible="{Binding AntilagExpertOptions.IsChecked}" Maximum="58" Minimum="28" Value="{Binding AntilagTargetBoostRoll}" />
                                            <Slider IsVisible="{Binding AntilagExpertOptions.IsChecked, Converter={StaticResource InvertedBoolConverter}}" Maximum="46" Minimum="28" Value="{Binding AntilagTargetBoostRoll}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding CanAdjustAntilagRpmDiff}">
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagExpertOptions.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagRpmDiffRoll}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagRpmDiffRoll, Converter={StaticResource AntilagRpmHystConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="450" Minimum="150" Value="{Binding AntilagRpmDiffRoll}" />
                                            </StackLayout>
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagRollRpmMin}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagRollRpmMin, Converter={StaticResource AntilagRpmConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="60" Minimum="15" Value="{Binding AntilagRollRpmMin}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagRollRpmMax}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagRollRpmMax, Converter={StaticResource AntilagRpmConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="70" Minimum="20" Value="{Binding AntilagRollRpmMax}" />
                                        </StackLayout>
                                        <Label
                                            FontSize="{StaticResource PrimaryFontSize}"
                                            HorizontalOptions="CenterAndExpand"
                                            HorizontalTextAlignment="Center" LineBreakMode="NoWrap"
                                            Text="{i18n:Translate FlashingCarOptionsPage_AntilagStandingSettings}" />
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagExpertOptions.IsChecked}">
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagDevOptions.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Vertical">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagIgnStand}" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagFlatIgnStand, Converter={StaticResource AntilagFlatIgnConverter}}" />
                                                    </StackLayout>
                                                    <Slider Maximum="5.0" Minimum="-40.0" Value="{Binding AntilagFlatIgnStand}" />
                                                </StackLayout>
                                                <StackLayout Orientation="Vertical">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagLambdaStand}" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagFlatLambdaStand, Converter={StaticResource AntilagFlatLambdaConverter}}" />
                                                    </StackLayout>
                                                    <Slider Maximum="1.1" Minimum="0.7" Value="{Binding AntilagFlatLambdaStand}" />
                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagDevOptions.IsChecked, Converter={StaticResource InvertedBoolConverter}}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Vertical">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagIgnStand}" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagCurveIgnStand, Converter={StaticResource AntilagPercentageConverter}}" />
                                                    </StackLayout>
                                                    <Slider Maximum="100" Minimum="0" Value="{Binding AntilagCurveIgnStand}" />
                                                </StackLayout>
                                                <StackLayout Orientation="Vertical">
                                                    <StackLayout Orientation="Horizontal">
                                                        <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagLambdaStand}" />
                                                        <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagCurveLambdaStand, Converter={StaticResource AntilagPercentageConverter}}" />
                                                    </StackLayout>
                                                    <Slider Maximum="100" Minimum="0" Value="{Binding AntilagCurveLambdaStand}" />
                                                </StackLayout>
                                            </StackLayout>
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagExpertOptions.IsChecked, Converter={StaticResource InvertedBoolConverter}}">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagAggressionStand}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagAggressionStand, Converter={StaticResource AntilagPercentageConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="100" Minimum="0" Value="{Binding AntilagAggressionStand}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagTargetBoostStand}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagTargetBoostStand, Converter={StaticResource AntilagBoostConverter}}" />
                                            </StackLayout>
                                            <Slider IsVisible="{Binding AntilagExpertOptions.IsChecked}" Maximum="58" Minimum="28" Value="{Binding AntilagTargetBoostStand}" />
                                            <Slider IsVisible="{Binding AntilagExpertOptions.IsChecked, Converter={StaticResource InvertedBoolConverter}}" Maximum="46" Minimum="28" Value="{Binding AntilagTargetBoostStand}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding CanAdjustAntilagRpmDiff}">
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagExpertOptions.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagRpmDiffStand}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagRpmDiffStand, Converter={StaticResource AntilagRpmHystConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="450" Minimum="150" Value="{Binding AntilagRpmDiffStand}" />
                                            </StackLayout>
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagRpmSetpoint}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagRpmSetpoint, Converter={StaticResource AntilagRpmConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="70" Minimum="15" Value="{Binding AntilagRpmSetpoint}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagExpertOptions.IsChecked}">
                                            <Label
                                                FontSize="{StaticResource PrimaryFontSize}"
                                                HorizontalOptions="CenterAndExpand"
                                                HorizontalTextAlignment="Center" LineBreakMode="NoWrap"
                                                Text="{i18n:Translate FlashingCarOptionsPage_AntilagSafetySettings}" />
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagDevOptions.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagSafetyTorqueCap}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagFlatSafetyTorqueCap, Converter={StaticResource AntilagSafetyTorqueCapConverter}}" />
                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding AntilagDevOptions.IsChecked, Converter={StaticResource InvertedBoolConverter}}" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagSafetyTorqueCap}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagCurveSafetyTorqueCap, Converter={StaticResource AntilagPercentageConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="100" Minimum="0" Value="{Binding AntilagCurveSafetyTorqueCap}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagCoolantTempMin}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagCoolantTempMin, Converter={StaticResource AntilagTempConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="140" Minimum="40" Value="{Binding AntilagCoolantTempMin}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagCoolantTempMax}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagCoolantTempMax, Converter={StaticResource AntilagTempConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="150" Minimum="50" Value="{Binding AntilagCoolantTempMax}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagEgtTempMax}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagEgtTempMax, Converter={StaticResource AntilagEgtConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="55" Minimum="8" Value="{Binding AntilagEgtTempMax}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagIatTempMax}" />
                                                    <Label HorizontalOptions="EndAndExpand" Text="{Binding AntilagIatTempMax, Converter={StaticResource AntilagTempConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="110" Minimum="10" Value="{Binding AntilagIatTempMax}" />
                                            </StackLayout>
                                            <StackLayout Orientation="Vertical" Margin="0,0,0,20" Spacing="10">
                                                <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                                    <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_AntilagOverBoostThreshold}" />
                                                    <Label HorizontalOptions="End" Text="{Binding AntilagOverBoostThreshold, Converter={StaticResource AntilagOverboostConverter}}" />
                                                </StackLayout>
                                                <Slider Maximum="500" Minimum="10" Value="{Binding AntilagOverBoostThreshold}" />
                                            </StackLayout>
                                        </StackLayout>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <HorizontalStackLayout>
                            <Label HorizontalOptions="StartAndExpand" VerticalOptions="Center" Margin="20,0,0,10" Text="{i18n:Translate FlashingCarOptionsPage_AntilagRequireDecat}">
                                <Label.IsVisible>
                                    <MultiBinding Converter="{StaticResource AllTrueMultiConverter}">
                                        <Binding Mode="OneWay" Path="Antilag.IsVisible" />
                                        <Binding Converter="{StaticResource InvertedBoolConverter}" Mode="OneWay" Path="Dcat.IsChecked" />
                                    </MultiBinding>
                                </Label.IsVisible>
                            </Label>
                            <ms:DecatRequiredLabel />
                        </HorizontalStackLayout>
                        <!--#endregion-->
                        <!--#region Flex fuel-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding EthanolSupport}" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=EthanolPreview}">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <ms:CustomOptionCheckboxRow BindingContext="{Binding FlexFuelSensorInstalled}" />
                                        <StackLayout Orientation="Horizontal" IsVisible="{Binding FlexFuelSensorInstalled.IsChecked}" Margin="0,0,0,20">
                                            <Label HorizontalOptions="StartAndExpand" HorizontalTextAlignment="Center" Text="{i18n:Translate FlashingCarOptionsPage_FlexFuelSensorModel}" VerticalTextAlignment="Center" />
                                            <Picker
                                                BackgroundColor="#88000000" HorizontalOptions="End"
                                                HorizontalTextAlignment="Center"
                                                ItemsSource="{Binding FlexFuelSensorModelList}"
                                                SelectedItem="{Binding FlexFuelSensorModel}"
                                                VerticalTextAlignment="Center" WidthRequest="270"
                                                x:Name="FlexFuelSensorModel_Picker" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding FlexFuelDelayToShowOnDashAllowed}" Margin="0,0,0,20">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_FlexFuelDelayToShowOnDash}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding FlexFuelDelayToShowOnDash, Converter={StaticResource FlexFuelTimeConverter}}" />
                                            </StackLayout>
                                            <Slider Maximum="50" Minimum="1" Value="{Binding FlexFuelDelayToShowOnDash}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding FlexFuelEthanolContentAllowed}" Margin="10">
                                            <ms:CustomOptionCheckboxRow BindingContext="{Binding FlexFuelBlending}" />
                                            <Label HorizontalOptions="Center" IsVisible="{Binding FlexFuelSensorInstalled.IsChecked}" Margin="0,0,0,20" Text="{i18n:Translate FlashingCarOptionsPage_UnmarkSensorInstalledForEthanolContent}" />
                                            <StackLayout Orientation="Vertical" IsVisible="{Binding FlexFuelSensorInstalled.IsChecked, Converter={StaticResource InvertedBoolConverter}}">
                                                <ms:CustomOptionCheckboxRow BindingContext="{Binding FlexFuelEthanolContent}" />
                                                <StackLayout Orientation="Vertical" Margin="20,0,20,0">
                                                    <StackLayout Orientation="Vertical" IsVisible="{Binding FlexFuelEthanolContent.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                        <StackLayout Orientation="Horizontal">
                                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_FlexFuelEthanolContentSlot0}" />
                                                            <StackLayout Orientation="Horizontal">
                                                                <Entry
                                                                    HorizontalOptions="Center"
                                                                    HorizontalTextAlignment="End"
                                                                    IsTextPredictionEnabled="false" Keyboard="Numeric"
                                                                    MaxLength="4"
                                                                    Text="{Binding FlexFuelEthanolContentSlot0}"
                                                                    VerticalTextAlignment="Center" WidthRequest="100" />
                                                                <Label HorizontalOptions="EndAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_FlexFuelEthanolContentSlot_PercentageUnit}" VerticalTextAlignment="Center" />
                                                            </StackLayout>
                                                        </StackLayout>
                                                        <Slider Maximum="100" Minimum="0" Value="{Binding FlexFuelEthanolContentSlot0}" />
                                                    </StackLayout>
                                                    <StackLayout Orientation="Vertical" IsVisible="{Binding FlexFuelEthanolContent.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                        <StackLayout Orientation="Horizontal">
                                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_FlexFuelEthanolContentSlot1}" />
                                                            <StackLayout Orientation="Horizontal">
                                                                <Entry
                                                                    HorizontalOptions="Center"
                                                                    HorizontalTextAlignment="End"
                                                                    IsTextPredictionEnabled="false" Keyboard="Numeric"
                                                                    MaxLength="4"
                                                                    Text="{Binding FlexFuelEthanolContentSlot1}"
                                                                    VerticalTextAlignment="Center" WidthRequest="100" />
                                                                <Label HorizontalOptions="EndAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_FlexFuelEthanolContentSlot_PercentageUnit}" VerticalTextAlignment="Center" />
                                                            </StackLayout>
                                                        </StackLayout>
                                                        <Slider Maximum="100" Minimum="0" Value="{Binding FlexFuelEthanolContentSlot1}" />
                                                    </StackLayout>
                                                    <StackLayout Orientation="Vertical" IsVisible="{Binding FlexFuelEthanolContent.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                        <StackLayout Orientation="Horizontal">
                                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_FlexFuelEthanolContentSlot2}" />
                                                            <StackLayout Orientation="Horizontal">
                                                                <Entry
                                                                    HorizontalOptions="Center"
                                                                    HorizontalTextAlignment="End"
                                                                    IsTextPredictionEnabled="false" Keyboard="Numeric"
                                                                    MaxLength="4"
                                                                    Text="{Binding FlexFuelEthanolContentSlot2}"
                                                                    VerticalTextAlignment="Center" WidthRequest="100" />
                                                                <Label HorizontalOptions="EndAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_FlexFuelEthanolContentSlot_PercentageUnit}" VerticalTextAlignment="Center" />
                                                            </StackLayout>
                                                        </StackLayout>
                                                        <Slider Maximum="100" Minimum="0" Value="{Binding FlexFuelEthanolContentSlot2}" />
                                                    </StackLayout>
                                                    <StackLayout Orientation="Vertical" IsVisible="{Binding FlexFuelEthanolContent.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                        <StackLayout Orientation="Horizontal">
                                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_FlexFuelEthanolContentSlot3}" />
                                                            <StackLayout Orientation="Horizontal">
                                                                <Entry
                                                                    HorizontalOptions="Center"
                                                                    HorizontalTextAlignment="End"
                                                                    IsTextPredictionEnabled="false" Keyboard="Numeric"
                                                                    MaxLength="4"
                                                                    Text="{Binding FlexFuelEthanolContentSlot3}"
                                                                    VerticalTextAlignment="Center" WidthRequest="100" />
                                                                <Label HorizontalOptions="EndAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_FlexFuelEthanolContentSlot_PercentageUnit}" VerticalTextAlignment="Center" />
                                                            </StackLayout>
                                                        </StackLayout>
                                                        <Slider Maximum="100" Minimum="0" Value="{Binding FlexFuelEthanolContentSlot3}" />
                                                    </StackLayout>
                                                    <StackLayout Orientation="Vertical" IsVisible="{Binding FlexFuelEthanolContent.IsChecked}" Margin="0,0,0,20" Spacing="10">
                                                        <StackLayout Orientation="Horizontal">
                                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_FlexFuelEthanolContentSlot4}" />
                                                            <StackLayout Orientation="Horizontal">
                                                                <Entry
                                                                    HorizontalOptions="Center"
                                                                    HorizontalTextAlignment="End"
                                                                    IsTextPredictionEnabled="false" Keyboard="Numeric"
                                                                    MaxLength="4"
                                                                    Text="{Binding FlexFuelEthanolContentSlot4}"
                                                                    VerticalTextAlignment="Center" WidthRequest="100" />
                                                                <Label HorizontalOptions="EndAndExpand" Text="{i18n:Translate FlashingCarOptionsPage_FlexFuelEthanolContentSlot_PercentageUnit}" VerticalTextAlignment="Center" />
                                                            </StackLayout>
                                                        </StackLayout>
                                                        <Slider Maximum="100" Minimum="0" Value="{Binding FlexFuelEthanolContentSlot4}" />
                                                    </StackLayout>
                                                </StackLayout>
                                            </StackLayout>
                                        </StackLayout>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                        <!--#region Exhaust Flap On-The-Fly-->
                        <Label Margin="20,0,0,10" Text="{i18n:Translate FlashingCarOptionsPage_MarkCcmForExhaustFlapOnTheFly}">
                            <Label.IsVisible>
                                <MultiBinding Converter="{StaticResource AllTrueMultiConverter}">
                                    <Binding Mode="OneWay" Path="CustomCodeMenu.IsVisible" />
                                    <Binding Converter="{StaticResource InvertedBoolConverter}" Mode="OneWay" Path="CustomCodeMenu.IsChecked" />
                                    <Binding Mode="OneWay" Path="ExhaustFlapOnTheFly.IsVisible" />
                                </MultiBinding>
                            </Label.IsVisible>
                        </Label>
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding ExhaustFlapOnTheFly}" x:Name="ExhaustFlapOTFRow" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=ExhaustFlapOTFPreview}">
                            <ms:CustomOptionCheckboxRow.IsVisible>
                                <MultiBinding Converter="{StaticResource AllTrueMultiConverter}">
                                    <Binding Mode="OneWay" Path="BindingContext.IsVisible" Source="{x:Reference ExhaustFlapOTFRow}" />
                                    <Binding Mode="OneWay" Path="BindingContext.IsVisible" Source="{x:Reference CustomCodeMenuRow}" />
                                    <Binding Mode="OneWay" Path="BindingContext.IsChecked" Source="{x:Reference CustomCodeMenuRow}" />
                                </MultiBinding>
                            </ms:CustomOptionCheckboxRow.IsVisible>
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <StackLayout Orientation="Horizontal">
                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ExhaustFlapOnTheFlyMaxLoad}" />
                                            <Label HorizontalOptions="EndAndExpand" Text="{Binding ExhaustFlapOnTheFlyMaxLoad, Converter={StaticResource ExhaustFlapOnTheFlyLoadConverter}}" />
                                        </StackLayout>
                                        <Label FontSize="{StaticResource ExtraSmallFontSize}" HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ExhaustFlapOnTheFlyMaxLoad_Info}" />
                                        <Slider Maximum="30" Minimum="0" Value="{Binding ExhaustFlapOnTheFlyMaxLoad}" />
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                        <!--#region Radiator Flap On-The-Fly-->
                        <Label Margin="20,0,0,10" Text="{i18n:Translate FlashingCarOptionsPage_MarkCcmForRadiatorFlapOnTheFly}">
                            <Label.IsVisible>
                                <MultiBinding Converter="{StaticResource AllTrueMultiConverter}">
                                    <Binding Mode="OneWay" Path="CustomCodeMenu.IsVisible" />
                                    <Binding Converter="{StaticResource InvertedBoolConverter}" Mode="OneWay" Path="CustomCodeMenu.IsChecked" />
                                    <Binding Mode="OneWay" Path="RadiatorFlapOnTheFly.IsVisible" />
                                </MultiBinding>
                            </Label.IsVisible>
                        </Label>
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding RadiatorFlapOnTheFly}" x:Name="RadiatorOTFRow">
                            <ms:CustomOptionCheckboxRow.IsVisible>
                                <MultiBinding Converter="{StaticResource AllTrueMultiConverter}">
                                    <Binding Mode="OneWay" Path="BindingContext.IsVisible" Source="{x:Reference RadiatorOTFRow}" />
                                    <Binding Mode="OneWay" Path="BindingContext.IsVisible" Source="{x:Reference CustomCodeMenuRow}" />
                                    <Binding Mode="OneWay" Path="BindingContext.IsChecked" Source="{x:Reference CustomCodeMenuRow}" />
                                </MultiBinding>
                            </ms:CustomOptionCheckboxRow.IsVisible>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding SwitchableMaps}" />
                        <!--#region Valet Mode-->
                        <Label Margin="20,0,0,10" Text="{i18n:Translate FlashingCarOptionsPage_MarkCcmForValetMode}">
                            <Label.IsVisible>
                                <MultiBinding Converter="{StaticResource AllTrueMultiConverter}">
                                    <Binding Mode="OneWay" Path="CustomCodeMenu.IsVisible" />
                                    <Binding Converter="{StaticResource InvertedBoolConverter}" Mode="OneWay" Path="CustomCodeMenu.IsChecked" />
                                    <Binding Mode="OneWay" Path="ValetMode.IsVisible" />
                                </MultiBinding>
                            </Label.IsVisible>
                        </Label>
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding ValetMode}" x:Name="ValetModeRow">
                            <ms:CustomOptionCheckboxRow.IsVisible>
                                <MultiBinding Converter="{StaticResource AllTrueMultiConverter}">
                                    <Binding Mode="OneWay" Path="BindingContext.IsVisible" Source="{x:Reference ValetModeRow}" />
                                    <Binding Mode="OneWay" Path="BindingContext.IsVisible" Source="{x:Reference CustomCodeMenuRow}" />
                                    <Binding Mode="OneWay" Path="BindingContext.IsChecked" Source="{x:Reference CustomCodeMenuRow}" />
                                </MultiBinding>
                            </ms:CustomOptionCheckboxRow.IsVisible>
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <StackLayout Orientation="Vertical">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ValetMaxPedal}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding ValetMaxPedal, Converter={StaticResource ValetMaxPedalConverter}}" />
                                            </StackLayout>
                                            <Label FontSize="{StaticResource ExtraSmallFontSize}" HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ValetMaxPedal_Info}" />
                                            <Slider Maximum="20" Minimum="0" Value="{Binding ValetMaxPedal}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ValetMaxEngineSpeed}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding ValetMaxEngineSpeed, Converter={StaticResource ValetMaxEngineSpeedConverter}}" />
                                            </StackLayout>
                                            <Label FontSize="{StaticResource ExtraSmallFontSize}" HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ValetMaxEngineSpeed_Info}" />
                                            <Slider Maximum="60" Minimum="0" Value="{Binding ValetMaxEngineSpeed}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ValetMaxVehicleSpeed}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding ValetMaxVehicleSpeed, Converter={StaticResource ValetMaxVehicleSpeedConverter}}" />
                                            </StackLayout>
                                            <Label FontSize="{StaticResource ExtraSmallFontSize}" HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ValetMaxVehicleSpeed_Info}" />
                                            <Slider Maximum="29" Minimum="0" Value="{Binding ValetMaxVehicleSpeed}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ValetRegularTorqueLevel}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding ValetRegularTorqueLevel, Converter={StaticResource ValetTorqueLevelConverter}}" />
                                            </StackLayout>
                                            <Label FontSize="{StaticResource ExtraSmallFontSize}" HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ValetRegularTorqueLevel_Info}" />
                                            <Slider Maximum="20" Minimum="0" Value="{Binding ValetRegularTorqueLevel}" />
                                        </StackLayout>
                                        <StackLayout Orientation="Vertical">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ValetRestrictiveTorqueLevel}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding ValetRestrictiveTorqueLevel, Converter={StaticResource ValetTorqueLevelConverter}}" />
                                            </StackLayout>
                                            <Label FontSize="{StaticResource ExtraSmallFontSize}" HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_ValetRestrictiveTorqueLevel_Info}" />
                                            <Slider Maximum="20" Minimum="0" Value="{Binding ValetRestrictiveTorqueLevel}" />
                                        </StackLayout>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                    </StackLayout>
                    <!--#endregion CUSTOM CODE-->
                    <!--#region CUSTOM CODE ADVANCED-->
                    <StackLayout x:Name="Section_CustomCodeAdvanced">
                        <StackLayout.IsVisible>
                            <MultiBinding Converter="{StaticResource AnyTrueMultiConverter}">
                                <Binding Converter="{StaticResource InvertedBoolConverter}" Mode="OneWay" Path="IsWizardMode" />
                                <Binding Mode="OneWay" Path="CurrentWizardStep" Converter="{StaticResource EqualToConverter}" ConverterParameter="7" />
                            </MultiBinding>
                        </StackLayout.IsVisible>
                        <Label Text="Custom Code Advanced" Style="{StaticResource LabelPageTitleStyle}" />
                        <Label
                            Text="Enable CCM to modify your car's tuning without flashing. Enable Limp Mode for better protection."
                            HorizontalTextAlignment="Center"
                            FontSize="{StaticResource ExtraSmallFontSize}"
                            IsVisible="{Binding IsWizardMode}"
                            Margin="{OnPlatform iOS='0,0,20,0', Default='0,0,0,0'}" />
                        <!--#region Custom code menu-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding CustomCodeMenu}" x:Name="CustomCodeMenuRow">
                            <ms:CustomOptionCheckboxRow.ItemTemplate>
                                <DataTemplate x:DataType="vms:FlashingCarOptionsPageViewModel">
                                    <StackLayout Orientation="Vertical">
                                        <StackLayout Orientation="Horizontal">
                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_CustomCodeMenuDelayToEnter}" />
                                            <Label HorizontalOptions="EndAndExpand" Text="{Binding CustomCodeMenuDelayToEnter, Converter={StaticResource CustomCodeMenuTime1Converter}}" />
                                        </StackLayout>
                                        <Label FontSize="{StaticResource ExtraSmallFontSize}" HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_CustomCodeMenuDelayToEnter_Info}" />
                                        <Slider Maximum="50" Minimum="1" Value="{Binding CustomCodeMenuDelayToEnter}" />
                                        <StackLayout Orientation="Horizontal">
                                            <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_CustomCodeMenuTimeout}" />
                                            <Label HorizontalOptions="EndAndExpand" Text="{Binding CustomCodeMenuTimeout, Converter={StaticResource CustomCodeMenuTime10Converter}}" />
                                        </StackLayout>
                                        <Label FontSize="{StaticResource ExtraSmallFontSize}" HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_CustomCodeMenuTimeout_Info}" />
                                        <Slider Maximum="60" Minimum="1" Value="{Binding CustomCodeMenuTimeout}" />
                                        <Label HorizontalOptions="StartAndExpand" Margin="0,50,10,10" FontSize="{StaticResource PrimaryFontSize}" Text="{i18n:Translate CO_UniRockerVsRockerCombo}" />
                                        <Label HorizontalOptions="StartAndExpand" Margin="0,0,10,10" Text="{i18n:Translate CO_UniRockerVsRockerCombo_Info}" />
                                        <ms:CustomOptionCheckboxRow BindingContext="{Binding CustomCodeMenuForceAllowAllButtonConfigurations}" />
                                        <ms:CustomOptionCheckboxRow BindingContext="{Binding UseRockerCombo}" />
                                        <StackLayout Orientation="Vertical" IsVisible="{Binding UseRockerCombo.IsChecked, Converter={StaticResource InvertedBoolConverter}}" Margin="0,0,0,20">
                                            <StackLayout Orientation="Horizontal">
                                                <Label HorizontalOptions="StartAndExpand" Text="{i18n:Translate CO_UniRockerEnterDetectionDelay}" />
                                                <Label HorizontalOptions="EndAndExpand" Text="{Binding UniRockerEnterDetectionDelay, Converter={StaticResource CustomCodeMenuTime1Converter}}" />
                                            </StackLayout>
                                            <Slider Maximum="50" Minimum="1" Value="{Binding UniRockerEnterDetectionDelay}" />
                                        </StackLayout>
                                    </StackLayout>
                                </DataTemplate>
                            </ms:CustomOptionCheckboxRow.ItemTemplate>
                        </ms:CustomOptionCheckboxRow>
                        <!--#endregion-->
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding MgfLimpModeProtectionEnabled}" />
                    </StackLayout>
                    <!--#endregion CUSTOM CODE ADVANCED-->
                    <!--#region DTC CODES-->
                    <StackLayout x:Name="Section_DTCCodes" IsVisible="{Binding IsWizardMode, Converter={StaticResource InvertedBoolConverter}, Mode=OneWay}">
                        <Label Text="DTC Codes" Style="{StaticResource LabelPageTitleStyle}" />
                        <ms:CustomOptionCheckboxRow BindingContext="{Binding RemoveUserDtcCodes}" CustomizeButtonText="{Binding x:DataType='vms:FlashingCarOptionsPageViewModel', Source={RelativeSource AncestorType={x:Type vms:FlashingCarOptionsPageViewModel}}, Path=DTCCodesPreview}" />
                    </StackLayout>
                    <!--#endregion DTC CODES-->
                </StackLayout>
            </ScrollView>
            <Grid Grid.Row="3" IsVisible="{OnPlatform WinUI=False, Default={Binding IsWizardMode}}" Margin="{OnPlatform iOS='0,0,20,0', Default='0,0,0,0'}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="0" />
                    <ColumnDefinition Width="0.7*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="0" />
                </Grid.ColumnDefinitions>
                <controls:BigButton
                    AutomationId="FlashingCarOptionsPage_PreviousButton"
                    BackgroundColor="{StaticResource AccentTextColor}"
                    Command="{Binding PreviousCommand}"
                    Clicked="ButtonPrev_Clicked"
                    IsVisible="{Binding IsWizardMode}"
                    IsEnabled="{Binding IsPreviousStepAvailable}"
                    Grid.Row="0" Grid.Column="1"
                    Text="{i18n:Translate FlashingCarOptionsPage_PreviousButton}"
                    Opacity="{Binding IsPreviousStepAvailable, Converter={StaticResource BoolToVisualOpacityConverter}}" />
                <controls:BigButton
                    AutomationId="FlashingCarOptionsPage_NextButton"
                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                    Command="{Binding NextCommand}"
                    Clicked="ButtonNext_Clicked"
                    IsVisible="{Binding IsWizardMode}"
                    IsEnabled="{Binding IsNextStepAvailable}"
                    Grid.Row="0" Grid.Column="2"
                    Text="{i18n:Translate FlashingCarOptionsPage_NextButton}"
                    Opacity="{Binding IsNextStepAvailable, Converter={StaticResource BoolToVisualOpacityConverter}}" />
            </Grid>
            <Grid Grid.Row="4" Margin="{OnPlatform iOS='0,0,20,0', Default='0,0,0,0'}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="{Binding IsWizardMode, Converter={StaticResource BoolToColumnHeightOnDesktopConverter}, ConverterParameter={OnPlatform WinUI=true, Default=false}}" />
                    <ColumnDefinition Width="0.7*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="{Binding IsWizardMode, Converter={StaticResource BoolToColumnHeightOnDesktopConverter}, ConverterParameter={OnPlatform WinUI=true, Default=false}}" />
                </Grid.ColumnDefinitions>
                <controls:BigButton
                    AutomationId="FlashingCarOptionsPage_PreviousButton"
                    BackgroundColor="{StaticResource AccentTextColor}"
                    Command="{Binding PreviousCommand}"
                    Clicked="ButtonPrev_Clicked"
                    IsVisible="{Binding IsWizardMode}"
                    IsEnabled="{Binding IsPreviousStepAvailable}"
                    Grid.Column="0" Text="&lt;"
                    Opacity="{Binding IsPreviousStepAvailable, Converter={StaticResource BoolToVisualOpacityConverter}}" />
                <controls:BigButton
                    AutomationId="FlashingCarOptionsPage_DefaultsButton"
                    BackgroundColor="{StaticResource AccentTextColor}"
                    Command="{Binding MiscCommand}"
                    Grid.Column="1"
                    Text="{i18n:Translate Misc_Title}" />
                <controls:BigButton
                    AutomationId="FlashingCarOptionsPage_SubmitButton"
                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                    Command="{Binding SubmitCommand}"
                    Grid.Column="2"
                    Text="{i18n:Translate CustomOptions_Flash}" />
                <controls:BigButton
                    AutomationId="FlashingCarOptionsPage_NextButton"
                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                    Command="{Binding NextCommand}"
                    Clicked="ButtonNext_Clicked"
                    IsVisible="{Binding IsWizardMode}"
                    IsEnabled="{Binding IsNextStepAvailable}"
                    Grid.Column="3" Text="&gt;"
                    Opacity="{Binding IsNextStepAvailable, Converter={StaticResource BoolToVisualOpacityConverter}}" />
            </Grid>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>