<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    controls:ShellTitleView.BackwardNavigationParameter="{Binding ViewModel}"
    controls:ShellTitleView.HasBarBackButton="True"
    x:Class="MgFlasher.Views.FlashingCarOptions.FlashingCarOptionsItemPage"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.FlashingCarOptions"
    x:DataType="vms:FlashingCarOptionsItemPageViewModel"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="10*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Label
                Grid.Row="0"
                Style="{StaticResource LabelPageTitleStyle}"
                Text="{Binding ViewModel.Option}"
                LineBreakMode="WordWrap"
                AutomationId="FlashingCarOptionsItemPage_TitleLabel" />
            <ScrollView Grid.Row="1" VerticalScrollBarVisibility="Never">
                <Grid x:Name="DisplayWrapper">
                    <Grid.Resources>
                        <ResourceDictionary>
                            <Style BasedOn="{StaticResource LabelMgBaseStyle}" TargetType="Label">
                                <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
                            </Style>
                            <Style BasedOn="{StaticResource RadioButtonMgBaseStyle}" TargetType="RadioButton">
                                <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
                            </Style>
                        </ResourceDictionary>
                    </Grid.Resources>
                </Grid>
            </ScrollView>
            <Grid Grid.Row="3">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="0.7*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <controls:BigButton
                    AutomationId="FlashingCarOptionsItemPage_DefaultsButton"
                    x:Name="DefaultsButton" Grid.Column="0"
                    BackgroundColor="{StaticResource AccentTextColor}"
                    Text="{i18n:Translate FlashingCarOptionsItemPage_Reset}"
                    Command="{Binding DefaultCommand}" />
                <controls:BigButton
                    AutomationId="FlashingCarOptionsItemPage_SubmitButton"
                    Grid.Column="1"
                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                    Text="{i18n:Translate FlashingCarOptionsItemPage_Save}"
                    Command="{Binding BackCommand}" />
            </Grid>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>