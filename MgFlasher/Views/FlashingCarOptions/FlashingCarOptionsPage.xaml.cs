﻿using System;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.ViewModels.FlashingCarOptions;
using MgFlasher.Views.FlashingCarOptions.Controls;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Controls.Compatibility;
using MgFlasher.Converters;

namespace MgFlasher.Views.FlashingCarOptions;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class FlashingCarOptionsPage : BasePage
{
    public FlashingCarOptionsPage() : base(DependencyResolver.Resolve<FlashingCarOptionsPageViewModel>(), PageType.FlashingCarOptions)
    {
        InitializeComponent();
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();

        if (BindingContext is not FlashingCarOptionsPageViewModel vm)
        {
            return;
        }

        if (vm.ScrollTo is not null)
        {
            var element = scrollView
                .GetChildren<CustomOptionCheckboxRow>()
                .FirstOrDefault(x => x.BindingContext == vm.ScrollTo);
            if (element is not null)
            {
                _ = Task.Run(async () => await ScrollToAsync(element));
            }
        }

        (BindingContext as FlashingCarOptionsPageViewModel).ShowGuidedModePrompt();
    }

    private async Task ScrollToAsync(CustomOptionCheckboxRow element)
    {
        try
        {
            await Task.Delay(250);
            await MainThread.InvokeOnMainThreadAsync(async () =>
                await scrollView.ScrollToAsync(element, ScrollToPosition.Center, true));
        }
        catch (Exception ex)
        {
            DependencyResolver.Resolve<ILogger<FlashingCarOptionsPage>>().LogError(ex, "OnAppearing callback error");
        }
    }

    private void ScrollView_Scrolled(object sender, ScrolledEventArgs e)
    {
        bool headerFound = false;
        string lowestInvisibleHeader = null;
        foreach (var section in stackLayout.Children)
        {
            if (section is Microsoft.Maui.Controls.StackLayout stack)
            {
                var header = stack.Children[0] as Label;
                var headerTop = header.Y + stack.Y - scrollView.ScrollY;
                var headerBottom = headerTop + header.Height;
                if (headerTop <= 0 && headerBottom > 0)
                {
                    stickyHeaderLabel.Text = string.Empty;
                    headerFound = true;
                    break;
                }
                else if (headerTop <= 0)
                {
                    lowestInvisibleHeader = header.Text;
                    headerFound = true;
                }
            }
        }

        if (headerFound && lowestInvisibleHeader != null)
        {
            if (lowestInvisibleHeader == "Developer Only" && !(BindingContext as FlashingCarOptionsPageViewModel).IsDeveloperOnlyVisible)
            {
                stickyHeaderLabel.Text = string.Empty;
            }
            else
            {
                stickyHeaderLabel.Text = lowestInvisibleHeader;
            }
        }
        else
        {
            stickyHeaderLabel.Text = string.Empty;
        }
    }

    private void ButtonPrev_Clicked(object sender, EventArgs e)
    {
        var currentStep = (BindingContext as FlashingCarOptionsPageViewModel)?.CurrentWizardStep ?? 1;
        var destinationStep = currentStep switch
        {
            1 => ProgressBarStep1,
            2 => ProgressBarStep2,
            3 => ProgressBarStep3,
            4 => ProgressBarStep4,
            5 => ProgressBarStep5,
            6 => ProgressBarStep6,
            7 => ProgressBarStep7,
            _ => ProgressBarStep1
        };

        wizardScroll.ScrollToAsync(destinationStep, ScrollToPosition.End, true);
    }

    private void ButtonNext_Clicked(object sender, EventArgs e)
    {
        var currentStep = (BindingContext as FlashingCarOptionsPageViewModel)?.CurrentWizardStep ?? 1;
        var destinationStep = currentStep switch
        {
            1 => ProgressBarStep1,
            2 => ProgressBarStep2,
            3 => ProgressBarStep3,
            4 => ProgressBarStep4,
            5 => ProgressBarStep5,
            6 => ProgressBarStep6,
            7 => ProgressBarStep7,
            _ => ProgressBarStep1
        };

        wizardScroll.ScrollToAsync(destinationStep, ScrollToPosition.Start, true);
    }
}