﻿using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.ViewModels;
using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using CommunityToolkit.Mvvm.Messaging;
using static MgFlasher.Helpers.MessagingCenterIdentifiers;

namespace MgFlasher.Views;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class FlashingCarPage : BasePage, IRecipient<FlashingCarLogMessage>
{
    public FlashingCarPage() : base(DependencyResolver.Resolve<FlashingCarPageViewModel>(), PageType.FlashingCar)
    {
        InitializeComponent();
        WeakReferenceMessenger.Default.Register(this);
    }

    public void Receive(FlashingCarLogMessage message)
    {
        MessagesListView.ScrollTo(message.Value, ScrollToPosition.MakeVisible, true);
    }
}