﻿using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.ViewModels;
using Microsoft.Maui.Controls.Xaml;
using System.Threading.Tasks;

namespace MgFlasher.Views;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class PendingPage : BasePage
{
    public PendingPage() : base(DependencyResolver.Resolve<PendingPageViewModel>(), PageType.Pending)
    {
        InitializeComponent();
    }

    public PendingPage(PendingPageViewModel viewModel) : base(viewModel, PageType.Pending)
    {
        InitializeComponent();
    }
}