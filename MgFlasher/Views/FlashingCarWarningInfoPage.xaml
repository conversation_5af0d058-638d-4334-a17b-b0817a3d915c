<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.FlashingCarWarningInfoPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    controls:ShellTitleView.HasBarBackButton="True"
    x:DataType="vms:FlashingCarWarningInfoPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="0.4*" />
                <RowDefinition Height="1.1*" />
                <RowDefinition Height="0.6*" />
                <RowDefinition Height="4.1*" />
                <RowDefinition Height="0.555*" />
            </Grid.RowDefinitions>
            <Image Grid.Row="1" Aspect="AspectFit" Source="payattention76.png" />
            <Label Grid.Row="2" Style="{StaticResource LabelPageTitleStyle}" FontAttributes="Bold" Text="{i18n:Translate FlashingCarWarning_Headline}" />
            <ListView
                Grid.Row="3" BackgroundColor="Transparent"
                HorizontalOptions="CenterAndExpand"
                ItemsSource="{Binding FlashingCarWarningInfos}"
                VerticalOptions="CenterAndExpand" SelectionMode="None"
                CachingStrategy="RecycleElement"
                RowHeight="{OnIdiom Phone={OnPlatform Default=75}, Desktop={OnPlatform Default=75}, Default={OnPlatform Default=90}}"
                SeparatorColor="White" SeparatorVisibility="None">
                <ListView.ItemTemplate>
                    <DataTemplate x:DataType="vms:FlashingCarWarningInfoItem">
                        <ViewCell>
                            <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Label
                                    Grid.Row="1" VerticalOptions="FillAndExpand"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalTextAlignment="Center"
                                    HorizontalTextAlignment="Center"
                                    FontSize="{StaticResource SecondaryFontSize}"
                                    Text="{Binding Text}" />
                            </Grid>
                        </ViewCell>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>
            <controls:BigButton
                Grid.Row="4"
                AutomationId="FlashingCarWarningInfoPage_SubmitButton"
                BackgroundColor="{StaticResource PrimarySuccessColor}"
                Command="{Binding StartFlashingCommand}"
                Text="{i18n:Translate FlashingCarWarning_Start}"
                IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>