<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.ResetAdaptationPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:views="clr-namespace:MgFlasher.Views"
    controls:ShellTitleView.HasBarBackButton="True"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:ResetAdaptationPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="2.81*" />
                <RowDefinition Height="0.08*" />
                <RowDefinition Height="0.52*" />
                <RowDefinition Height="0.02*" />
                <RowDefinition Height="0.52*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate ResetAdaption_Headline}" />
            <ListView
                Grid.Row="1" BackgroundColor="Transparent"
                CachingStrategy="RecycleElement"
                ItemTemplate="{StaticResource OptionalItemTemplate}"
                ItemsSource="{Binding OptionalItemViewModels}"
                RowHeight="58" SeparatorVisibility="None" />
            <controls:BigButton Grid.Row="3" BackgroundColor="{StaticResource PrimaryAccentColor}" Command="{Binding MarkAllCommand}" Text="{i18n:Translate ResetAdaption_MarkAll}" />
            <controls:BigButton Grid.Row="5" BackgroundColor="{StaticResource PrimarySuccessColor}" Command="{Binding SubmitCommand}" Text="{i18n:Translate ResetAdaption_Reset}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>