<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.PendingPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:views="clr-namespace:MgFlasher.Views"
    IsBusy="True" Shell.NavBarIsVisible="False"
    xmlns:progressBar="clr-namespace:Syncfusion.Maui.ProgressBar;assembly=Syncfusion.Maui.ProgressBar"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:PendingPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="2*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="2*" />
            </Grid.RowDefinitions>
            <Grid Grid.Row="1" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="0.5*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="0.5*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="0.5*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <controls:GifImage
                    Grid.Row="0" x:Name="Gif" Grid.Column="1"
                    Aspect="AspectFit" Source="preloader.gif" />
                <Label
                    Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3"
                    FontSize="{StaticResource PrimaryFontSize}"
                    AutomationId="PendingPage_TextLabel"
                    HorizontalOptions="CenterAndExpand"
                    HorizontalTextAlignment="Center"
                    VerticalOptions="FillAndExpand"
                    Text="{Binding Text, Mode=OneWay}" />
                <progressBar:SfLinearProgressBar
                    IsVisible="{Binding ShowProgressCounter, Mode=OneWay}"
                    Grid.Column="0" Grid.ColumnSpan="3" Grid.Row="2"
                    Minimum="0" Maximum="100"
                    Progress="{Binding Progress, Mode=OneWay}"
                    HorizontalOptions="Center"
                    VerticalOptions="FillAndExpand"
                    MaximumWidthRequest="200"
                    ProgressFill="{StaticResource PrimaryImportantTextColor}"
                    AnimationEasing="{x:Static Easing.CubicInOut}" />
            </Grid>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>