<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="MgFlasher.Views.Logger.MyCarLoggerView"
    x:DataType="vms:MyCarLoggerViewModel"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:behaviors="clr-namespace:MgFlasher.Behaviors"
    xmlns:loggercontrols="clr-namespace:MgFlasher.Views.Logger.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="13*" />
            <RowDefinition Height="1.5*" />
        </Grid.RowDefinitions>
        <Grid Grid.Row="0" Margin="-5,0,-5,0" Padding="10,0,10,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Grid Grid.Row="0" HorizontalOptions="CenterAndExpand" IsVisible="{Binding LoggerStarted, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}" VerticalOptions="CenterAndExpand">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Label
                    Grid.Column="0" Grid.ColumnSpan="4" Grid.Row="0"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    Text="{i18n:Translate LoggerPage_PleasePressToStartLoggerAsync}"
                    VerticalOptions="Center" />
                <controls:SmallButton
                    BackgroundColor="{StaticResource PrimarySuccessTextColor}"
                    Command="{Binding TryStartLoggerCommand}"
                    Grid.Column="1" Grid.ColumnSpan="2" Grid.Row="1"
                    Grid.RowSpan="2" HorizontalOptions="FillAndExpand"
                    Text="{i18n:Translate LoggerPage_Button_StartMeasurements}" />
            </Grid>
            <loggercontrols:GaugesDashboard
                Grid.Row="0" HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand"
                ItemsSource="{Binding GaugesDashboardViewModel.GaugeItems, Mode=OneWay}"
                x:Name="gaugeDashboard">
                <loggercontrols:GaugesDashboard.IsVisible>
                    <MultiBinding Converter="{StaticResource AllTrueMultiConverter}">
                        <Binding Path="LoggerStarted" Mode="OneWay" />
                        <Binding Path="IsGaugeModeSelected" Mode="OneWay" />
                    </MultiBinding>
                </loggercontrols:GaugesDashboard.IsVisible>
                <loggercontrols:GaugesDashboard.Behaviors>
                    <behaviors:EventToCommandBehavior Command="{Binding GaugesDashboardViewModel.GaugeItemsAutoAdjustedCommand}" EventName="ItemsAutoAdjusted" />
                </loggercontrols:GaugesDashboard.Behaviors>
            </loggercontrols:GaugesDashboard>
            <loggercontrols:GaugesList
                Grid.Row="0" HorizontalOptions="FillAndExpand"
                ItemsSource="{Binding GaugesDashboardViewModel.GaugeItems, Mode=OneWay}"
                VerticalOptions="FillAndExpand" x:Name="gaugeList">
                <loggercontrols:GaugesList.IsVisible>
                    <MultiBinding Converter="{StaticResource AllTrueMultiConverter}">
                        <Binding Path="LoggerStarted" Mode="OneWay" />
                        <Binding Path="IsListModeSelected" Mode="OneWay" />
                    </MultiBinding>
                </loggercontrols:GaugesList.IsVisible>
            </loggercontrols:GaugesList>
        </Grid>
        <Grid ColumnSpacing="5" Grid.Row="1" HorizontalOptions="FillAndExpand">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="0.2*" />
            </Grid.ColumnDefinitions>
            <controls:BigButton
                BackgroundColor="{StaticResource PrimarySuccessTextColor}"
                Command="{Binding StartLogToFileCommand}"
                Grid.Column="0"
                IsEnabled="{Binding LoggerStarted, Mode=OneWay}"
                IsVisible="{Binding LoggingToFile, Converter={StaticResource InvertedBoolConverter}}"
                Opacity="{Binding LoggerStarted, Converter={StaticResource BoolToVisualOpacityConverter}}"
                Text="{i18n:Translate Logger_Button_Start}" />
            <controls:BigButton
                BackgroundColor="{StaticResource PrimaryImportantTextColor}"
                Command="{Binding StopLogToFileCommand}"
                Grid.Column="0" Grid.ColumnSpan="2"
                IsVisible="{Binding LoggingToFile}"
                Text="{i18n:Translate Logger_Button_Stop}" />
            <controls:BigButton
                AutomationId="LoggerPage_LogsButton"
                BackgroundColor="{StaticResource PrimaryAccentColor}"
                Command="{Binding GoToLogsCommand}"
                Grid.Column="1"
                IsVisible="{Binding LoggingToFile, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}"
                Text="{i18n:Translate LoggerPage_Logs}" />
            <controls:BigButton
                AutomationId="LoggerPage_MiscButton"
                BackgroundColor="{StaticResource PrimaryAccentColor}"
                Command="{Binding MiscCommand}"
                Grid.Column="2" Text="&#x22EE;" />
        </Grid>
    </Grid>
</ContentView>