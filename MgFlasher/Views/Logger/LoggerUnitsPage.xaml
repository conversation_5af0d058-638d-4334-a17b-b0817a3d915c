<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.Logger.LoggerUnitsPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:views="clr-namespace:MgFlasher.Views"
    controls:ShellTitleView.HasBarBackButton="True"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView"
    x:DataType="vms:LoggerUnitsPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.Resources>
                <ResourceDictionary>
                    <Style TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}">
                        <Setter Property="BackgroundColor" Value="Transparent" />
                        <Setter Property="HorizontalOptions" Value="End" />
                    </Style>
                    <Style TargetType="Picker" BasedOn="{StaticResource PickerMgBaseStyle}">
                        <Setter Property="Title" Value="Default   " />
                        <Setter Property="TitleColor" Value="{StaticResource PrimaryTextColor}" />
                        <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                        <Setter Property="BackgroundColor" Value="Transparent" />
                        <Setter Property="HorizontalOptions" Value="StartAndExpand" />
                    </Style>
                </ResourceDictionary>
            </Grid.Resources>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="9*" />
                <RowDefinition Height="1*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Text="{i18n:Translate LoggerUnitsPage_Title}" Style="{StaticResource LabelPageTitleStyle}" />
            <CollectionView
                Grid.Row="1" Margin="0,0,0,5"
                BackgroundColor="Transparent" SelectionMode="None"
                ItemsSource="{Binding OptionalItemViewModels, Mode=OneWay}"
                VerticalOptions="FillAndExpand" CanReorderItems="False"
                ItemSizingStrategy="MeasureFirstItem">
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="vms:LoggerUnitUserConfigurationOptionalItemViewModel">
                        <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" HeightRequest="{OnIdiom Phone={OnPlatform Default=60}, Desktop={OnPlatform Default=90}, Default={OnPlatform Android=65, Default=55}}">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="0.03*" />
                                <RowDefinition Height="1.4*" />
                                <RowDefinition Height="0.03*" />
                            </Grid.RowDefinitions>
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="6*" />
                                    <ColumnDefinition Width="3.1*" />
                                </Grid.ColumnDefinitions>
                                <Label
                                    Grid.Column="0" Margin="5,0,0,0"
                                    Text="{Binding SourceGroup, Mode=OneWay}"
                                    LineBreakMode="WordWrap"
                                    HorizontalOptions="StartAndExpand"
                                    FontSize="{StaticResource SmallFontSize}"
                                    VerticalTextAlignment="Center" />
                                <Picker
                                    Grid.Column="1" HorizontalOptions="EndAndExpand"
                                    Title="{i18n:Translate LoggerUnitsPage_SelectUnit}"
                                    VerticalOptions="Center"
                                    WidthRequest="{OnPlatform iOS=140, Default=-1}"
                                    ItemsSource="{Binding AvailableUnits}"
                                    SelectedItem="{Binding Option}" />
                            </Grid>
                            <Grid
                                Grid.Row="2"
                                BackgroundColor="{StaticResource ListItemSeparatorColor}"
                                HorizontalOptions="FillAndExpand" HeightRequest="1"
                                VerticalOptions="FillAndExpand" />
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <controls:BigButton Grid.Column="0" BackgroundColor="{StaticResource PrimaryAccentColor}" Command="{Binding LoggerUnitDefaultsCommand}" Text="{i18n:Translate LoggerUnitsPage_Defaults}" />
                <controls:BigButton
                    Grid.Column="1"
                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                    Command="{Binding SubmitCommand}"
                    IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                    Text="{i18n:Translate LoggerUnitsPage_Save}" />
            </Grid>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>