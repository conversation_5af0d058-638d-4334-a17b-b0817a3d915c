<?xml version="1.0" encoding="UTF-8" ?>
<ResourceDictionary x:Class="MgFlasher.Views.Logger.Resources.GaugeValuesStylesResource" xmlns="http://schemas.microsoft.com/dotnet/2021/maui" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" xmlns:local="clr-namespace:MgFlasher.Resources">
    <ResourceDictionary.MergedDictionaries>
        <local:ControlsResource />
    </ResourceDictionary.MergedDictionaries>
    <Style x:Key="GaugeLabelStyle" TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}">
        <Setter Property="FontSize">
            <OnIdiom x:TypeArguments="x:Double">
                <OnIdiom.Tablet>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="iOS" Value="21" />
                        <On Platform="Android" Value="26" />
                    </OnPlatform>
                </OnIdiom.Tablet>
                <OnIdiom.Phone>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="iOS" Value="15" />
                    </OnPlatform>
                </OnIdiom.Phone>
                <OnIdiom.Desktop>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="Default" Value="15" />
                    </OnPlatform>
                </OnIdiom.Desktop>
            </OnIdiom>
        </Setter>
    </Style>
    <Style x:Key="GaugeValueStyle" TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}">
        <Setter Property="FontSize">
            <OnIdiom x:TypeArguments="x:Double">
                <OnIdiom.Tablet>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="iOS" Value="33" />
                        <On Platform="Android" Value="38" />
                    </OnPlatform>
                </OnIdiom.Tablet>
                <OnIdiom.Phone>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="iOS" Value="30" />
                        <On Platform="Android" Value="22" />
                    </OnPlatform>
                </OnIdiom.Phone>
                <OnIdiom.Desktop>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="Default" Value="30" />
                    </OnPlatform>
                </OnIdiom.Desktop>
            </OnIdiom>
        </Setter>
    </Style>
    <Style x:Key="BigGaugeLabelStyle" TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}">
        <Setter Property="FontSize">
            <OnIdiom x:TypeArguments="x:Double">
                <OnIdiom.Tablet>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="iOS" Value="32" />
                        <On Platform="Android" Value="34" />
                    </OnPlatform>
                </OnIdiom.Tablet>
                <OnIdiom.Phone>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="iOS" Value="24" />
                        <On Platform="Android" Value="17" />
                    </OnPlatform>
                </OnIdiom.Phone>
                <OnIdiom.Desktop>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="Default" Value="24" />
                    </OnPlatform>
                </OnIdiom.Desktop>
            </OnIdiom>
        </Setter>
    </Style>
    <Style x:Key="BigGaugeValueStyle" TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}">
        <Setter Property="FontSize">
            <OnIdiom x:TypeArguments="x:Double">
                <OnIdiom.Tablet>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="iOS" Value="60" />
                        <On Platform="Android" Value="80" />
                    </OnPlatform>
                </OnIdiom.Tablet>
                <OnIdiom.Phone>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="iOS" Value="70" />
                        <On Platform="Android" Value="80" />
                    </OnPlatform>
                </OnIdiom.Phone>
                <OnIdiom.Desktop>
                    <OnPlatform x:TypeArguments="x:Double">
                        <On Platform="Default" Value="70" />
                    </OnPlatform>
                </OnIdiom.Desktop>
            </OnIdiom>
        </Setter>
    </Style>
</ResourceDictionary>