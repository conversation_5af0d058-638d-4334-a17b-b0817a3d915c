<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="MgFlasher.Views.Logger.LoggerFileMapView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:MgFlasher.Converters"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views.Logger"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    xmlns:models="clr-namespace:MgFlasher.ViewModels.Logger.Models"
    xmlns:maps="clr-namespace:Microsoft.Maui.Controls.Maps;assembly=Microsoft.Maui.Controls.Maps"
    x:Name="root" x:DataType="vms:LoggerFileMapViewModel">
    <maps:Map x:Name="map" ItemsSource="{Binding Pins, Mode=OneWay}">
        <maps:Map.ItemTemplate>
            <DataTemplate x:DataType="models:LoggerFileMapPointItemModel">
                <maps:Pin Location="{Binding Location}" Label="{Binding Label}" />
            </DataTemplate>
        </maps:Map.ItemTemplate>
    </maps:Map>
</ContentView>