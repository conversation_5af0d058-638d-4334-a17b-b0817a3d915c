﻿using MgFlasher.Behaviors;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User.Storage;
using MgFlasher.Views.Logger.Controls;
using MgFlasher.Views.Logger.Services;
using System.ComponentModel;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Views.Logger.Behaviors;

public class GaugeViewLayoutBoundsBehavior : BehaviorBase<GaugeViewContainer>
{
    private GaugeViewLayoutPositioner _positioner;

    public static readonly BindableProperty ShapeProperty = BindableProperty.Create(nameof(Shape), typeof(GaugeLogShape), typeof(GaugeViewLayoutBoundsBehavior), null, propertyChanged: OnShapePropertyChanged);

    public GaugeLogShape Shape
    {
        get { return (GaugeLogShape)GetValue(ShapeProperty); }
        set { SetValue(ShapeProperty, value); }
    }

    public GaugeViewLayoutBoundsBehavior()
    {
        _positioner = new GaugeViewLayoutPositioner();
    }

    protected override void OnAttachedTo(GaugeViewContainer bindable)
    {
        base.OnAttachedTo(bindable);

        bindable.PropertyChanged += OnContainerPropertyChanged;

        Calculate();
    }

    protected override void OnDetachingFrom(GaugeViewContainer bindable)
    {
        base.OnDetachingFrom(bindable);

        bindable.PropertyChanged -= OnContainerPropertyChanged;
    }

    private static void OnShapePropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var behavior = bindable as GaugeViewLayoutBoundsBehavior;

        behavior.Calculate();
    }

    private void Calculate()
    {
        if (AssociatedObject is null)
            return;

        _positioner.Adjust(AssociatedObject);
    }

    private void OnContainerPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(GaugeViewContainer.Parent) && AssociatedObject?.Parent != null)
        {
            Calculate();
        }
    }
}