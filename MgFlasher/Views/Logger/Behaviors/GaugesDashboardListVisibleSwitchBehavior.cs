﻿using MgFlasher.Behaviors;
using MgFlasher.ViewModels.Logger;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Views.Logger.Behaviors;

public class GaugesDashboardListVisibleSwitchBehavior : BehaviorBase<View>
{
    public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameof(ItemsSource), typeof(ObservableCollection<GaugeItemViewModel>), typeof(GaugesDashboardListVisibleSwitchBehavior), propertyChanged: OnDisplayedItemsSourcePropertyChanged);

    public ObservableCollection<GaugeItemViewModel> ItemsSource
    {
        get { return (ObservableCollection<GaugeItemViewModel>)GetValue(ItemsSourceProperty); }
        set { SetValue(ItemsSourceProperty, value); }
    }

    private static void OnDisplayedItemsSourcePropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var behavior = bindable as GaugesDashboardListVisibleSwitchBehavior;

        if (oldValue is ObservableCollection<GaugeItemViewModel> old)
        {
            old.CollectionChanged -= (o, e) => OnCollectionChanged(behavior, e);
        }
        if (newValue is ObservableCollection<GaugeItemViewModel> @new)
        {
            @new.CollectionChanged += (o, e) => OnCollectionChanged(behavior, e);
            OnCollectionChanged(behavior, null);
        }
    }

    private static void OnCollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
    {
        var behavior = sender as GaugesDashboardListVisibleSwitchBehavior;
        if (behavior?.AssociatedObject is null)
        {
            return;
        }

        behavior.AssociatedObject.IsVisible = behavior.ItemsSource?.Count > 0;
    }
}