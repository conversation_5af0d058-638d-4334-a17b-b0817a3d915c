﻿using MgFlasher.Behaviors;
using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User.Storage;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;

namespace MgFlasher.Views.Logger.Behaviors;

public class LoggerDisplayButtonColorBehavior : BehaviorBase<Button>
{
    public static readonly BindableProperty ValueProperty = BindableProperty.Create(nameof(Value), typeof(object), typeof(LoggerDisplayButtonColorBehavior), null, propertyChanged: OnEnumPropertyChanged);

    public object Value
    {
        get { return GetValue(ValueProperty); }
        set { SetValue(ValueProperty, value); }
    }

    private static void OnEnumPropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var behavior = bindable as LoggerDisplayButtonColorBehavior;
        behavior.AssociatedObject.BackgroundColor = GetColor(newValue);
    }

    private static Color GetColor(object value)
    {
        if (value is ModuleId moduleId)
        {
            return moduleId == ModuleId.DME_MASTER ?
                (Color)App.Current.Resources["AccentSuccessTextColor"] :
                (Color)App.Current.Resources["PrimarySuccessColor"];
        }
        else if (value is GaugePresentationType presentationType)
        {
            return presentationType == GaugePresentationType.Circular ?
                (Color)App.Current.Resources["PrimarySuccessTextColor"] :
                (Color)App.Current.Resources["AccentSuccessTextColor"];
        }
        else if (value is bool bVal)
        {
            return bVal ?
                (Color)App.Current.Resources["PrimarySuccessTextColor"] :
                (Color)App.Current.Resources["PrimaryImportantTextColor"];
        }
        else if (value is LoggerAlertType alertType)
        {
            return alertType switch
            {
                LoggerAlertType.Above => (Color)App.Current.Resources["PrimarySuccessTextColor"],
                LoggerAlertType.Below => (Color)App.Current.Resources["PrimaryImportantTextColor"],
                LoggerAlertType.Equals => (Color)App.Current.Resources["AccentSuccessTextColor"],
                LoggerAlertType.OutsideRange => (Color)App.Current.Resources["GaugeValueLinePrimaryColor"],
                LoggerAlertType.InsideRange => (Color)App.Current.Resources["GaugeValuePrimaryColor"],
                _ => throw new System.NotImplementedException("Not supported logger alert type"),
            };
        }
        else if (value is Color color)
        {
            return color;
        }

        return (Color)App.Current.Resources["AccentSuccessTextColor"];
    }
}