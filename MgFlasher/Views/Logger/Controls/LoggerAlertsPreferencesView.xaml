<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    x:Class="MgFlasher.Views.Logger.Controls.LoggerAlertsPreferencesView"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    x:DataType="vms:LoggerAlertsPreferencesViewModel">
    <ContentView.Content>
        <ScrollView VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
            <StackLayout Orientation="Vertical">
                <!--  Alert destinations  -->
                <BoxView Style="{StaticResource Separator}" />
                <StackLayout Orientation="Vertical" Spacing="0" HorizontalOptions="FillAndExpand">
                    <Label FontAttributes="Bold" Text="{i18n:Translate LoggerAlertsPreferencesView_AlertDestinations_Title}" HorizontalOptions="StartAndExpand" />
                    <controls:SearchableSfListView ItemTemplate="{StaticResource OptionalItemTemplate}" ItemsSource="{Binding AlertDestinations, Mode=OneWay}" ItemSize="{OnIdiom Phone={OnPlatform Default=60}, Desktop={OnPlatform Default=60}, Default={OnPlatform Android=65, Default=55}}" />
                </StackLayout>
            </StackLayout>
        </ScrollView>
    </ContentView.Content>
</ContentView>