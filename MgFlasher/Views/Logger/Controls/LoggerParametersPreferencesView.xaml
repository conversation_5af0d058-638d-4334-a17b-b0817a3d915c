<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MgFlasher.Views.Logger.Controls.LoggerParametersPreferencesView"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    x:DataType="vms:LoggerParametersPreferencesViewModel">
    <ContentView.Content>
        <ScrollView VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
            <StackLayout Orientation="Vertical">
                <StackLayout.Resources>
                    <ResourceDictionary>
                        <Style TargetType="Label" BasedOn="{StaticResource LabelMgBaseStyle}">
                            <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
                        </Style>
                        <Style TargetType="controls:SmallButton" BasedOn="{StaticResource ButtonMgBaseStyle}">
                            <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
                        </Style>
                    </ResourceDictionary>
                </StackLayout.Resources>
                <!--  Dual ecu logging  -->
                <BoxView IsVisible="{Binding ModuleLoggingPreferenceSelectionVisible, Mode=OneWay}" Style="{StaticResource Separator}" />
                <StackLayout Orientation="Horizontal" IsVisible="{Binding ModuleLoggingPreferenceSelectionVisible, Mode=OneWay}" Spacing="0" HorizontalOptions="FillAndExpand">
                    <Label FontAttributes="Bold" Text="{i18n:Translate SettingsPage_DualEcuLogging_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <Grid HorizontalOptions="End" VerticalOptions="Center">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="{OnPlatform iOS=Auto, Default=*}" />
                        </Grid.ColumnDefinitions>
                        <controls:BigButton
                            Grid.Column="0" WidthRequest="30" HeightRequest="10"
                            Text="?"
                            Command="{Binding ShowDualEcuLoggingInfoCommand}"
                            BackgroundColor="{StaticResource AccentSuccessTextColor}" />
                    </Grid>
                </StackLayout>
                <Picker IsVisible="{Binding ModuleLoggingPreferenceSelectionVisible, Mode=OneWay}" ItemsSource="{Binding ModuleLoggingPreferenceTypes, Mode=OneWay}" ItemDisplayBinding="{Binding Translation, Mode=OneWay}" SelectedItem="{Binding SelectedLoggerPreference}" />
                <!--  Log master as slave  -->
                <BoxView IsVisible="{Binding LogMasterModuleAsSlaveVisible, Mode=OneWay}" Style="{StaticResource Separator}" />
                <StackLayout Orientation="Horizontal" IsVisible="{Binding LogMasterModuleAsSlaveVisible, Mode=OneWay}" Spacing="0" HorizontalOptions="FillAndExpand">
                    <Label FontAttributes="Bold" Text="{i18n:Translate LoggerPreferencesPage_LogMasterAsSlave_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <controls:Checkbox HorizontalOptions="End" VerticalOptions="FillAndExpand" IsChecked="{Binding LogMasterModuleAsSlave, Mode=TwoWay}" />
                </StackLayout>
                <!--  Simulate random fake logger falues  -->
                <BoxView IsVisible="{Binding FakeValueLoggingVisible, Mode=OneWay}" Style="{StaticResource Separator}" />
                <StackLayout Orientation="Horizontal" IsVisible="{Binding FakeValueLoggingVisible, Mode=OneWay}" Spacing="0" HorizontalOptions="FillAndExpand">
                    <Label FontAttributes="Bold" Text="{i18n:Translate LoggerPreferencesPage_FakeValueLogging_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <controls:Checkbox HorizontalOptions="End" VerticalOptions="FillAndExpand" IsChecked="{Binding FakeValueLogging, Mode=TwoWay}" />
                </StackLayout>
                <!--  Log ECU communication  -->
                <BoxView IsVisible="{Binding LogEcuCommunicationVisible, Mode=OneWay}" Style="{StaticResource Separator}" />
                <StackLayout Orientation="Horizontal" IsVisible="{Binding LogEcuCommunicationVisible, Mode=OneWay}" Spacing="0" HorizontalOptions="FillAndExpand">
                    <Label FontAttributes="Bold" Text="{i18n:Translate LoggerPreferencesPage_LogEcuCommunication_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <controls:Checkbox HorizontalOptions="End" VerticalOptions="FillAndExpand" IsChecked="{Binding LogEcuCommunication, Mode=TwoWay}" />
                </StackLayout>
                <!--  Present a2l names in params  -->
                <BoxView IsVisible="{Binding A2LNamesVisible, Mode=OneWay}" Style="{StaticResource Separator}" />
                <StackLayout Orientation="Horizontal" IsVisible="{Binding A2LNamesVisible, Mode=OneWay}" Spacing="0" HorizontalOptions="FillAndExpand">
                    <Label FontAttributes="Bold" Text="{i18n:Translate LoggerPreferencesPage_PresentA2lMeasurementNameInParams_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <controls:Checkbox HorizontalOptions="End" VerticalOptions="FillAndExpand" IsChecked="{Binding PresentA2lMeasurementName, Mode=TwoWay}" />
                </StackLayout>
                <!--  Present parameters values as hex  -->
                <BoxView Style="{StaticResource Separator}" />
                <StackLayout Orientation="Horizontal" Spacing="0" HorizontalOptions="FillAndExpand">
                    <Label FontAttributes="Bold" Text="{i18n:Translate LoggerPreferencesPage_PresentHexValues_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <controls:Checkbox HorizontalOptions="End" VerticalOptions="FillAndExpand" IsChecked="{Binding PresentHexValues, Mode=TwoWay}" />
                </StackLayout>
                <!--  Track car location  -->
                <BoxView Style="{StaticResource Separator}" />
                <StackLayout Orientation="Horizontal" Spacing="0" HorizontalOptions="FillAndExpand">
                    <Label FontAttributes="Bold" Text="{i18n:Translate LoggerPreferencesPage_TrackCarLocation_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <controls:Checkbox HorizontalOptions="End" VerticalOptions="FillAndExpand" IsChecked="{Binding TrackCarLocation, Mode=TwoWay}" />
                </StackLayout>
                <!--  Logger file buffer size  -->
                <BoxView Style="{StaticResource Separator}" IsVisible="{Binding LoggerFileBufferSizeVisible, Mode=OneWay}" />
                <Label
                    IsVisible="{Binding LoggerFileBufferSizeVisible, Mode=OneWay}"
                    FontAttributes="Bold"
                    Text="{i18n:Translate LoggerPreferencesPage_LoggerFileBufferSize_Title}"
                    HorizontalOptions="StartAndExpand"
                    VerticalOptions="Center" />
                <Slider
                    IsVisible="{Binding LoggerFileBufferSizeVisible, Mode=OneWay}"
                    Maximum="30000" Minimum="30"
                    Value="{Binding LoggerFileBufferSize}"
                    HorizontalOptions="FillAndExpand" />
                <!--  Upload logger variables  -->
                <BoxView IsVisible="{Binding UploadLoggerVariablesVisible, Mode=OneWay}" Style="{StaticResource Separator}" />
                <StackLayout Orientation="Horizontal" IsVisible="{Binding UploadLoggerVariablesVisible, Mode=OneWay}" Spacing="0" HorizontalOptions="FillAndExpand">
                    <Label FontAttributes="Bold" Text="{i18n:Translate LoggerPreferencesPage_CustomLoggerVariablesUpload_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <controls:SmallButton HorizontalOptions="End" Text="{i18n:Translate LoggerPreferencesPage_CustomLoggerVariablesUpload_Button}" Command="{Binding UploadLoggerVariablesCommand}" BackgroundColor="{StaticResource AccentSuccessTextColor}" />
                </StackLayout>
                <!--  Clear & share uploaded logger variables  -->
                <BoxView IsVisible="{Binding ClearCustomLoggerVariablesVisible, Mode=OneWay}" Style="{StaticResource Separator}" />
                <StackLayout Orientation="Horizontal" IsVisible="{Binding ClearCustomLoggerVariablesVisible, Mode=OneWay}" Spacing="0" HorizontalOptions="FillAndExpand">
                    <Label FontAttributes="Bold" Text="{i18n:Translate LoggerPreferencesPage_UploadedLoggerVariables_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <controls:SmallButton HorizontalOptions="End" Text="{i18n:Translate LoggerPreferencesPage_UploadedLoggerVariablesClear_Button}" Command="{Binding ClearCustomLoggerVariablesCommand}" BackgroundColor="{StaticResource PrimaryImportantTextColor}" />
                    <Image
                        Margin="5,0,0,0" HorizontalOptions="End"
                        Aspect="AspectFit" HeightRequest="29"
                        Source="socialsmall.png" VerticalOptions="Center"
                        WidthRequest="25">
                        <Image.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding ShareCustomLoggerVariablesCommand}" />
                        </Image.GestureRecognizers>
                    </Image>
                </StackLayout>
            </StackLayout>
        </ScrollView>
    </ContentView.Content>
</ContentView>