<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MgFlasher.Views.Logger.Controls.GaugeViewContainer"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    xmlns:gauges="clr-namespace:MgFlasher.Views.Logger.Controls"
    xmlns:prefs="clr-namespace:MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User.Storage;assembly=MgFlasher.Flasher.Services"
    xmlns:logBehaviors="clr-namespace:MgFlasher.Views.Logger.Behaviors"
    x:Name="root" x:DataType="vms:GaugeItemViewModel">
    <ContentView.Resources>
        <ResourceDictionary>
            <ControlTemplate x:Key="CircularGaugeViewControlTemplate">
                <gauges:CircularGaugeView BindingContext="{TemplateBinding Parent.BindingContext}" />
            </ControlTemplate>
            <ControlTemplate x:Key="RectangleGaugeViewControlTemplate">
                <gauges:RectangleGaugeView BindingContext="{TemplateBinding Parent.BindingContext}" />
            </ControlTemplate>
        </ResourceDictionary>
    </ContentView.Resources>
    <ContentView.Behaviors>
        <logBehaviors:GaugeViewLayoutBoundsBehavior Shape="{Binding Shape, Mode=OneWay}" />
    </ContentView.Behaviors>
    <ContentView.Content>
        <AbsoluteLayout>
            <Frame
                Margin="{OnPlatform Default='5,15,8,0', Android='1'}"
                AbsoluteLayout.LayoutFlags="All"
                AbsoluteLayout.LayoutBounds="0.5,0.5,1,1"
                CornerRadius="60"
                BackgroundColor="{Binding FrameColor, Source={x:Reference root}}"
                HasShadow="False" BorderColor="Transparent" />
            <ContentView AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0.5,0.5,1,1">
                <ContentView.Style>
                    <Style TargetType="ContentView">
                        <Setter Property="ControlTemplate" Value="{StaticResource CircularGaugeViewControlTemplate}" />
                        <Style.Triggers>
                            <DataTrigger TargetType="ContentView" Binding="{Binding PresentationType, Mode=OneWay}" Value="{x:Static prefs:GaugePresentationType.Rectangle}" x:DataType="vms:GaugeItemViewModel">
                                <Setter Property="ControlTemplate" Value="{StaticResource RectangleGaugeViewControlTemplate}" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ContentView.Style>
            </ContentView>
        </AbsoluteLayout>
    </ContentView.Content>
</ContentView>