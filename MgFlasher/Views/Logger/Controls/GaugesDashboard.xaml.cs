﻿using MgFlasher.Behaviors;
using MgFlasher.ViewModels.Logger;
using MgFlasher.Views.Logger.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using MgFlasher.Helpers;
using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using Microsoft.Maui;
using Microsoft.Maui.Graphics;
using Syncfusion.Maui.DataSource.Extensions;

namespace MgFlasher.Views.Logger.Controls;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class GaugesDashboard : ContentView, IDisposable
{
    private readonly ILogger<GaugesDashboard> _logger;
    private readonly GaugesDashboardTouchManager _touchManager;
    private bool _disposed;

    public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameof(ItemsSource), typeof(ObservableCollection<GaugeItemViewModel>), typeof(GaugesDashboard), null, propertyChanged: OnItemsSourcePropertyChanged);

    public ObservableCollection<GaugeItemViewModel> ItemsSource
    {
        get { return (ObservableCollection<GaugeItemViewModel>)GetValue(ItemsSourceProperty); }
        set { SetValue(ItemsSourceProperty, value); }
    }

    public static readonly BindableProperty IsEmptySourceProperty = BindableProperty.Create(nameof(IsEmptySource), typeof(bool), typeof(GaugesDashboard), true);

    public bool IsEmptySource
    {
        get => (bool)GetValue(IsEmptySourceProperty);
        set => SetValue(IsEmptySourceProperty, value);
    }

    public event EventHandler<IReadOnlyList<GaugeItemViewModel>> ItemsAutoAdjusted;

    public GaugesDashboard()
    {
        InitializeComponent();

        _touchManager = new GaugesDashboardTouchManager();
        _logger = DependencyResolver.Resolve<ILogger<GaugesDashboard>>();
    }

    public void Dispose()
    {
        _disposed = true;

        if (ItemsSource != null)
        {
            ItemsSource.ForEach(s => s.PropertyChanged -= OnGaugeItemViewModelPropertyChanged);
            ItemsSource.CollectionChanged -= OnGaugeItemCollectionChanged;
        }

        _touchManager.Dispose();
        if (layout?.Children != null)
        {
            layout.Children.OfType<VisualElement>().ForEach(x => x.Behaviors.Clear());
            layout.Children.OfType<Element>().ForEach(x => x.Effects.OfType<TouchEffect>().ForEach(x => x.TouchAction -= OnGaugeTouchEffectAction));
            layout.Children.OfType<Element>().ForEach(x => x.Effects.Clear());
            layout.Children.Clear();
        }
    }

    private void SetDisplayedItemsSource()
    {
        if (_disposed)
        {
            return;
        }

        if (!IsLayoutPositionerInitialized())
        {
            return;
        }

        var existing = layout.Children
            .OfType<GaugeViewContainer>()
            .ToDictionary(x => x, x => x.BindingContext as GaugeItemViewModel)
            .OrderBy(x => x.Value?.Shape?.Color == Colors.Transparent.ToArgbHex() ? -1 : 1);

        var @new = ItemsSource?
            .Where(x => x.Visible)
            .OrderBy(x => x.Shape?.Color == Colors.Transparent.ToArgbHex() ? -1 : 1)
            .ToList() ?? Enumerable.Empty<GaugeItemViewModel>();

        var toBeDeleted = existing.Where(e => !@new.Any(x => x.ModuleId == e.Value.ModuleId && x.DefinitionCode == e.Value.DefinitionCode));
        foreach (var toBeDeletedItem in toBeDeleted.ToList())
        {
            RemoveItem(toBeDeletedItem.Key);
        }

        var toBeAdded = @new
            .Where(x => !existing.Any(e => x.ModuleId == e.Value.ModuleId && x.DefinitionCode == e.Value.DefinitionCode))
            .ToList();
        foreach (var toBeAddedItem in toBeAdded)
        {
            AddItem(toBeAddedItem);
        }

        var toBeUpdated = existing
            .ToDictionary(x =>
                    (x.Key, x.Value),
                x => ItemsSource?.FirstOrDefault(s => s.ModuleId == x.Value.ModuleId && s.DefinitionCode == x.Value.DefinitionCode))
            .Where(x => x.Key.Value != x.Value)
            .ToList();
        foreach (var toBeUpdatedItem in toBeUpdated)
        {
            toBeUpdatedItem.Key.Key.BindingContext = toBeUpdatedItem.Value;
        }

        AutoAdjustIfRequired();
    }

    private void OnGaugeItemViewModelPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        var vm = sender as GaugeItemViewModel;
        if (e.PropertyName == nameof(GaugeItemViewModel.Visible))
        {
            if (vm.Visible && !layout.Children.OfType<BindableObject>().Any(x => x.BindingContext == vm))
            {
                if (!IsLayoutPositionerInitialized())
                {
                    return;
                }

                AddItem(vm);
            }
            else if (!vm.Visible && layout.Children.OfType<BindableObject>().FirstOrDefault(x => x.BindingContext == vm) is GaugeViewContainer container)
            {
                if (!IsLayoutPositionerInitialized())
                {
                    return;
                }

                RemoveItem(container);
            }
        }
    }

    private static void OnItemsSourcePropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var dashboard = bindable as GaugesDashboard;

        if (oldValue is ObservableCollection<GaugeItemViewModel> oldItems)
        {
            oldItems.CollectionChanged -= dashboard.OnGaugeItemCollectionChanged;
            oldItems.ForEach(s => s.PropertyChanged -= dashboard.OnGaugeItemViewModelPropertyChanged);
        }

        if (newValue is ObservableCollection<GaugeItemViewModel> newItems)
        {
            newItems.CollectionChanged += dashboard.OnGaugeItemCollectionChanged;
            newItems.ForEach(s => s.PropertyChanged += dashboard.OnGaugeItemViewModelPropertyChanged);

            dashboard.SetDisplayedItemsSource();
        }
    }

    private void OnGaugeItemCollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
    {
        if (_disposed)
        {
            return;
        }

        var existing = layout.Children
            .OfType<GaugeViewContainer>()
            .ToDictionary(x => x, x => x.BindingContext as GaugeItemViewModel);

        if (e.Action == NotifyCollectionChangedAction.Add)
        {
            foreach (var toBeAdded in e.NewItems.OfType<GaugeItemViewModel>().Where(x => x.Visible))
            {
                AddItem(toBeAdded);
            }
        }
        else if (e.Action == NotifyCollectionChangedAction.Remove)
        {
            var toDelete = existing
                .Where(x => x.Value is null || e.OldItems.OfType<GaugeItemViewModel>()
                    .Any(o => o.ModuleId == x.Value.ModuleId && o.DefinitionCode == x.Value.DefinitionCode))
                .ToList();

            foreach (var toBeDeleted in toDelete)
            {
                RemoveItem(toBeDeleted.Key);
            }
        }
    }

    private void OnGaugeTouchEffectAction(object sender, TouchActionEventArgs args)
    {
        if (_disposed)
        {
            return;
        }

        try
        {
            var container = sender as GaugeViewContainer;
            _touchManager.ContainerTouched(container, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "OnGaugeTouchEffectAction error");
        }
    }

    private void AddItem(GaugeItemViewModel toBeAddedItem)
    {
        var container = new GaugeViewContainer()
        {
            BindingContext = toBeAddedItem
        };
        var touchEffect = new TouchEffect();
        touchEffect.TouchAction += OnGaugeTouchEffectAction;
        container.Effects.Add(touchEffect);
        layout.Children.Add(container);
        UpdateIsEmptySource();
    }

    private void RemoveItem(GaugeViewContainer container)
    {
        layout.Children.Remove(container);

        UpdateIsEmptySource();
    }

    public void OnAppearing()
    {
        GridAutoLayoutPositionerPlatformSettings.CalculateSize(this.Parent as View);

        if (layout.Children.Count == 0)
        {
            SetDisplayedItemsSource();
        }
    }

    protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        base.OnPropertyChanged(propertyName);

        if (propertyName == nameof(IsVisible))
        {
            OnAppearing();
        }
    }

    private void UpdateIsEmptySource()
    {
        IsEmptySource = layout.Children.Count == 0;
    }

    private bool IsLayoutPositionerInitialized()
    {
        if (!GridAutoLayoutPositionerPlatformSettings.Initialized)
        {
            GridAutoLayoutPositionerPlatformSettings.CalculateSize(this.Parent as View);
        }

        return GridAutoLayoutPositionerPlatformSettings.Initialized;
    }

    private void AutoAdjustIfRequired()
    {
        var positioner = new GaugeViewLayoutPositioner();
        var updated = layout.Children.OfType<GaugeViewContainer>().Select(x => x.BindingContext as GaugeItemViewModel).ToList();
        if (!positioner.ShouldAutoAdjust(updated))
        {
            return;
        }

        positioner.AutoAdjustByGrid(updated);
        var visible = updated.Where(x => x.Visible && x.Shape != null).ToList();
        ItemsAutoAdjusted?.Invoke(this, visible);
    }
}