<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MgFlasher.Views.Logger.Controls.LoggerAlertsModuleView"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    xmlns:logBehaviors="clr-namespace:MgFlasher.Views.Logger.Behaviors"
    x:DataType="vms:LoggerAlertsModuleViewModel">
    <ContentView.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <controls:SearchableSfListView
                Margin="0" Grid.Row="0"
                IsVisible="{Binding IsModuleEnabled, Mode=OneWay}"
                ItemSize="{OnIdiom Phone={OnPlatform Default=70}, Desktop={OnPlatform Default=70}, Default={OnPlatform Android=75, Default=65}}"
                ItemsSource="{Binding Items, Mode=OneWay}">
                <controls:SearchableSfListView.ItemTemplate>
                    <DataTemplate x:DataType="vms:LoggerAlertDefinitionConfigurationViewModel">
                        <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="7.2*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <StackLayout
                                Orientation="Vertical" Grid.Column="0" Spacing="7"
                                VerticalOptions="FillAndExpand"
                                HorizontalOptions="FillAndExpand">
                                <Label FontSize="{StaticResource SecondaryFontSize}" LineBreakMode="NoWrap" Text="{Binding DisplayName, Mode=OneWay}" HorizontalOptions="FillAndExpand" />
                                <StackLayout Orientation="Horizontal" Spacing="5" HorizontalOptions="FillAndExpand">
                                    <controls:SmallButton Command="{Binding ChangeEnabledStateCommand}">
                                        <controls:SmallButton.Behaviors>
                                            <logBehaviors:LoggerDisplayButtonColorBehavior Value="{Binding Enabled}" />
                                        </controls:SmallButton.Behaviors>
                                        <controls:SmallButton.Resources>
                                            <ResourceDictionary>
                                                <Style TargetType="controls:SmallButton" BasedOn="{StaticResource RoundedButtonMgStyle}">
                                                    <Setter Property="Text" Value="{i18n:Translate LoggerAlertsModuleView_Disabled}" />
                                                    <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                    <Style.Triggers>
                                                        <DataTrigger TargetType="controls:SmallButton" Binding="{Binding Enabled}" Value="True" x:DataType="vms:LoggerAlertDefinitionConfigurationViewModel">
                                                            <Setter Property="Text" Value="{i18n:Translate LoggerAlertsModuleView_Enabled}" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </ResourceDictionary>
                                        </controls:SmallButton.Resources>
                                    </controls:SmallButton>
                                    <controls:SmallButton Text="{Binding AlertType.Translation}" Style="{StaticResource RoundedButtonMgStyle}" Command="{Binding ChangeAlertTypeCommand}">
                                        <controls:SmallButton.Behaviors>
                                            <logBehaviors:LoggerDisplayButtonColorBehavior Value="{Binding AlertType.PreferenceType}" />
                                        </controls:SmallButton.Behaviors>
                                    </controls:SmallButton>
                                    <controls:SmallButton Text="{Binding Arguments}" Style="{StaticResource RoundedButtonMgStyle}" BackgroundColor="{StaticResource PrimaryImportantTextColor}" />
                                </StackLayout>
                            </StackLayout>
                            <Image
                                Grid.Column="1" Aspect="AspectFit" HeightRequest="40"
                                HorizontalOptions="Center" Source="iconexit.png"
                                VerticalOptions="Center" WidthRequest="40">
                                <Image.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding GoToAlertSettingsCommand}" />
                                </Image.GestureRecognizers>
                            </Image>
                        </Grid>
                    </DataTemplate>
                </controls:SearchableSfListView.ItemTemplate>
            </controls:SearchableSfListView>
            <Label
                Text="{i18n:Translate LoggerAlertsModuleView_ModuleNotSupportedOrNotEnabled}"
                Grid.Row="0" HorizontalOptions="Center"
                VerticalOptions="Center"
                HorizontalTextAlignment="Center"
                IsVisible="{Binding IsModuleEnabled, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}" />
        </Grid>
    </ContentView.Content>
</ContentView>