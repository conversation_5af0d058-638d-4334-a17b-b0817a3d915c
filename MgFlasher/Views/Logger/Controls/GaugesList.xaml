<ContentView
    x:Class="MgFlasher.Views.Logger.Controls.GaugesList"
    xmlns:views="clr-namespace:MgFlasher.Views.Logger.Controls"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Name="RootView"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ProgressBar;assembly=Syncfusion.Maui.ProgressBar"
    xmlns:vm="clr-namespace:MgFlasher.ViewModels.Logger"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers">
    <ContentView.Content>
        <Grid
            Margin="0" Padding="0"
            BindingContext="{x:Reference Name=RootView}"
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <SearchBar
                Grid.Row="0"
                Placeholder="{i18n:Translate Logger_FilterParameters}"
                TextColor="White" TextChanged="OnSearchBarTextChanged"
                Margin="0,0,0,8" TextTransform="Uppercase" />
            <CollectionView Grid.Row="1" ItemsSource="{Binding VisibleItems}">
                <CollectionView.ItemTemplate x:DataType="vm:GaugeItemViewModel">
                    <DataTemplate>
                        <Grid Grid.Row="0" ColumnSpacing="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="{OnIdiom Phone=*, Desktop=1.5*}" />
                                <ColumnDefinition Width="{OnIdiom Phone=0, Desktop=*}" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid Grid.Column="0" Grid.Row="0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Label
                                    Grid.Row="0" Margin="0,0,0,0" LineBreakMode="NoWrap"
                                    FontSize="{StaticResource GaugeFontSize}"
                                    FontAttributes="Bold"
                                    Text="{Binding MGName, Mode=OneWay}" />
                                <Label
                                    Grid.Row="1" Margin="0,0,0,0" LineBreakMode="NoWrap"
                                    FontSize="{StaticResource GaugeFontSize}"
                                    FontAttributes="Italic"
                                    Text="{Binding A2LName, Mode=OneWay}" />
                            </Grid>
                            <Label
                                Grid.Row="{OnIdiom Phone=1, Desktop=0}"
                                Grid.Column="{OnIdiom Phone=0, Desktop=1}"
                                LineBreakMode="NoWrap"
                                FontSize="{StaticResource ExtraSemiLargeFontSize}"
                                TextColor="{StaticResource PrimaryTextColor}">
                                <Label.Text>
                                    <MultiBinding StringFormat="{}{0} {1}">
                                        <Binding Path="CurrentValueString" Mode="OneWay" />
                                        <Binding Path="Unit" Mode="OneWay" />
                                    </MultiBinding>
                                </Label.Text>
                            </Label>
                            <BoxView Grid.Row="{OnIdiom Phone=2, Desktop=1}" Grid.ColumnSpan="2" HeightRequest="1" BackgroundColor="Gray" />
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </Grid>
    </ContentView.Content>
</ContentView>