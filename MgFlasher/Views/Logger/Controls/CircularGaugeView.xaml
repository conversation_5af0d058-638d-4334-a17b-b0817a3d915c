<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="MgFlasher.Views.Logger.Controls.CircularGaugeView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:gauge="clr-namespace:Syncfusion.Maui.Gauges;assembly=Syncfusion.Maui.Gauges"
    xmlns:local="clr-namespace:MgFlasher.Views.Logger.Controls"
    x:Name="RootGaugeView"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    x:DataType="vms:GaugeItemViewModel">
    <ContentView.Resources>
        <Style x:Key="GaugeAxisLabel" TargetType="Label">
            <Setter Property="FontAttributes" Value="Bold" />
            <Setter Property="TextColor" Value="{StaticResource GaugeRimExternalRing}" />
            <Setter Property="FontSize" Value="{StaticResource SmallGaugeFontSize}" />
        </Style>
        <x:Double x:Key="SessionMaxPointerWidth">1</x:Double>
        <x:Double x:Key="SessionMaxPointerHeight">13</x:Double>
        <x:Double x:Key="TriangleSize">10</x:Double>
        <x:Double x:Key="RectanglePointerHeight">13</x:Double>
        <x:Double x:Key="RectanglePointerWidth">3</x:Double>
        <x:String x:Key="MainColor">GaugeRangePointerMainColor</x:String>
        <x:String x:Key="ShadingColor">GaugeRangePointerShadingColor</x:String>
        <x:String x:Key="ShapePointerColor">GaugeRangeShapePointerColor</x:String>
    </ContentView.Resources>
    <ContentView.Content>
        <Grid RowSpacing="0" Padding="0,0,0,0" Margin="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="0.116*" />
            </Grid.RowDefinitions>
            <gauge:SfRadialGauge Grid.Row="0" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand" BackgroundColor="Transparent">
                <gauge:SfRadialGauge.Axes>
                    <!--  Main axis of the gauge  -->
                    <gauge:RadialAxis
                        RadiusFactor="1" ShowLabels="False" ShowTicks="False"
                        BackgroundColor="Transparent" StartAngle="155"
                        EndAngle="25"
                        Minimum="{x:Static local:CircularGaugeViewConstants.CircularMin}"
                        Maximum="{x:Static local:CircularGaugeViewConstants.CircularMax}">
                        <gauge:RadialAxis.Annotations>
                            <gauge:GaugeAnnotation DirectionValue="{OnPlatform iOS=85, Default=90}" PositionFactor="0" HorizontalAlignment="Center">
                                <gauge:GaugeAnnotation.Content>
                                    <!--  Current value of the parameter, center of display  -->
                                    <Label
                                        LineBreakMode="NoWrap"
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center"
                                        MinimumWidthRequest="{OnPlatform iOS=75}"
                                        x:DataType="vms:GaugeItemViewModel"
                                        FontSize="{StaticResource ExtraSemiLargeFontSize}"
                                        TextColor="{StaticResource PrimaryTextColor}"
                                        Text="{Binding CurrentValueString, Mode=OneWay}" />
                                </gauge:GaugeAnnotation.Content>
                            </gauge:GaugeAnnotation>
                            <gauge:GaugeAnnotation DirectionValue="90" PositionFactor="{OnPlatform iOS=0.25, Default=0.27}">
                                <gauge:GaugeAnnotation.Content>
                                    <!--  Unit of the parameter  -->
                                    <Label
                                        x:DataType="vms:GaugeItemViewModel"
                                        FontAttributes="Italic"
                                        Text="{Binding Unit, Mode=OneWay}"
                                        TextColor="{StaticResource PrimaryTextColor}"
                                        FontSize="{StaticResource GaugeFontSize}" />
                                </gauge:GaugeAnnotation.Content>
                            </gauge:GaugeAnnotation>
                            <gauge:GaugeAnnotation DirectionValue="145" PositionFactor="0.80" HorizontalAlignment="Center">
                                <gauge:GaugeAnnotation.Content>
                                    <!--  Minimum value in this session  -->
                                    <Label
                                        FontAttributes="Italic"
                                        TextColor="{StaticResource PrimaryTextColor}"
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center"
                                        FontSize="{StaticResource GaugeFontSize}"
                                        LineBreakMode="NoWrap">
                                        <Label.Text>
                                            <MultiBinding Converter="{StaticResource ValueWithDisplayPrecisionConverter}">
                                                <Binding Path="SessionMin" Mode="OneWay" />
                                                <Binding Path="DisplayPrecision" Mode="OneWay" />
                                                <Binding Path="PresentHexValues" Mode="OneWay" />
                                            </MultiBinding>
                                        </Label.Text>
                                    </Label>
                                </gauge:GaugeAnnotation.Content>
                            </gauge:GaugeAnnotation>
                            <gauge:GaugeAnnotation DirectionValue="155" PositionFactor="0.50" HorizontalAlignment="Center">
                                <gauge:GaugeAnnotation.Content>
                                    <!--  First tick label - 0%  -->
                                    <Label
                                        Style="{StaticResource GaugeAxisLabel}"
                                        LineBreakMode="NoWrap"
                                        MinimumWidthRequest="{OnPlatform iOS=15}"
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center">
                                        <Label.Text>
                                            <MultiBinding Converter="{StaticResource ValueWithDisplayPrecisionConverter}">
                                                <Binding Path="Min" Mode="OneWay" />
                                                <Binding Path="DisplayPrecision" Mode="OneWay" />
                                                <Binding Path="PresentHexValues" Mode="OneWay" />
                                            </MultiBinding>
                                        </Label.Text>
                                    </Label>
                                </gauge:GaugeAnnotation.Content>
                            </gauge:GaugeAnnotation>
                            <gauge:GaugeAnnotation DirectionUnit="AxisValue" DirectionValue="24" PositionFactor="0.50" HorizontalAlignment="Center">
                                <gauge:GaugeAnnotation.Content>
                                    <!--  Second tick label - 20%  -->
                                    <Label
                                        Style="{StaticResource GaugeAxisLabel}"
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center"
                                        MinimumWidthRequest="{OnPlatform iOS=15}"
                                        LineBreakMode="NoWrap">
                                        <Label.Text>
                                            <MultiBinding Converter="{StaticResource MinMaxPercentileConverter}" ConverterParameter="0.2">
                                                <Binding Path="Min" Mode="OneWay" />
                                                <Binding Path="Max" Mode="OneWay" />
                                                <Binding Path="DisplayPrecision" Mode="OneWay" />
                                            </MultiBinding>
                                        </Label.Text>
                                    </Label>
                                </gauge:GaugeAnnotation.Content>
                            </gauge:GaugeAnnotation>
                            <gauge:GaugeAnnotation DirectionUnit="AxisValue" DirectionValue="48" PositionFactor="0.50" HorizontalAlignment="Center">
                                <gauge:GaugeAnnotation.Content>
                                    <!--  Third tick label - 40%  -->
                                    <Label
                                        Style="{StaticResource GaugeAxisLabel}"
                                        LineBreakMode="NoWrap"
                                        MinimumWidthRequest="{OnPlatform iOS=15}"
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center">
                                        <Label.Text>
                                            <MultiBinding Converter="{StaticResource MinMaxPercentileConverter}" ConverterParameter="0.4">
                                                <Binding Path="Min" Mode="OneWay" />
                                                <Binding Path="Max" Mode="OneWay" />
                                                <Binding Path="DisplayPrecision" Mode="OneWay" />
                                            </MultiBinding>
                                        </Label.Text>
                                    </Label>
                                </gauge:GaugeAnnotation.Content>
                            </gauge:GaugeAnnotation>
                            <gauge:GaugeAnnotation DirectionUnit="AxisValue" DirectionValue="72" PositionFactor="0.50" HorizontalAlignment="Center">
                                <gauge:GaugeAnnotation.Content>
                                    <!--  Fourth tick label - 60%  -->
                                    <Label
                                        Style="{StaticResource GaugeAxisLabel}"
                                        LineBreakMode="NoWrap"
                                        MinimumWidthRequest="{OnPlatform iOS=15}"
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center">
                                        <Label.Text>
                                            <MultiBinding Converter="{StaticResource MinMaxPercentileConverter}" ConverterParameter="0.6">
                                                <Binding Path="Min" Mode="OneWay" />
                                                <Binding Path="Max" Mode="OneWay" />
                                                <Binding Path="DisplayPrecision" Mode="OneWay" />
                                            </MultiBinding>
                                        </Label.Text>
                                    </Label>
                                </gauge:GaugeAnnotation.Content>
                            </gauge:GaugeAnnotation>
                            <gauge:GaugeAnnotation DirectionUnit="AxisValue" DirectionValue="96" PositionFactor="0.50" HorizontalAlignment="Center">
                                <gauge:GaugeAnnotation.Content>
                                    <!--  Fifth tick label - 80%  -->
                                    <Label
                                        Style="{StaticResource GaugeAxisLabel}"
                                        MinimumWidthRequest="{OnPlatform iOS=15}"
                                        LineBreakMode="NoWrap"
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center">
                                        <Label.Text>
                                            <MultiBinding Converter="{StaticResource MinMaxPercentileConverter}" ConverterParameter="0.8">
                                                <Binding Path="Min" Mode="OneWay" />
                                                <Binding Path="Max" Mode="OneWay" />
                                                <Binding Path="DisplayPrecision" Mode="OneWay" />
                                            </MultiBinding>
                                        </Label.Text>
                                    </Label>
                                </gauge:GaugeAnnotation.Content>
                            </gauge:GaugeAnnotation>
                            <gauge:GaugeAnnotation DirectionValue="25" PositionFactor="0.45" HorizontalAlignment="End">
                                <gauge:GaugeAnnotation.Content>
                                    <!--  Last tick label - 100%  -->
                                    <Label Style="{StaticResource GaugeAxisLabel}" LineBreakMode="NoWrap" HorizontalOptions="FillAndExpand" HorizontalTextAlignment="Center">
                                        <Label.Text>
                                            <MultiBinding Converter="{StaticResource ValueWithDisplayPrecisionConverter}" x:DataType="vms:GaugeItemViewModel">
                                                <Binding Path="Max" Mode="OneWay" />
                                                <Binding Path="DisplayPrecision" Mode="OneWay" />
                                                <Binding Path="PresentHexValues" Mode="OneWay" />
                                            </MultiBinding>
                                        </Label.Text>
                                    </Label>
                                </gauge:GaugeAnnotation.Content>
                            </gauge:GaugeAnnotation>
                            <gauge:GaugeAnnotation DirectionValue="42.5" PositionFactor="0.70" HorizontalAlignment="Center">
                                <gauge:GaugeAnnotation.Content>
                                    <!--  Maximum value in this session  -->
                                    <Label
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center" LineBreakMode="NoWrap"
                                        FontAttributes="Italic"
                                        MinimumWidthRequest="{OnPlatform iOS=25}"
                                        TextColor="{StaticResource PrimaryTextColor}"
                                        FontSize="{StaticResource GaugeFontSize}">
                                        <Label.Text>
                                            <MultiBinding Converter="{StaticResource ValueWithDisplayPrecisionConverter}">
                                                <Binding Path="SessionMax" Mode="OneWay" />
                                                <Binding Path="DisplayPrecision" Mode="OneWay" />
                                                <Binding Path="PresentHexValues" Mode="OneWay" />
                                            </MultiBinding>
                                        </Label.Text>
                                    </Label>
                                </gauge:GaugeAnnotation.Content>
                            </gauge:GaugeAnnotation>
                        </gauge:RadialAxis.Annotations>
                        <gauge:RadialAxis.AxisLineStyle>
                            <gauge:RadialLineStyle Fill="{StaticResource GaugeAxisLineColor}" ThicknessUnit="Factor" Thickness="0.20" />
                        </gauge:RadialAxis.AxisLineStyle>
                        <gauge:RadialAxis.Ranges>
                            <!--  Main moving gauge pointer  -->
                            <gauge:RadialRange
                                StartValue="{x:Static local:CircularGaugeViewConstants.CircularMin}"
                                EndValue="{x:Static local:CircularGaugeViewConstants.CircularMax}"
                                Fill="{StaticResource GaugeRimInternalRing}"
                                WidthUnit="Factor" StartWidth="0.01" EndWidth="0.01"
                                OffsetUnit="Factor" RangeOffset="0.08" />
                            <!--  Shading color of the gauge pointer  -->
                            <gauge:RadialRange
                                StartValue="{x:Static local:CircularGaugeViewConstants.CircularMin}"
                                EndValue="{x:Static local:CircularGaugeViewConstants.CircularMax}"
                                Fill="{StaticResource GaugeRimExternalRing}"
                                WidthUnit="Factor" StartWidth="0.01" EndWidth="0.01"
                                OffsetUnit="Factor" RangeOffset="0.05" />
                        </gauge:RadialAxis.Ranges>
                        <gauge:RadialAxis.Pointers>
                            <gauge:RangePointer
                                OffsetUnit="Factor" PointerOffset="0.16"
                                WidthUnit="Factor" PointerWidth="0.06"
                                x:DataType="vms:GaugeItemViewModel"
                                EnableAnimation="{Binding IsAnimationEnabled, Mode=OneWay}"
                                AnimationDuration="{Binding AnimationDuration, Mode=OneWay}"
                                Fill="{Binding GaugeColor, Converter={StaticResource GaugeColorConverter}, ConverterParameter={StaticResource MainColor}}"
                                Value="{Binding CircularValue, Mode=OneWay, Converter={StaticResource CircularValueConverter}}" />
                            <gauge:RangePointer
                                OffsetUnit="Factor" PointerOffset="0.22"
                                WidthUnit="Factor" PointerWidth="0.10"
                                x:DataType="vms:GaugeItemViewModel"
                                EnableAnimation="{Binding IsAnimationEnabled, Mode=OneWay}"
                                AnimationDuration="{Binding AnimationDuration, Mode=OneWay}"
                                Fill="{Binding GaugeColor, Converter={StaticResource GaugeColorConverter}, ConverterParameter={StaticResource ShadingColor}}"
                                Value="{Binding CircularValue, Mode=OneWay, Converter={StaticResource CircularValueConverter}}" />

                            <gauge:ShapePointer
                                Value="0.5" ShapeType="InvertedTriangle"
                                BorderWidth="1"
                                Stroke="{StaticResource GaugeRimInternalRing}"
                                OffsetUnit="Factor" Offset="0" ShapeWidth="1.5"
                                ShapeHeight="1.5"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="12" ShapeType="InvertedTriangle" BorderWidth="1"
                                Stroke="{StaticResource GaugeRimInternalRing}"
                                OffsetUnit="Factor" Offset="0" ShapeWidth="3"
                                ShapeHeight="3"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="24" ShapeType="InvertedTriangle" BorderWidth="1"
                                Stroke="{StaticResource GaugeRimInternalRing}"
                                OffsetUnit="Factor" Offset="0" ShapeWidth="1.5"
                                ShapeHeight="1.5"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="36" ShapeType="InvertedTriangle" BorderWidth="1"
                                Stroke="{StaticResource GaugeRimInternalRing}"
                                OffsetUnit="Factor" Offset="0" ShapeWidth="3"
                                ShapeHeight="3"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="48" ShapeType="InvertedTriangle" BorderWidth="1"
                                Stroke="{StaticResource GaugeRimInternalRing}"
                                OffsetUnit="Factor" Offset="0" ShapeWidth="1.5"
                                ShapeHeight="1.5"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="60" ShapeType="InvertedTriangle" BorderWidth="1"
                                Stroke="{StaticResource GaugeRimInternalRing}"
                                OffsetUnit="Factor" Offset="0" ShapeWidth="3"
                                ShapeHeight="3"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="72" ShapeType="InvertedTriangle" BorderWidth="1"
                                Stroke="{StaticResource GaugeRimInternalRing}"
                                OffsetUnit="Factor" Offset="0" ShapeWidth="1.5"
                                ShapeHeight="1.5"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="84" ShapeType="InvertedTriangle" BorderWidth="1"
                                Stroke="{StaticResource GaugeRimInternalRing}"
                                OffsetUnit="Factor" Offset="0" ShapeWidth="3"
                                ShapeHeight="3"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="96" ShapeType="InvertedTriangle" BorderWidth="1"
                                Stroke="{StaticResource GaugeRimInternalRing}"
                                OffsetUnit="Factor" Offset="0" ShapeWidth="1.5"
                                ShapeHeight="1.5"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="108" ShapeType="InvertedTriangle"
                                BorderWidth="1"
                                Stroke="{StaticResource GaugeRimInternalRing}"
                                OffsetUnit="Factor" Offset="0" ShapeWidth="3"
                                ShapeHeight="3"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="119.5" ShapeType="InvertedTriangle"
                                BorderWidth="1"
                                Stroke="{StaticResource GaugeRimInternalRing}"
                                OffsetUnit="Factor" Offset="0" ShapeWidth="1.5"
                                ShapeHeight="1.5"
                                Fill="{StaticResource GaugeBackgroundColor}" />

                            <gauge:ShapePointer
                                Value="12" ShapeType="Rectangle" OffsetUnit="Factor"
                                Offset="0.14" ShapeWidth="1" ShapeHeight="13"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="24" ShapeType="Rectangle" OffsetUnit="Factor"
                                Offset="0.14" ShapeWidth="1" ShapeHeight="13"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="36" ShapeType="Rectangle" OffsetUnit="Factor"
                                Offset="0.14" ShapeWidth="1" ShapeHeight="13"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="48" ShapeType="Rectangle" OffsetUnit="Factor"
                                Offset="0.14" ShapeWidth="1" ShapeHeight="13"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="60" ShapeType="Rectangle" OffsetUnit="Factor"
                                Offset="0.14" ShapeWidth="1" ShapeHeight="13"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="72" ShapeType="Rectangle" OffsetUnit="Factor"
                                Offset="0.14" ShapeWidth="1" ShapeHeight="13"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="84" ShapeType="Rectangle" OffsetUnit="Factor"
                                Offset="0.14" ShapeWidth="1" ShapeHeight="13"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="96" ShapeType="Rectangle" OffsetUnit="Factor"
                                Offset="0.14" ShapeWidth="1" ShapeHeight="13"
                                Fill="{StaticResource GaugeBackgroundColor}" />
                            <gauge:ShapePointer
                                Value="108" ShapeType="Rectangle" OffsetUnit="Factor"
                                Offset="0.14" ShapeWidth="1" ShapeHeight="13"
                                Fill="{StaticResource GaugeBackgroundColor}" />

                            <gauge:ShapePointer
                                Value="{Binding CircularValue, Mode=OneWay, Converter={StaticResource CircularValueConverter}}"
                                Fill="{Binding GaugeColor, Converter={StaticResource GaugeColorConverter}, ConverterParameter={StaticResource ShapePointerColor}}"
                                AnimationDuration="{Binding AnimationDuration, Mode=OneWay}"
                                EnableAnimation="{Binding IsAnimationEnabled, Mode=OneWay}"
                                ShapeType="Rectangle" OffsetUnit="Factor"
                                HasShadow="True"
                                ShapeHeight="{StaticResource RectanglePointerHeight}"
                                ShapeWidth="{StaticResource RectanglePointerWidth}"
                                Offset="0.14" />
                            <gauge:ShapePointer
                                Value="{Binding CircularValue, Mode=OneWay, Converter={StaticResource CircularValueConverter}}"
                                Fill="{Binding GaugeColor, Converter={StaticResource GaugeColorConverter}, ConverterParameter={StaticResource ShapePointerColor}}"
                                AnimationDuration="{Binding AnimationDuration, Mode=OneWay}"
                                EnableAnimation="{Binding IsAnimationEnabled, Mode=OneWay}"
                                ShapeType="InvertedTriangle" OffsetUnit="Pixel"
                                Offset="-0.7" HasShadow="True"
                                ShapeHeight="{StaticResource TriangleSize}"
                                ShapeWidth="{StaticResource TriangleSize}" />
                            <gauge:ShapePointer
                                Value="{Binding SessionMaxPointer, Mode=OneWay, Converter={StaticResource CircularValueConverter}}"
                                Fill="{Binding GaugeColor, Converter={StaticResource GaugeColorConverter}, ConverterParameter={StaticResource ShapePointerColor}}"
                                AnimationDuration="{Binding AnimationDuration, Mode=OneWay}"
                                EnableAnimation="{Binding IsAnimationEnabled, Mode=OneWay}"
                                ShapeType="Rectangle" OffsetUnit="Factor"
                                HasShadow="False"
                                ShapeHeight="{StaticResource SessionMaxPointerHeight}"
                                ShapeWidth="{StaticResource SessionMaxPointerWidth}"
                                Offset="0.14" />
                        </gauge:RadialAxis.Pointers>
                    </gauge:RadialAxis>
                </gauge:SfRadialGauge.Axes>
            </gauge:SfRadialGauge>
            <!--  Name of the parameter  -->
            <Label
                Grid.Row="1" Margin="0,0,0,0"
                VerticalOptions="CenterAndExpand"
                VerticalTextAlignment="Center"
                HorizontalTextAlignment="Center"
                HorizontalOptions="CenterAndExpand"
                LineBreakMode="NoWrap"
                FontSize="{StaticResource GaugeFontSize}"
                Text="{Binding TitleShortened, Mode=OneWay}" />
        </Grid>
    </ContentView.Content>
</ContentView>