<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:input="clr-namespace:InputKit.Shared.Controls;assembly=InputKit.Maui"
    x:Class="MgFlasher.Views.Logger.Controls.LoggerDisplaySelectColorView"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    x:DataType="vms:LoggerDisplayPageViewModel">
    <VerticalStackLayout>
        <input:RadioButtonGroupView RadioButtonGroup.GroupName="colorsSet">
            <input:RadioButton Text="Grey" IsChecked="{Binding IsGreySelected}" TextFontSize="{StaticResource SecondaryFontSize}" />
            <input:RadioButton Text="Red" IsChecked="{Binding IsRedSelected}" TextFontSize="{StaticResource SecondaryFontSize}" />
            <input:RadioButton Text="Blue" IsChecked="{Binding IsBlueSelected}" TextFontSize="{StaticResource SecondaryFontSize}" />
        </input:RadioButtonGroupView>
    </VerticalStackLayout>
</ContentView>