﻿using MgFlasher.ViewModels.Logger;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Runtime.CompilerServices;
using MgFlasher.Helpers;
using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using Syncfusion.Maui.DataSource.Extensions;
using System.Threading;
using System.Collections.Specialized;
using System.ComponentModel;

namespace MgFlasher.Views.Logger.Controls;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class GaugesList : ContentView
{
    private readonly ILogger<GaugesList> _logger;

    public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameof(ItemsSource), typeof(ObservableCollection<GaugeItemViewModel>), typeof(GaugesList), null, propertyChanged: OnItemsSourcePropertyChanged);

    private string _filter = string.Empty;

    public ObservableCollection<GaugeItemViewModel> ItemsSource
    {
        get { return (ObservableCollection<GaugeItemViewModel>)GetValue(ItemsSourceProperty); }

        set
        {
            SetValue(ItemsSourceProperty, value);
            OnPropertyChanged(nameof(VisibleItems));
        }
    }

    public ObservableCollection<GaugeItemViewModel> VisibleItems
    {
        get
        {
            if (ItemsSource == null)
            {
                return new ObservableCollection<GaugeItemViewModel>();
            }

            if (_filter == string.Empty)
            {
                return ItemsSource.Where(x => x.Visible).ToObservableCollection();
            }
            else
            {
                return ItemsSource
                    .Where(x => x.Visible)
                    .Where(x => x.MGName.Contains(_filter, StringComparison.InvariantCultureIgnoreCase)
                             || x.A2LName.Contains(_filter, StringComparison.InvariantCultureIgnoreCase))
                    .ToObservableCollection();
            }
        }
    }

    public static readonly BindableProperty IsEmptySourceProperty = BindableProperty.Create(nameof(IsEmptySource), typeof(bool), typeof(GaugesList), true);

    public bool IsEmptySource
    {
        get => (bool)GetValue(IsEmptySourceProperty);
        set => SetValue(IsEmptySourceProperty, value);
    }

    public GaugesList()
    {
        InitializeComponent();

        _logger = DependencyResolver.Resolve<ILogger<GaugesList>>();
    }

    protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        base.OnPropertyChanged(propertyName);
        if (propertyName == nameof(ItemsSource))
        {
            OnPropertyChanged(nameof(VisibleItems));
        }
    }

    private void SetDisplayedItemsSource()
    {
        OnPropertyChanged(nameof(VisibleItems));
    }

    private void OnGaugeItemViewModelPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        var vm = sender as GaugeItemViewModel;
        if (e.PropertyName == nameof(GaugeItemViewModel.Visible))
        {
            SetDisplayedItemsSource();
        }
    }

    private static void OnItemsSourcePropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var list = bindable as GaugesList;
        if (oldValue is ObservableCollection<GaugeItemViewModel> oldItems)
        {
            oldItems.ForEach(s => s.PropertyChanged -= list.OnGaugeItemViewModelPropertyChanged);
        }

        if (newValue is ObservableCollection<GaugeItemViewModel> newItems)
        {
            newItems.ForEach(s => s.PropertyChanged += list.OnGaugeItemViewModelPropertyChanged);

            list.SetDisplayedItemsSource();
        }
    }

    private void OnSearchBarTextChanged(object sender, TextChangedEventArgs e)
    {
        SearchBar searchBar = (SearchBar)sender;
        _filter = searchBar.Text;
        OnPropertyChanged(nameof(VisibleItems));
    }
}