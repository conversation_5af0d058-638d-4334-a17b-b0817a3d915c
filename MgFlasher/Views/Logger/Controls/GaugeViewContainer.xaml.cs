﻿using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Graphics;
using Microsoft.Maui.Controls;

namespace MgFlasher.Views.Logger.Controls;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class GaugeViewContainer : ContentView
{
    public static readonly BindableProperty FrameColorProperty =
        BindableProperty.Create(nameof(FrameColor), typeof(Color), typeof(GaugeViewContainer), Colors.Transparent);

    public Color FrameColor
    {
        get => (Color)GetValue(FrameColorProperty);
        set => SetValue(FrameColorProperty, value);
    }

    public GaugeViewContainer()
    {
        InitializeComponent();
    }
}