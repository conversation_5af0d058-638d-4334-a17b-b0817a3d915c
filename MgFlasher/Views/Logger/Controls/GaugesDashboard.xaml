<ContentView
    x:Class="MgFlasher.Views.Logger.Controls.GaugesDashboard"
    xmlns:views="clr-namespace:MgFlasher.Views.Logger.Controls"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Name="RootView"
    xmlns:sf="clr-namespace:Syncfusion.Maui.ProgressBar;assembly=Syncfusion.Maui.ProgressBar"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers">
    <ContentView.Content>
        <Grid
            Margin="0" Padding="0"
            BindingContext="{x:Reference Name=RootView}"
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand">
            <AbsoluteLayout
                x:DataType="views:GaugesDashboard" x:Name="layout"
                Margin="0" Padding="0"
                BindingContext="{x:Reference Name=RootView}"
                VerticalOptions="FillAndExpand"
                HorizontalOptions="FillAndExpand"
                IsVisible="{Binding IsEmptySource, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}" />
            <sf:SfCircularProgressBar
                IsIndeterminate="True" HorizontalOptions="Center"
                WidthRequest="100" HeightRequest="100"
                ProgressThickness="10" VerticalOptions="Center"
                BackgroundColor="Transparent"
                ProgressFill="{StaticResource PrimaryColor}"
                TrackFill="Transparent"
                IsVisible="{Binding IsEmptySource, Mode=OneWay}" />
            <Label
                HeightRequest="150" VerticalOptions="Center"
                HorizontalOptions="Center"
                Text="{i18n:Translate Logger_Msg_NoGaugesToDisplay}"
                IsVisible="{Binding IsEmptySource, Mode=OneWay}" />
        </Grid>
    </ContentView.Content>
</ContentView>