<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="MgFlasher.Views.Logger.Controls.RectangleGaugeView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    x:DataType="vms:GaugeItemViewModel">
    <ContentView.Content>
        <Grid Padding="5" Margin="5" BackgroundColor="{StaticResource PrimaryAccentColor}" RowSpacing="{OnIdiom Phone={OnPlatform Default=4}, Desktop={OnPlatform Default=4}, Default={OnPlatform Default=4, Android=0}}">
            <Grid.RowDefinitions>
                <RowDefinition Height="0.45*" />
                <RowDefinition Height="0.9*" />
                <RowDefinition Height="0.025*" />
                <RowDefinition Height="1.0*" />
            </Grid.RowDefinitions>
            <controls:ClassicProgressBar
                Grid.Row="0" Grid.Column="0" Margin="6,0,6,0"
                HalfSizeMode="True" HorizontalOptions="FillAndExpand"
                ProgressColor="{StaticResource PrimaryImportantTextColor}"
                Progress="{Binding ProgressValue, Mode=OneWay}"
                BaseColor="{StaticResource PrimaryAccentColor}"
                ForegroundProgressColor="{StaticResource PrimaryImportantTextColor}"
                VerticalOptions="FillAndExpand" />
            <Label
                Grid.Row="1" FontAttributes="Bold"
                FontSize="{StaticResource SmallFontSize}"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                Style="{StaticResource GaugeLabelStyle}"
                Text="{Binding Title, Mode=OneWay}"
                VerticalTextAlignment="Center" />
            <Grid
                Grid.Row="2"
                BackgroundColor="{StaticResource ListItemSeparatorColor}"
                HorizontalOptions="FillAndExpand"
                MinimumHeightRequest="1"
                VerticalOptions="FillAndExpand" />
            <Grid Grid.Row="3" ColumnSpacing="0" HorizontalOptions="FillAndExpand">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <VerticalStackLayout
                    Grid.Column="0" Margin="0" Padding="0"
                    HorizontalOptions="StartAndExpand" Spacing="0"
                    VerticalOptions="Center">
                    <Label LineBreakMode="NoWrap" Style="{StaticResource GaugeLabelStyle}" VerticalTextAlignment="Center">
                        <Label.FormattedText>
                            <FormattedString>
                                <Span Text="{i18n:Translate SmallGaugeView_Min}" />
                                <Span Text="{Binding SessionMin, Mode=OneWay}" />
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                    <Label LineBreakMode="NoWrap" Style="{StaticResource GaugeLabelStyle}" VerticalTextAlignment="Center">
                        <Label.FormattedText>
                            <FormattedString>
                                <Span Text="{i18n:Translate SmallGaugeView_Max}" />
                                <Span Text="{Binding SessionMax, Mode=OneWay}" />
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                </VerticalStackLayout>
                <Grid Grid.Column="1" Margin="0,-5,0,-5" HorizontalOptions="EndAndExpand" VerticalOptions="Center">
                    <Grid.RowSpacing>
                        <OnIdiom x:TypeArguments="x:Double">
                            <OnIdiom.Tablet>
                                <OnPlatform x:TypeArguments="x:Double">
                                    <On Platform="iOS" Value="2" />
                                    <On Platform="Android" Value="0" />
                                </OnPlatform>
                            </OnIdiom.Tablet>
                            <OnIdiom.Phone>
                                <OnPlatform x:TypeArguments="x:Double">
                                    <On Platform="iOS" Value="5" />
                                    <On Platform="Android" Value="0" />
                                </OnPlatform>
                            </OnIdiom.Phone>
                        </OnIdiom>
                    </Grid.RowSpacing>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="1.5*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Label
                        Grid.Row="0" FontAttributes="Bold"
                        HorizontalTextAlignment="End" LineBreakMode="NoWrap"
                        Style="{StaticResource GaugeValueStyle}"
                        Text="{Binding CurrentValueString, Mode=OneWay}"
                        TextColor="{StaticResource PrimaryImportantTextColor}"
                        VerticalTextAlignment="Center" />
                    <Label Grid.Row="1" HorizontalTextAlignment="End" Style="{StaticResource GaugeLabelStyle}" VerticalTextAlignment="Center">
                        <Label.FormattedText>
                            <FormattedString>
                                <Span Text="[ " />
                                <Span Text="{Binding Unit, Mode=OneWay, UpdateSourceEventName=PropertyChanged}" />
                                <Span Text=" ]" />
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                </Grid>
            </Grid>
        </Grid>
    </ContentView.Content>
</ContentView>