<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MgFlasher.Views.Logger.Controls.LoggerDisplayGaugesView"
    xmlns:logBehaviors="clr-namespace:MgFlasher.Views.Logger.Behaviors"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    x:DataType="vms:LoggerDisplayGaugesViewModel">
    <ContentView.Content>
        <controls:SearchableSfListView Margin="0" ItemSize="{OnIdiom Phone={OnPlatform Default=70}, Desktop={OnPlatform Default=70}, Default={OnPlatform Android=75, Default=65}}" ItemsSource="{Binding Items, Mode=OneWay}">
            <controls:SearchableSfListView.ItemTemplate>
                <DataTemplate x:DataType="vms:GaugeLogDefinitionConfigurationViewModel">
                    <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="7.2*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <StackLayout
                            Orientation="Vertical" Grid.Column="0" Spacing="7"
                            VerticalOptions="FillAndExpand"
                            HorizontalOptions="FillAndExpand">
                            <Label FontSize="{StaticResource SecondaryFontSize}" LineBreakMode="NoWrap" Text="{Binding Name, Mode=OneWay}" HorizontalOptions="FillAndExpand" />
                            <StackLayout Orientation="Horizontal" Spacing="5" HorizontalOptions="FillAndExpand">
                                <controls:SmallButton Text="{Binding ModuleName}" Style="{StaticResource RoundedButtonMgStyle}">
                                    <controls:SmallButton.Behaviors>
                                        <logBehaviors:LoggerDisplayButtonColorBehavior Value="{Binding ModuleId}" />
                                    </controls:SmallButton.Behaviors>
                                </controls:SmallButton>
                                <controls:SmallButton Text="{Binding PresentationType.Translation}" Style="{StaticResource RoundedButtonMgStyle}" Command="{Binding ChangePresentationTypeStateCommand}">
                                    <controls:SmallButton.Behaviors>
                                        <logBehaviors:LoggerDisplayButtonColorBehavior Value="{Binding PresentationType.PreferenceType}" />
                                    </controls:SmallButton.Behaviors>
                                </controls:SmallButton>
                                <controls:SmallButton Command="{Binding ChangeVisibleStateCommand}">
                                    <controls:SmallButton.Behaviors>
                                        <logBehaviors:LoggerDisplayButtonColorBehavior Value="{Binding Visible}" />
                                    </controls:SmallButton.Behaviors>
                                    <controls:SmallButton.Resources>
                                        <ResourceDictionary>
                                            <Style TargetType="controls:SmallButton" BasedOn="{StaticResource RoundedButtonMgStyle}">
                                                <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                                <Setter Property="Text" Value="{i18n:Translate LoggerDisplayGaugesView_Hidden}" />
                                                <Style.Triggers>
                                                    <DataTrigger TargetType="controls:SmallButton" Binding="{Binding Visible}" Value="True" x:DataType="vms:GaugeLogDefinitionConfigurationViewModel">
                                                        <Setter Property="Text" Value="{i18n:Translate LoggerDisplayGaugesView_Visible}" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ResourceDictionary>
                                    </controls:SmallButton.Resources>
                                </controls:SmallButton>
                            </StackLayout>
                        </StackLayout>
                        <Image
                            Grid.Column="2" Aspect="AspectFit" HeightRequest="40"
                            HorizontalOptions="Center" Source="iconexit.png"
                            VerticalOptions="Center" WidthRequest="40">
                            <Image.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding GoToGaugeSettingsCommand}" />
                            </Image.GestureRecognizers>
                        </Image>
                    </Grid>
                </DataTemplate>
            </controls:SearchableSfListView.ItemTemplate>
        </controls:SearchableSfListView>
    </ContentView.Content>
</ContentView>