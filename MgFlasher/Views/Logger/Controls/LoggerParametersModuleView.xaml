<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    x:Class="MgFlasher.Views.Logger.Controls.LoggerParametersModuleView"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    x:DataType="vms:LoggerParametersModuleViewModel">
    <ContentView.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <controls:SearchableSfListView
                Grid.Row="0"
                ItemTemplate="{StaticResource OptionalItemTemplate}"
                ItemsSource="{Binding Items, Mode=OneWay}"
                IsVisible="{Binding IsModuleEnabled, Mode=OneWay}"
                ItemSize="{OnIdiom Phone={OnPlatform Default=60}, Desktop={OnPlatform Default=60}, Default={OnPlatform Android=65, Default=55}}" />
            <Label
                Text="{i18n:Translate LoggerParametersModuleView_ModuleNotSupportedOrNotEnabled}"
                Grid.Row="0" HorizontalOptions="Center"
                VerticalOptions="Center"
                HorizontalTextAlignment="Center"
                IsVisible="{Binding IsModuleEnabled, Mode=OneWay, Converter={StaticResource InvertedBoolConverter}}" />
        </Grid>
    </ContentView.Content>
</ContentView>