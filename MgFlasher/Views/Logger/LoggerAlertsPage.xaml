<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:views="clr-namespace:MgFlasher.Views"
    x:Class="MgFlasher.Views.Logger.LoggerAlertsPage"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:converters="clr-namespace:MgFlasher.Views.Logger.Converters"
    xmlns:loggercontrols="clr-namespace:MgFlasher.Views.Logger.Controls"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:tabView="clr-namespace:Syncfusion.Maui.TabView;assembly=Syncfusion.Maui.TabView"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    controls:ShellTitleView.HasBarBackButton="True"
    controls:ShellTitleView.HasContextOptionsButton="True"
    x:DataType="vms:LoggerAlertsPageViewModel"
    x:Name="root">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.Resources>
                <ResourceDictionary>
                    <converters:LoggerAlertsModuleSelectorConverter x:Key="LoggerAlertsModuleSelectorConverter" />
                </ResourceDictionary>
            </Grid.Resources>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="7.7*" />
                <RowDefinition Height="0.7*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate LoggerAlertsPage_Title}" />
            <tabView:SfTabView Grid.Row="1" Margin="0,0,0,5" SelectedIndex="{Binding SelectedTabIndex}">
                <tabView:SfTabItem Header="{i18n:Translate LoggerAlertsPage_Tab_Master}">
                    <tabView:SfTabItem.Content>
                        <loggercontrols:LoggerAlertsModuleView BindingContext="{Binding BindingContext.LoggerAlertsModules.Modules, Source={x:Reference root}, Converter={StaticResource LoggerAlertsModuleSelectorConverter}, ConverterParameter=0}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
                <tabView:SfTabItem Header="{i18n:Translate LoggerAlertsPage_Tab_Slave}">
                    <tabView:SfTabItem.Content>
                        <loggercontrols:LoggerAlertsModuleView BindingContext="{Binding BindingContext.LoggerAlertsModules.Modules, Source={x:Reference root}, Mode=OneWay, Converter={StaticResource LoggerAlertsModuleSelectorConverter}, ConverterParameter=1}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
                <tabView:SfTabItem Header="{i18n:Translate LoggerAlertsPage_Tab_Preferences}">
                    <tabView:SfTabItem.Content>
                        <loggercontrols:LoggerAlertsPreferencesView BindingContext="{Binding BindingContext.LoggerAlertsPreferences, Source={x:Reference root}, Mode=OneWay}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
            </tabView:SfTabView>
            <controls:BigButton
                Grid.Row="3"
                IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                BackgroundColor="{StaticResource PrimarySuccessColor}"
                Command="{Binding SubmitCommand}"
                Text="{i18n:Translate LoggerAlertsPage_Save}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>