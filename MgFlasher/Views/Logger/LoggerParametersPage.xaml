<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.Logger.LoggerParametersPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:converters="clr-namespace:MgFlasher.Views.Logger.Converters"
    xmlns:tabView="clr-namespace:Syncfusion.Maui.TabView;assembly=Syncfusion.Maui.TabView"
    xmlns:loggercontrols="clr-namespace:MgFlasher.Views.Logger.Controls"
    x:Name="root"
    x:DataType="vms:LoggerParametersPageViewModel"
    HideSoftInputOnTapped="True"
    controls:ShellTitleView.HasBarBackButton="True">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.Resources>
                <ResourceDictionary>
                    <converters:LoggerParametersModuleSelectorConverter x:Key="LoggerParametersModuleSelectorConverter" />
                </ResourceDictionary>
            </Grid.Resources>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="7*" />
                <RowDefinition Height="{OnPlatform Default=1.4*, WinUI=0.7*}" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate LoggerOptionsPage_Title}" />
            <tabView:SfTabView Grid.Row="1" Margin="0,0,0,5" SelectedIndex="{Binding SelectedTabIndex}">
                <tabView:SfTabItem Header="{i18n:Translate LoggerParametersPage_Tab_Master}">
                    <tabView:SfTabItem.Content>
                        <loggercontrols:LoggerParametersModuleView BindingContext="{Binding BindingContext.LoggerParametersModules.Modules, Source={x:Reference root}, Converter={StaticResource LoggerParametersModuleSelectorConverter}, ConverterParameter=0}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
                <tabView:SfTabItem Header="{i18n:Translate LoggerParametersPage_Tab_Slave}">
                    <tabView:SfTabItem.Content>
                        <loggercontrols:LoggerParametersModuleView BindingContext="{Binding BindingContext.LoggerParametersModules.Modules, Source={x:Reference root}, Mode=OneWay, Converter={StaticResource LoggerParametersModuleSelectorConverter}, ConverterParameter=1}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
                <tabView:SfTabItem Header="{i18n:Translate LoggerParametersPage_Tab_Preferences}">
                    <tabView:SfTabItem.Content>
                        <loggercontrols:LoggerParametersPreferencesView BindingContext="{Binding BindingContext.LoggerParametersPreferences, Source={x:Reference root}, Mode=OneWay}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
            </tabView:SfTabView>
            <Grid Grid.Row="2" VerticalOptions="End">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="{OnPlatform Default=Auto, WinUI=0}" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="{OnPlatform Default=0, WinUI=*}" />
                </Grid.ColumnDefinitions>
                <controls:BigButton
                    Grid.Row="0" Grid.Column="0"
                    IsEnabled="{Binding IsDefaultsEnabled}"
                    BackgroundColor="{StaticResource PrimaryAccentColor}"
                    Command="{Binding LoggerDefaultsCommand}"
                    Text="{i18n:Translate LoggerOptionsPage_Defaults}" />
                <controls:BigButton
                    Grid.Row="0" Grid.Column="1"
                    IsEnabled="{Binding IsRemoveAllEnabled}"
                    BackgroundColor="{StaticResource PrimaryAccentColor}"
                    Command="{Binding LoggerRemoveAllCommand}"
                    Text="{i18n:Translate LoggerOptionsPage_RemoveAll}" />
                <controls:BigButton
                    Grid.Column="{OnPlatform Default=0, WinUI=3}"
                    Grid.Row="{OnPlatform Default=1, WinUI=0}"
                    Grid.ColumnSpan="{OnPlatform Default=2, WinUI=1}"
                    AutomationId="LoggerParametersPage_SubmitButton"
                    IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                    Command="{Binding SubmitCommand}"
                    Text="{i18n:Translate LoggerOptionsPage_Save}" />
            </Grid>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>