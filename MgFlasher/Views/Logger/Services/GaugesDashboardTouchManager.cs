﻿using MgFlasher.Behaviors;
using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.ViewModels.Logger;
using MgFlasher.Views.Logger.Controls;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Microsoft.Maui.Graphics;
using Microsoft.Maui.Controls;
using Microsoft.Maui;
using Microsoft.Maui.Devices;

namespace MgFlasher.Views.Logger.Services;

public class GaugesDashboardTouchManager : IDisposable
{
    class DragInfo
    {
        public DragInfo(long id, Point pressPoint)
        {
            Id = id;
            PressPoint = pressPoint;
        }

        public long Id { private set; get; }

        public Point PressPoint { private set; get; }
    }

    public static bool IsUnlocked { get; set; }

    private Dictionary<GaugeViewContainer, DragInfo> _dragDictionary = new Dictionary<GaugeViewContainer, DragInfo>();
    private Stopwatch _doublePressStopWatch;
    private GaugeViewContainer _doublePressContainer;

    public void ContainerTouched(GaugeViewContainer container, TouchActionEventArgs args)
    {
        switch (args.Type)
        {
            case TouchActionType.Pressed:
                OnPressed(container, args);
                break;

            case TouchActionType.Moved:
                OnMoved(container, args);
                break;

            case TouchActionType.Released:
                OnReleased(container, args);
                break;
        }
    }

    private void OnPressed(GaugeViewContainer container, TouchActionEventArgs args)
    {
        if (!_dragDictionary.ContainsKey(container) && IsUnlocked)
        {
            HighlightPressedItem(container);

            _dragDictionary.Add(container, new DragInfo(args.Id, args.Location));
            var touchEffect = (TouchEffect)container.Effects.FirstOrDefault(e => e is TouchEffect);
            touchEffect.Capture = true;
        }

        ListenForDoublePress(container);
    }

    private void OnMoved(GaugeViewContainer container, TouchActionEventArgs args)
    {
        if (_dragDictionary.ContainsKey(container) && _dragDictionary[container].Id == args.Id && IsUnlocked)
        {
            var rect = AbsoluteLayout.GetLayoutBounds(container);
            var initialLocation = _dragDictionary[container].PressPoint;
            rect.X += args.Location.X - initialLocation.X;
            rect.Y += args.Location.Y - initialLocation.Y;
            AbsoluteLayout.SetLayoutBounds(container, rect);
        }
    }

    private void OnReleased(GaugeViewContainer container, TouchActionEventArgs args)
    {
        if (_dragDictionary.ContainsKey(container) && _dragDictionary[container].Id == args.Id && IsUnlocked)
        {
            NormalizeReleasedItem(container);

            _dragDictionary.Remove(container);

            if (container.Parent != null)
            {
                var rectangle = AbsoluteLayout.GetLayoutBounds(container);
                var collisionResult = HandleCollisions(container, rectangle);
                SaveShape(container, rectangle, collisionResult);
            }
        }
    }

    private static CollisionConfigurationArgs HandleCollisions(GaugeViewContainer container, Rect rectangle)
    {
        var layout = (container.Parent as AbsoluteLayout);
        var collision = false;
        var result = new CollisionConfigurationArgs()
        {
            Brothers = new Dictionary<(ModuleId ModuleId, string DefinitionCode), Flasher.Services.CarLogger.Gauges.Configuration.User.Storage.GaugeLogShape>(),
            Color = Colors.Transparent.ToArgbHex()
        };
        foreach (var brother in layout.Children.Where(x => x != container).OfType<GaugeViewContainer>())
        {
            var brotherRectangle = AbsoluteLayout.GetLayoutBounds(brother);
            if (rectangle.IntersectsWith(brotherRectangle))
            {
                var brotherVm = brother.BindingContext as GaugeItemViewModel;

                collision = true;
                brother.FrameColor = Colors.Transparent;
                brotherVm.Shape.Color = Colors.Transparent.ToArgbHex();

                result.Brothers.Add((brotherVm.ModuleId, brotherVm.DefinitionCode), brotherVm.Shape);
            }
        }
        container.FrameColor = collision ?
            (Color)App.Current.Resources["PrimaryAccentColor"] :
            Colors.Transparent;

        result.Color = container.FrameColor.ToArgbHex();

        return result;
    }

    private static void SaveShape(GaugeViewContainer container, Rect rectangle, CollisionConfigurationArgs collision)
    {
        var vm = container.BindingContext as GaugeItemViewModel;
        var shouldSave = collision.Brothers.Any() ||
                         vm.Shape.X != rectangle.X || vm.Shape.Y != rectangle.Y ||
                         vm.Shape?.Color != collision.Color;

        vm.Shape.X = rectangle.X;
        vm.Shape.Y = rectangle.Y;
        vm.Shape.Color = collision.Color;

        if (shouldSave)
        {
            vm.SaveModifiedShapeCommand.Execute(collision);
        }
    }

    private void HighlightPressedItem(GaugeViewContainer container)
    {
        var context = container.BindingContext as GaugeItemViewModel;

        container.ScaleX = (context?.Shape?.ScaleW ?? 1) * 1.05;
        container.ScaleY = (context?.Shape?.ScaleH ?? 1) * 1.05;

        var parent = container.Parent as AbsoluteLayout;
        parent.ZIndex += 1;
    }

    private void NormalizeReleasedItem(GaugeViewContainer container)
    {
        var context = container.BindingContext as GaugeItemViewModel;

        container.ScaleX = context?.Shape?.ScaleW ?? 1;
        container.ScaleY = context?.Shape?.ScaleH ?? 1;
    }

    private void ListenForDoublePress(GaugeViewContainer container)
    {
        if (_doublePressStopWatch is null || _doublePressStopWatch.ElapsedMilliseconds > 500)
        {
            _doublePressStopWatch = Stopwatch.StartNew();
            _doublePressContainer = container;
        }
        else if (_doublePressStopWatch.ElapsedMilliseconds < 500 && _doublePressContainer == container)
        {
            var vm = container.BindingContext as GaugeItemViewModel;
            vm.GaugeChangePromptCommand.Execute(null);
            FixIosTouchPipelineOnPopup(container);
            _doublePressStopWatch = null;
            _doublePressContainer = null;
        }
        else
        {
            _doublePressStopWatch = null;
            _doublePressContainer = null;
        }
    }

    private void FixIosTouchPipelineOnPopup(GaugeViewContainer container)
    {
        if (DeviceInfo.Platform == DevicePlatform.iOS && _dragDictionary.ContainsKey(container))
        {
            NormalizeReleasedItem(container);
            _dragDictionary.Remove(container);
        }
    }

    public void Dispose()
    {
        _dragDictionary.Clear();
    }
}