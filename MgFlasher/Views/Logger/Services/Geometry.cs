﻿using Microsoft.Maui.Maps;
using Microsoft.Maui.Devices.Sensors;
using System;
using System.Linq;
using System.Collections.Generic;

namespace MgFlasher.Views.Logger.Services;

public static class Geometry
{
    public static Location GetCenter(IEnumerable<Location> locations)
    {
        var averageLongitude = locations.Average(p => p.Longitude);
        var averageLatitude = locations.Average(p => p.Latitude);

        var center = new Location(averageLatitude, averageLongitude);
        return center;
    }

    public static Distance CalculateMaxDistanceFromCentroid(Location center, IEnumerable<Location> points)
    {
        var maxDistance = points
            .Select(p => CalculateDistance(center, p))
            .OrderByDescending(x => x.Meters)
            .First();
        return maxDistance;
    }

    public static Distance CalculateDistance(Location point1, Location point2)
    {
        const double R = 6371000; // Radius of the Earth in meters
        double lat1 = DegreesToRadians(point1.Latitude);
        double lon1 = DegreesToRadians(point1.Longitude);
        double lat2 = DegreesToRadians(point2.Latitude);
        double lon2 = DegreesToRadians(point2.Longitude);

        double dlat = lat2 - lat1;
        double dlon = lon2 - lon1;

        double a = Math.Sin(dlat / 2) * Math.Sin(dlat / 2) +
                   Math.Cos(lat1) * Math.Cos(lat2) *
                   Math.Sin(dlon / 2) * Math.Sin(dlon / 2);
        double c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

        return Distance.FromMeters(R * c);
    }

    private static double DegreesToRadians(double degrees)
    {
        return degrees * Math.PI / 180;
    }
}