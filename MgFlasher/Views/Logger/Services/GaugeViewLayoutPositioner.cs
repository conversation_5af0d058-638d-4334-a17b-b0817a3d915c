﻿using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User.Storage;
using MgFlasher.ViewModels.Logger;
using MgFlasher.Views.Logger.Controls;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Maui.Graphics;
using Microsoft.Maui.Controls;
using Microsoft.Maui;
using Syncfusion.Maui.DataSource.Extensions;

namespace MgFlasher.Views.Logger.Services;

public class GaugeViewLayoutPositioner
{
    public bool ShouldAutoAdjust(IEnumerable<IShapeHolderViewModel> updated)
    {
        return updated.All(x => x.Shape is null || (x.Shape.X == 0 && x.Shape.Y == 0)) && updated.Count() > 1;
    }

    public void AutoAdjustByGrid(IEnumerable<IShapeHolderViewModel> items)
    {
        var gaugesCount = Math.Min(GridAutoLayoutPositionerPlatformSettings.GaugeCountLimit, items.Count());

        var includedItems = items.Take(gaugesCount).ToList();
        var gaugeIndex = 0;

        for (int rowIndex = 0; rowIndex < GridAutoLayoutPositionerPlatformSettings.Rows; rowIndex++)
        {
            for (int columnIndex = 0; columnIndex < GridAutoLayoutPositionerPlatformSettings.Columns; columnIndex++)
            {
                var shape = new GaugeLogShape()
                {
                    ScaleH = 1,
                    ScaleW = 1,
                    X = GridAutoLayoutPositionerPlatformSettings.RefGaugeWidth * columnIndex,
                    Y = GridAutoLayoutPositionerPlatformSettings.RefGaugeHeight * rowIndex,
                    Color = Colors.Transparent.ToArgbHex()
                };
                if (includedItems.ElementAtOrDefault(gaugeIndex++) is not IShapeHolderViewModel vm)
                {
                    break;
                }

                vm.Shape = shape;
            }
        }

        items.Except(includedItems).ForEach(x =>
        {
            x.Shape = null;
            x.Visible = false;
        });
    }

    public void Adjust(GaugeViewContainer container)
    {
        var context = container.BindingContext as GaugeItemViewModel;
        if (context?.Shape is null)
        {
            container.IsVisible = false;

            return;
        }

        container.IsVisible = true;

        var rectangle = GetRectangle(context.Shape);
        AbsoluteLayout.SetLayoutBounds(container, rectangle);
        container.ScaleX = context.Shape.ScaleW;
        container.ScaleY = context.Shape.ScaleH;
        container.FrameColor = !string.IsNullOrEmpty(context.Shape.Color) ?
            Color.FromArgb(context.Shape.Color) : Colors.Transparent;

        if (container.Parent is Layout layout && container.FrameColor != Colors.Transparent)
        {
            layout.ZIndex += 1;
        }
    }

    private Rect GetRectangle(GaugeLogShape shape)
    {
        var point = new Point(shape.X, shape.Y);
        var size = new Size(
            GridAutoLayoutPositionerPlatformSettings.RefGaugeWidth,
            GridAutoLayoutPositionerPlatformSettings.RefGaugeHeight);

        var rectangle = new Rect(point, size);

        return rectangle;
    }
}