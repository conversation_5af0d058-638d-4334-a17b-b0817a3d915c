﻿using System;
using Microsoft.Maui.Devices;
using Microsoft.Maui.Controls;

namespace MgFlasher.Views.Logger.Services;

public static class GridAutoLayoutPositionerPlatformSettings
{
    public static int Rows { get; set; }
    public static int Columns { get; set; }
    public static int GaugeCountLimit => Rows * Columns;
    public static double RefGaugeWidth { get; set; }
    public static double RefGaugeHeight { get; set; }
    public static double RefDashboardWidth { get; set; }
    public static double RefDashboardHeight { get; set; }

    public static bool Initialized => RefGaugeHeight > 10d;

    static GridAutoLayoutPositionerPlatformSettings()
    {
        Rows = 4;
        Columns = DeviceInfo.Idiom switch
        {
            _ when DeviceInfo.Idiom == DeviceIdiom.Tablet => 3,
            _ when DeviceInfo.Idiom == DeviceIdiom.Phone => 2,
            _ when DeviceInfo.Idiom == DeviceIdiom.Desktop => 4,
            _ => 4
        };
    }

    public static void CalculateSize(View layout)
    {
        if (layout is null || layout.Height <= 10d)
        {
            return;
        }

        RefDashboardWidth = layout.Width;
        RefDashboardHeight = layout.Height;

        RefGaugeWidth = layout.Width / Columns;
        RefGaugeHeight = layout.Height / Rows;
    }
}