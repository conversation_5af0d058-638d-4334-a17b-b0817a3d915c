<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="MgFlasher.Views.Logger.LoggerFileChartView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    x:Name="root" x:DataType="vms:LoggerFileChartViewModel">
    <WebView IsVisible="{Binding Url, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Source="{Binding Url, Mode=OneWay}" />
</ContentView>