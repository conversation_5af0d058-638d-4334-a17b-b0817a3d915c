<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.Logger.LoggerFilePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:loggerviews="clr-namespace:MgFlasher.Views.Logger"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    xmlns:tabView="clr-namespace:Syncfusion.Maui.TabView;assembly=Syncfusion.Maui.TabView"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    controls:ShellTitleView.HasBarBackButton="True"
    controls:ShellTitleView.HasContextOptionsButton="{Binding ContextOptions, Converter={StaticResource CollectionToVisibility}}"
    x:DataType="vms:LoggerFilePageViewModel" x:Name="root">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}" Padding="10,20,10,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <tabView:SfTabView
                Margin="0" Grid.Row="0" TabBarPlacement="Top"
                IndicatorPlacement="Top"
                SelectionChanged="SfTabView_SelectionChanged"
                SelectionChanging="SfTabView_SelectionChanging"
                SelectedIndex="{Binding SelectedTabIndex}"
                x:Name="sfTabView" Loaded="sfTabView_Loaded">
                <tabView:SfTabItem Header="{i18n:Translate LoggerFilePage_Tab_General}">
                    <tabView:SfTabItem.Content>
                        <loggerviews:LoggerFileGeneralView Margin="0,0,0,10" BindingContext="{Binding BindingContext.General, Source={x:Reference root}}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
                <tabView:SfTabItem Header="{i18n:Translate LoggerFilePage_Tab_Chart}">
                    <tabView:SfTabItem.Content>
                        <loggerviews:LoggerFileChartView Margin="0,0,0,10" BindingContext="{Binding BindingContext.Chart, Source={x:Reference root}}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
            </tabView:SfTabView>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>