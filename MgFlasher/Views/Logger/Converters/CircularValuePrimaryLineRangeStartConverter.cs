﻿using System;
using System.Globalization;
using MgFlasher.Views.Logger.Controls;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Views.Logger.Converters;

public class CircularValuePrimaryLineRangeStartConverter : IValueConverter
{
    private readonly double _circularMinValue = CircularGaugeViewConstants.CircularMin;
    private readonly double _circular1ThMax = CircularGaugeViewConstants.CircularMax * 0.01d * 2.5;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var circularValue = (double)value;

        var rangeStart = circularValue - (_circular1ThMax / 2.5d);

        return Math.Max(_circularMinValue, rangeStart);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}