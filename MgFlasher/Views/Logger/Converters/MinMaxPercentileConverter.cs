﻿using System;
using System.Globalization;
using System.Linq;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Views.Logger.Converters;

public class MinMaxPercentileConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values.Length < 3 || values.Any(x => x is null))
        {
            return "";
        }

        var multiplier = parameter switch
        {
            "0.2" => 0.2d,
            "0.4" => 0.4d,
            "0.6" => 0.6d,
            "0.8" => 0.8d,
            _ => throw new InvalidOperationException()
        };
        var min = (double)values[0];
        var max = (double)values[1];
        var precision = (int)values[2];

        var result = Math.Round((max - min) * multiplier, precision);
        var kiloFormatted = result.ToKiloFormat();

        return kiloFormatted;
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}