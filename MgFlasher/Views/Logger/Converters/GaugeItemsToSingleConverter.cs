﻿using MgFlasher.ViewModels.Logger;
using System;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Views.Logger.Converters;

public class GaugeItemsToSingleConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var index = int.Parse((string)parameter);
        var items = value as ObservableCollection<GaugeItemViewModel>;

        return items?.ElementAtOrDefault(index);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}