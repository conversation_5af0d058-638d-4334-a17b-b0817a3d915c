﻿using MgFlasher.ViewModels.Logger.Models;
using Microsoft.Maui.Controls;

namespace MgFlasher.Views.Logger.Converters;

public class MapItemTemplateSelector : DataTemplateSelector
{
    public DataTemplate PinTemplate { get; set; }
    public DataTemplate PolylineTemplate { get; set; }

    protected override DataTemplate OnSelectTemplate(object item, BindableObject container)
    {
        if (item is LoggerFileMapPointItemModel)
        {
            return PinTemplate;
        }
        else if (item is LoggerFileMapPolylineItemModel)
        {
            return PolylineTemplate;
        }
        return null;
    }
}