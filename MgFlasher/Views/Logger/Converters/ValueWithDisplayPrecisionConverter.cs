﻿using System;
using System.Globalization;
using System.Linq;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Views.Logger.Converters;

public class ValueWithDisplayPrecisionConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values.Length < 2 || values.Take(2).Any(x => x is null))
        {
            return "";
        }

        var value = (double)values[0];
        var precision = (int)values[1];
        var unit = values.ElementAtOrDefault(2) as string;
        var presentHexValues = values.ElementAtOrDefault(2) is bool val && val;

        if (presentHexValues)
        {
            return ((int)Math.Round(value, 0)).ToString("X2");
        }

        var result = Math.Round(value, precision);
        var kiloFormatted = result.ToKiloFormat();
        if (!string.IsNullOrEmpty(unit))
        {
            kiloFormatted += $" {unit}";
        }

        return kiloFormatted;
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}