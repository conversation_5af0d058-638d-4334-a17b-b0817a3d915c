﻿using System;
using System.Globalization;
using MgFlasher.Views.Logger.Controls;
using Microsoft.Maui.Controls;

namespace MgFlasher.Views.Logger.Converters;

public class CircularValueConverter : IValueConverter
{
    private readonly double _rangeStart = CircularGaugeViewConstants.CircularMin;
    private readonly double _rangeEnd = CircularGaugeViewConstants.CircularMax;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var circularValue = (double)value;

        if (circularValue > _rangeEnd)
        {
            return _rangeEnd;
        }
        else if (circularValue < _rangeStart)
        {
            return _rangeStart;
        }

        return circularValue;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}