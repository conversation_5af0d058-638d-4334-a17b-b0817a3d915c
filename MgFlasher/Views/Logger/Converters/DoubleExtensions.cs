﻿using System.Globalization;

namespace MgFlasher.Views.Logger.Converters;

public static class DoubleExtensions
{
    public static string ToKiloFormat(this double num) => num switch
    {
        >= 100000000 => (num / 1000000D).ToString("0.#M"),
        >= 1000000 => (num / 1000000D).ToString("0.##M"),
        >= 100000 => (num / 1000D).ToString("0.#k"),
        >= 1000 => (num / 1000D).ToString("0.#k"),
        _ => num.ToString(CultureInfo.InvariantCulture)
    };
}