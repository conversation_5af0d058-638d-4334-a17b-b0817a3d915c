﻿using MgFlasher.Ecu.Common.Extensions;
using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Views.Logger.Converters;

public class LoggerAlertTypeToArgumentVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var alertType = (LoggerAlertType)value;
        return alertType.In(LoggerAlertType.InsideRange, LoggerAlertType.OutsideRange);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}