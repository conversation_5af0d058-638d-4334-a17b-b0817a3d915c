﻿using MgFlasher.Ecu.Common.Extensions;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Devices;
using System;
using System.Globalization;

namespace MgFlasher.Views.Logger.Converters;

public class PixelSizesScaleConverter : IValueConverter
{
    private readonly double baseWidth = 1440d;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var baseValue = (double)value;

        double recalculatedValue;

        if (DeviceInfo.Platform.In(DevicePlatform.Android) && DeviceInfo.Idiom == DeviceIdiom.Phone)
        {
            recalculatedValue = baseValue / baseWidth * DeviceDisplay.Current.MainDisplayInfo.Width;
        }
        else if (DeviceInfo.Platform.In(DevicePlatform.iOS) && DeviceInfo.Idiom == DeviceIdiom.Phone)
        {
            recalculatedValue = baseValue / baseWidth * DeviceDisplay.Current.MainDisplayInfo.Width;
        }
        else
        {
            recalculatedValue = baseValue;
        }

        return recalculatedValue;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}