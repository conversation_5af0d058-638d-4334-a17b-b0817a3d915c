﻿using MgFlasher.Flasher.Services.CarLogger.Gauges.Configuration.User;
using Microsoft.Maui.Controls;
using System;
using System.Collections.Generic;
using System.Globalization;

namespace MgFlasher.Views.Logger.Converters;

public class GaugeColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var selectedGaugeColor = (GaugeColor)value;
        var colorName = parameter as string;

        return Application.Current.Resources.TryGetValue($"{colorName}_{selectedGaugeColor}", out object color)
                ? color
                : throw new KeyNotFoundException("Missing color!");
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}