﻿using MgFlasher.Ecu.Logger.Configuration.User.Storage;
using MgFlasher.Localization.Resources;
using System;
using System.Globalization;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Views.Logger.Converters;

public class LoggerAlertTypeToArgumentNameConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var alertType = (LoggerAlertType)value;
        var param = (string)parameter;
        return alertType switch
        {
            LoggerAlertType.Equals => AppResources.LoggerAlertType_ArgumentName_Value,
            LoggerAlertType.Above => AppResources.LoggerAlertType_ArgumentName_Value,
            LoggerAlertType.Below => AppResources.LoggerAlertType_ArgumentName_Value,
            LoggerAlertType.InsideRange when param == "0" => AppResources.LoggerAlertType_ArgumentName_Min,
            LoggerAlertType.InsideRange when param == "1" => AppResources.LoggerAlertType_ArgumentName_Max,
            LoggerAlertType.OutsideRange when param == "0" => AppResources.LoggerAlertType_ArgumentName_Min,
            LoggerAlertType.OutsideRange when param == "1" => AppResources.LoggerAlertType_ArgumentName_Max,
            _ => throw new InvalidOperationException("Not supported alert type")
        };
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}