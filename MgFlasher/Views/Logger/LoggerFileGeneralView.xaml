<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="MgFlasher.Views.Logger.LoggerFileGeneralView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:MgFlasher.Converters"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views.Logger"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:badge="clr-namespace:Syncfusion.Maui.Core;assembly=Syncfusion.Maui.Core"
    xmlns:export="clr-namespace:MgFlasher.Flasher.Services.CarLogger.Exporters.Models;assembly=MgFlasher.Flasher.Services"
    x:Name="root"
    x:DataType="vms:LoggerFileGeneralViewModel">
    <ScrollView>
        <StackLayout Orientation="Vertical">
            <BoxView IsVisible="{Binding Filename, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
            <Label IsVisible="{Binding Filename, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate LoggerFileGeneralView_Filename}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
            <Label IsVisible="{Binding Filename, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding Filename, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
            <BoxView IsVisible="{Binding CustomCodeVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
            <Label IsVisible="{Binding CustomCodeVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate LoggerFileGeneralView_CustomCodeVersion}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
            <Label IsVisible="{Binding CustomCodeVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding CustomCodeVersion, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
            <BoxView IsVisible="{Binding AppVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
            <Label IsVisible="{Binding AppVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate LoggerFileGeneralView_AppVersion}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
            <Label IsVisible="{Binding AppVersion, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding AppVersion, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
            <BoxView IsVisible="{Binding Platform, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
            <Label IsVisible="{Binding Platform, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate LoggerFileGeneralView_Platform}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
            <Label IsVisible="{Binding Platform, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding Platform, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
            <BoxView IsVisible="{Binding HasFlashContext}" Style="{StaticResource Separator}" />
            <StackLayout
                IsVisible="{Binding HasFlashContext}"
                Orientation="Horizontal" Padding="0" Margin="0"
                HorizontalOptions="StartAndExpand">
                <Label HorizontalOptions="Start" VerticalOptions="Center" Text="{i18n:Translate LoggerFileGeneralView_FlashContext}" FontSize="{StaticResource SecondaryFontSize}" />
                <Image
                    Aspect="AspectFit" HeightRequest="40"
                    HorizontalOptions="End" Source="iconexit.png"
                    VerticalOptions="Center" WidthRequest="40">
                    <Image.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding GoToFlashHistoryDetailsCommand}" />
                    </Image.GestureRecognizers>
                </Image>
            </StackLayout>
            <BoxView Style="{StaticResource Separator}" />
            <Label Text="{i18n:Translate LoggerFileGeneralView_ShareTo}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
            <StackLayout Orientation="Horizontal">
                <badge:SfBadgeView BadgeText="&#x2714;" IsVisible="{Binding HasLogsCloudUrl}">
                    <badge:SfBadgeView.BadgeSettings>
                        <badge:BadgeSettings Type="Info" Position="TopRight" />
                    </badge:SfBadgeView.BadgeSettings>
                    <badge:SfBadgeView.Content>
                        <controls:SmallButton
                            Margin="5"
                            BackgroundColor="{StaticResource PrimarySuccessColor}"
                            Text="{i18n:Translate LoggerFileGeneralView_Share_LogsCloud}"
                            Command="{Binding ShareCommand}"
                            CommandParameter="{x:Static export:ExporterProviderType.MgFlasherLogsCloud}" />
                    </badge:SfBadgeView.Content>
                </badge:SfBadgeView>
                <controls:SmallButton
                    Margin="5"
                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                    Text="{i18n:Translate LoggerFileGeneralView_Share_LogsCloud}"
                    Command="{Binding ShareCommand}"
                    CommandParameter="{x:Static export:ExporterProviderType.MgFlasherLogsCloud}"
                    IsVisible="{Binding HasLogsCloudUrl, Converter={StaticResource InvertedBoolConverter}}" />
                <controls:SmallButton
                    Margin="5"
                    BackgroundColor="{StaticResource AccentTextColor}"
                    Text="{i18n:Translate LoggerFileGeneralView_Share_File}"
                    Command="{Binding ShareCommand}"
                    CommandParameter="{x:Static export:ExporterProviderType.File}" />
                <controls:SmallButton
                    Margin="5"
                    BackgroundColor="{StaticResource AccentTextColor}"
                    Text="{i18n:Translate LoggerFileGeneralView_Share_Spoolstreet}"
                    Command="{Binding ShareCommand}"
                    CommandParameter="{x:Static export:ExporterProviderType.Spoolstreet}" />
            </StackLayout>
            <BoxView Style="{StaticResource Separator}" />
            <StackLayout Orientation="Horizontal" HorizontalOptions="End">
                <controls:BigButton Margin="5" BackgroundColor="{StaticResource AccentTextColor}" Text="{i18n:Translate LoggerFileGeneralView_Rename}" Command="{Binding RenameCommand}" />
                <controls:BigButton Margin="5" BackgroundColor="{StaticResource PrimaryColor}" Text="{i18n:Translate LoggerFileGeneralView_Delete}" Command="{Binding DeleteCommand}" />
            </StackLayout>
        </StackLayout>
    </ScrollView>
</ContentView>