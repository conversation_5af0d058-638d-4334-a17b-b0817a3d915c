﻿using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Maps;
using MgFlasher.ViewModels.Logger;
using System.ComponentModel;
using System.Linq;
using MgFlasher.Views.Logger.Services;
using Microsoft.Maui.Controls.Maps;
using Microsoft.Maui.Graphics;

namespace MgFlasher.Views.Logger;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class LoggerFileMapView : ContentView
{
    public LoggerFileMapViewModel ViewModel => (LoggerFileMapViewModel)BindingContext;

    public LoggerFileMapView()
    {
        InitializeComponent();
        map.PropertyChanged += OnMapPropertyChanged;
    }

    private void OnMapPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(Map.ItemsSource) && (ViewModel.MapItems?.Any() ?? false))
        {
            CenterMap();
            DrawPolylines();
        }
    }

    private void DrawPolylines()
    {
        var polylines = ViewModel.Polylines;
        foreach (var polyline in polylines.Where(x => x.Items.Any()))
        {
            var elem = new Polyline();
            elem.StrokeColor = (Color)App.Current.Resources["ItemSelectedHoverColor"];
            elem.StrokeWidth = 8;
            foreach (var item in polyline.Items)
                elem.Geopath.Add(item);
            map.MapElements.Add(elem);
        }
    }

    private void CenterMap()
    {
        var points = ViewModel.MapItems.SelectMany(x => x.Items).ToList();
        var center = Geometry.GetCenter(points);
        var distance = Geometry.CalculateMaxDistanceFromCentroid(center, points);
        var mapSpan = MapSpan.FromCenterAndRadius(center, distance);
        map.MoveToRegion(mapSpan);
    }
}