<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MgFlasher.Views.Logger.LoggerDisplayGaugeItemPage"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    controls:ShellTitleView.HasBarBackButton="True"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    x:DataType="vms:LoggerDisplayGaugeItemPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="3.32*" />
                <RowDefinition Height="0.52*" />
                <RowDefinition Height="0.52*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate LoggerDisplayGaugeItemPage_Title}" />
            <ScrollView Grid.Row="1">
                <StackLayout Orientation="Vertical">
                    <BoxView IsVisible="{Binding Name, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding Name, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate LoggerDisplayGaugeItemPage_Name}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding Name, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding Name, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView IsVisible="{Binding A2lMeasurementName, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Style="{StaticResource Separator}" />
                    <Label IsVisible="{Binding A2lMeasurementName, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{i18n:Translate LoggerDisplayGaugeItemPage_A2lMeasurementName}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label IsVisible="{Binding A2lMeasurementName, Converter={StaticResource StringNullabilityToVisibilityConverter}}" Text="{Binding A2lMeasurementName, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView Style="{StaticResource Separator}" />
                    <Label Text="{i18n:Translate LoggerDisplayGaugeItemPage_ModuleName}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label Text="{Binding ModuleName, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <BoxView Style="{StaticResource Separator}" />
                    <HorizontalStackLayout Spacing="0" HorizontalOptions="FillAndExpand">
                        <Label FontAttributes="Bold" Text="{i18n:Translate LoggerDisplayGaugeItemPage_Visible_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                        <controls:Checkbox HorizontalOptions="End" VerticalOptions="FillAndExpand" IsChecked="{Binding Visible, Mode=TwoWay}" />
                    </HorizontalStackLayout>
                    <BoxView Style="{StaticResource Separator}" />
                    <Label Text="{i18n:Translate LoggerDisplayGaugeItemPage_GaugePresentationType}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <Picker ItemsSource="{Binding PresentationTypes, Mode=OneWay}" SelectedItem="{Binding PresentationType}" ItemDisplayBinding="{Binding Translation}" HorizontalOptions="FillAndExpand" />
                    <BoxView Style="{StaticResource Separator}" />
                    <HorizontalStackLayout Spacing="0" HorizontalOptions="FillAndExpand">
                        <Label FontAttributes="Bold" Text="{i18n:Translate LoggerDisplayGaugeItemPage_KeepAspectRatio_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                        <controls:Checkbox HorizontalOptions="End" VerticalOptions="FillAndExpand" IsChecked="{Binding KeepAspectRatio, Mode=TwoWay}" />
                    </HorizontalStackLayout>
                    <BoxView Style="{StaticResource Separator}" />
                    <Label Text="{i18n:Translate LoggerDisplayGaugeItemPage_GaugePresentationSize_Width}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <Slider Maximum="4" Minimum="0.5" Value="{Binding ScaleW}" HorizontalOptions="FillAndExpand" />
                    <BoxView Style="{StaticResource Separator}" />
                    <Label Text="{i18n:Translate LoggerDisplayGaugeItemPage_GaugePresentationSize_Height}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                    <Slider Maximum="4" Minimum="0.5" Value="{Binding ScaleH}" HorizontalOptions="FillAndExpand" />
                    <BoxView Style="{StaticResource Separator}" />
                </StackLayout>
            </ScrollView>
            <controls:BigButton Grid.Row="2" BackgroundColor="{StaticResource PrimaryAccentColor}" Command="{Binding DefaultsCommand}" Text="{i18n:Translate LoggerDisplayGaugeItemPage_Defaults}" />
            <controls:BigButton Grid.Row="3" BackgroundColor="{StaticResource PrimarySuccessColor}" Command="{Binding SubmitCommand}" Text="{i18n:Translate LoggerDisplayGaugeItemPage_Submit}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>