<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MgFlasher.Views.Logger.LoggerAlertItemPage"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    controls:ShellTitleView.HasBarBackButton="True"
    xmlns:converters="clr-namespace:MgFlasher.Views.Logger.Converters"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    x:DataType="vms:LoggerAlertItemPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.Resources>
                <ResourceDictionary>
                    <converters:LoggerAlertTypeToArgumentNameConverter x:Key="LoggerAlertTypeToArgumentNameConverter" />
                    <converters:LoggerAlertTypeToArgumentVisibilityConverter x:Key="LoggerAlertTypeToArgumentVisibilityConverter" />
                </ResourceDictionary>
            </Grid.Resources>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="3.82*" />
                <RowDefinition Height="0.52*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate LoggerAlertItemPage_Title}" />
            <ScrollView Grid.Row="1">
                <StackLayout Orientation="Vertical">
                    <!--  ModuleId  -->
                    <BoxView Style="{StaticResource Separator}" />
                    <Label Text="{i18n:Translate LoggerAlertItemPage_ModuleName}" HorizontalOptions="Start" FontSize="{StaticResource SecondaryFontSize}" />
                    <Label Text="{Binding ModuleName, Mode=OneWay}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <!--  DefinitionCode  -->
                    <BoxView Style="{StaticResource Separator}" />
                    <Label Text="{i18n:Translate LoggerAlertItemPage_DefinitionCode_Title}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <Picker ItemsSource="{Binding AvailableLoggerDefinitions, Mode=OneWay}" ItemDisplayBinding="{Binding DisplayName, Mode=OneWay}" SelectedItem="{Binding SelectedLoggerDefinition}" />
                    <!--  Alert Type  -->
                    <BoxView Style="{StaticResource Separator}" />
                    <Label Text="{i18n:Translate LoggerAlertItemPage_AlertType_Title}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <Picker ItemsSource="{Binding AvailableAlertTypes, Mode=OneWay}" ItemDisplayBinding="{Binding Translation, Mode=OneWay}" SelectedItem="{Binding SelectedAlertType}" />
                    <!--  Alert Argument 0  -->
                    <BoxView Style="{StaticResource Separator}" />
                    <HorizontalStackLayout>
                        <Label Text="{Binding SelectedAlertType.PreferenceType, Mode=OneWay, Converter={StaticResource LoggerAlertTypeToArgumentNameConverter}, ConverterParameter=0}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <Label FontSize="{StaticResource SecondaryFontSize}" HorizontalOptions="EndAndExpand">
                            <MultiBinding Converter="{StaticResource ValueWithDisplayPrecisionConverter}">
                                <Binding Path="AlertArgument0" Mode="OneWay" />
                                <Binding Path="DisplayPrecision" Mode="OneWay" />
                                <Binding Path="Unit" Mode="OneWay" />
                            </MultiBinding>
                        </Label>
                    </HorizontalStackLayout>
                    <Slider Maximum="{Binding ArgumentMax, Mode=OneWay}" Minimum="{Binding ArgumentMin, Mode=OneWay}" Value="{Binding AlertArgument0}" HorizontalOptions="FillAndExpand" />
                    <!--  Alert Argument 1  -->
                    <BoxView Style="{StaticResource Separator}" IsVisible="{Binding SelectedAlertType.PreferenceType, Mode=OneWay, Converter={StaticResource LoggerAlertTypeToArgumentVisibilityConverter}}" />
                    <HorizontalStackLayout IsVisible="{Binding SelectedAlertType.PreferenceType, Mode=OneWay, Converter={StaticResource LoggerAlertTypeToArgumentVisibilityConverter}}">
                        <Label Text="{Binding SelectedAlertType.PreferenceType, Mode=OneWay, Converter={StaticResource LoggerAlertTypeToArgumentNameConverter}, ConverterParameter=1}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                        <Label FontSize="{StaticResource SecondaryFontSize}" HorizontalOptions="EndAndExpand">
                            <MultiBinding Converter="{StaticResource ValueWithDisplayPrecisionConverter}">
                                <Binding Path="AlertArgument1" Mode="OneWay" />
                                <Binding Path="DisplayPrecision" Mode="OneWay" />
                                <Binding Path="Unit" Mode="OneWay" />
                            </MultiBinding>
                        </Label>
                    </HorizontalStackLayout>
                    <Slider
                        Maximum="{Binding ArgumentMax, Mode=OneWay}"
                        Minimum="{Binding ArgumentMin, Mode=OneWay}"
                        Value="{Binding AlertArgument1}"
                        HorizontalOptions="FillAndExpand"
                        IsVisible="{Binding SelectedAlertType.PreferenceType, Mode=OneWay, Converter={StaticResource LoggerAlertTypeToArgumentVisibilityConverter}}" />
                    <!--  Enabled  -->
                    <BoxView Style="{StaticResource Separator}" />
                    <HorizontalStackLayout Spacing="0" HorizontalOptions="FillAndExpand">
                        <Label FontAttributes="Bold" Text="{i18n:Translate LoggerAlertItemPage_Enabled_Title}" HorizontalOptions="StartAndExpand" VerticalOptions="Center" />
                        <controls:Checkbox HorizontalOptions="End" VerticalOptions="FillAndExpand" IsChecked="{Binding Enabled, Mode=TwoWay}" />
                    </HorizontalStackLayout>
                    <!--  AlertName  -->
                    <BoxView Style="{StaticResource Separator}" />
                    <Label Text="{i18n:Translate LoggerAlertItemPage_AlertName_Title}" FontSize="{StaticResource SecondaryFontSize}" FontAttributes="Bold" />
                    <Entry Placeholder="{i18n:Translate LoggerAlertItemPage_AlertName_Placeholder}" Text="{Binding AlertName, Mode=TwoWay}" />
                    <BoxView Style="{StaticResource Separator}" />
                </StackLayout>
            </ScrollView>
            <controls:BigButton
                Grid.Row="2"
                BackgroundColor="{StaticResource PrimarySuccessColor}"
                Command="{Binding SubmitCommand}"
                HorizontalOptions="FillAndExpand"
                Text="{i18n:Translate LoggerAlertItemPage_Submit}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>