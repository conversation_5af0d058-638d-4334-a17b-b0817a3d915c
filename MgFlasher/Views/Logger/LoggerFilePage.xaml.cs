using MgFlasher.Helpers;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using MgFlasher.ViewModels.Logger;
using Microsoft.Maui;
using Microsoft.Maui.Controls.Xaml;
using Syncfusion.Maui.TabView;

namespace MgFlasher.Views.Logger;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class LoggerFilePage : BasePage
{
    private readonly ITabComponentNavigationService _tabComponentNavigationService;

    public new LoggerFilePageViewModel ViewModel => (LoggerFilePageViewModel)BindingContext;

    public LoggerFilePage() : base(DependencyResolver.Resolve<LoggerFilePageViewModel>(), PageType.LoggerFile)
    {
        _tabComponentNavigationService = DependencyResolver.Resolve<ITabComponentNavigationService>();
        InitializeComponent();
    }

    private async void SfTabView_SelectionChanged(object sender, TabSelectionChangedEventArgs e)
    {
        await _tabComponentNavigationService.HandleSelectionChanged(ViewModel, sender, e);
    }

    private void SfTabView_SelectionChanging(object sender, SelectionChangingEventArgs e)
    {
        _tabComponentNavigationService.HandleSelectionChanging(ViewModel, sender, e);
    }

    private void sfTabView_Loaded(object sender, System.EventArgs e)
    {
#if !WINDOWS
        var item = new SfTabItem()
        {
            Header = AppResources.LoggerFilePage_Tab_Map,
            Content = new LoggerFileMapView() { Margin = new Thickness(0, 0, 0, 10), BindingContext = ViewModel.Map }
        };
        sfTabView.Items.Add(item);
#endif
    }
}