﻿using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.ViewModels.Logger;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Maui.Controls.Xaml;
using Microsoft.Maui.Controls;
using Microsoft.Maui;

namespace MgFlasher.Views.Logger;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class LoggerAlertItemPage : BasePage
{
    public LoggerAlertItemPage() : base(DependencyResolver.Resolve<LoggerAlertItemPageViewModel>(), PageType.LoggerAlertItem)
    {
        InitializeComponent();
    }
}