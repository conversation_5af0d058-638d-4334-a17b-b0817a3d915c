<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MgFlasher.Views.Logger.LoggerDisplayPageView"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:loggercontrols="clr-namespace:MgFlasher.Views.Logger.Controls"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    controls:ShellTitleView.HasBarBackButton="True"
    xmlns:tabView="clr-namespace:Syncfusion.Maui.TabView;assembly=Syncfusion.Maui.TabView"
    x:Name="root"
    x:DataType="vms:LoggerDisplayPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="7*" />
                <RowDefinition Height="{OnPlatform Default=1.4*, WinUI=0.7*}" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate LoggerDisplayPage_Title}" />
            <tabView:SfTabView Grid.Row="1" Margin="0,0,0,5">
                <tabView:SfTabItem Header="{i18n:Translate LoggerDisplayPage_Tab_Gauges}">
                    <tabView:SfTabItem.Content>
                        <loggercontrols:LoggerDisplayGaugesView Grid.Row="1" Margin="0,0,0,0" BindingContext="{Binding BindingContext.Gauges, Source={x:Reference root}}" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
                <tabView:SfTabItem Header="{i18n:Translate LoggerDisplayPage_Colors}">
                    <tabView:SfTabItem.Content>
                        <loggercontrols:LoggerDisplaySelectColorView Grid.Row="1" Margin="0,0,0,0" />
                    </tabView:SfTabItem.Content>
                </tabView:SfTabItem>
            </tabView:SfTabView>
            <Grid Grid.Row="2" VerticalOptions="End">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="{OnPlatform Default=Auto, WinUI=0}" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="{OnPlatform Default=0, WinUI=*}" />
                </Grid.ColumnDefinitions>
                <controls:BigButton
                    Grid.Row="0" Grid.Column="0"
                    BackgroundColor="{StaticResource PrimaryAccentColor}"
                    Command="{Binding MiscCommand}"
                    Text="{i18n:Translate LoggerDisplayPage_Misc}" />
                <controls:BigButton
                    Grid.Row="0" Grid.Column="1"
                    BackgroundColor="{StaticResource PrimaryAccentColor}"
                    Command="{Binding DefaultsCommand}"
                    Text="{i18n:Translate LoggerDisplayPage_Defaults}" />
                <controls:BigButton
                    Grid.Column="{OnPlatform Default=0, WinUI=3}"
                    Grid.Row="{OnPlatform Default=1, WinUI=0}"
                    Grid.ColumnSpan="{OnPlatform Default=2, WinUI=1}"
                    IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}"
                    BackgroundColor="{StaticResource PrimarySuccessColor}"
                    Command="{Binding SubmitCommand}"
                    Text="{i18n:Translate LoggerDisplayPage_Save}" />
            </Grid>
        </Grid>
    </views:BasePage.Body>
</views:BasePage>