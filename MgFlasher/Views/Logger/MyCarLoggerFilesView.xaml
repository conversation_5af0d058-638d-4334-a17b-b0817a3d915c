<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="MgFlasher.Views.Logger.MyCarLoggerFilesView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    x:DataType="vms:MyCarLoggerFilesViewModel">
    <controls:SearchableSfListView Margin="0" ItemsSource="{Binding OptionalItemViewModels, Mode=OneWay}" ItemSize="{OnIdiom Phone={OnPlatform Default=70}, Desktop={OnPlatform Default=70}, Default={OnPlatform Android=75, Default=65}}">
        <controls:SearchableSfListView.ItemTemplate>
            <DataTemplate x:DataType="vms:LoggerFileItemViewModel">
                <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1.1*" />
                        <ColumnDefinition Width="5.02*" />
                        <ColumnDefinition Width="0.88*" />
                    </Grid.ColumnDefinitions>
                    <Grid Grid.Column="0" Grid.ColumnSpan="3" Margin="0,3,0,3">
                        <Grid.Resources>
                            <ResourceDictionary>
                                <Style TargetType="Grid" BasedOn="{StaticResource GridMgBaseStyle}">
                                    <Setter Property="BackgroundColor" Value="Transparent" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsChecked, Mode=OneWay}" TargetType="Grid" Value="True" x:DataType="vms:LoggerFileItemViewModel">
                                            <Setter Property="BackgroundColor" Value="{StaticResource ItemSelectedHoverColor}" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ResourceDictionary>
                        </Grid.Resources>
                    </Grid>
                    <Grid Grid.Column="0" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="0.10*" />
                            <RowDefinition Height="1.4*" />
                            <RowDefinition Height="0.10*" />
                        </Grid.RowDefinitions>
                        <controls:Checkbox Grid.Row="1" HorizontalOptions="CenterAndExpand" IsChecked="{Binding IsChecked, Mode=TwoWay}" />
                    </Grid>
                    <Label Grid.Column="1" FontSize="{StaticResource SecondaryFontSize}" Text="{Binding Option, Mode=OneWay}" VerticalTextAlignment="Center" />
                    <Image
                        Grid.Column="2" Aspect="AspectFit" HeightRequest="40"
                        HorizontalOptions="Center" Source="iconexit.png"
                        VerticalOptions="Center" WidthRequest="40">
                        <Image.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding GoToDetailsCommand}" />
                        </Image.GestureRecognizers>
                    </Image>
                </Grid>
            </DataTemplate>
        </controls:SearchableSfListView.ItemTemplate>
    </controls:SearchableSfListView>
</ContentView>