<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:views="clr-namespace:MgFlasher.Views"
    x:Class="MgFlasher.Views.Logger.LoggerReplaceGaugeItemPage"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:sfList="clr-namespace:Syncfusion.Maui.ListView;assembly=Syncfusion.Maui.ListView"
    xmlns:logBehaviors="clr-namespace:MgFlasher.Views.Logger.Behaviors"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels.Logger"
    controls:ShellTitleView.HasBarBackButton="True"
    x:DataType="vms:LoggerReplaceGaugeItemPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="3.84*" />
                <RowDefinition Height="0.52*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate LoggerReplaceGaugeItemPage_Title}" />
            <sfList:SfListView
                Grid.Row="1" Margin="0,0,0,5"
                BackgroundColor="Transparent" SelectionMode="Single"
                ItemSize="{OnIdiom Phone={OnPlatform Default=70}, Desktop={OnPlatform Default=70}, Default={OnPlatform Android=75, Default=65}}"
                ItemsSource="{Binding Items, Mode=OneWay}"
                SelectedItem="{Binding SelectedItem}">
                <sfList:SfListView.ItemTemplate>
                    <DataTemplate x:DataType="vms:GaugeLogDefinitionConfigurationViewModel">
                        <ViewCell>
                            <StackLayout Orientation="Vertical" Spacing="0" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
                                <Label FontSize="{StaticResource SecondaryFontSize}" LineBreakMode="NoWrap" Text="{Binding Name, Mode=OneWay}" HorizontalOptions="FillAndExpand" />
                                <HorizontalStackLayout Spacing="5">
                                    <HorizontalStackLayout.Resources>
                                        <Style BasedOn="{StaticResource LabelMgBaseStyle}" TargetType="Label">
                                            <Setter Property="FontSize" Value="{StaticResource ExtraSmallFontSize}" />
                                            <Setter Property="VerticalTextAlignment" Value="Center" />
                                            <Setter Property="HorizontalOptions" Value="Start" />
                                        </Style>
                                    </HorizontalStackLayout.Resources>
                                    <controls:SmallButton Text="{Binding ModuleName}" Style="{StaticResource RoundedButtonMgStyle}">
                                        <controls:SmallButton.Behaviors>
                                            <logBehaviors:LoggerDisplayButtonColorBehavior Value="{Binding ModuleId}" />
                                        </controls:SmallButton.Behaviors>
                                    </controls:SmallButton>
                                    <controls:SmallButton Text="{Binding PresentationType.Translation}" Style="{StaticResource RoundedButtonMgStyle}">
                                        <controls:SmallButton.Behaviors>
                                            <logBehaviors:LoggerDisplayButtonColorBehavior Value="{Binding PresentationType.PreferenceType}" />
                                        </controls:SmallButton.Behaviors>
                                    </controls:SmallButton>
                                </HorizontalStackLayout>
                            </StackLayout>
                        </ViewCell>
                    </DataTemplate>
                </sfList:SfListView.ItemTemplate>
            </sfList:SfListView>
            <controls:BigButton
                Grid.Row="2"
                BackgroundColor="{StaticResource PrimarySuccessColor}"
                IsEnabled="{Binding CanSubmit, Mode=OneWay}"
                Command="{Binding SubmitCommand}"
                Text="{i18n:Translate LoggerReplaceGaugeItemPage_Submit}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>