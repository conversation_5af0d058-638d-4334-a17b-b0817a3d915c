﻿using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.ViewModels;
using MgFlasher.ViewModels.CustomCodeDiagnostics;
using Microsoft.Maui.Controls.Xaml;

namespace MgFlasher.Views;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class ExhaustFlapDiagnosticsPage : BasePage
{
    public ExhaustFlapDiagnosticsPage() : base(DependencyResolver.Resolve<ExhaustFlapDiagnosticsPageViewModel>(), PageType.ExhaustFlapDiagnostics)
    {
        InitializeComponent();
    }
}