<views:BasePage
    x:Class="MgFlasher.Views.ForgotPasswordPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:behaviors="clr-namespace:MgFlasher.Behaviors"
    xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="clr-namespace:MgFlasher.Views"
    controls:ShellTitleView.HasBarBackButton="True"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:ForgotPasswordPageViewModel"
    HideSoftInputOnTapped="True" mc:Ignorable="d">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="2.7*" />
                <RowDefinition Height="0.1*" />
                <RowDefinition Height="0.52*" />
                <RowDefinition Height="0.02*" />
                <RowDefinition Height="0.52*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Text="{i18n:Translate ForgotPasswordPage_Title}" Style="{StaticResource LabelPageTitleStyle}" />
            <VerticalStackLayout
                Grid.Row="1" Margin="20,0,20,0"
                HorizontalOptions="FillAndExpand" Spacing="15"
                VerticalOptions="CenterAndExpand">
                <VerticalStackLayout>
                    <Label Text="{i18n:Translate Login_Email}" />
                    <Entry IsReadOnly="{Binding IsBusy, Mode=OneWay}" Placeholder="Please enter your email" Text="{Binding Email, Mode=TwoWay}">
                        <Entry.Behaviors>
                            <behaviors:EmailValidatorBehavior IsValid="{Binding IsEmailValid, Mode=OneWayToSource}" />
                        </Entry.Behaviors>
                    </Entry>
                </VerticalStackLayout>
                <Label
                    FontSize="{StaticResource SmallFontSize}"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    IsVisible="{Binding HasMessage}"
                    Text="{Binding Message}"
                    TextColor="{StaticResource PrimaryImportantTextColor}" />
            </VerticalStackLayout>
            <controls:BigButton
                Grid.Row="5" Margin="20,0,20,0"
                BackgroundColor="{StaticResource PrimaryAccentColor}"
                Command="{Binding RecoverCommand}"
                IsEnabled="{Binding CanRecover, Mode=OneWay}"
                Text="{i18n:Translate ForgotPassword_Button}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>