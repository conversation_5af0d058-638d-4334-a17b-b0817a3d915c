<views:BasePage
    x:Class="MgFlasher.Views.FeedbackPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:behaviors="clr-namespace:MgFlasher.Behaviors"
    xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    mc:Ignorable="d"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:DataType="vms:FeedbackPageViewModel">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="3.32*" />
                <RowDefinition Height="0.02*" />
                <RowDefinition Height="0.52*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Text="{i18n:Translate FeedbackPage_Title}" Style="{StaticResource LabelPageTitleStyle}" />
            <VerticalStackLayout Grid.Row="1" HorizontalOptions="FillAndExpand" Spacing="15" VerticalOptions="FillAndExpand">
                <VerticalStackLayout>
                    <Label Text="{i18n:Translate Feedback_Subject}" />
                    <Entry IsReadOnly="{Binding IsBusy, Mode=OneWay}" Placeholder="{i18n:Translate Feedback_PleaseEnterSubject}" Text="{Binding Subject, Mode=TwoWay}" />
                </VerticalStackLayout>
                <VerticalStackLayout>
                    <Label Text="{i18n:Translate Feedback_CustomerEmail}" />
                    <Entry IsReadOnly="{Binding IsBusy, Mode=OneWay}" Placeholder="{i18n:Translate Feedback_PleaseEnterYourEmail}" Text="{Binding CustomerEmail, Mode=TwoWay}">
                        <Entry.Behaviors>
                            <behaviors:EmailValidatorBehavior IsValid="{Binding IsCustomerEmailValid, Mode=OneWayToSource}" />
                        </Entry.Behaviors>
                    </Entry>
                </VerticalStackLayout>
                <VerticalStackLayout VerticalOptions="FillAndExpand">
                    <Label Text="{i18n:Translate Feedback_Message}" />
                    <Editor
                        BackgroundColor="Transparent"
                        IsReadOnly="{Binding IsBusy, Mode=OneWay}"
                        Placeholder="{i18n:Translate Feedback_PleaseEnterMessage}"
                        Text="{Binding Message, Mode=TwoWay}"
                        VerticalOptions="FillAndExpand" />
                </VerticalStackLayout>
            </VerticalStackLayout>
            <controls:BigButton
                Grid.Row="3"
                BackgroundColor="{StaticResource PrimaryAccentColor}"
                Command="{Binding SubmitCommand}"
                IsEnabled="{Binding CanSubmit, Mode=OneWay}"
                Text="{i18n:Translate Feedback_Submit}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>