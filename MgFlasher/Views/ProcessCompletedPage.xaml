<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="MgFlasher.Views.ProcessCompletedPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:MgFlasher.Converters"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    x:DataType="vms:ProcessCompletedPageViewModel"
    controls:ShellTitleView.HasBarBackButton="True"
    controls:ShellTitleView.BarBackButtonVisible="True">
    <views:BasePage.Body>
        <Grid Style="{StaticResource MainGridPageStyle}">
            <Grid.Resources>
                <ResourceDictionary>
                    <converters:CommandResultToColorConverter x:Key="CommandResultToColorConverter" />
                    <converters:CommandResultToTextConverter x:Key="CommandResultToTextConverter" />
                </ResourceDictionary>
            </Grid.Resources>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="2.3*" />
                <RowDefinition Height="1.4*" />
                <RowDefinition Height="0.6*" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Margin="-20,0,-20,0" Style="{StaticResource LabelPageTitleStyle}" Text="{i18n:Translate ProcessCompletedPage_Title}" />
            <Image
                Grid.Row="1" Margin="0,22,0,0" Aspect="AspectFit"
                Source="chipflasheritunesartwork.png"
                VerticalOptions="Center"
                IsVisible="{Binding Success}" />
            <Image
                Grid.Row="1" Margin="0,20,0,0" Aspect="AspectFit"
                Source="chipflasherfailureitunesartwork.png"
                VerticalOptions="Center"
                IsVisible="{Binding Success, Converter={StaticResource InvertedBoolConverter}}" />
            <StackLayout Orientation="Vertical" Grid.Row="2" HorizontalOptions="Center" VerticalOptions="Center">
                <Label
                    FontSize="{StaticResource PrimaryFontSize}"
                    FontAttributes="Bold" HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    Text="{Binding Result, Converter={StaticResource CommandResultToTextConverter}}"
                    TextColor="{Binding Result, Converter={StaticResource CommandResultToColorConverter}, Mode=OneWay}" />
                <Label FontSize="{StaticResource PrimaryFontSize}" HorizontalOptions="Center" HorizontalTextAlignment="Center" Text="{Binding Message}" />
            </StackLayout>
            <controls:BigButton
                AutomationId="ProcessCompletedPage_SubmitButton"
                Grid.Row="3"
                BackgroundColor="{StaticResource PrimaryAccentColor}"
                Command="{Binding BackToMainMenuCommand}"
                Text="{i18n:Translate ProcessCompleted_Back}" />
        </Grid>
    </views:BasePage.Body>
</views:BasePage>