<?xml version="1.0" encoding="UTF-8" ?>
<r:AppShell
    x:Class="MgFlasher.AppShellGlobal"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:MgFlasher.Views.Controls"
    xmlns:i18n="clr-namespace:MgFlasher.Helpers"
    xmlns:models="clr-namespace:MgFlasher.Models"
    xmlns:r="clr-namespace:MgFlasher"
    xmlns:views="clr-namespace:MgFlasher.Views"
    xmlns:vms="clr-namespace:MgFlasher.ViewModels"
    x:Name="shell" x:DataType="vms:SideMenuViewModel"
    BackgroundColor="{StaticResource TitleViewBackgroundColor}"
    FlyoutBackgroundColor="{StaticResource PrimaryBackgroundColor}"
    FlyoutHeaderBehavior="CollapseOnScroll">

    <Shell.Resources>
        <ResourceDictionary>
            <Style Class="FlyoutItemLabelStyle" TargetType="Label">
                <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                <Setter Property="FontSize" Value="{StaticResource PrimaryFontSize}" />
                <Setter Property="Margin" Value="-5,0,0,0" />
                <Setter Property="VisualStateManager.VisualStateGroups">
                    <VisualStateGroupList>
                        <VisualStateGroup x:Name="CommonStates">
                            <VisualState x:Name="Normal">
                                <VisualState.Setters>
                                    <Setter Property="TextColor" Value="{StaticResource PrimaryTextColor}" />
                                </VisualState.Setters>
                            </VisualState>
                        </VisualStateGroup>
                    </VisualStateGroupList>
                </Setter>
            </Style>
        </ResourceDictionary>
    </Shell.Resources>

    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsVisible="False" />
    </Shell.BackButtonBehavior>

    <Shell.FlyoutHeader>
        <VerticalStackLayout Margin="0" Padding="{OnPlatform iOS='20,20,20,76', Default='20'}">
            <Image
                Aspect="AspectFit" HorizontalOptions="FillAndExpand"
                MaximumHeightRequest="90" Source="logo_276.png"
                VerticalOptions="FillAndExpand" />
        </VerticalStackLayout>
    </Shell.FlyoutHeader>

    <MenuItem Command="{Binding NavigateCommand}" CommandParameter="{x:Static models:PageType.User}" IconImageSource="menuusersmall.png" Text="{i18n:Translate Menu_User}" />
    <MenuItem Command="{Binding NavigateCommand}" CommandParameter="{x:Static models:PageType.MyCars}" IconImageSource="menumycarsmall.png" Text="{i18n:Translate Menu_MyCars}" />
    <ShellContent Title="{i18n:Translate Menu_About}" ContentTemplate="{DataTemplate views:AboutPage}" Icon="menudemosmall.png" />
    <MenuItem Command="{Binding NavigateCommand}" CommandParameter="{x:Static models:PageType.KnowledgeBase}" IconImageSource="icon_faq.png" Text="{i18n:Translate Menu_Support}" />
    <MenuItem Command="{Binding NavigateCommand}" CommandParameter="{x:Static models:PageType.Settings}" IconImageSource="menuusersmall.png" Text="{i18n:Translate Menu_Settings}" />
    <MenuItem Command="{Binding NavigateCommand}" CommandParameter="{x:Static models:ActionType.Exit}" IconImageSource="iconexit.png" Text="{i18n:Translate Menu_Exit}" />

    <Shell.FlyoutFooter>
        <Grid HeightRequest="80" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <HorizontalStackLayout Grid.Row="0" HorizontalOptions="Center" Spacing="0" VerticalOptions="EndAndExpand">
                <Label FontAttributes="Bold" Text="mgflasher" />
            </HorizontalStackLayout>
        </Grid>
    </Shell.FlyoutFooter>
</r:AppShell>