﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net9.0-ios;net9.0-android</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
		<UseMaui>True</UseMaui>
		<OutputType>Exe</OutputType>
		<SingleProject>true</SingleProject>
		<ApplicationTitle>MG Flasher</ApplicationTitle>
		<PackageId>com.jrautoperformance.mgflasher</PackageId>
		<ApplicationId>com.jrautoperformance.mgflasher</ApplicationId>
		<ApplicationIdGuid>D293BC7D-685A-4BDB-AE10-D5A301A96137</ApplicationIdGuid>
		<ApplicationDisplayVersion>452</ApplicationDisplayVersion>
		<ApplicationVersion>1337</ApplicationVersion>
		<Version>$(ApplicationDisplayVersion)</Version>
		<ApplicationId Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">com.mgflasher.app</ApplicationId>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.4</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">29.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>
		<TargetPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">35</TargetPlatformVersion>
		<UseInterpreter Condition="'$(Configuration)'=='Debug'">true</UseInterpreter>
		<StringEncryption>hash</StringEncryption>
		<WindowsSdkPackageVersion>10.0.19041.38</WindowsSdkPackageVersion>
	</PropertyGroup>
	<ItemGroup>
		<None Include="..\Settings.XamlStyler" Link="Settings.XamlStyler" />
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#141415" />
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#141415" BaseSize="128,128" />
		<MauiImage Include="Resources\Images\*.png" />
		<MauiImage Include="Resources\Images\CustomOptions\*.svg" />
		<MauiImage Include="Resources\Images\CustomOptions\*.png" />
		<MauiImage Include="Resources\Images\flashinganimation.gif" />
		<MauiImage Include="Resources\Images\preloader.gif" />
		<MauiImage Update="Resources\Images\*.gif" Resize="False" />
		<MauiFont Include="Resources\Fonts\*" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="CommunityToolkit.Maui.Maps" Version="3.0.1" />
		<PackageReference Include="FFImageLoading.Maui" Version="1.2.7" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="9.0.30" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="9.0.30" />
		<PackageReference Include="CommunityToolkit.Maui" Version="11.0.0" />
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
		<PackageReference Include="Microsoft.Maui.Controls.Maps" Version="9.0.30" />
		<PackageReference Include="Plugin.Maui.Audio" Version="3.0.1" />
		<PackageReference Include="Shiny.Notifications" Version="3.3.4" />
		<PackageReference Include="Syncfusion.Maui.Gauges" Version="28.1.41" />
		<PackageReference Include="Syncfusion.Maui.Inputs" Version="28.1.41" />
		<PackageReference Include="Syncfusion.Maui.Popup" Version="28.1.41" />
		<PackageReference Include="Syncfusion.Maui.TabView" Version="28.1.41" />
		<PackageReference Include="Syncfusion.Maui.ProgressBar" Version="28.1.41" />
		<PackageReference Include="Syncfusion.Maui.Buttons" Version="28.1.41" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="InputKit.Maui" Version="4.5.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.1" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.1" />		
		<PackageReference Include="Shiny.Push" version="3.3.4" />
		<PackageReference Include="Shiny.Hosting.Maui" version="3.3.4" />
		<PackageReference Include="Shiny.Push.AzureNotificationHubs" Version="3.3.4" />
		<PackageReference Include="Shiny.Jobs" version="3.3.4" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\MgFlasher.Flasher.Commands\MgFlasher.Flasher.Commands.csproj" />
		<ProjectReference Include="..\MgFlasher.Flasher.Services\MgFlasher.Flasher.Services.csproj" />
		<ProjectReference Include="..\MgFlasher.Files\MgFlasher.Files.csproj" />
		<ProjectReference Include="..\MgFlasher.Logging\MgFlasher.Logging.csproj" />
		<ProjectReference Include="..\MG_Flash.Core\MG_Flash.Core.csproj" />
		<ProjectReference Include="..\MgFlasher.Localization\MgFlasher.Localization.csproj" />
	</ItemGroup>
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)'=='Debug|net9.0-android'">
		<_MauiForceXamlCForDebug>true</_MauiForceXamlCForDebug>
	</PropertyGroup>
	<PropertyGroup Condition="$(TargetFramework.Contains('-android')) and '$(Configuration)' == 'Release'">
		<AndroidKeyStore>True</AndroidKeyStore>
		<AndroidSigningKeyStore>..\distribution-files\certs\android-cert\mgflasher.keystore</AndroidSigningKeyStore>
		<AndroidSigningStorePass>mgflasher</AndroidSigningStorePass>
		<AndroidSigningKeyAlias>mgflasher</AndroidSigningKeyAlias>
		<AndroidSigningKeyPass>mgflasher</AndroidSigningKeyPass>
		<AndroidPackageFormat>aab</AndroidPackageFormat>
	</PropertyGroup>
	<ItemGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">
		<AndroidEnvironment Include="Platforms\Android\environments.txt" />
		<GoogleServicesJson Include="Platforms\Android\google-services.json" />
		<None Remove="Platforms\Android\Resources\**" />
	</ItemGroup>
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)'=='Release|net9.0-ios'">
		<MtouchNoSymbolStrip>True</MtouchNoSymbolStrip>
		<BuildIpa>True</BuildIpa>
		<CodesignKey>Apple Distribution: JR Auto Performance Inc. (9GBFVVF687)</CodesignKey>
		<CodesignProvision>MGFlasherDistProvisioningProfile</CodesignProvision>
	</PropertyGroup>
	<ItemGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">
		<BundleResource Include="Platforms\iOS\PrivacyInfo.xcprivacy" LogicalName="PrivacyInfo.xcprivacy" />
		<BundleResource Include="Platforms\iOS\Resources\*.png" LogicalName="%(Filename)%(Extension)" />
		<BundleResource Include="Platforms\iOS\Resources\*.gif" LogicalName="%(Filename)%(Extension)" />
		<BundleResource Include="Resources\Images\*" LogicalName="%(Filename)%(Extension)" />
	</ItemGroup>
	<ItemGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">
		<Content Include="Resources\Raw\alert.mp3">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<Link>alert.mp3</Link>
		</Content>
		<None Remove="Platforms\Windows\Resources\**" />
		<MauiImage Include="Platforms\Windows\Resources\*.png" Link="%(Filename)%(Extension)" />
		<MauiImage Include="Platforms\Windows\Resources\*.gif" Link="%(Filename)%(Extension)" />
	</ItemGroup>
	<ItemGroup Condition="$(TargetFramework.Contains('-windows'))">
		<PackageReference Include="WinUIEx" Version="2.5.1" />
		<PackageReference Include="Tools.InnoSetup" Version="[6.3.1]" />
		<PackageReference Include="Microsoft.Windows.CsWinRT" Version="2.2.0" />
	</ItemGroup>
	<PropertyGroup Condition="$(TargetFramework.Contains('-windows')) and '$(Configuration)' == 'Release'">
		<GenerateAppxPackageOnBuild>true</GenerateAppxPackageOnBuild>
		<RuntimeIdentifier>win10-x64</RuntimeIdentifier>
		<PublishExe>true</PublishExe>
		<WindowsAppSDKSelfContained>true</WindowsAppSDKSelfContained>
	</PropertyGroup>
</Project>
