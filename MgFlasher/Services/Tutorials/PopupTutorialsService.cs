﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using MgFlasher.Models;
using Microsoft.Maui.Storage;

namespace MgFlasher.Services.Tutorials;

public static class PopupTutorialsService
{
    private const string PopupShownKeyPrefix = "TutorialPopupShown_";

    public static bool IsPopupShown(PageType page)
    {
        return Preferences.Get(PopupShownKeyPrefix + page, false);
    }

    public static void MarkPopupAsShown(PageType page)
    {
        Preferences.Set(PopupShownKeyPrefix + page, true);
    }

    public static void ResetAllPopupTutorials()
    {
        foreach (PageType pageType in Enum.GetValues(typeof(PageType)))
        {
            Preferences.Remove(PopupShownKeyPrefix + pageType);
        }
    }

    public static ObservableCollection<PopupTutorial> GetTutorial(PageType pageType)
    {
        // If tutorial is not showing up, then either the respective ViewModel is not calling
        // base.OnNavigatedTo, or it is displayed as a tab (no fix yet for that). Remember to add Title in
        // GetPageTitle as well when adding a new tutorial!
        return pageType switch
        {
            PageType.BuyFlashStage => null,
            PageType.User => null,
            PageType.MyCar => null,
            PageType.ReadDtc => null,
            PageType.SwitchableMapDiagnostics => null,
            PageType.BurbleDiagnostics => null,
            PageType.MaxCoolinDiagnostics => null,
            PageType.AntilagDiagnostics => null,
            PageType.EthanolOverrideDiagnostics => null,
            PageType.ExhaustFlapDiagnostics => null,
            PageType.RadiatorFlapDiagnostics => null,
            PageType.ValetModeDiagnostics => null,
            PageType.FlashingCar => null,
            PageType.ProcessCompleted => null,
            PageType.FlashingCarOptions => CreateListTutorial(
            [
                "Here you can customize options for the flashing",
                "Enable or disable options using checkboxes",
                "Some options have Cog icons - You can click on them to access more detailed options",
                "You can find a detailed desciription of each option in our User Manual on mgflasher.com"
            ]),
            PageType.FlashingCarWarningInfo => null,
            PageType.ResetAdaptation => null,
            PageType.LoggerParameters => CreateListTutorial(
            [
                "This is a list of available parameters which you can log",
                "Enable/disable according to your needs",
                "You can use the search bar to find specific parameters quicker than scrolling",
                "Navigate to Preferences tab to enable GPS data to be logged as well"
            ]),
            PageType.Pending => null, // No tutorials on Pending Page, will crash Windows vesion!
            PageType.ReadModule => null,
            PageType.About => CreateListTutorial(
            [
                "Here you can find information about your app version",
                "Use the 'Share App Logs' button when asked by support, to export logs which help the development team to find issues"
            ]),
            PageType.CustomMapFiles => null,
            PageType.ForgotPassword => null,
            PageType.Login => CreateListTutorial(
            [
                "Login using your credentials",
                "The same credentials can be used at shop.mgflasher.com",
                "If you don't have an acount, you can register a new account"
            ]),
            PageType.Register => CreateListTutorial(
            [
                "Here you can create a new account",
                "Enter your information and a safe password",
                "Remember what email you are using and the password, you will be using it to login to MG Flasher"
            ]),
            PageType.MyCars => CreateListTutorial(
            [
                "This is a list of all Cars you have connected to from this device",
                "Click the cog to Sync, Rename or Delete the car",
                "Click on the Car or the enter icon to open the Car, check its details and proceed to Flashing",
                "You can also connect to a new Car from here",
                "Open the menu and navigate to each page to familiarize yourself with them."
            ]),
            PageType.LoggerUnits => null,
            PageType.Feedback => null,
            PageType.StageVersions => null,
            PageType.SyncPage => null,
            PageType.LoggerFile => null,
            PageType.Settings => null,
            PageType.CustomCodeVersions => null,
            PageType.LoggerDisplay => CreateListTutorial(
            [
                "This is the Logger - here you can capture live parameters from the car and storem them in a file for analysis",
                "To change what parameters are captured, open the context menu and select 'Go to display settings'",
                "Click 'Activate' to start reading parameters data from the Car",
                "Click 'Start' and after you are done, the 'Stop' buttons to capture parameters' values to a log file",
                "Click on the 'Logs' button to view created log files"
            ]),
            PageType.LoggerDisplayGaugeItem => null,
            PageType.LoggerReplaceGaugeItem => null,
            PageType.LoggerAlerts => null,
            PageType.LoggerAlertItem => null,
            PageType.UserDtcRemovalList => null,
            PageType.FlashingCarOptionsItem => null,
            PageType.MyCarFlashHistoryItemDetail => null,
            PageType.KnowledgeBase => null,
            _ => throw new NotImplementedException()
        };
    }

    public static string GetPageTitle(PageType currentPageType)
    {
        return currentPageType switch
        {
            PageType.BuyFlashStage => "",
            PageType.User => "",
            PageType.MyCar => "",
            PageType.ReadDtc => "",
            PageType.SwitchableMapDiagnostics => "",
            PageType.BurbleDiagnostics => "",
            PageType.MaxCoolinDiagnostics => "",
            PageType.AntilagDiagnostics => "",
            PageType.EthanolOverrideDiagnostics => "",
            PageType.ExhaustFlapDiagnostics => "",
            PageType.RadiatorFlapDiagnostics => "",
            PageType.ValetModeDiagnostics => "",
            PageType.FlashingCar => "",
            PageType.ProcessCompleted => "",
            PageType.FlashingCarOptions => "Flashing Options Tutorial",
            PageType.FlashingCarWarningInfo => "",
            PageType.ResetAdaptation => "",
            PageType.LoggerParameters => "Logger Parameters Selection Tutorial",
            PageType.Pending => "",
            PageType.ReadModule => "",
            PageType.About => "About Page Tutorial",
            PageType.CustomMapFiles => "",
            PageType.ForgotPassword => "",
            PageType.Login => "Login Tutorial",
            PageType.Register => "Registration Tutorial",
            PageType.MyCars => "My Cars List Tutorial",
            PageType.LoggerUnits => "",
            PageType.Feedback => "",
            PageType.StageVersions => "",
            PageType.SyncPage => "",
            PageType.LoggerFile => "",
            PageType.Settings => "",
            PageType.CustomCodeVersions => "",
            PageType.LoggerDisplay => "Logger Tutorial",
            PageType.LoggerDisplayGaugeItem => "",
            PageType.LoggerReplaceGaugeItem => "",
            PageType.LoggerAlerts => "",
            PageType.LoggerAlertItem => "",
            PageType.UserDtcRemovalList => "",
            PageType.FlashingCarOptionsItem => "",
            PageType.MyCarFlashHistoryItemDetail => "",
            PageType.KnowledgeBase => "",
            _ => throw new NotImplementedException()
        };
    }

    public static ObservableCollection<PopupTutorial> CreateListTutorial(List<string> steps)
    {
        var tutorial = new ObservableCollection<PopupTutorial>();
        for (int i = 0; i < steps.Count; i++)
        {
            var stepLabel = $"Step {i + 1}: {steps[i]}";
            var tutorialStep = new PopupTutorial { Step = stepLabel };
            tutorial.Add(tutorialStep);
        }

        return tutorial;
    }
}