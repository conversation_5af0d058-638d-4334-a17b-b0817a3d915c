﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MgFlasher.Flasher.Client.Backend.Client.Contract;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Backend;
using MgFlasher.Flasher.Services.User.Dialogs;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Storage;

namespace MgFlasher.Services;

public class AppVersionBlockService(IUserDialogs userDialogs, ILogger<AppVersionBlockService> logger, IBackendService backendService, IAppUpdateService appUpdateService) : IAppVersionBlockService
{
    public const string AppVersionBlockKey = "app_version_block";

    public async Task BlockAppVersion()
    {
        logger.LogInformation("App has been blocked from flashing.");
        Preferences.Set(AppVersionBlockKey, true);
    }

    public async Task NotifyUserOfBlock()
    {
        logger.LogInformation("User notified about app blocked from flashing.");
        await userDialogs.AlertAsync($"This version (v{AppInfo.VersionString}) of MG Flasher is no longer supported, please update to the latest version to continue!");
        await appUpdateService.CloseAppAndNavigateToStore();
    }

    public async Task UnblockAppVersion()
    {
        var currentStatus = Preferences.Get(AppVersionBlockKey, false);
        if (currentStatus)
        {
            Preferences.Set(AppVersionBlockKey, false);
            logger.LogInformation("App has been unblocked from flashing after update.");
        }
    }

    public async Task CheckAndUpdateAppVersionBlockStatus()
    {
        var appVersionMain = AppInfo.VersionString.Contains('.') ? AppInfo.VersionString.Split('.').First() : AppInfo.VersionString;
        var request = new AppVersionBlockRequest()
        {
            AppVersion = AppInfo.VersionString,
        };

        var result = await backendService.GetAppVersionBlockStatus(request);
        if (result)
        {
            await BlockAppVersion();
        }
        else
        {
            await UnblockAppVersion();
        }
    }

    public async Task CheckAppVersionBlockStatus()
    {
        var currentStatus = Preferences.Get(AppVersionBlockKey, false);
        if (currentStatus)
        {
            await NotifyUserOfBlock();
        }
    }
}