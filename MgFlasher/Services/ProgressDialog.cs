﻿using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.ViewModels;

namespace MgFlasher.Services;

public class ProgressDialog : IProgressDialog
{
    private readonly PageLoaderViewModel _pageLoaderViewModel;

    public ProgressDialog(PageLoaderViewModel pageLoaderViewModel, string title)
    {
        _pageLoaderViewModel = pageLoaderViewModel;
        _pageLoaderViewModel.LoadingTitle = title;
        _pageLoaderViewModel.IsLoading = true;
    }

    public void Dispose()
    {
        _pageLoaderViewModel.IsLoading = false;
        _pageLoaderViewModel.LoadingTitle = AppResources.Loading;
    }
}