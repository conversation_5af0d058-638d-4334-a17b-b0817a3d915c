﻿using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.FlashHistory;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MgFlasher.CustomCode.Storage.Changesets;

namespace MgFlasher.Services;

public class ChangesetVersionNameViewModelsFactory : IChangesetVersionNameViewModelsFactory
{
    private readonly ICarCompatabilityService _compatibilityService;
    private readonly IFlashHistoryRepository _flashHistoryService;
    private readonly IAppContext _appContext;

    public ChangesetVersionNameViewModelsFactory(
        ICarCompatabilityService carCompatabilityService,
        IFlashHistoryRepository flashHistoryService,
        IAppContext appContext)
    {
        _compatibilityService = carCompatabilityService;
        _flashHistoryService = flashHistoryService;
        _appContext = appContext;
    }

    public async Task<IEnumerable<ChangesetVersionNameViewModel>> CreateAsync(StageType stageType, CustomCodeId customCodeVersion,
        Car car, Action<IRadioButtonViewModel> checkedChanged)
    {
        var changesetIsNewerMap = new Dictionary<Changeset, bool>();

        var versionNameGroups = GetChangesetsGroupedByVersionName(stageType, customCodeVersion, car);

        var result = new List<ChangesetVersionNameViewModel>();
        foreach (var versionNameGroup in versionNameGroups)
        {
            var newestVersion = versionNameGroup.Select(v => Version.Parse(v.VersionNumber)).OrderByDescending(v => v).FirstOrDefault();
            var lastRelease = versionNameGroup
                .OrderByDescending(c => Version.Parse(c.VersionNumber))
                .ThenByDescending(c => c.ReleasedAtUtc)
                .FirstOrDefault();
            var isNewer = await _flashHistoryService.IsNewerChangeset(car.Fingerprint, lastRelease);
            var releasedAt = ParseDate(lastRelease.ReleasedAtUtc);
            var versionNumberVms = versionNameGroup
                .OrderByDescending(c => Version.Parse(c.VersionNumber))
                .ThenByDescending(c => c.ReleasedAtUtc)
                .Select(c => CreateVersionNumberViewModel(checkedChanged, c, newestVersion))
                .ToList();

            result.Add(new ChangesetVersionNameViewModel(
                stageType: stageType,
                versionName: versionNameGroup.Key,
                isNewVersion: isNewer,
                newestVersionNumberReleasedAt: releasedAt,
                changesetId: lastRelease.Id,
                newestVersionNumber: lastRelease.VersionNumber,
                versionNumberViewModels: versionNumberVms,
                checkedAction: checkedChanged));
        }

        await TryToCheckLastFlashedItem(result, car);

        return result;
    }

    private static ChangesetVersionNumberViewModel CreateVersionNumberViewModel(Action<IRadioButtonViewModel> checkedChanged, Changeset changeset, Version newestVersion)
    {
        var versionNumber = $"v{changeset.VersionNumber}";
        var isNewestVersion = Version.Parse(changeset.VersionNumber).CompareTo(newestVersion) == 0;
        var releaseNotes = CreateReleaseNotes(changeset);
        return new ChangesetVersionNumberViewModel(versionNumber: versionNumber,
            isNewestVersion: isNewestVersion,
            releaseNotes: releaseNotes,
            changesetId: changeset.Id,
            checkedChanged: checkedChanged);
    }

    private static string CreateReleaseNotes(Changeset changeset)
    {
        var sb = new StringBuilder();
        foreach (var patch in changeset.Patches?.OrderByDescending(x => x.PatchNumber) ?? Enumerable.Empty<ChangesetPatch>())
        {
            var releasedAt = ParseDate(patch.ReleasedAtUtc);
            sb.AppendLine($"{AppResources.AvailableStageVersion_OtsPatch} - {releasedAt:yyyy-HH-mm}");
            sb.AppendLine(!string.IsNullOrEmpty(patch.ReleaseNotes) ? patch.ReleaseNotes : AppResources.AvailableStageVersion_EmptyReleaseNotes);
            sb.AppendLine();
        }
        sb.Append(changeset.ReleaseNotes);
        return sb.ToString();
    }

    private List<IGrouping<string, Changeset>> GetChangesetsGroupedByVersionName(StageType stageType, CustomCodeId customCodeVersion, Car car)
    {
        var changesets = car.Changesets.GetChangesetsByType(stageType);
        var result = changesets
            .Where(cv => _compatibilityService.IsOtsMapCompatibleWithCustomCodeVersion(car, Convert.ToDouble(cv.VersionNumber, CultureInfo.InvariantCulture), customCodeVersion))
            .Where(c => _compatibilityService.IsChinaSupportedFuelVersion(car, c))
            .GroupBy(v => v.VersionName)
            .OrderByDescending(v => GetOrderKey(v.Key))
            .ToList();
        return result;
    }

    private async Task TryToCheckLastFlashedItem(List<ChangesetVersionNameViewModel> result, Car car)
    {
        if (result is null || result.Count < 1)
        {
            MarkFirstOption(result);
            return;
        }

        var lastFlashHistory = await _flashHistoryService.GetLastSucceddedFlashHistoryItem(car.Fingerprint);
        if (lastFlashHistory is null)
        {
            MarkFirstOption(result);
            return;
        }

        var versionNameGroup = result.FirstOrDefault(r => r.StageType == lastFlashHistory.StageType && r.VersionName == lastFlashHistory.VersionName);
        if (versionNameGroup is null)
        {
            MarkFirstOption(result);
            return;
        }

        versionNameGroup.IsChecked = true;
        return;
    }

    private void MarkFirstOption(List<ChangesetVersionNameViewModel> result)
    {
        if (result is null || !result.Any())
        {
            return;
        }
        result.FirstOrDefault().IsChecked = true;
    }

    private int GetOrderKey(string versionName)
    {
        var oct = versionName
            .Replace("RON", "", StringComparison.OrdinalIgnoreCase)
            .Replace("OCT", "", StringComparison.OrdinalIgnoreCase);
        if (int.TryParse(oct, NumberStyles.Integer, CultureInfo.InvariantCulture, out int value))
        {
            return value;
        }
        return 0;
    }

    private static DateTime ParseDate(string raw)
    {
        return DateTime.Parse(raw, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal).ToLocalTime();
    }
}