﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MgFlasher.Services;

/// <summary>
/// A service for handling App Updates.
/// </summary>
public interface IAppUpdateService
{
    /// <summary>
    /// Closes the application gracefully and opens the correct store for user for quick update.
    /// </summary>
    /// <returns></returns>
    Task CloseAppAndNavigateToStore();

    /// <summary>
    /// Notifies the user about the new version if it is available for download.
    /// </summary>
    /// <returns></returns>
    Task NotifyNewVersionAvailableAsync();
}