﻿using MgFlasher.Files;
using MgFlasher.Flasher.Services;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Cars.Files;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Localization.Resources;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Services.Navigation;
using MgFlasher.Models;

namespace MgFlasher.Services;

public class ShareStockMapCommand : ICommand
{
    private readonly ICarDirectoryPathFactory _directoryPathFactory;
    private readonly IDCANService _dCANService;
    private readonly IFilesService _filesService;
    private readonly IUserDialogs _userDialogs;
    private readonly IShareService _shareService;
    private readonly INavigationService _navigationService;
    private readonly ILogger<ShareStockMapCommand> _logger;

    public ShareStockMapCommand(
        ICarDirectoryPathFactory directoryPathFactory,
        IDCANService dCANService,
        IFilesService filesService,
        IUserDialogs userDialogs,
        IShareService shareService,
        INavigationService navigationService,
        ILogger<ShareStockMapCommand> logger)
    {
        _directoryPathFactory = directoryPathFactory;
        _dCANService = dCANService;
        _filesService = filesService;
        _userDialogs = userDialogs;
        _shareService = shareService;
        _navigationService = navigationService;
        _logger = logger;
    }

    public event EventHandler CanExecuteChanged;

    public bool CanExecute(object parameter) => true;

    public async void Execute(object parameter)
    {
        try
        {
            if (await _dCANService.GetConnectedCar() is not Car connectedCar || connectedCar.StockMap is null)
            {
                await _userDialogs.AlertAsync(AppResources.MissingStockFiles, okText: AppResources.Ok);
                return;
            }

            var stockMap = await GetStockMapAsync(connectedCar.Fingerprint);
            if (IsStockMapIncorrect(connectedCar.VIN, stockMap))
            {
                await _userDialogs.AlertAsync(AppResources.StockFile_DoesntExist, okText: AppResources.Ok);
                await _navigationService.NavigateAsync(PageType.SyncPage);
            }

            stockMap = await GetStockMapAsync(connectedCar.Fingerprint);
            if (IsStockMapIncorrect(connectedCar.VIN, stockMap))
            {
                await _userDialogs.AlertAsync(AppResources.Error_Occured, okText: AppResources.Ok);
                return;
            }

            await _shareService.Share(connectedCar, stockMap);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Couldnt share stock map");
        }
    }

    private async Task<FileDescriptor> GetStockMapAsync(string fingerprint)
    {
        var maps = await _filesService.GetFileDescriptorsAsync(fingerprint);
        var stockMap = maps.FirstOrDefault(x => x.Type == FileType.STOCK);
        return stockMap;
    }

    private bool IsStockMapIncorrect(string vin, FileDescriptor stockMap) =>
        stockMap is null || !stockMap.Exists(vin, _directoryPathFactory);
}