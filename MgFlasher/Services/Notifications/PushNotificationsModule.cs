using MgFlasher.Flasher.Services.User.Notifications;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Maui.Hosting;
using Shiny;
using Shiny.Push;

namespace MgFlasher.Services.Notifications;

public static class PushNotificationsModule
{
    public static MauiAppBuilder UsePushNotifications(this MauiAppBuilder builder)
    {
        builder.UseShiny();
        return builder;
    }

    public static IServiceCollection AddPushNotifications(this IServiceCollection services)
    {
        var config = NotificationsConfig.Create();
        services.AddPush<MgFlasherPushDelegate>();
        services.AddPushAzureNotificationHubs<MgFlasherPushDelegate>(config.ListenConnectionString, config.NotificationHubName);
        services.AddSingleton<ITagsListener, TagsListener>();
        return services;
    }

#if WINDOWS
    private static IServiceCollection AddPush<T>(this IServiceCollection services) where T : IPushDelegate
    {
        services.AddSingleton<IPushManager, Platforms.Windows.Services.WindowsPushManager>();
        return services;
    }
    private static IServiceCollection AddPushAzureNotificationHubs<T>(this IServiceCollection services, string _, string __) where T : IPushDelegate
    {
        return services;
    }
#endif
}