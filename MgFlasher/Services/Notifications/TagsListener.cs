using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.Flasher.Services.User.Notifications;
using Microsoft.Extensions.Logging;
using Shiny;
using Shiny.Push;

namespace MgFlasher.Services.Notifications;

public class TagsListener(IPushManager manager, ILogger<TagsListener> logger) : ITagsListener
{
    private bool _supported;

    public async Task InitAsync()
    {
        try
        {
            var result = await manager.RequestAccess();
            _supported = result.Status == AccessState.Available && manager.Tags is not null;
            logger.LogInformation("InitAsync - PushAccess [{Status}] [{Token}] [{Supported}]", result.Status, result.RegistrationToken, _supported);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "TagsListener could not be initialized");
        }
    }

    public async Task ListenAsync(IEnumerable<string> tags)
    {
        if (!_supported)
            return;

        var items = tags?.ToArray() ?? [];
        try
        {
            var result = await manager.RequestAccess();
            if (result == PushAccessState.Denied)
            {
                logger.LogInformation("Access denied");
                return;
            }
            await manager.Tags.ClearTags();
            await manager.Tags.SetTags(items);
            logger.LogInformation("SetTags success");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Could not set notifications tags [{Tags}]", string.Join(", ", items));
        }
    }
}