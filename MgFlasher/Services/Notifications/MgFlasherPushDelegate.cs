using System;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Models;
using MgFlasher.Services.Navigation;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Dispatching;
using Microsoft.Maui.Storage;
using Shiny.Notifications;
using Shiny.Push;

namespace MgFlasher.Services.Notifications;

public class MgFlasherPushDelegate(
    INavigationService navigationService,
    IUserDialogs userDialogs,
    ILogger<MgFlasherPushDelegate> logger,
    ICarRepository carRepository,
    IAppVersionBlockService appVersionBlockService) : IPushDelegate
{
    private const string AlertCommand = "Alert";
    private const string NavigateCommand = "Navigate";
    private const string DeprecateCommand = "Deprecated";
    private const string AppVersionBlockCommand = "BlockFlashingInAppVersion";
    private const string CommandParameter = "CommandParameter";
    private const string TitleParameter = "Title";
    private const string TextParameter = "Text";

    private static DateTime? _commandTimeStamp;

    public async Task OnEntry(PushNotification notification)
    {
        try
        {
            logger.LogInformation($"{nameof(OnEntry)} - data [{{notification}}]", JsonSerializer.Serialize(notification));
            if (_commandTimeStamp != null && _commandTimeStamp > DateTime.UtcNow.AddSeconds(-5))
            {
                return;
            }

            _commandTimeStamp = DateTime.UtcNow;
            var command = notification.Data.TryGetValue("Command", out var result) ? result : "";
            if (command == AlertCommand)
            {
                await HandleAlertCommandAsync(notification);
            }
            else if (command == NavigateCommand)
            {
                await HandleNavigateCommandAsync(notification);
            }
            else if (command == DeprecateCommand)
            {
                await HandleDeprecateCommand(notification, false);
            }
            else if (command == AppVersionBlockCommand)
            {
                await HandleAppVersionBlockCommand(notification, false);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Could not handle notification");
        }
    }

    public Task OnReceived(PushNotification arg)
    {
        logger.LogInformation($"{nameof(OnReceived)} - data [{{notification}}]", JsonSerializer.Serialize(arg));
        try
        {
            var command = arg.Data.TryGetValue("Command", out var result) ? result : "";
            if (command == DeprecateCommand)
            {
                HandleDeprecateCommand(arg, true);
            }
            else if (command == AppVersionBlockCommand)
            {
                HandleAppVersionBlockCommand(arg, true);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Could not handle notification");
        }

        return Task.CompletedTask;
    }

    public Task OnNewToken(string token)
    {
        logger.LogInformation($"{nameof(OnNewToken)} - token [{{token}}]", token);
        return Task.CompletedTask;
    }

    public Task OnUnRegistered(string token)
    {
        logger.LogInformation($"{nameof(OnUnRegistered)} - token [{{token}}]", token);
        return Task.CompletedTask;
    }

    private async Task HandleAlertCommandAsync(PushNotification push)
    {
        var title = push.Notification?.Title ?? (push.Data.TryGetValue(TitleParameter, out var titleData) ? titleData : "");
        var text = push.Notification?.Message ?? (push.Data.TryGetValue(TextParameter, out var textData) ? textData : "");
        await userDialogs.AlertAsync(text, title);
    }

    private async Task HandleNavigateCommandAsync(PushNotification push)
    {
        if (!push.Data.TryGetValue(CommandParameter, out var commandParameter))
        {
            logger.LogWarning("Could not handle navigate command");
            return;
        }

        if (!Enum.TryParse<PageType>(commandParameter, true, out var pageType))
        {
            logger.LogWarning("Could not handle navigate command - invalid parameter [{CommandParameter}]", commandParameter);
            return;
        }

        var title = push.Notification?.Title ?? (push.Data.TryGetValue(TitleParameter, out var titleData) ? titleData : "");
        var text = push.Notification?.Message ?? (push.Data.TryGetValue(TextParameter, out var textData) ? textData : "");
        await userDialogs.AlertAsync(text, title);
        await Application.Current.Dispatcher.DispatchAsync(async () =>
        {
            await navigationService.NavigateAsync(pageType);
        });
    }

    private async Task HandleDeprecateCommand(PushNotification push, bool isSilent)
    {
        logger.LogInformation($"{nameof(HandleDeprecateCommand)} - handling command [{{notification}}]", JsonSerializer.Serialize(push));
        if (isSilent)
        {
            await userDialogs.Toast(new(push.Notification.Message, true));
        }

        if (!push.Data.TryGetValue(CommandParameter, out var commandParameter))
        {
            logger.LogWarning("Could not handle deprecate command");
            return;
        }

        if (!int.TryParse(commandParameter, out int customCodeChangesetId))
        {
            logger.LogWarning("Could not handle deprecate command - invalid parameter [{CommandParameter}]", commandParameter);
            return;
        }

        foreach (var car in await carRepository.GetAll())
        {
            var customCodeChangesetDoReprecate = car.CustomCodeChangesets.FirstOrDefault(x => x.Id == customCodeChangesetId);
            if (customCodeChangesetDoReprecate is null)
            {
                continue;
            }

            customCodeChangesetDoReprecate.Deprecated = true;
            await carRepository.SaveCarAsync(car);
        }
    }

    private async Task HandleAppVersionBlockCommand(PushNotification push, bool isSilent)
    {
        logger.LogInformation($"{nameof(HandleAppVersionBlockCommand)} - handling command [{{notification}}]", JsonSerializer.Serialize(push));
        if (isSilent)
        {
            await userDialogs.Toast(new(push.Notification.Message, true));
        }

        if (!push.Data.TryGetValue(CommandParameter, out var commandParameter))
        {
            logger.LogWarning("Could not handle app version block command");
            return;
        }

        if (!int.TryParse(commandParameter, out int appVersion))
        {
            logger.LogWarning("Could not handle app version block command - invalid parameter [{CommandParameter}]", commandParameter);
            return;
        }

        if (appVersion.ToString() == AppInfo.VersionString)
        {
            await appVersionBlockService.BlockAppVersion();
            await appVersionBlockService.NotifyUserOfBlock();
        }
    }
}