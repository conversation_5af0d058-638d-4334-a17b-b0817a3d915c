namespace MgFlasher.Services.Notifications;

public class NotificationsConfig
{
    public string NotificationHubName { get; }
    public string ListenConnectionString { get; }

    private NotificationsConfig(string notificationHubName, string listenConnectionString)
    {
        NotificationHubName = notificationHubName;
        ListenConnectionString = listenConnectionString;
    }

    public static NotificationsConfig Create()
    {
#if DEBUG
        return new NotificationsConfig(
            "mgflasher-hub-dev",
            "Endpoint=sb://mgflasher-hub-namespace.servicebus.windows.net/;SharedAccessKeyName=DefaultListenSharedAccessSignature;SharedAccessKey=zbJc6XL/xCVCGIsMgYM3ONkeY8fzUC4UGSIASnmGK/c=");
#else
            return new NotificationsConfig(
                "mgflasher-hub",
                "Endpoint=sb://mgflasher-hub-namespace.servicebus.windows.net/;SharedAccessKeyName=DefaultListenSharedAccessSignature;SharedAccessKey=GqLlFF4J5nf/UkKZLZSXFbALnK2XDDU1rBz8ahDHRQs=");
#endif
    }
}