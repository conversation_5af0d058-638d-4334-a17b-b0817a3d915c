﻿using MgFlasher.CustomCode;
using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Flasher.Services.Models;
using MgFlasher.Localization.Resources;
using MgFlasher.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UNI_Flash;

namespace MgFlasher.Services;

public class StageDescriptionFactory : IStageDescriptionFactory
{
    private readonly ICarCompatabilityService _carCompatabilityService;

    public StageDescriptionFactory(ICarCompatabilityService carCompatabilityService)
    {
        _carCompatabilityService = carCompatabilityService;
    }

    public async Task<StageDescription> CreateAsync(Car car, FlashAllowanceItem flashAllowanceItem)
    {
        var result = flashAllowanceItem.ProductType switch
        {
            FlashAllowanceProductType.Flasher => GetFlasherDescription(car),
            FlashAllowanceProductType.OtsBundle => GetOtsBundlerDescription(car),
            FlashAllowanceProductType.Ultimate => GetUltimateDescription(car),
            FlashAllowanceProductType.CustomMap => GetCustomMapDescription(car),
            FlashAllowanceProductType.Stock => GetStockDescription(car),
            FlashAllowanceProductType.OtsStage1 => await GetStage1DescriptionAsync(car, flashAllowanceItem),
            FlashAllowanceProductType.OtsStage2 => await GetStage2DescriptionAsync(car, flashAllowanceItem),
            FlashAllowanceProductType.OtsStage2_5 => await GetStage25DescriptionAsync(car, flashAllowanceItem),
            _ => new StageDescription(),
        };

        return result;
    }

    private async Task<string> GetSwitchableMapsDescriptionAsync(Car car, FlashAllowanceItem flashAllowanceItem)
    {
        var slotDescriptions = await _carCompatabilityService
            .GetSwitchableMapsSlotDescriptionListAsync(car, productType: flashAllowanceItem.ProductType);
        var switchableMapsSlotDescriptionString = string.Join('\n', slotDescriptions.ToArray());

        return switchableMapsSlotDescriptionString;
    }

    private string GetSupportedFuelString(Car car, FlashAllowanceItem flashAllowanceItem)
    {
        var supportedFuelList = CreateSupportedFuelList(flashAllowanceItem.ProductType, car);
        var supportedFuelString = string.Join('\n', supportedFuelList.ToArray());

        return supportedFuelString;
    }

    private async Task<StageDescription> GetStage25DescriptionAsync(Car car, FlashAllowanceItem flashAllowanceItem)
    {
        var supportedFuelString = GetSupportedFuelString(car, flashAllowanceItem);
        var switchableMapsSlotDescriptionString = await GetSwitchableMapsDescriptionAsync(car, flashAllowanceItem);
        var customCodeVersionInstalled = CustomCodeVersions.GetCustomCodeVersionFromString(car.EcuMaster.CustomCodeVersionInstalled);

        return new StageDescription
        {
            Headline1 = GetEngineNameString(car) + AppResources.Stage_Stage2_5_Headline,
            Headline2 = car.IsB5X() ? AppResources.Stage_Headline1 : null,
            Headline3 = AppResources.Stage_Headline2,
            Headline4 = AppResources.Stage_Headline_SupportedFuelMaps,
            Headline5 = AppResources.Stage_Headline_SwitchableMapSlots,
            Subline5 = AppResources.Stage_Headline_BaseMap,
            Subline6 = _carCompatabilityService.GetLowestCarSwitchableMapFuelTypeName(car, flashAllowanceItem.ProductType),
            Subline1 = car.IsB5X() ? (car.IsPpc ? AppResources.Stage_Stage2_5_Subline1 : AppResources.Stage_Stage2_5_Subline1_Aurix) : (car.IsPpc ? AppResources.B48_Stage_Stage2_5_Subline1 : AppResources.B48_Stage_Stage2_5_Subline1_Aurix),
            Subline4 = car.IsB5X() ? (car.IsPpc ? AppResources.Stage_Stage2_Subline4 : AppResources.Stage_Stage2_Subline4_Aurix) : null,
            Block1 = AppResources.Stage_Stage2_5_Block1,
            Block2 = car.IsB5X() ? (car.IsPpc ? AppResources.Stage_Stage2_Block2 : AppResources.Stage_Stage2_Block2_Aurix) : (car.IsPpc ? AppResources.B48_Stage_Stage2_Block2 : AppResources.B48_Stage_Stage2_Block2_Aurix),
            List = AppResources.Stage_StageX_List
                .Split('\n')
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x)),
            List2Headline = AppResources.Stage_Stage2_List2_Headline,
            List2 = AppResources.Stage_StageX_List2.Split('\n').Select(x => x.Trim())
                .Select(s => !_carCompatabilityService.IsSwitchableMapSupportedCar(car, customCodeVersionInstalled) && s.Contains("Switchable") ? "" : s) //select which features appear when here!
                .Where(x => !string.IsNullOrWhiteSpace(x)),
            List3 = supportedFuelString.Split('\n')
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x)),
            List4 = switchableMapsSlotDescriptionString.Split('\n')
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x))
        };
    }

    private async Task<StageDescription> GetStage2DescriptionAsync(Car car, FlashAllowanceItem flashAllowanceItem)
    {
        var supportedFuelString = GetSupportedFuelString(car, flashAllowanceItem);
        var switchableMapsSlotDescriptionString = await GetSwitchableMapsDescriptionAsync(car, flashAllowanceItem);
        var customCodeVersionInstalled = CustomCodeVersions.GetCustomCodeVersionFromString(car.EcuMaster.CustomCodeVersionInstalled);

        return new StageDescription
        {
            Headline1 = GetEngineNameString(car) + AppResources.Stage_Stage2_Headline,
            Headline2 = car.IsB5X() ? AppResources.Stage_Headline1 : null,
            Headline3 = AppResources.Stage_Headline2,
            Headline4 = AppResources.Stage_Headline_SupportedFuelMaps,
            Headline5 = AppResources.Stage_Headline_SwitchableMapSlots,
            Subline5 = AppResources.Stage_Headline_BaseMap,
            Subline6 = _carCompatabilityService.GetLowestCarSwitchableMapFuelTypeName(car, flashAllowanceItem.ProductType),
            Subline1 = car switch
            {
                _ when car.IsB38() => AppResources.B38_Stage_Stage2_Subline1,
                _ when car.IsS68() => AppResources.S68_Stage_Stage2_Subline1,
                _ when car.IsN74() => AppResources.N74_Stage_Stage2_Subline1,
                _ when car.IsN63() => AppResources.N63_Stage_Stage2_Subline1,
                _ when car.IsS63() => AppResources.S63_Stage_Stage2_Subline1,
                _ when car.IsS58() => AppResources.S58_Stage_Stage2_Subline1,
                _ when car.IsB5X() && car.IsPpc => AppResources.Stage_Stage2_Subline1,
                _ when car.IsB5X() && car.IsAurix => AppResources.Stage_Stage2_Subline1_Aurix,
                _ when car.IsMini() && car.StockMap is null => AppResources.Stage_Stage2_Subline1_Mini_225KW,
                _ when car.IsMini() && car.StockMap.PowerBetween(int.MinValue, 170) => AppResources.Stage_Stage2_Subline1_Mini_140KW,
                _ when car.IsMini() && car.StockMap.PowerBetween(224, int.MaxValue) => AppResources.Stage_Stage2_Subline1_Mini_225KW,
                _ when car.IsMini() && car.StockMap.PowerBetween(169, 225) => AppResources.Stage_Stage2_Subline1_Mini_170KW,
                _ when car.IsB48() && car.StockMap is null => AppResources.Stage_Stage2_Subline1_B48_225KW,
                _ when car.IsB48() && car.StockMap.PowerBetween(int.MinValue, 170) => AppResources.Stage_Stage2_Subline1_B48_141KW,
                _ when car.IsB48() && car.StockMap.PowerBetween(220, int.MaxValue) => AppResources.Stage_Stage2_Subline1_B48_225KW,
                _ when car.IsB48() && car.StockMap.PowerBetween(169, 221) => AppResources.Stage_Stage2_Subline1_B48_199KW,
                _ when car.IsPpc => AppResources.B48_Stage_Stage2_Subline1,
                _ when car.IsAurix => AppResources.B48_Stage_Stage2_Subline1_Aurix,
                _ => AppResources.B48_Stage_Stage2_Subline1_Aurix
            },
            Subline4 = car.IsB5X() ? (car.IsPpc ? AppResources.Stage_Stage2_Subline4 : AppResources.Stage_Stage2_Subline4_Aurix) : null,
            Block1 = AppResources.Stage_Stage2_Block1,
            Block2 = car switch
            {
                _ when car.IsB5X() && car.IsPpc => AppResources.Stage_Stage2_Block2,
                _ when car.IsB5X() && car.IsAurix => AppResources.Stage_Stage2_Block2_Aurix,
                _ when car.IsMini() && car.StockMap is not null && car.StockMap.PowerBetween(int.MinValue, 170) => AppResources.Stage_Stage2_Block2_Mini_140KW,
                _ when car.IsN63() => AppResources.Stage_StageX_Block2_N63,
                _ when car.IsS63() => AppResources.Stage_StageX_Block2_S63,
                _ when car.IsS58() => AppResources.Stage_StageX_Block2_S58,
                _ when car.IsPpc => AppResources.B48_Stage_Stage2_Block2,
                _ when car.IsAurix => AppResources.B48_Stage_Stage2_Block2_Aurix,
                _ => AppResources.B48_Stage_Stage2_Block2_Aurix
            },
            List = AppResources.Stage_StageX_List
                .Split('\n')
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x)),
            List2Headline = AppResources.Stage_Stage2_List2_Headline,
            List2 = AppResources.Stage_StageX_List2.Split('\n').Select(x => x.Trim())
                .Select(s => !_carCompatabilityService.IsSwitchableMapSupportedCar(car, customCodeVersionInstalled) && s.Contains("Switchable") ? "" : s) //select which features appear when here!
                .Where(x => !string.IsNullOrWhiteSpace(x)),
            List3 = supportedFuelString.Split('\n')
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x)),
            List4 = switchableMapsSlotDescriptionString.Split('\n')
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x))
        };
    }

    private async Task<StageDescription> GetStage1DescriptionAsync(Car car, FlashAllowanceItem flashAllowanceItem)
    {
        var supportedFuelString = GetSupportedFuelString(car, flashAllowanceItem);
        var switchableMapsSlotDescriptionString = await GetSwitchableMapsDescriptionAsync(car, flashAllowanceItem);
        var customCodeVersionInstalled = CustomCodeVersions.GetCustomCodeVersionFromString(car.EcuMaster.CustomCodeVersionInstalled);

        return new StageDescription
        {
            Headline1 = GetEngineNameString(car) + AppResources.Stage_Stage1_Headline,
            Headline2 = car.IsB5X() ? AppResources.Stage_Headline1 : null,
            Headline3 = AppResources.Stage_Headline2,
            Headline4 = AppResources.Stage_Headline_SupportedFuelMaps,
            Headline5 = AppResources.Stage_Headline_SwitchableMapSlots,
            Subline5 = AppResources.Stage_Headline_BaseMap,
            Subline6 = _carCompatabilityService.GetLowestCarSwitchableMapFuelTypeName(car, flashAllowanceItem.ProductType),
            Subline1 = car switch
            {
                _ when car.IsB38() => AppResources.B38_Stage_Stage1_Subline1,
                _ when car.IsS68() => AppResources.S68_Stage_Stage1_Subline1,
                _ when car.IsN74() => AppResources.N74_Stage_Stage1_Subline1,
                _ when car.IsN63() => AppResources.N63_Stage_Stage1_Subline1,
                _ when car.IsS63() => AppResources.S63_Stage_Stage1_Subline1,
                _ when car.IsS58() => AppResources.S58_Stage_Stage1_Subline1,
                _ when car.IsB5X() && car.IsPpc => AppResources.Stage_Stage1_Subline1,
                _ when car.IsB5X() && car.IsAurix => AppResources.Stage_Stage1_Subline1_Aurix,
                _ when car.IsMini() && car.StockMap is null => AppResources.Stage_Stage1_Subline1_Mini_225KW,
                _ when car.IsMini() && car.StockMap.PowerBetween(int.MinValue, 170) => AppResources.Stage_Stage1_Subline1_Mini_140KW,
                _ when car.IsMini() && car.StockMap.PowerBetween(224, int.MaxValue) => AppResources.Stage_Stage1_Subline1_Mini_225KW,
                _ when car.IsMini() && car.StockMap.PowerBetween(169, 225) => AppResources.Stage_Stage1_Subline1_Mini_170KW,
                _ when car.IsB48() && car.StockMap is null => AppResources.Stage_Stage1_Subline1_B48_225KW,
                _ when car.IsB48() && car.StockMap.PowerBetween(int.MinValue, 170) => AppResources.Stage_Stage1_Subline1_B48_141KW,
                _ when car.IsB48() && car.StockMap.PowerBetween(220, int.MaxValue) => AppResources.Stage_Stage1_Subline1_B48_225KW,
                _ when car.IsB48() && car.StockMap.PowerBetween(169, 221) => AppResources.Stage_Stage1_Subline1_B48_199KW,
                _ when car.IsPpc => AppResources.B48_Stage_Stage1_Subline1,
                _ when car.IsAurix => AppResources.B48_Stage_Stage1_Subline1_Aurix,
                _ => AppResources.B48_Stage_Stage1_Subline1_Aurix
            },
            Block1 = car.IsB5X() ? (car.IsPpc ? AppResources.Stage_Stage1_Block1 : AppResources.Stage_Stage1_Block1_Aurix) : (car.IsPpc ? AppResources.B48_Stage_Stage1_Block1 : AppResources.B48_Stage_Stage1_Block1_Aurix),
            List = AppResources.Stage_StageX_List
                .Split('\n')
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x)),
            List2Headline = AppResources.Stage_Stage1_List2_Headline,
            List2 = AppResources.Stage_StageX_List2.Split('\n').Select(x => x.Trim())
                .Select(s => !_carCompatabilityService.IsSwitchableMapSupportedCar(car, customCodeVersionInstalled) && s.Contains("Switchable") ? "" : s) //select which features appear when here!
                .Where(x => !string.IsNullOrWhiteSpace(x)),
            List3 = supportedFuelString.Split('\n')
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x)),
            List4 = switchableMapsSlotDescriptionString.Split('\n')
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x)),
            Block2 = car switch
            {
                _ when car.IsB5X() && car.IsPpc => AppResources.Stage_Stage1_Block2,
                _ when car.IsB5X() && car.IsAurix => AppResources.Stage_Stage1_Block2_Aurix,
                _ when car.IsMini() && car.StockMap is not null && car.StockMap.PowerBetween(int.MinValue, 170) => AppResources.Stage_Stage1_Block2_Mini_140KW,
                _ when car.IsN63() => AppResources.Stage_StageX_Block2_N63,
                _ when car.IsS63() => AppResources.Stage_StageX_Block2_S63,
                _ when car.IsS58() => AppResources.Stage_StageX_Block2_S58,
                _ when car.IsPpc => AppResources.B48_Stage_Stage1_Block2,
                _ when car.IsAurix => AppResources.B48_Stage_Stage1_Block2_Aurix,
                _ => AppResources.B48_Stage_Stage1_Block2_Aurix
            }
        };
    }

    private StageDescription GetStockDescription(Car car)
    {
        return new StageDescription
        {
            Headline1 = GetEngineNameString(car) + AppResources.ProductType_Stock_Description_Headline,
            Block1 = AppResources.ProductType_Stock_Description_Text,
        };
    }

    private StageDescription GetCustomMapDescription(Car car)
    {
        return new StageDescription
        {
            Headline1 = GetEngineNameString(car) + AppResources.Stage_CustomMap,
            Block1 = AppResources.Stage_CustomMap_Block1,
            List = AppResources.Stage_CustomMap_List1.Split('\n').Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x)),
        };
    }

    private StageDescription GetUltimateDescription(Car car)
    {
        return new StageDescription
        {
            Headline1 = GetEngineNameString(car) + AppResources.ProductType_Ultimate_Description_Headline,
            Block1 = AppResources.ProductType_Ultimate_Description_Text
        };
    }

    private StageDescription GetOtsBundlerDescription(Car car)
    {
        return new StageDescription
        {
            Headline1 = GetEngineNameString(car) + AppResources.ProductType_OtsMapBundle_Description_Headline,
            Block1 = AppResources.ProductType_OtsMapBundle_Description_Text
        };
    }

    private StageDescription GetFlasherDescription(Car car)
    {
        return new StageDescription
        {
            Headline1 = GetEngineNameString(car) + AppResources.ProductType_Flasher_Description_Headline,
            Block1 = AppResources.ProductType_Flasher_Description_Text,
            List = AppResources.ProductType_Flasher_List.Split('\n').Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x)),
            Block2 = AppResources.ProductType_Flasher_Block2
        };
    }

    private string GetEngineNameString(Car car)
    {
        return (!string.IsNullOrWhiteSpace(car.EngineCode) ? (car.EngineCode + " Gen " + (car.IsPpc ? "1" : "2")) : AppResources.Stage_MissingEngineModel) + "\n";
    }

    private List<string> CreateSupportedFuelList(FlashAllowanceProductType productType, Car car)
    {
        var results = new List<string> { };

        StageType stageType = productType switch
        {
            FlashAllowanceProductType.OtsStage1 => StageType.Stage1,
            FlashAllowanceProductType.OtsStage2 => StageType.Stage2,
            FlashAllowanceProductType.OtsStage2_5 => StageType.Stage2_5,
            _ => StageType.Stock,
        };

        if (stageType != StageType.Stock)
        {
            var supportedFuelTypes = car.Changesets
                .Where(t => t.Type == stageType)
                .Where(c => _carCompatabilityService.IsChinaSupportedFuelVersion(car, c))
                .GroupBy(v => v.VersionName)
                .Select(i => i.Key)
                .ToList();

            var fuelTypesOnlyBySwitchableMaps = new List<string> { "E30", "E50", "E85" };
            foreach (var version in car.Changesets.Where(t => t.Type == stageType)
                         .Where(c => _carCompatabilityService.IsChinaSupportedFuelVersion(car, c))
                         .Where(n => !string.IsNullOrWhiteSpace(n.ReleaseNotes))
                         .Where(s => s.ReleaseNotes.Contains("Switchable Map"))
                         .ToList())
            {
                foreach (var fuelType in fuelTypesOnlyBySwitchableMaps)
                {
                    if (version.ReleaseNotes.Contains(fuelType))
                    {
                        supportedFuelTypes.Add(fuelType + AppResources.Stage_SupportedFuelViaSwitchableMaps);
                    }
                }
            }
            results = supportedFuelTypes.GroupBy(x => x)
                .Select(x => x.Key)
                .ToList();
        }
        return results;
    }
}