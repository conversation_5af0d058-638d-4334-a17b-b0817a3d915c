﻿using System;
using System.Threading;
using System.Threading.Tasks;
using CommunityToolkit.Maui.Core;
using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using MgFlasher.ViewModels;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Dispatching;

namespace MgFlasher.Services;

public class MauiUserDialogs : IUserDialogs
{
    private readonly PageLoaderViewModel _pageLoaderViewModel;
    private readonly IAppContext _appContext;

    public MauiUserDialogs(PageLoaderViewModel pageLoaderViewModel, IAppContext appContext)
    {
        _pageLoaderViewModel = pageLoaderViewModel;
        _appContext = appContext;
    }

    public async Task<string> ActionSheetAsync(string title, string cancel, string destructive, CancellationToken? cancelToken = null, params string[] buttons)
    {
        return await Application.Current.Dispatcher.DispatchAsync(async () =>
        {
            var result = await Shell.Current.DisplayActionSheet(title ?? AppResources.MG_Flasher, cancel, destructive, buttons);
            return result switch
            {
                null => "",
                _ when result == cancel => "",
                _ => result
            };
        });
    }

    public async Task<PromptResult> PromptAsync(PromptConfig promptConfig)
    {
        return await Application.Current.Dispatcher.DispatchAsync(async () =>
        {
            var result = await Shell.Current.DisplayPromptAsync(promptConfig.Title ?? AppResources.MG_Flasher, promptConfig.Message, promptConfig.OkText, initialValue: promptConfig.Text);
            return new PromptResult() { Ok = !string.IsNullOrEmpty(result), Text = result, Value = result };
        });
    }

    public async Task AlertAsync(string message, string title = null, string okText = null)
    {
        await Application.Current.Dispatcher.DispatchAsync(async () =>
        {
            if (_appContext.IsEcuTestUser())
            {
                return;
            }

            await Shell.Current.DisplayAlert(title ?? AppResources.MG_Flasher, message, okText ?? AppResources.Ok);
        });
    }

    public async Task<bool> ConfirmAsync(string message, string title = null, string okText = null, string cancelText = null)
    {
        return await Application.Current.Dispatcher.DispatchAsync(async () =>
        {
            if (_appContext.IsEcuTestUser())
            {
                return true;
            }

            var result = await Shell.Current.DisplayAlert(title ?? AppResources.MG_Flasher, message, okText ?? AppResources.Ok, cancelText ?? AppResources.Cancel);
            return result;
        });
    }

    public IProgressDialog Loading(string title)
    {
        return new ProgressDialog(_pageLoaderViewModel, title);
    }

    public async Task Toast(ToastConfig cfg)
    {
        var cancellationTokenSource = new CancellationTokenSource();
        var duration = cfg.Long ? ToastDuration.Long : ToastDuration.Short;
        var fontSize = 14;

        await Application.Current.Dispatcher.DispatchAsync(async () =>
        {
            var toast = CommunityToolkit.Maui.Alerts.Toast.Make(cfg.Message, duration, fontSize);
            await toast.Show(cancellationTokenSource.Token);
        });
    }
}