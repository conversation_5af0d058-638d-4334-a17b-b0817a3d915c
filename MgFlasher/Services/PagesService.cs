﻿using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Models;
using MgFlasher.Views;
using MgFlasher.Views.Logger;
using System;
using MgFlasher.Views.FlashingCarOptions;
using MgFlasher.Views.MyCar;

namespace MgFlasher.Services;

public class PagesService : IPagesService
{
    private readonly IAppContext _appContext;

    public PagesService(IAppContext appContext)
    {
        _appContext = appContext;
    }

    public BasePage GetPage(PageType id) => id switch
    {
        PageType.AntilagDiagnostics => new AntilagDiagnosticsPage(),
        PageType.BurbleDiagnostics => new BurbleDiagnosticsPage(),
        PageType.EthanolOverrideDiagnostics => new EthanolOverrideDiagnosticsPage(),
        PageType.ExhaustFlapDiagnostics => new ExhaustFlapDiagnosticsPage(),
        PageType.RadiatorFlapDiagnostics => new RadiatorFlapDiagnosticsPage(),
        PageType.MaxCoolinDiagnostics => new MaxCoolinDiagnosticsPage(),
        PageType.ValetModeDiagnostics => new ValetModeDiagnosticsPage(),
        PageType.SwitchableMapDiagnostics => new SwitchableMapDiagnosticsPage(),
        PageType.MyCars => new MyCarsPage(),
        PageType.MyCar => new MyCarPage(),
        PageType.ReadDtc => new ReadDtcPage(),
        PageType.ProcessCompleted => new ProcessCompletedPage(),
        PageType.FlashingCarOptions => new FlashingCarOptionsPage(),
        PageType.FlashingCarWarningInfo => new FlashingCarWarningInfoPage(),
        PageType.MyCarFlashHistoryItemDetail => new MyCarFlashHistoryItemDetailPage(),
        PageType.ResetAdaptation => new ResetAdaptationPage(),
        PageType.LoggerParameters => new LoggerParametersPage(),
        PageType.LoggerUnits => new LoggerUnitsPage(),
        PageType.LoggerDisplay => new LoggerDisplayPageView(),
        PageType.LoggerDisplayGaugeItem => new LoggerDisplayGaugeItemPage(),
        PageType.LoggerReplaceGaugeItem => new LoggerReplaceGaugeItemPage(),
        PageType.LoggerAlerts => new LoggerAlertsPage(),
        PageType.LoggerAlertItem => new LoggerAlertItemPage(),
        PageType.LoggerFile => new LoggerFilePage(),
        PageType.BuyFlashStage => new BuyFlashStagePage(),
        PageType.FlashingCar => new FlashingCarPage(),
        PageType.ReadModule => new ReadModulePage(),
        PageType.Pending => new PendingPage(),
        PageType.About => new AboutPage(),
        PageType.CustomMapFiles => new CustomMapFilesPage(),
        PageType.Login when _appContext.RetailMode == RetailModeEnum.MG_Flasher => new LoginPage(),
        PageType.Login when _appContext.RetailMode == RetailModeEnum.China => new SmsLoginPage(),
        PageType.User => new UserPage(),
        PageType.Feedback => new FeedbackPage(),
#if ANDROID
        PageType.KnowledgeBase => new FeedbackPage(),
#else
        PageType.KnowledgeBase => new KnowledgeBasePage(),
#endif
        PageType.Register => new RegisterPage(),
        PageType.ForgotPassword => new ForgotPasswordPage(),
        PageType.StageVersions => new StageVersionsPage(),
        PageType.CustomCodeVersions => new CustomCodeVersionsPage(),
        PageType.SyncPage => new SyncPage(),
        PageType.Settings => new SettingsPage(),
        PageType.UserDtcRemovalList => new UserDtcRemovalListPage(),
        PageType.FlashingCarOptionsItem => new FlashingCarOptionsItemPage(),
        _ => throw new NotImplementedException("There is no view associated with the page id"),
    };
}