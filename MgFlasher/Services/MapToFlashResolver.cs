﻿using MgFlasher.Flasher.Services.Cars;
using MgFlasher.Localization.Resources;
using MgFlasher.Services.Navigation;
using System.Threading.Tasks;

namespace MgFlasher.Services;

public class MapToFlashResolver : IMapToFlashResolver
{
    private readonly INavigationService _navigationService;

    public MapToFlashResolver(INavigationService navigationService)
    {
        _navigationService = navigationService;
    }

    public async Task<string> Resolve(params string[] options)
    {
        return await _navigationService.DisplayActionSheet(
            AppResources.Flash_Msg_ChooseMap,
            AppResources.Cancel,
            options);
    }
}