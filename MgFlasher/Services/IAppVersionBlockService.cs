﻿using System.Threading.Tasks;

namespace MgFlasher.Services;

/// <summary>
/// An interface for handling blocking the flashing processes in the app.
/// </summary>
public interface IAppVersionBlockService
{
    /// <summary>
    /// Check if the app is blocked from flashing on backend.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    Task CheckAndUpdateAppVersionBlockStatus();

    /// <summary>
    /// Check if the app is blocked from flashing in cache.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    Task CheckAppVersionBlockStatus();

    /// <summary>
    /// Blocks flashing in the app.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    Task BlockAppVersion();

    /// <summary>
    /// Alerts the user about blocke version.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    Task NotifyUserOfBlock();

    /// <summary>
    /// Unblocks flashing in the app.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    Task UnblockAppVersion();
}