﻿using System.Threading;
using System.Threading.Tasks;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Helpers;
using Microsoft.Extensions.Logging;

namespace MgFlasher.Services;

public class TracableUserDialogs : IUserDialogs
{
    private readonly ILogger<TracableUserDialogs> _logger;
    private readonly IUserDialogs _instance;

    public TracableUserDialogs(ILogger<TracableUserDialogs> logger, MauiUserDialogs userDialogs)
    {
        _logger = logger;
        _instance = userDialogs;
    }

    public async Task<string> ActionSheetAsync(string title, string cancel, string destructive, CancellationToken? cancelToken = null, params string[] buttons)
    {
        var result = await _instance.ActionSheetAsync(title, cancel, destructive, cancelToken, buttons);
        _logger.LogInformation("Action sheet presented [{Title}] with options [{Options}]. User selected [{Result}]", title ?? "NO TITLE", string.Join(", ", buttons ?? new string[0]), result ?? "NO RESULT");
        return result;
    }

    public Task AlertAsync(string message, string title = null, string okText = null)
    {
        _logger.LogInformation("Alert displayed [{Title}] with message [{Message}]", title ?? "NO TITLE", message);
        return _instance.AlertAsync(message, title, okText);
    }

    public async Task<bool> ConfirmAsync(string message, string title = null, string okText = null, string cancelText = null)
    {
        var result = await _instance.ConfirmAsync(message, title, okText, cancelText);
        _logger.LogInformation("Confirmation dialog displayed [{Title}] with message [{Message}]. User confirmed [{Result}]", title ?? "NO TITLE", message, result);
        return result;
    }

    public IProgressDialog Loading(string title = null)
    {
        _logger.LogInformation("Loading presented [{Title}]", title ?? "NO TITLE");
        return _instance.Loading(title);
    }

    public async Task<PromptResult> PromptAsync(PromptConfig config)
    {
        var result = await _instance.PromptAsync(config);
        _logger.LogInformation("Prompt presented [{Title}] with message [{Message}]. User decision [{Result}]",
            config.Title ?? "NO TITLE", config.Message, result?.Text);
        return result;
    }

    public async Task Toast(ToastConfig cfg)
    {
        _logger.LogInformation("Toast presented [{Message}]", cfg.Message);
        await _instance.Toast(cfg);
    }
}