﻿using System.IO;
using System.Threading.Tasks;
using Microsoft.Maui.Graphics.Platform;
using Microsoft.Maui.Media;

namespace MgFlasher.Services;

public class PicturePicker : IPicturePicker
{
    public async Task<byte[]> GetImageStreamAsync()
    {
        var result = await MediaPicker.PickPhotoAsync();
        if (result is null)
        {
            return null;
        }
        var stream = await result.OpenReadAsync();
        var resized = ResizeImage(stream, 512, 384);
        return resized;
    }

    private byte[] ResizeImage(Stream imageData, float width, float height)
    {
        var image = PlatformImage.FromStream(imageData);
        var @new = image.Resize(width, height);
        var ms = new MemoryStream();
        @new.Save(ms);
        return ms.ToArray();
    }
}