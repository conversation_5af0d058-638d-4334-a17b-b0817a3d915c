﻿using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.User.Dialogs;
using MgFlasher.Localization.Resources;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Devices;
using System;
using System.Threading.Tasks;

namespace MgFlasher.Services;

/// <summary>
/// A class for handling checking and notifing about app version status.
/// </summary>
public class AppUpdateService(ICloseAppService closeAppService,
    ILogger<AppUpdateService> logger,
    IAppContext appContext,
    IUserDialogs userDialogs,
    ILatestVersionChecker latestVersionChecker) : IAppUpdateService
{
    /// <inheritdoc />
    public async Task CloseAppAndNavigateToStore()
    {
        string appStoreUrl = GetAppStoreUrl();
        logger.LogInformation("Navigating to store for update - {URL}.", appStoreUrl);

        if (!string.IsNullOrEmpty(appStoreUrl))
        {
            await Browser.OpenAsync(appStoreUrl, BrowserLaunchMode.External);
        }

        closeAppService.CloseApplication();
    }

    /// <summary>
    /// Gets the app store URL based on the platform.
    /// </summary>
    /// <returns></returns>
    private static string GetAppStoreUrl()
    {
        if (DeviceInfo.Platform == DevicePlatform.iOS || DeviceInfo.Platform == DevicePlatform.macOS || DeviceInfo.Platform == DevicePlatform.MacCatalyst)
        {
            return "https://apps.apple.com/pl/app/mg-flasher/id1492813749";
        }
        else if (DeviceInfo.Platform == DevicePlatform.Android)
        {
            return "https://play.google.com/store/apps/details?id=com.mgflasher.app&hl=en";
        }
        else if (DeviceInfo.Platform == DevicePlatform.WinUI)
        {
            return "ms-windows-store://pdp/?ProductId=9PNPT0F0F7XB";
        }

        return "https://www.mgflasher.com/";
    }

    /// <inheritdoc />
    public async Task NotifyNewVersionAvailableAsync()
    {
        if (appContext.RetailMode != RetailModeEnum.MG_Flasher)
        {
            return;
        }

        try
        {
            (bool newest, string latestVersion) = await latestVersionChecker.GetVersionInfoAsync();
            if (newest)
            {
                return;
            }

            var msg = string.Format(AppResources.MgFlasher_NewVersionAvailable, latestVersion);
            await userDialogs.Toast(new(msg, true));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "NotifyNewVersionAvailableAsync error");
        }
    }
}