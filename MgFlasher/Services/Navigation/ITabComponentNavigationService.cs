﻿using Syncfusion.Maui.TabView;
using System.Threading.Tasks;

namespace MgFlasher.Services.Navigation;

public interface ITabComponentNavigationService
{
    void NavigateToTab<T>(ITabsComponent tabsComponent) where T : ITabComponent;

    Task HandleSelectionChanged<T>(T tabsComponent, object sender, TabSelectionChangedEventArgs e) where T : ITabsComponent;

    void HandleSelectionChanging<T>(T tabsComponent, object sender, SelectionChangingEventArgs e) where T : ITabsComponent;
}