﻿using MgFlasher.Flasher.Services.AppContext;
using MgFlasher.Flasher.Services.User;
using MgFlasher.Helpers;
using MgFlasher.Models;
using MgFlasher.Models.NavigationArgs;
using MgFlasher.ViewModels;
using MgFlasher.Views;
using MgFlasher.Views.Controls;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Graphics;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;

namespace MgFlasher.Services.Navigation;

public class NavigationService : INavigationService
{
    private SemaphoreSlim _semaphoreSlim = new SemaphoreSlim(1, 1);
    private readonly ITabComponentNavigationService _tabComponentNavigationService;
    private readonly IPagesService _pagesService;
    private readonly IAppContext _appContext;
    private readonly ILogger<NavigationService> _logger;

    private readonly Stack<PageType> _pageNavigationStack;

    private INavigationRoot _navigationRoot;

    public PageViewModel CurrentViewModel { get; private set; }

    public NavigationService(
        ITabComponentNavigationService tabComponentNavigationService,
        IPagesService pagesService,
        IAppContext appContext,
        ILogger<NavigationService> logger)
    {
        _tabComponentNavigationService = tabComponentNavigationService;
        _pagesService = pagesService;
        _appContext = appContext;
        _logger = logger;
        _pageNavigationStack = new Stack<PageType>();
    }

    public void Init(INavigationRoot navigationRoot)
    {
        _navigationRoot = navigationRoot;
    }

    public void OpenFlyout()
    {
        if (!_navigationRoot.CurrentPageContainer.FlyoutIsPresented)
        {
            _navigationRoot.CurrentPageContainer.FlyoutIsPresented = true;
            //Workaround to open the flyout
            _navigationRoot.CurrentPageContainer.CurrentPage.Layout(new Rect(0, 0, Shell.Current.CurrentPage.Width + 1, Shell.Current.CurrentPage.Height + 1));
            _logger.LogInformation("Side menu openned");
        }
    }

    public async Task NavigateToTabbedComponent<T>(PageType pageType, object arg = null) where T : ITabComponent
    {
        if (_navigationRoot.CurrentPage.ViewModel is not ITabsComponent || _navigationRoot.CurrentPage.PageId != pageType)
        {
            await NavigateAsync(pageType, arg);
        }

        if (_navigationRoot.CurrentPage.ViewModel is ITabsComponent vm && _navigationRoot.CurrentPage.PageId == pageType)
        {
            _tabComponentNavigationService.NavigateToTab<T>(vm);
        }
    }

    public async Task NavigateAsync(PageType pageId, object arg = null)
    {
        if (_navigationRoot.CurrentPage?.PageId == pageId)
        {
            return;
        }

        if (!await _semaphoreSlim.WaitAsync(TimeSpan.FromSeconds(3)))
        {
            _logger.LogInformation("Navigate - {PageId} - timeout", pageId);
            return;
        }

        try
        {
            _logger.LogInformation("Navigate - {PageId}", pageId);
            await NavigateWithLimitationsCheck(pageId, arg, false);
        }
        finally
        {
            _semaphoreSlim.Release();
        }
    }

    public async Task NavigateToPreviousAsync(object arg)
    {
        if (!await _semaphoreSlim.WaitAsync(TimeSpan.FromSeconds(2)))
        {
            _logger.LogInformation("Navigate to previous - timeout");
            return;
        }

        try
        {
            arg ??= _navigationRoot.CurrentPage is not null ? ShellTitleView.GetBackwardNavigationParameter(_navigationRoot.CurrentPage) : null;

            var currentPageId = _navigationRoot.CurrentPage.PageId;
            var forceBackNavigationPage = currentPageId.GetCustomNavigationAttributeValue(s => s.ForceBackNavigationTo);

            PageType? pageType = null;

            if (_pageNavigationStack.Count > 0)
            {
                pageType = _pageNavigationStack.Pop();
            }

            _logger.LogInformation("Navigate to previous - {PageType}", pageType);

            if (forceBackNavigationPage.HasValue)
            {
                await NavigateWithLimitationsCheck(forceBackNavigationPage.Value, arg, true);
            }
            else if (pageType.HasValue)
            {
                await NavigateWithLimitationsCheck(pageType.Value, arg, true);
            }
        }
        finally
        {
            _semaphoreSlim.Release();
        }
    }

    private async Task NavigateWithLimitationsCheck(PageType pageId, object arg, bool isBackNavigation)
    {
        var userAuthorizationRequired = pageId.GetCustomNavigationAttributeValue(s => s.UserAuthorizationRequired);
        var disabledFor = pageId.GetCustomNavigationAttributeValue(s => s.DisabledForMode);

        if (disabledFor.HasValue && _appContext.RetailMode == disabledFor)
        {
            return;
        }
        else if (userAuthorizationRequired && !_appContext.IsLoggedIn())
        {
            await NavigateAsync(PageType.Login, new LoginPageNavigationArgs()
            {
                NextPage = pageId,
                NextPageArguments = arg
            }, isBackNavigation);
        }
        else
        {
            await NavigateAsync(pageId, arg, isBackNavigation);
        }
    }

    private async Task NavigateAsync(PageType pageId, object arg, bool isBackNavigation)
    {
        var currentPage = _navigationRoot.CurrentPage;
        BasePage nextPage = null;

        try
        {
            if (currentPage is not null)
            {
                currentPage.ViewModel.IsBusy = true;
            }

            nextPage = _pagesService.GetPage(pageId);
            nextPage.ViewModel.IsBusy = true;

            if (currentPage is not null)
            {
                await currentPage.ViewModel.OnNavigatedFromAsync(pageId);
                if (currentPage.PageId != PageType.Pending && currentPage.PageId != PageType.Login && !isBackNavigation)
                {
                    _pageNavigationStack.Push(currentPage.PageId);
                }
            }

            await nextPage.ViewModel.OnNavigatedToAsync(currentPage?.PageId ?? PageType.Pending, arg);

            await PushPageToNavigationStackAsync(nextPage, true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception during navigation to {NewPageId}", pageId);
            throw;
        }
        finally
        {
            if (currentPage?.ViewModel != null)
            {
                currentPage.ViewModel.IsBusy = false;
            }

            if (nextPage?.ViewModel != null)
            {
                nextPage.ViewModel.IsBusy = false;
                CurrentViewModel = nextPage.ViewModel;
            }
        }
    }

    private async Task PushPageToNavigationStackAsync(BasePage newPage, bool animated)
    {
        if (_navigationRoot.CurrentPage == newPage)
        {
            return;
        }

        CloseFlyout();

        _logger.LogInformation("Navigating from \"{CurrentPageId}\" to \"{NewPageId}\"", _navigationRoot.CurrentPage?.PageId, newPage.PageId);

        var navigation = _navigationRoot.CurrentPageContainer.Navigation;
        await navigation.PushAsync(newPage, animated);

        foreach (var page in navigation.NavigationStack.ToList())
        {
            if (page == newPage || page is null)
            {
                continue;
            }

            if (page is BasePage disposable)
            {
                disposable.Dispose();
            }

            navigation.RemovePage(page);
        }
    }

    public Task<string> DisplayActionSheet(string title, string cancel, params string[] buttons)
    {
        return _navigationRoot.CurrentPage.DisplayActionSheet(
            title,
            cancel,
            null,
            buttons
        );
    }

    private void CloseFlyout()
    {
        if (_navigationRoot.CurrentPageContainer.FlyoutIsPresented)
        {
            _navigationRoot.CurrentPageContainer.FlyoutIsPresented = false;
        }
    }
}