﻿using MgFlasher.Models;
using MgFlasher.ViewModels;
using System.Threading.Tasks;

namespace MgFlasher.Services.Navigation;

public interface INavigationService
{
    PageViewModel CurrentViewModel { get; }

    void Init(INavigationRoot mainPage);

    void OpenFlyout();

    Task NavigateToTabbedComponent<T>(PageType pageType, object arg = null) where T : ITabComponent;

    Task NavigateAsync(PageType pageId, object arg = null);

    Task NavigateToPreviousAsync(object arg = null);

    Task<string> DisplayActionSheet(string title, string cancel, params string[] buttons);
}