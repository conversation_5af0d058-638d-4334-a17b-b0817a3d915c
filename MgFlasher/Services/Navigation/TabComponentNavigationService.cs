﻿using MgFlasher.Helpers;
using Microsoft.Extensions.Logging;
using Syncfusion.Maui.TabView;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace MgFlasher.Services.Navigation;

public class TabComponentNavigationService(ILogger<TabComponentNavigationService> logger) : ITabComponentNavigationService
{
    public void NavigateToTab<T>(ITabsComponent tabbedComponent) where T : ITabComponent
    {
        if (tabbedComponent.Tabs.FirstOrDefault(x => x.GetType() == typeof(T)) is ITabComponent tab)
        {
            logger.LogInformation("Navigating to tab {TabId}", typeof(T).Name);
            (var canSelect, var canSelectAction) = tabbedComponent.CanSelect(tab);
            if (!canSelect & canSelectAction != null)
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await canSelectAction();
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error while switching tab");
                    }
                });
            }
            else
            {
                tabbedComponent.SelectedTabIndex = tabbedComponent.Tabs.IndexOf(tab);
                return;
            }
        }

        var children = tabbedComponent.Tabs.OfType<ITabsComponent>().SelectMany(main => main.Tabs.Select(x => (Main: main, Tab: x)));
        foreach (var child in children)
        {
            if (child.Tab.GetType() == typeof(T) && child.Main is ITabComponent mainTab)
            {
                tabbedComponent.SelectedTabIndex = tabbedComponent.Tabs.IndexOf(mainTab);
                NavigateToTab<T>(child.Main);
                return;
            }
        }
    }

    public async Task HandleSelectionChanged<T>(T tabsComponent, object sender, TabSelectionChangedEventArgs e) where T : ITabsComponent
    {
        var tabView = (SfTabView)sender;
        var next = tabView.GetTabContext<ITabComponent>(e.NewIndex);
        await tabsComponent.OnNavigatedToTabAsync(next);
    }

    public void HandleSelectionChanging<T>(T tabsComponent, object sender, SelectionChangingEventArgs e) where T : ITabsComponent
    {
        var tabView = (SfTabView)sender;
        var tab = tabView.GetTabContext<ITabComponent>(e.Index);
        (var canSelect, var canSelectAction) = tabsComponent.CanSelect(tab);
        e.Cancel = !canSelect;
        if (!canSelect & canSelectAction != null)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await canSelectAction();
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error while selecting tab");
                }
            });
        }
    }
}