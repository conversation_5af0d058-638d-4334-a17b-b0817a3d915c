using System.Threading.Tasks;

namespace MgFlasher.Services;

/// <summary>
/// An interface for checking the latest version of the app.
/// </summary>
public interface ILatestVersionChecker
{
    /// <summary>
    /// Get latest available version from the relevant app store.
    /// </summary>
    /// <returns></returns>
    Task<(bool UsingLatestVersion, string LatestVersion)> GetVersionInfoAsync();
}