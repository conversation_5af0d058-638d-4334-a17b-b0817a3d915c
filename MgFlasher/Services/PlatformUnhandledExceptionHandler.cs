using System;
using System.Linq;
using System.Net.Sockets;
using MgFlasher.Helpers;
using Microsoft.Extensions.Logging;
using Sentry;

namespace MgFlasher.Services;

public static class PlatformUnhandledExceptionHandler
{
    public static void LogError<TParent>(Exception ex, string msg)
    {
        if (ex is AggregateException age && age.InnerExceptions.Any(x => x is SocketException))
        {
            return;
        }
        if (!DependencyResolver.Initialized && SentrySdk.IsEnabled)
        {
            SentrySdk.CaptureException(ex);
            return;
        }
        if (!DependencyResolver.Initialized)
        {
            Console.Error.WriteLine($"{msg} - {ex}");
            return;
        }
        var logger = DependencyResolver.Resolve<ILogger<TParent>>();
        if (ex is not null)
        {
            logger.LogError(ex, msg);
        }
        else
        {
            logger.LogError(msg);
        }
    }
}