﻿using MgFlasher.Flasher.Services.Models;
using MgFlasher.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MgFlasher.CustomCode.Storage.Changesets;

namespace MgFlasher.Services;

public interface IChangesetVersionNameViewModelsFactory
{
    Task<IEnumerable<ChangesetVersionNameViewModel>> CreateAsync(StageType stageType, CustomCodeId customCodeVersion,
        Car car, Action<IRadioButtonViewModel> checkedChanged);
}