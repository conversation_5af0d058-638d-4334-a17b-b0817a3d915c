﻿using System.Threading.Tasks;
using MgFlasher.Flasher.Services.CarLogger.Alerts;
using Microsoft.Maui.Storage;
using Plugin.Maui.Audio;

namespace MgFlasher.Services;

public class SoundPlayer(IAudioManager audioManager) : ISoundPlayer
{
    public async Task PlayAsync()
    {
        var track = await FileSystem.OpenAppPackageFileAsync(GetAlertSoundPath());
        var player = audioManager.CreatePlayer(track);
        player.Play();
    }

    private string GetAlertSoundPath()
    {
#if WINDOWS
        return "alert.mp3";
#else
        return "alert";
#endif
    }
}