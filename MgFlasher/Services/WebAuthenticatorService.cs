﻿using MgFlasher.Flasher.Client.Shop;
using System;
using System.Threading.Tasks;
using MgFlasher.Flasher.Client.Shop.Contract;

namespace MgFlasher.Services;

#if IOS || ANDROID
public class WebAuthenticatorService : IWebAuthenticatorService
{
    public async Task<ShopWebAuthenticatorResult> AuthenticateAsync(string scheme)
    {
        var uri = new Uri($"{ShopDefaults.Url}api/mobileauth/Login?scheme={scheme}");
        var callbackUri = new Uri("mgflasher://");
        var authResult = await Microsoft.Maui.Authentication.WebAuthenticator.AuthenticateAsync(uri, callbackUri);

        return ToShopWebAuthenticatorResult(authResult);
    }

    private static ShopWebAuthenticatorResult ToShopWebAuthenticatorResult(Microsoft.Maui.Authentication.WebAuthenticatorResult result) => new ShopWebAuthenticatorResult()
    {
        AccessToken = result.AccessToken,
        ExpiresIn = result.ExpiresIn,
        IdToken = result.IdToken,
        Properties = result.Properties,
        RefreshToken = result.RefreshToken,
        RefreshTokenExpiresIn = result.RefreshTokenExpiresIn
    };
}
#elif WINDOWS       
public class WebAuthenticatorService : IWebAuthenticatorService
{
    public async Task<ShopWebAuthenticatorResult> AuthenticateAsync(string scheme)
    {
        var uri = new Uri($"{ShopDefaults.Url}api/mobileauth/Login?scheme={scheme}");
        var callbackUri = new Uri("mgflasher://");
        var authResult = await WinUIEx.WebAuthenticator.AuthenticateAsync(uri, callbackUri);

        return ToShopWebAuthenticatorResult(authResult);
    }

    private static ShopWebAuthenticatorResult ToShopWebAuthenticatorResult(WinUIEx.WebAuthenticatorResult result) => new ShopWebAuthenticatorResult()
    {
        AccessToken = result.AccessToken,
        ExpiresIn = result.ExpiresIn,
        IdToken = result.IdToken,
        Properties = result.Properties,
        RefreshToken = result.RefreshToken,
        RefreshTokenExpiresIn = result.RefreshTokenExpiresIn,
    };
}
#endif