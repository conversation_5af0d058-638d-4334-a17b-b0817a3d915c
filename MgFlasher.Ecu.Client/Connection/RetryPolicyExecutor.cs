﻿using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading.Tasks;
using MgFlasher.Ecu.Client.Connection.Contract;
using Microsoft.Extensions.Logging;
using Polly;

namespace MgFlasher.Ecu.Client.Connection;

public class RetryPolicyExecutor : IRetryPolicyExecutor
{
    private readonly ILogger<RetryPolicyExecutor> _logger;

    public RetryPolicyExecutor(ILogger<RetryPolicyExecutor> logger)
    {
        _logger = logger;
    }

    public async Task<RawEcuResponse> ExecuteAndRepeatOnNackOrErrorMessageAsync(Func<Task<RawEcuResponse>> action)
    {
        return await Policy
            .HandleResult<RawEcuResponse>(x => x is null || x.IsNack)
            .RetryAsync(10,
                (a, b) => _logger.LogInformation("Repeating[{attempt}] message send due to nack or null message", b))
            .ExecuteAsync(action);
    }

    public RawEcuResponse WaitForDataOrNackMessage(UDS_DiagCmd diagCmd, ConcurrentBag<RawEcuResponse> responses)
    {
        bool IsDataOrNackMessage(RawEcuResponse rs) => rs != null && (rs.IsNack || (rs.IsData && rs.HasContent(diagCmd)));

        return Policy.HandleResult<RawEcuResponse>(x =>
            {
                return x == null;
            })
            .WaitAndRetry(20, x => TimeSpan.FromMilliseconds(15))
            .Execute(() =>
            {
                var rs = responses.FirstOrDefault(IsDataOrNackMessage);
                return rs;
            });
    }

    public RawEcuResponse WaitForAckOrNackMessage(long sentTimestamp, ConcurrentBag<RawEcuResponse> responses)
    {
        bool IsAckNackMessage(RawEcuResponse rs) => (rs.IsAck || rs.IsNack) && rs.Timestamp > sentTimestamp;

        return Policy.HandleResult<RawEcuResponse>(x =>
            {
                return x == null;
            })
            .WaitAndRetry(20, x => TimeSpan.FromMilliseconds(15))
            .Execute(() =>
            {
                var rs = responses.FirstOrDefault(IsAckNackMessage);
                return rs;
            });
    }
}