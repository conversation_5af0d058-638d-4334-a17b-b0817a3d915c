﻿using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using MgFlasher.Ecu.Client.Connection.Contract;

namespace MgFlasher.Ecu.Client.Connection;

public interface IRetryPolicyExecutor
{
    RawEcuResponse WaitForAckOrNackMessage(long sentTimestamp, ConcurrentBag<RawEcuResponse> responses);

    RawEcuResponse WaitForDataOrNackMessage(UDS_DiagCmd diagCmd, ConcurrentBag<RawEcuResponse> ecuResponses);

    Task<RawEcuResponse> ExecuteAndRepeatOnNackOrErrorMessageAsync(Func<Task<RawEcuResponse>> action);
}