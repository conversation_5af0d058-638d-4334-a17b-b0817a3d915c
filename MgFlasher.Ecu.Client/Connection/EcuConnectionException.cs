﻿using System;

namespace MgFlasher.Ecu.Client.Connection;

public class EcuConnectionException : Exception
{
    public static class ErrorCodes
    {
        public const string ConnectionLost = nameof(ConnectionLost);
        public const string CouldNotConnect = nameof(CouldNotConnect);
    }

    public string ErrorCode { get; }
    public string AdditionalMessage { get; }

    public EcuConnectionException(string errorCode, string additionalMessage = null)
        : base(errorCode + (!string.IsNullOrEmpty(additionalMessage) ? $": [{additionalMessage}]" : ""))
    {
        ErrorCode = errorCode;
        AdditionalMessage = additionalMessage;
    }
}