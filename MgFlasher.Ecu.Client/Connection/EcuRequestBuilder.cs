﻿using System;
using MgFlasher.Ecu.Client.Commands;
using MgFlasher.Ecu.Client.Connection.Contract;

namespace MgFlasher.Ecu.Client.Connection;

public class EcuRequestBuilder : IEcuRequestBuilder
{
    private const TesterInterfaceId _testerId = TesterInterfaceId.ETHERNET;
    private ModuleId? _moduleId;
    private EcuRequest _request;

    public EcuRequestBuilder()
    {
    }

    public EcuRequestBuilder(ModuleId moduleId)
    {
        _moduleId = moduleId;
    }

    public IEcuRequestBuilder Create(EcuConnectionContext context)
    {
        if (!context.ActiveModule.HasValue)
        {
            throw new InvalidOperationException($"Module not initialized. Please call {nameof(InitializeModuleCommand)}");
        }

        return new EcuRequestBuilder(context.ActiveModule.Value);
    }

    public EcuRequest Build()
    {
        if (_request is null)
        {
            throw new InvalidOperationException("Builder incorrectly configured");
        }

        return _request;
    }

    public IEcuRequestBuilder DiagnosticsSessionControl(UDS_DiagMode diagMode)
    {
        var data = UDSFormatCmd(UDS_DiagCmd.DiagnosticSessionControl, (byte)diagMode);

        _request = new EcuRequest(data, _testerId, _moduleId.Value, false);

        return this;
    }

    public IEcuRequestBuilder TesterPresent()
    {
        var data = UDSFormatCmd(UDS_DiagCmd.TesterPresent, 0x00);
        _request = new EcuRequest(data, _testerId, _moduleId.Value, false);

        return this;
    }

    public IEcuRequestBuilder ClearDynamicallyDefinedDataIdentifier(UDS_RecordIdentifier recordIdentifier)
    {
        var recordCommonIdentifier = (ushort)recordIdentifier;

        var data = new byte[]
        {
            (byte)UDS_DiagCmd.DynamicallyDefineDataIdentifier,
            (byte)UDS_DynamicallyDefineDataIdentifierAction.clearDynamicallyDefinedDataIdentifier,
            (byte)(recordCommonIdentifier >> 8),
            (byte)recordCommonIdentifier
        };

        _request = new EcuRequest(data, _testerId, _moduleId.Value, true);

        return this;
    }

    public IEcuRequestBuilder DefineByIdentifier(UDS_RecordIdentifier recordIdentifier, ushort[] ids, byte[] sourcePosIds, byte[] lengthsIds)
    {
        var recordCommonIdentifier = (ushort)recordIdentifier;
        var data = new byte[4 + ids.Length * 4];
        data[0] = (byte)UDS_DiagCmd.DynamicallyDefineDataIdentifier;
        data[1] = (byte)UDS_DynamicallyDefineDataIdentifierAction.defineByIdentifier;
        data[2] = (byte)(recordCommonIdentifier >> 8);
        data[3] = (byte)recordCommonIdentifier;

        for (int ddi = 0; ddi < ids.Length; ddi++)
        {
            int ddi_base = ddi * 4 + 4;
            data[ddi_base + 0] = (byte)(ids[ddi] >> 8);
            data[ddi_base + 1] = (byte)ids[ddi];
            data[ddi_base + 2] = sourcePosIds[ddi];
            data[ddi_base + 3] = lengthsIds[ddi];
        }

        _request = new EcuRequest(data, _testerId, _moduleId.Value, true);

        return this;
    }

    public IEcuRequestBuilder DefineByMemoryAddress(UDS_RecordIdentifier recordIdentifier, uint[] memoryAddress, uint[] memorySize)
    {
        var recordCommonIdentifier = (ushort)recordIdentifier;
        byte recordSize = 5, memFormat = 0x14;

        var data = new byte[5 + memoryAddress.Length * recordSize];
        data[0] = (byte)UDS_DiagCmd.DynamicallyDefineDataIdentifier;
        data[1] = (byte)UDS_DynamicallyDefineDataIdentifierAction.defineByMemoryAddress;
        data[2] = (byte)(recordCommonIdentifier >> 8);
        data[3] = (byte)recordCommonIdentifier;
        data[4] = memFormat;

        for (int ddi = 0; ddi < memoryAddress.Length; ddi++)
        {
            int ddi_base = ddi * recordSize + 5;
            byte[] memBlock = ResponseStripLeading(FormatAddressSize(memoryAddress[ddi], memorySize[ddi], UDS_AddressFormat.COMPACT), 1);
            memBlock.CopyTo(data, ddi_base);
        }

        _request = new EcuRequest(data, _testerId, _moduleId.Value, true);

        return this;
    }

    public IEcuRequestBuilder ReadDataByIdentifier(UDS_RecordIdentifier recordIdentifier, int dataMinimumSize)
    {
        var recordCommonIdentifier = (ushort)recordIdentifier;
        var data = new byte[]
        {
            (byte)UDS_DiagCmd.ReadDataByIdentifier,
            (byte)(recordCommonIdentifier >> 8),
            (byte)recordCommonIdentifier
        };

        _request = new EcuRequest(data, _testerId, _moduleId.Value, true, dataMinimumSize);

        return this;
    }

    public IEcuRequestBuilder RoutineControl(UDS_RoutineAction routineAction, UDS_RoutineIdentifier routineIdentifier, byte[] arguments)
    {
        var data = new byte[4 + arguments.Length];
        data[0] = (byte)UDS_DiagCmd.RoutineControl;
        data[1] = (byte)routineAction;
        data[2] = (byte)((ushort)routineIdentifier >> 8);
        data[3] = (byte)((ushort)routineIdentifier);
        arguments.CopyTo(data, 4);

        _request = new EcuRequest(data, _testerId, _moduleId.Value, true);

        return this;
    }

    public IEcuRequestBuilder ReadMemoryByAddress(uint startAddress, uint size)
    {
        var cmd = FormatAddressSize(startAddress, size, UDS_AddressFormat.COMPACT);
        var data = new byte[1 + cmd.Length];
        data[0] = (byte)UDS_DiagCmd.ReadMemoryByAddress;
        cmd.CopyTo(data, 1);

        _request = new EcuRequest(data, _testerId, _moduleId.Value, true);

        return this;
    }

    private static byte[] ResponseStripLeading(byte[] data, byte leadingToStrip)
    {
        if (leadingToStrip > data.Length)
        {
            return Array.Empty<byte>();
        }

        int strippedLength = data.Length - leadingToStrip;
        byte[] strippedFrame = new byte[strippedLength];
        Buffer.BlockCopy(data, leadingToStrip, strippedFrame, 0, strippedLength);
        return strippedFrame;
    }

    private byte[] FormatAddressSize(uint startAddress, uint size, UDS_AddressFormat format)
    {
        var memBlockLength = format switch
        {
            UDS_AddressFormat.COMPACT => 6,
            UDS_AddressFormat.MID => 7,
            _ => 9
        };
        var lengthSizeLengthAddress = format switch
        {
            UDS_AddressFormat.COMPACT => 0x14,
            UDS_AddressFormat.MID => 0x24,
            _ => 0x44
        };

        var memBlock = new byte[memBlockLength];
        memBlock[0] = (byte)lengthSizeLengthAddress;
        memBlock[1] = (byte)(startAddress >> 24);
        memBlock[2] = (byte)(startAddress >> 16);
        memBlock[3] = (byte)(startAddress >> 8);
        memBlock[4] = (byte)startAddress;

        if (format == UDS_AddressFormat.COMPACT)
        {
            memBlock[5] = (byte)size;
        }
        else if (format == UDS_AddressFormat.MID)
        {
            memBlock[5] = (byte)(size >> 8); // take care, BMW fast transport cannot handle more than 255 payload)
            memBlock[6] = (byte)size;
        }
        else
        {
            memBlock[5] = (byte)(size >> 24);
            memBlock[6] = (byte)(size >> 16);
            memBlock[7] = (byte)(size >> 8);
            memBlock[8] = (byte)size;
        }

        return memBlock;
    }

    private byte[] UDSFormatCmd(UDS_DiagCmd serviceId, byte subCommand, UDS_SuppressPositiveResponse suppressPositiveResponse = UDS_SuppressPositiveResponse.No)
    {
        byte[] data = new byte[2];
        data[0] = (byte)serviceId;
        data[1] = (byte)((byte)subCommand | (byte)suppressPositiveResponse);
        return data;
    }
}