﻿using System.Threading.Tasks;
using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Ecu.Client.Connection.Standard;
using Microsoft.Extensions.Logging;

namespace MgFlasher.Ecu.Client.Connection;

public class EcuConnectionFactory : IEcuConnectionFactory
{
    private readonly ITcpClientConnector _tcpClientConnector;
    private readonly ILoggerFactory _loggerFactory;

    public EcuConnectionFactory(
        ITcpClientConnector tcpClientConnector,
        ILoggerFactory loggerFactory)
    {
        _tcpClientConnector = tcpClientConnector;
        _loggerFactory = loggerFactory;
    }

    public virtual async Task<IEcuConnection> CreateAsync(string host, int port)
    {
        return await CreateTcpClientConnectionAsync(host, port);
    }

    public async Task<IEcuConnection> CreateTcpClientConnectionAsync(string host, int port)
    {
        var tcpClient = await _tcpClientConnector.ConnectAsync(host, port);
        var context = new EcuConnectionContext(host, port);
        var connection = new EnettStandardEcuConnection(context, tcpClient, _loggerFactory);

        return connection;
    }
}