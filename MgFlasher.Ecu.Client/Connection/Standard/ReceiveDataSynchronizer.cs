﻿using System;
using System.Collections.Generic;
using System.Threading;

namespace MgFlasher.Ecu.Client.Connection.Standard;

public class ReceiveDataSynchronizer : IDisposable
{
    private readonly EcuFrameLogger _ecuFrameLogger;
    private readonly AutoResetEvent _event = new AutoResetEvent(false);
    private readonly Queue<byte[]> _queue = new Queue<byte[]>();

    public readonly object Lock = new object();

    public ReceiveDataSynchronizer(EcuFrameLogger ecuFrameLogger)
    {
        _ecuFrameLogger = ecuFrameLogger;
    }

    public void Dispose()
    {
        _event.Reset();
        _queue.Clear();
    }

    public void ClearWithEventSetQueue()
    {
        lock (Lock)
        {
            _event.Reset();
            _queue.Clear();
        }
    }

    public int ResetEventOnEmptyQueue()
    {
        int recTels = 0;
        lock (Lock)
        {
            recTels = _queue.Count;
            if (recTels == 0)
            {
                _event.Reset();
            }
        }

        return recTels;
    }

    public byte[] Dequeue()
    {
        byte[] data = null;
        lock (Lock)
        {
            if (_queue.Count > 0)
            {
                data = _queue.Dequeue();
            }
        }

        return data;
    }

    public bool WaitForData(int timeout)
    {
        return _event.WaitOne(timeout, false);
    }

    public void Enqueue(byte[] diagBuffer, long telLen, int tcpDiagRecLen)
    {
        lock (Lock)
        {
            if (_queue.Count > 256)
            {
                _queue.Dequeue();
            }

            byte[] recTelTemp = new byte[telLen];
            Array.Copy(diagBuffer, recTelTemp, tcpDiagRecLen);

            _ecuFrameLogger.LogResponse(recTelTemp);
            _queue.Enqueue(recTelTemp);
            _event.Set();
        }
    }
}