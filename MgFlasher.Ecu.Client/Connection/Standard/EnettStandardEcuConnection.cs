﻿using System;
using System.IO;
using System.Net.Sockets;
using System.Threading.Tasks;
using MgFlasher.Ecu.Client.Connection.Contract;
using Microsoft.Extensions.Logging;

namespace MgFlasher.Ecu.Client.Connection.Standard;

public class EnettStandardEcuConnection : IEcuConnection
{
    private readonly TcpClient _tcpClient;
    private readonly ILogger<EnettStandardEcuConnection> _logger;
    private readonly NetworkStream _stream;
    private readonly StandardTransmitter _transmitter;

    public EcuConnectionContext Context { get; }

    public EnettStandardEcuConnection(EcuConnectionContext context, TcpClient tcpClient, ILoggerFactory loggerFactory)
    {
        Context = context;
        _tcpClient = tcpClient;
        _logger = loggerFactory.CreateLogger<EnettStandardEcuConnection>();
        _stream = _tcpClient.GetStream();
        _transmitter = new StandardTransmitter(_stream, new EcuFrameLogger(loggerFactory), loggerFactory);
    }

    public async Task<EcuResponse> Send(EcuRequest request)
    {
        try
        {
            var rs = await _transmitter.Send(request);

            return rs;
        }
        catch (InsufficientExecutionStackException ex)
        {
            _logger.LogInformation(ex, "Ecu channel closed. Send messages not allowed");

            throw new EcuConnectionException(EcuConnectionException.ErrorCodes.ConnectionLost);
        }
        catch (IOException ex)
        {
            _logger.LogInformation(ex, "Ecu channel closed. Send messages not allowed");

            throw new EcuConnectionException(EcuConnectionException.ErrorCodes.ConnectionLost);
        }
        catch (SocketException ex)
        {
            _logger.LogInformation(ex, "Ecu channel closed. Send messages not allowed");

            throw new EcuConnectionException(EcuConnectionException.ErrorCodes.ConnectionLost);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error while sending request to ecu");

            return EcuResponse.UnknownError();
        }
    }

    public async ValueTask DisposeAsync()
    {
        try
        {
            _transmitter.Dispose();
            _stream.Close();
            _tcpClient.Close();
            _tcpClient.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dispose error");
        }

        await Task.Yield();
    }
}