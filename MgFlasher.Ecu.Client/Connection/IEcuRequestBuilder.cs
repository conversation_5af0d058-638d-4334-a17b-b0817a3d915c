﻿using MgFlasher.Ecu.Client.Connection.Contract;

namespace MgFlasher.Ecu.Client.Connection;

public interface IEcuRequestBuilder
{
    IEcuRequestBuilder Create(EcuConnectionContext context);

    IEcuRequestBuilder DiagnosticsSessionControl(UDS_DiagMode diagMode);

    IEcuRequestBuilder DefineByIdentifier(UDS_RecordIdentifier recordIdentifier, ushort[] ids, byte[] sourcePosIds, byte[] lengthsIds);

    IEcuRequestBuilder DefineByMemoryAddress(UDS_RecordIdentifier recordIdentifier, uint[] memoryAddress, uint[] memorySize);

    IEcuRequestBuilder ClearDynamicallyDefinedDataIdentifier(UDS_RecordIdentifier recordIdentifier);

    IEcuRequestBuilder ReadDataByIdentifier(UDS_RecordIdentifier recordIdentifier, int dataMinimumSize = 0);

    IEcuRequestBuilder RoutineControl(UDS_RoutineAction routineAction, UDS_RoutineIdentifier routineIdentifier, byte[] arguments);

    IEcuRequestBuilder TesterPresent();

    IEcuRequestBuilder ReadMemoryByAddress(uint startAddress, uint size);

    EcuRequest Build();
}