using System;
using System.Linq;
using Microsoft.Extensions.Logging;

namespace MgFlasher.Ecu.Client.Connection;

public class Ecu<PERSON>rameLogger
{
    private readonly ILogger _logger;
    public static bool Enabled { get; set; }

    public EcuFrameLogger(ILoggerFactory loggerFactory)
    {
        _logger = loggerFactory.CreateLogger<EcuFrameLogger>();
    }

    public void LogRequest(byte[] frame)
    {
        if (Enabled)
        {
            _logger.LogInformation($"[ECU] [{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}] RQ: [{{Data}}]", string.Join(" ", frame.Select(x => $"{x:X2}")));
        }
    }

    public void LogResponse(byte[] frame)
    {
        if (Enabled)
        {
            _logger.LogInformation($"[ECU] [{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}] RS: [{{Data}}]", string.Join(" ", frame.Select(x => $"{x:X2}")));
        }
    }
}