﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<GeneratePackageOnBuild>true</GeneratePackageOnBuild>
		<IncludeSource>True</IncludeSource>
		<IncludeSymbols>True</IncludeSymbols>
		<AssemblyName>MgFlasher.Ecu.Client</AssemblyName>
		<RootNamespace>MgFlasher.Ecu.Client</RootNamespace>
		<Company>JR Auto Performance Inc.</Company>
		<Authors>MGFlasher Team</Authors>
		<VersionPrefix>1.0.0</VersionPrefix>
		<RepositoryUrl>https://github.com/mgflasher-team/mgflasher-app</RepositoryUrl>
		<StringEncryption>hash</StringEncryption>
		<ResourceEncryption>true</ResourceEncryption>
		<ControlFlowObfuscation>if=on;switch=on;case=on;call=on</ControlFlowObfuscation>
		<ControlFlowIterations>3</ControlFlowIterations>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="MediatR" Version="12.4.1" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.1" />		
		<PackageReference Include="Polly" Version="8.5.1" />
		<PackageReference Condition="'$(Configuration)' == 'Release' " Include="Babel.Obfuscator" Version="10.9.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\MgFlasher.Ecu.Common\MgFlasher.Ecu.Common.csproj" />
	</ItemGroup>

</Project>
