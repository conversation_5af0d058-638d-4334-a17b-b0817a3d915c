﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<IsPackable>false</IsPackable>
	</PropertyGroup>

	<ItemGroup>
		<Content Include="..\xunit.runner.json" Link="xunit.runner.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoFixture" Version="4.18.1" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
		<PackageReference Include="NSubstitute" Version="5.3.0" />
		<PackageReference Include="FluentAssertions" Version="7.0.0" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.0.1">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="XunitXml.TestLogger" Version="5.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\MgFlasher.Ecu.Client\MgFlasher.Ecu.Client.csproj" />
	</ItemGroup>

</Project>