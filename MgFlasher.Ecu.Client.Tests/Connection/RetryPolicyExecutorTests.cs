﻿using System;
using System.Threading.Tasks;
using FluentAssertions;
using MgFlasher.Ecu.Client.Connection;
using MgFlasher.Ecu.Client.Connection.Contract;
using Xunit;
using Xunit.Abstractions;

namespace MgFlasher.Ecu.Client.Tests.Connection;

public class RetryPolicyExecutorTests
{
    private readonly RetryPolicyExecutor _target;

    public RetryPolicyExecutorTests(ITestOutputHelper testOutputHelper)
    {
        _target = new RetryPolicyExecutor(XUnitLogger.CreateLogger<RetryPolicyExecutor>(testOutputHelper));
    }

    [Fact]
    public async Task ExecuteAndRepeatOnNackOrErrorMessageAsync_ShouldNotRepeat_OnException()
    {
        //arrange
        var repeatCounter = 0;
        Func<Task<RawEcuResponse>> input = async () =>
        {
            repeatCounter++;
            await Task.Yield();
            throw new Exception();
        };

        //act
        Func<Task> act = async () => await _target.ExecuteAndRepeatOnNackOrErrorMessageAsync(input);

        //assert
        await act.Should().ThrowAsync<Exception>();
        repeatCounter.Should().Be(1);
    }
}