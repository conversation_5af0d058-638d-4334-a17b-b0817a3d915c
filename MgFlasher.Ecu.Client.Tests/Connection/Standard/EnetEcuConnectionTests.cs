﻿using System;
using System.Threading.Tasks;
using FluentAssertions;
using MgFlasher.Ecu.Client.Connection;
using MgFlasher.Ecu.Client.Connection.Contract;
using MgFlasher.Ecu.Client.Connection.Standard;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;
using Xunit.Abstractions;

namespace MgFlasher.Ecu.Client.Tests.Connection.Standard;

[Collection("Sequential")]
public class EnetEcuConnectionTests
{
    private const string AurixIpAddress = "szymonsbasement.duckdns.org:8886";
    private const string PpcIpAddress = "szymonsbasement.duckdns.org:8887";
    private readonly TcpClientConnector _tcpClientConnector;
    private readonly EcuConnectionFactory _connectionFactory;
    private readonly EcuRequestBuilder _ecuRequestBuilder;
    private readonly ILoggerFactory _loggerFactory;

    public EnetEcuConnectionTests(ITestOutputHelper testOutputHelper)
    {
        _loggerFactory = Substitute.For<ILoggerFactory>();
        _tcpClientConnector = new TcpClientConnector();
        _connectionFactory = new EcuConnectionFactory(_tcpClientConnector, _loggerFactory);
        _ecuRequestBuilder = new EcuRequestBuilder();
    }

    [Theory]
    [Trait("Category", "RealDevice")]
    [InlineData(AurixIpAddress)]
    [InlineData(PpcIpAddress)]
    public async Task Send_TcpClient_TesterPresent(string ip)
    {
        //arrange
        await using var connection = await CreateConnection(ip, (h, p) => _connectionFactory.CreateTcpClientConnectionAsync(h, p));
        var rq = _ecuRequestBuilder
            .Create(connection.Context)
            .TesterPresent()
            .Build();

        //act
        var rs = await connection.Send(rq);

        //assert
        rs.IsDataResponse.Should().BeFalse();
        rs.Data.Should().BeNull();
        rs.FullResponse.Should().HaveCount(10);
        rs.FullResponse.Should().BeEquivalentTo(new byte[] { 0, 0, 0, 4, 0, 2, 244, 18, 62, 0 });
    }

    [Theory]
    [Trait("Category", "RealDevice")]
    [InlineData(AurixIpAddress, 992948)]
    [InlineData(PpcIpAddress, 992296)]
    public async Task Send_TcpClient_SgbdIndex(string ip, int expected)
    {
        //arrange
        await using var connection = await CreateConnection(ip, (h, p) => _connectionFactory.CreateTcpClientConnectionAsync(h, p));
        var rq = _ecuRequestBuilder
            .Create(connection.Context)
            .ReadDataByIdentifier(UDS_RecordIdentifier.SGBDIndex)
            .Build();

        //act
        var rs = await connection.Send(rq);

        //assert
        rs.Success.Should().BeTrue();
        ((rs.Data[0] << 16) + (rs.Data[1] << 8) + rs.Data[2]).Should().Be(expected);
    }

    private async Task<IEcuConnection> CreateConnection(string ip, Func<string, int, Task<IEcuConnection>> selector)
    {
        var splitted = ip.Split(":");
        var connection = await selector.Invoke(splitted[0], int.Parse(splitted[1]));
        connection.Context.UpdateActiveModule(ModuleId.DME_MASTER);

        return connection;
    }
}